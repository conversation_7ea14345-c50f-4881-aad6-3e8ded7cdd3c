CREATE TABLE service_type (
	id serial PRIMARY KEY,
	name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
	active boolean default false
);

CREATE TABLE pet_type (
	id serial PRIMARY KEY,
	name VARCHAR(50) UNIQUE NOT NULL
);

CREATE TABLE general_pet_size (
	id serial PRIMARY KEY,
	size VARCHAR(50) UNIQUE NOT NULL,
	weight_value int,
	weight_unit VARCHAR(5),
	active boolean default false
);

CREATE TABLE pet_cologne
(
    id serial PRIMARY KEY,
	name text UNIQUE NOT NULL,
    extra_charge double precision,
    extra_currency text,
	active boolean default false
);

CREATE TABLE addon_service
(
    id serial PRIMARY KEY,
	name text UNIQUE NOT NULL,
    tack_on_extra_minutes int,
    tack_on_extra_amount decimal(10,5),
	tack_on_extra_amount_currency VARCHAR(10),
    active boolean default false
);	

CREATE TABLE feeding_information
(
    id serial PRIMARY KEY,
	display boolean,
    feed_count int,
    grain_free_recipes text[],
	grain_full_recipes text[],
	amount_per_extra_cup decimal(10,5),
    amount_currency text
);		

CREATE TABLE personality_parameter (
	id serial PRIMARY KEY,
	name VA<PERSON><PERSON>R(50) UNIQUE NOT NULL,
	active boolean default false
);

CREATE TABLE unfriendly_behaviour_trigger (
	id serial PRIMARY KEY,
	name VARCHAR(50) UNIQUE NOT NULL,
	active boolean default false
);

CREATE TABLE threat_reaction (
	id serial PRIMARY KEY,
	name VARCHAR(50) UNIQUE NOT NULL,
	active boolean default false
);

CREATE TABLE bitting_history (
	id serial PRIMARY KEY,
	name VARCHAR(50) UNIQUE NOT NULL,
	active boolean default false
);

CREATE TABLE venue_address (
	id serial PRIMARY KEY,
	street1 VARCHAR(50),
	street2 VARCHAR(50),
	city VARCHAR(50),
	state VARCHAR(50),
	zipcode VARCHAR(50),
	country VARCHAR(50)	
);

CREATE TABLE temperament (
	id serial PRIMARY KEY,
	pet_type_id int NOT NULL,
	name VARCHAR(50) UNIQUE NOT NULL,
	active boolean default false,
	FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON UPDATE CASCADE
);


