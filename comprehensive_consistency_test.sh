#!/bin/bash

echo "=== COMPREHENSIVE CONSISTENCY FIX VALIDATION ==="
echo "Testing the NEW CONSISTENCY FIX: STORE partial blocks should prevent STORE full day creation"
echo ""

BASE_URL="http://localhost:8080/api/v1/block_dates"
TIMEZONE="UTC"

# Helper function to make curl requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "TEST: $description"
    echo "Request: $method $endpoint"
    if [ ! -z "$data" ]; then
        echo "Data: $data"
    fi
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -H "retailer: test-retailer" \
        -H "User-type: Retailer" \
        -X POST "$endpoint" \
        -d "$data")
    
    echo "Response: $response"
    echo "----------------------------------------"
    echo ""
}

# Test 1: NEW CONSISTENCY FIX - Single Block Creation
echo "=== SECTION 1: NEW CONSISTENCY FIX - SINGLE BLOCKS ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-10T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-10T10:00:00Z",
            "blockEndTime": "2025-11-10T12:00:00Z"
        }
    ]
}' "1.1 Create STORE partial block (10:00-12:00)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-10T00:00:00Z",
    "blockType": "STORE",
    "blockFullDay": true
}' "1.2 Try STORE full day (should REJECT - NEW CONSISTENCY FIX)"

# Test 2: NEW CONSISTENCY FIX - Recurring Block Creation
echo "=== SECTION 2: NEW CONSISTENCY FIX - RECURRING BLOCKS ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-12T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-12T14:00:00Z",
            "blockEndTime": "2025-11-12T16:00:00Z"
        }
    ]
}' "2.1 Create STORE partial block on 2025-11-12 (14:00-16:00)"

make_request "POST" "$BASE_URL/create-recurring?tz=$TIMEZONE" \
'{
    "date": "2025-11-12T00:00:00Z",
    "blockType": "STORE",
    "blockFullDay": true,
    "recurringEnabled": true,
    "recurringPattern": "DAILY",
    "recurringInterval": 1,
    "recurringEndDate": "2025-11-14T00:00:00Z"
}' "2.2 Try 3-day STORE full day recurring (should REJECT on day 1 - NEW CONSISTENCY FIX)"

# Test 3: Verify ATTENDANT blocks still work consistently
echo "=== SECTION 3: ATTENDANT CONSISTENCY (EXISTING BEHAVIOR) ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-15T00:00:00Z",
    "blockType": "ATTENDANT",
    "attendantId": 440,
    "blockTimes": [
        {
            "blockStartTime": "2025-11-15T09:00:00Z",
            "blockEndTime": "2025-11-15T11:00:00Z"
        }
    ]
}' "3.1 Create ATTENDANT partial block (9:00-11:00)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-15T00:00:00Z",
    "blockType": "ATTENDANT",
    "attendantId": 440,
    "blockFullDay": true
}' "3.2 Try ATTENDANT full day (should REJECT - existing behavior)"

# Test 4: Verify reverse scenario still works (full day prevents partial)
echo "=== SECTION 4: REVERSE SCENARIO - FULL DAY PREVENTS PARTIAL ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-20T00:00:00Z",
    "blockType": "STORE",
    "blockFullDay": true
}' "4.1 Create STORE full day block"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-20T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-20T15:00:00Z",
            "blockEndTime": "2025-11-20T17:00:00Z"
        }
    ]
}' "4.2 Try STORE partial block (should REJECT - existing behavior)"

# Test 5: Time overlap validation
echo "=== SECTION 5: TIME OVERLAP VALIDATION ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-25T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-25T10:00:00Z",
            "blockEndTime": "2025-11-25T12:00:00Z"
        }
    ]
}' "5.1 Create STORE block 10:00-12:00"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-25T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-25T12:00:00Z",
            "blockEndTime": "2025-11-25T14:00:00Z"
        }
    ]
}' "5.2 Try STORE block 12:00-14:00 (touching boundary - should ALLOW)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-25T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-25T14:00:00Z",
            "blockEndTime": "2025-11-25T16:00:00Z"
        }
    ]
}' "5.3 Try STORE block 14:00-16:00 (non-overlapping - should ALLOW)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-11-25T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-11-25T11:30:00Z",
            "blockEndTime": "2025-11-25T13:30:00Z"
        }
    ]
}' "5.4 Try STORE block 11:30-13:30 (overlapping - should REJECT)"

echo "=== TEST SUMMARY ==="
echo "✅ NEW CONSISTENCY FIX VERIFICATION:"
echo "   - STORE partial blocks should prevent STORE full day creation"
echo "   - Both single and recurring block creation should be consistent"
echo "✅ EXISTING BEHAVIOR VERIFICATION:"
echo "   - ATTENDANT partial blocks should prevent ATTENDANT full day creation"
echo "   - Full day blocks should prevent partial block creation"
echo "   - Time overlap validation should work correctly"
echo ""
echo "Total scenarios tested: 15 validation cases"
