# Environment configuration
ACTIVE_PROFILE=dev

# AWS Configuration
#AWS_ACCESS_KEY_ID=JDSJDSFJDFJJJSF<PERSON>
#AWS_SECRET_ACCESS_KEY=WQDEFJWJWDEJWKQEQWER
#AWS_REGION=us-west-1
#AWS_BUCKET_NAME=demo-bucket
#AWS_END_POINT=https://demo-bucket.s3-us-west-2.amazonaws.com/
AWS_KINESIS_SHARD_FOR_SERVICING=SDGFDG
KINESIS_REGION=us-east-1
KINESIS_APP_NAME=etailbookit-api
KINESIS_SHARD_FOR_SLOT_GENERATION=0
KINESIS_STREAM_FOR_SERVICE_SYNC=etailpet-dev-stream
KINESIS_STREAM_FOR_SLOT_GENERATION=bookit-dev-slot-generation

# Database Configuration
#DATA_SOURCE=localhost:5433/prod2025sept11
#DB_USERNAME=bookit
#DB_PASSWORD=root
DATA_SOURCE=localhost:5432/bookit-db-v2
DB_USERNAME=postgres
DB_PASSWORD=newpassword

# Flyway Database Configuration (for migrations)
DATA_SOURCE_URL=localhost:5432/bookit-db-v2
DATA_SOURCE_USERNAME=postgres
DATA_SOURCE_PASSWORD=newpassword


# Application Settings
CATALINA_OPTS=
SCHEDULER_INTERVEL=5
spring.servlet.multipart.max-file=

# E-commerce API
ECOMM_API_BASE_URL=https://dev.etailpet.com/
ECOMM_API_CLIENT_ID=izbH1FIOyPzEbOL3lRelJ7ZjbOIj1TPWOPK69iHu
ECOMM_API_CLIENT_SECRET=8YcxZMPUcIyUpdwgv1R74EpQvCbFA48bRGHqjHGWOnjfXdegXtoMFqcQ40vx8tLvYcV3mwUQUTsHkK8NGx5relmyfj7pN9jCtxCzdQrDhFnRuyrVfBeO27sARWwMIJsy
FEIGN_CLIENT_URL=https://pos.dev.etailpet.com/

# Google Calendar API Configuration
# Get Client ID and Secret from: https://console.cloud.google.com/
#GOOGLE_CALENDAR_CLIENT_ID=714300633365-pceak540lvmr58ugbe1uno7gh4qhp4n8.apps.googleusercontent.com
#GOOGLE_CALENDAR_CLIENT_SECRET=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

# Frontend callback URL - Where Google redirects after user authorizes
# IMPORTANT: Configure this URL in Google Cloud Console -> Authorized redirect URIs
# Development:
#GOOGLE_CALENDAR_REDIRECT_URI=http://localhost:3000/google-calendar-sync/callback
# Production: https://your-frontend.com/google-calendar-sync/callback

# Frontend invitation URL - Where users land from email
# Development:
#GOOGLE_CALENDAR_FRONTEND_URL=http://localhost:3000/google-calendar-sync/collab
# Production: https://your-frontend.com/google-calendar-sync/collab

# Security / Certificates
KEYSTORE_LOCATION=
KEYSTORE_PASSWORD=

# Integrations
SENDGRID_API_KEY=
SENTRY_DSN=

KINESIS_CREDENTIAL_ACCESSKEY=gggggggggggggg
KINESIS_CREDENTIAL_SECRETKEY=hhhhhhhhhhhhhh

GOOGLE_CALENDAR_FRONTEND_URL=https://etailpet.com
GOOGLE_CALENDAR_REDIRECT_URI=https://etailpet.com/accounts/google/callback/
GOOGLE_CALENDAR_CLIENT_ID=************-bbv6jlru8hru2oshoii711o9c0qg3sm6.apps.googleusercontent.com
GOOGLE_CALENDAR_CLIENT_SECRET=DthDx6tWMHyz9f6C7wg2_SAv


AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_REGION=us-east-1
AWS_BUCKET_NAME=demo-bucket
AWS_END_POINT=http://localhost:4566
