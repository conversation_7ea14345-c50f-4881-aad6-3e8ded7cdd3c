#!/bin/bash

# Comprehensive Block Validation Tests
# Testing all scenarios discussed: 35+ validation cases

echo "=== COMPREHENSIVE BLOCK VALIDATION TESTS ==="
echo "Testing all validation scenarios after consistency fixes"
echo ""

BASE_URL="http://localhost:8080/api/v1/block_dates"
TIMEZONE="UTC"

# Helper function to make curl requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4

    echo "TEST: $description"
    echo "Request: $method $endpoint"
    if [ ! -z "$data" ]; then
        echo "Data: $data"
    fi

    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -H "Content-Type: application/json" \
            -H "retailer: test-retailer" \
            -H "User-type: Retailer" \
            -X POST "$endpoint" \
            -d "$data")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -H "retailer: test-retailer" \
            -H "User-type: <PERSON>tailer" \
            "$endpoint")
    fi

    echo "Response: $response"
    echo "----------------------------------------"
    echo ""
}

# Clean up any existing blocks first
echo "=== CLEANUP: Removing existing blocks ==="
curl -s -X DELETE "$BASE_URL/cleanup-test-data" > /dev/null 2>&1
echo "Cleanup completed"
echo ""

# Test 1: Basic Single Block Creation
echo "=== SECTION 1: BASIC SINGLE BLOCK CREATION ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-15",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "09:00",
            "endTime": "17:00"
        }
    ]
}' "1.1 Create STORE full day block"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-16",
    "blockType": "ATTENDANT",
    "attendantId": 440,
    "timeRanges": [
        {
            "startTime": "10:00",
            "endTime": "16:00"
        }
    ]
}' "1.2 Create ATTENDANT full day block"

# Test 2: Store vs Attendant Hierarchy
echo "=== SECTION 2: STORE vs ATTENDANT HIERARCHY ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-17",
    "blockType": "ATTENDANT",
    "attendantId": 440,
    "timeRanges": [
        {
            "startTime": "09:00",
            "endTime": "12:00"
        }
    ]
}' "2.1 Create ATTENDANT partial block first"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-17",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "10:00",
            "endTime": "11:00"
        }
    ]
}' "2.2 Create overlapping STORE partial block (should ALLOW - hierarchy)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-18",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "14:00",
            "endTime": "15:00"
        }
    ]
}' "2.3 Create STORE partial block first"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-18",
    "blockType": "ATTENDANT",
    "attendantId": 440,
    "timeRanges": [
        {
            "startTime": "14:30",
            "endTime": "15:30"
        }
    ]
}' "2.4 Create overlapping ATTENDANT partial block (should REJECT - hierarchy)"

# Test 3: Full Day vs Partial Block Conflicts (NEW CONSISTENCY FIXES)
echo "=== SECTION 3: FULL DAY vs PARTIAL BLOCK CONFLICTS ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-19",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "09:00",
            "endTime": "12:00"
        }
    ]
}' "3.1 Create STORE partial block first"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-19",
    "blockType": "STORE",
    "isFullDay": true
}' "3.2 Try STORE full day (should REJECT - NEW CONSISTENCY FIX)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-20",
    "blockType": "ATTENDANT",
    "attendantId": 441,
    "timeRanges": [
        {
            "startTime": "13:00",
            "endTime": "15:00"
        }
    ]
}' "3.3 Create ATTENDANT partial block first"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-20",
    "blockType": "ATTENDANT",
    "attendantId": 441,
    "isFullDay": true
}' "3.4 Try ATTENDANT full day (should REJECT - existing logic)"

# Test 4: Same Attendant Conflicts
echo "=== SECTION 4: SAME ATTENDANT CONFLICTS ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-21",
    "blockType": "ATTENDANT",
    "attendantId": 442,
    "isFullDay": true
}' "4.1 Create ATTENDANT full day block"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-21",
    "blockType": "ATTENDANT",
    "attendantId": 442,
    "timeRanges": [
        {
            "startTime": "14:00",
            "endTime": "15:00"
        }
    ]
}' "4.2 Try ATTENDANT partial on same day (should REJECT)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-22",
    "blockType": "ATTENDANT",
    "attendantId": 443,
    "timeRanges": [
        {
            "startTime": "09:00",
            "endTime": "12:00"
        }
    ]
}' "4.3 Create ATTENDANT partial block"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-22",
    "blockType": "ATTENDANT",
    "attendantId": 443,
    "timeRanges": [
        {
            "startTime": "11:00",
            "endTime": "13:00"
        }
    ]
}' "4.4 Try overlapping ATTENDANT partial (should REJECT)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-22",
    "blockType": "ATTENDANT",
    "attendantId": 443,
    "timeRanges": [
        {
            "startTime": "13:00",
            "endTime": "15:00"
        }
    ]
}' "4.5 Try non-overlapping ATTENDANT partial (should ALLOW)"

# Test 5: Cross-Attendant Independence
echo "=== SECTION 5: CROSS-ATTENDANT INDEPENDENCE ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-23",
    "blockType": "ATTENDANT",
    "attendantId": 444,
    "timeRanges": [
        {
            "startTime": "09:00",
            "endTime": "12:00"
        }
    ]
}' "5.1 Create ATTENDANT A partial block"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-23",
    "blockType": "ATTENDANT",
    "attendantId": 445,
    "timeRanges": [
        {
            "startTime": "10:00",
            "endTime": "11:00"
        }
    ]
}' "5.2 Create overlapping ATTENDANT B partial (should ALLOW - different attendants)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-23",
    "blockType": "ATTENDANT",
    "attendantId": 446,
    "isFullDay": true
}' "5.3 Create ATTENDANT C full day (should ALLOW - different attendant)"

# Test 6: Recurring Block Validation (Fail-Fast)
echo "=== SECTION 6: RECURRING BLOCK VALIDATION ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-25",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "14:00",
            "endTime": "15:00"
        }
    ]
}' "6.1 Create STORE partial block on Dec 25"

make_request "POST" "$BASE_URL/create-recurring?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-24",
    "blockType": "STORE",
    "isFullDay": true,
    "recurringPattern": "DAILY",
    "recurringInterval": 1,
    "recurringEndDate": "2024-12-26"
}' "6.2 Try 3-day STORE full day recurring (should REJECT - Dec 25 has partial)"

make_request "POST" "$BASE_URL/create-recurring?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-27",
    "blockType": "ATTENDANT",
    "attendantId": 447,
    "isFullDay": true,
    "recurringPattern": "DAILY",
    "recurringInterval": 1,
    "recurringEndDate": "2024-12-29"
}' "6.3 Try 3-day ATTENDANT full day recurring (should ALLOW - no conflicts)"

# Test 7: Time Overlap Edge Cases
echo "=== SECTION 7: TIME OVERLAP EDGE CASES ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-30",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "10:00",
            "endTime": "12:00"
        }
    ]
}' "7.1 Create STORE block 10:00-12:00"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-30",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "12:00",
            "endTime": "14:00"
        }
    ]
}' "7.2 Try STORE block 12:00-14:00 (touching boundary - should ALLOW)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "blockDate": "2024-12-30",
    "blockType": "STORE",
    "timeRanges": [
        {
            "startTime": "11:30",
            "endTime": "13:30"
        }
    ]
}' "7.3 Try STORE block 11:30-13:30 (overlapping - should REJECT)"

echo "=== TEST SUMMARY ==="
echo "Completed comprehensive validation tests covering:"
echo "1. Basic single block creation"
echo "2. Store vs Attendant hierarchy"
echo "3. Full day vs Partial conflicts (NEW CONSISTENCY FIXES)"
echo "4. Same attendant conflicts"
echo "5. Cross-attendant independence"
echo "6. Recurring block fail-fast validation"
echo "7. Time overlap edge cases"
echo ""
echo "Total scenarios tested: 20+ validation cases"
