# -------- Build stage --------
FROM maven:3.6.3-openjdk-11-slim AS builder
WORKDIR /build

# Copy pom first to leverage dependency caching
COPY pom.xml .
RUN mvn -B -ntp dependency:go-offline

# Copy source and build
COPY src ./src
RUN mvn -B -ntp -DskipTests package


# -------- Runtime stage --------
FROM tomcat:9.0-jre11-temurin-jammy
ENV CATALINA_HOME=/usr/local/tomcat
WORKDIR ${CATALINA_HOME}

# Remove default webapps (saves size + security)
RUN rm -rf ${CATALINA_HOME}/webapps/*

# Ensure required directories exist and are writable by the non-root user
RUN mkdir -p ${CATALINA_HOME}/conf/Catalina/localhost ${CATALINA_HOME}/webapps \
    && chown -R 10001:root ${CATALINA_HOME}/conf ${CATALINA_HOME}/webapps

# Copy keystore into image
# COPY .config/keystore ${CATALINA_HOME}/keystore

# Deploy WAR as ROOT with correct ownership directly
COPY --chown=10001:root --from=builder /build/target/*.war ${CATALINA_HOME}/webapps/ROOT.war

# Create non-root user
RUN useradd -r -u 10001 -g root appuser

USER 10001
EXPOSE 8080

# Healthcheck using wget (already available in base)
HEALTHCHECK --interval=30s --timeout=5s --retries=5 \
    CMD wget -qO- http://localhost:8080/actuator/health || exit 1

CMD ["catalina.sh", "run"]
