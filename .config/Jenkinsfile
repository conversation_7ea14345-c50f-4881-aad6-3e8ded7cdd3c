pipeline {
	agent any
	options {
		buildDiscarder(logRotator(numToKeepStr: '5'))
		timestamps()
		disableConcurrentBuilds()
	}
	triggers { githubPush() }
	environment {
		AWS_REGISTRY = "${env.AWS_ACCOUNT_ID}.dkr.ecr.${env.AWS_DEFAULT_REGION}.amazonaws.com"
		DOCKERFILE = '.config/Dockerfile'
		REMOTE_DIR = "${env.REMOTE_DIR ?: '/home/<USER>/bookit'}"
		SERVER_USER = "${env.SERVER_USER ?: 'ubuntu'}"
		IMAGE_TAG = "${env.IMAGE_TAG ?: 'latest'}"
	}
	stages {
		stage('Set Server Environment') {
			steps {
				script {
					env.GIT_COMMIT_MSG = sh(script: "git log -1 --pretty=%B ${env.GIT_COMMIT}", returnStdout: true).trim()
					def envConfig = [
						develop: [
							SECRET_MANAGER: "${BOOKIT_DEV_ID}",
							SERVER: "${BOOKIT_DEV_SERVER_IP}",
							DOCKER_IMAGE_NAME: "bookit-dev"
						],
						uat: [
							SECRET_MANAGER: "${BOOKIT_UAT_ID}",
							SERVER: "${BOOKIT_UAT_SERVER_IP}",
							DOCKER_IMAGE_NAME: "bookit-uat"
						]
					][env.BRANCH_NAME]

					if (!envConfig) error "No config defined for branch: ${env.BRANCH_NAME}"

					env.AWS_SECRET_MANAGER = envConfig.SECRET_MANAGER
					env.APP_SERVER = envConfig.SERVER
					env.COMPOSE_FILE = (env.BRANCH_NAME == 'develop') ? '.config/docker-compose.dev.yml' : '.config/docker-compose.uat.yml'
					env.DOCKER_IMAGE_NAME = envConfig.DOCKER_IMAGE_NAME
					echo "Server set to: ${env.APP_SERVER} for branch: ${env.BRANCH_NAME} (compose: ${env.COMPOSE_FILE})"
					slackSend(color: '#FFFF00', message: "Bookit Pipeline started for ${env.BRANCH_NAME}\nBuild: ${env.BUILD_NUMBER}\nCommit: ${env.GIT_COMMIT_MSG}")
				}
			}
		}

		stage('ECR Login') {
			steps { sh "aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_REGISTRY}" }
		}
		stage('Docker Build & Push') {
			steps {
				script {
					env.BUILD_IMAGE = "${AWS_REGISTRY}/${env.DOCKER_IMAGE_NAME}:${env.IMAGE_TAG}"
					sh "docker build -t ${BUILD_IMAGE} -f ${DOCKERFILE} ."
					sh "docker push ${BUILD_IMAGE}"
				}
			}
		}
		stage('Prepare Env File') {
			steps {
				script {
					sh """
						aws secretsmanager get-secret-value \
						--secret-id ${AWS_SECRET_MANAGER} \
						--query SecretString \
						--output text | jq -r 'to_entries | map("\\(.key)=\\(.value|tostring)") | .[]' > .env
					"""
				}
			}
		}

		stage('Prepare Keystore') {
			steps {
				sh """
					rm -rf .config/keystore && mkdir -p .config/keystore && \
					aws s3 cp s3://${BOOKIT_KEYSTORE_BUCKET}/${env.BRANCH_NAME}/keystore/ .config/keystore/ --recursive
				"""
			}
		}		
		stage('Deploy To Server') {
			steps {
				script {
					sh """
						set -e
						: \${APP_SERVER:?APP_SERVER not set}
						scp -o StrictHostKeyChecking=no ${COMPOSE_FILE} ${SERVER_USER}@${APP_SERVER}:${REMOTE_DIR}/docker-compose.yml
						scp -o StrictHostKeyChecking=no .env ${SERVER_USER}@${APP_SERVER}:${REMOTE_DIR}/.env
						ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${APP_SERVER} -t "cd ${REMOTE_DIR} && \
						aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_REGISTRY} && \
						docker pull ${AWS_REGISTRY}/${env.DOCKER_IMAGE_NAME}:${env.IMAGE_TAG} && \
						docker compose -f docker-compose.yml up -d --build bookit-app && \
						docker image prune -af"
						rm -f .env
					"""
				}
			}
		}
	}
	post {
		success {
			script { try { slackSend(color: '#2eb886', message: "Bookit ${env.BRANCH_NAME} deploy SUCCESS: ${env.JOB_NAME} #${env.BUILD_NUMBER} -> ${env.BUILD_URL}") } catch(err) { echo 'Slack not configured' } }
		}
		failure {
			script { try { slackSend(color: '#ff0000', message: "Bookit ${env.BRANCH_NAME} deploy FAILED: ${env.JOB_NAME} #${env.BUILD_NUMBER} -> ${env.BUILD_URL}") } catch(err) { echo 'Slack not configured' } }
		}
	}
} 