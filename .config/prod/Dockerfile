FROM maven:3.9.6-eclipse-temurin-11 AS builder
WORKDIR /build
COPY pom.xml .
COPY src ./src
RUN mvn clean package -Dmaven.test.skip=true


FROM tomcat:9.0-jre11-temurin
ENV CATALINA_HOME=/usr/local/tomcat
WORKDIR ${CATALINA_HOME}
# Install curl for healthcheck
RUN apt-get update && apt-get install -y --no-install-recommends curl && rm -rf /var/lib/apt/lists/*
# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*
# New Relic agent and config
COPY .config/newrelic/newrelic.jar ${CATALINA_HOME}/newrelic/newrelic.jar
COPY .config/newrelic/newrelic.yml ${CATALINA_HOME}/newrelic/newrelic.yml
# Copy keystore into image
COPY .config/keystore ${CATALINA_HOME}/keystore
ENV NEW_RELIC_APP_NAME="bookit-prod"
ENV NEW_RELIC_LICENSE_KEY=""
ENV NEW_RELIC_LOG_DIR="/usr/local/tomcat/newrelic/logs"
ENV CATALINA_OPTS="-javaagent:/usr/local/tomcat/newrelic/newrelic.jar -Dnewrelic.config.app_name=${NEW_RELIC_APP_NAME} -Dnewrelic.config.license_key=${NEW_RELIC_LICENSE_KEY} -Dnewrelic.config.log_file_name=STDOUT -Dnewrelic.config.log_level=info ${CATALINA_OPTS}"
# Deploy application WAR as ROOT
COPY --from=builder /build/target/*.war ${CATALINA_HOME}/webapps/ROOT.war
# Non-root user
RUN useradd -r -u 10001 -g root appuser && mkdir -p ${NEW_RELIC_LOG_DIR} && chown -R appuser:root ${CATALINA_HOME}
EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=5s --retries=5 CMD curl -fsS http://localhost:8080/actuator/health || exit 1
USER 10001
CMD ["catalina.sh", "run"] 