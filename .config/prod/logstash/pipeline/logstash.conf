input {
    gelf {
        port => 12201
	codec => json_lines
    }
}
filter{
      if "|ERROR|" in [message]{ 
      grok{
        match => ['message',"\[.+\] - %{IP:ip}\|%{LOGLEVEL:loglevel}\| %{PATH:file}\|%{NUMBER:line}\|%{WORD:tag}\|%{GREEDYDATA:content}"]
      }

      mutate {
        replace => [ "message", "%{content}" ] 
        remove_field => ["content"]
      } 
    } 
 
    multiline{ 
        pattern => "^\[" 
        what => "previous" 
        negate=> true 
    } 
 
    if "|DEBUG| flush_multi_line" in [message]{ 
      drop{} 
    } 
}
output {

		elasticsearch {
			hosts => ["${KIBANA_ES_HOST}"]
			index => "${KIBANA_ES_INDEX_BOOKIT}"
			user =>  "${KIBANA_ES_USER}"
			password => "${KIBANA_ES_PASS}"
		}

}
