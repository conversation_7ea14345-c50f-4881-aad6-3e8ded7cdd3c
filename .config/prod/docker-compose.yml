services:
  bookit-app:
    image: ${IMAGE_NAME}:${IMAGE_TAG}
    container_name: bookit-app
    ports:
      - "8080:8080"
    env_file:
      - ./.env
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-file: "5"
        max-size: "50m"
    networks:
      - etailpet

  logstash:
    image: ${IMAGE_NAME}:logstash
    container_name: ${APP_NAME}-logstash
    env_file:
      - ./.env    
    restart: unless-stopped
    networks:
      - etailpet

networks:
  etailpet:
    driver: bridge 