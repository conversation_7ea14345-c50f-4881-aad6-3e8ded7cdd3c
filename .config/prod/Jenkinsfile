pipeline {
	agent any
	options { buildDiscarder(logRotator(numToKeepStr: '10')); timestamps(); disableConcurrentBuilds() }
	environment {
		IMAGE_NAME = 'bookit'
		IMAGE_TAG = 'latest'
		ENV = 'prod'
		SERVER_IP = "${BOOKIT_PROD_SERVER_IP}"
		SERVER_USER = "${env.SERVER_USER ?: 'ubuntu'}"
		REMOTE_DIR = "${env.REMOTE_DIR ?: '/home/<USER>/bookit'}"
		AWS_REGISTRY = "${env.AWS_ACCOUNT_ID}.dkr.ecr.${env.AWS_DEFAULT_REGION}.amazonaws.com"
		AWS_SECRET_MANAGER = "${BOOKIT_UAT_ID}"
		DOCKERFILE = '.config/prod/Dockerfile'
	}
	stages {

		stage('Build & Test') {
			when { branch 'master' }
			steps {
				sh 'mvn -B -ntp -DskipTests=false test'
				sh 'mvn -B -ntp -DskipTests package'
			}
		}
		stage('Docker Build') {
			when { branch 'master' }
			steps {
				sh 'docker build -t ${IMAGE_NAME}:${IMAGE_TAG} -f ${DOCKERFILE} .'
				sh 'cp .config/prod/docker-compose.yml docker-compose.yml'
			}
		}
		stage('ECR Login') {
			steps { sh "aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_REGISTRY}" }
		}
		stage('Docker Build & Push') {
			steps {
				script {
					env.BUILD_IMAGE = "${AWS_REGISTRY}/${DOCKER_IMAGE_NAME}:${IMAGE_TAG}"
					sh "docker build -t ${BUILD_IMAGE} -f ${DOCKERFILE} ."
					sh "docker push ${BUILD_IMAGE}"
				}
			}
		}
		stage('Prepare Env File') {
			steps {
				script {
					sh """
						aws secretsmanager get-secret-value \
						--secret-id ${AWS_SECRET_MANAGER} \
						--query SecretString \
						--output text | jq -r 'to_entries | map("\\(.key)=\\(.value|tostring)") | .[]' > .env
					"""
				}
			}
		}

		stage('Prepare Keystore') {
			when { branch 'master' }
			steps { 
					sh 'rm -rf .config/prod/keystore && mkdir -p .config/prod/keystore && \
			
						aws s3 cp s3://${BOOKIT_KEYSTORE_BUCKET}/${env.BRANCH_NAME}/keystore/ .config/prod/keystore/ --recursive' 
				}
		}

		stage('Deploy To Server') {
			when { branch 'master' }
			steps {
				sh 'scp $WORKSPACE/.env ${SERVER_USER}@${SERVER_IP}:${REMOTE_DIR} && rm -f .env'
				sh 'ssh ${SERVER_USER}@${SERVER_IP} -t "aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_REGISTRY} && \
					docker pull ${AWS_REGISTRY}/${IMAGE_NAME}-${ENV}:${IMAGE_TAG} && \
					cd ${REMOTE_DIR} && export IMAGE_NAME=${AWS_REGISTRY}/${IMAGE_NAME}-${ENV}:${IMAGE_TAG} IMAGE_TAG=${IMAGE_TAG} && \
					docker compose -f docker-compose.yml up -d --build bookit-app && docker image prune -a -f"'
			}
		}
		stage('Local Cleanup') { when { branch 'master' } steps { sh 'docker image prune -a -f' } }
	}
	post {
		success {
			script { try { slackSend(color: '#00FF00', message: "Bookit PROD deploy SUCCESS: ${env.JOB_NAME} #${env.BUILD_NUMBER} -> ${env.BUILD_URL}") } catch(err) { echo 'Slack not configured' } }
		}
		failure {
			script { try { slackSend(color: '#FF0000', message: "Bookit PROD deploy FAILED: ${env.JOB_NAME} #${env.BUILD_NUMBER} -> ${env.BUILD_URL}") } catch(err) { echo 'Slack not configured' } }
		}
	}
} 