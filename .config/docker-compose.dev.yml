services:
  bookit-app:
    image: ${IMAGE_NAME}:${IMAGE_TAG}
    container_name: bookit-app
    ports:
      - "8080:8080"
    networks:
      - etailpet
    env_file:
      - ./.env
    depends_on:
      - db
    restart: always
    logging:
      driver: "json-file"
      options:
        max-file: "5"
        max-size: "50m"
  db:
    image: postgres:17.6-alpine3.21
    container_name: bookit-db
    volumes:
      - postgres_data_17:/var/lib/postgresql/data/
    env_file:
      - ./.env.db
    networks:
      - etailpet

volumes:
  postgres_data_17:

networks:
  etailpet:
    driver: bridge 