package com.sayone.etailbookit.exception;

import io.sentry.Sentry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class SentryConfig {

    @Value("${sentry.dsn}")
    private String sentryDsn;

    @PostConstruct
    public void initializeSentry() {
        // Only initialize Sentry if DSN is provided
        if (!sentryDsn.isEmpty()) {
            Sentry.init(options -> {
                options.setDsn(sentryDsn);
                options.setTracesSampleRate(0.0); // Disable traces if unnecessary
                options.setAttachStacktrace(false); // Avoid capturing stack traces globally
            });
        } else {
            System.out.println("Sentry DSN is not configured. Sentry initialization skipped.");
        }
    }
}