package com.sayone.etailbookit.exception;

import com.sayone.etailbookit.dto.BaseResponseDto;
import io.sentry.Sentry;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@ControllerAdvice
@RestController
public class CustomizedResponseEntityExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(EntityNotFoundException.class)
    public final ResponseEntity<BaseResponseDto> handleNotFoundExceptions(EntityNotFoundException ex, WebRequest request) {
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.NOT_FOUND.value());
        response.setStatusMessage(HttpStatus.NOT_FOUND.getReasonPhrase());
        response.setData(ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(EtailBookItException.class)
    public final ResponseEntity<BaseResponseDto> handleEtailBookItException(EtailBookItException ex, WebRequest request) {
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.CONFLICT.value()); // Use a 409 Conflict status or any suitable code
        response.setStatusMessage(HttpStatus.CONFLICT.getReasonPhrase());
        response.setData(ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.CONFLICT);
    }


    @ExceptionHandler(BadRequestException.class)
    public final ResponseEntity<BaseResponseDto> handleBadRequestExceptions(BadRequestException ex, WebRequest request) {
        System.out.println("=== BadRequestException handler called ===");
        System.out.println("Exception message: " + ex.getMessage());
        System.out.println("Request: " + request.getDescription(false));
        
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.BAD_REQUEST.value());
        response.setStatusMessage(HttpStatus.BAD_REQUEST.getReasonPhrase());
        response.setData(ex.getMessage());
        
        System.out.println("Response: " + response.toString());
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(UnAuthorizedException.class)
    public final ResponseEntity<BaseResponseDto> handleUnAuthorizedException(UnAuthorizedException ex, WebRequest request) {
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.UNAUTHORIZED.value());
        response.setStatusMessage(HttpStatus.UNAUTHORIZED.getReasonPhrase());
        response.setData(ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.BAD_REQUEST.value());
        response.setStatusMessage(HttpStatus.BAD_REQUEST.getReasonPhrase());
        response.setData(ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public final ResponseEntity handleExceptions(Exception ex, WebRequest request) {
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setStatusMessage(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
        response.setData(ex.getMessage());
        return new ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

   /* @ExceptionHandler(Exception.class)
    public final ResponseEntity<BaseResponseDto> handleExceptions(Exception ex, WebRequest request) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;

        // Log to Sentry only if the exception is unexpected and results in a 5xx error
        if (isUnexpectedException(ex) && status.is5xxServerError()) {
            Sentry.captureException(ex);
        }

        // Prepare response for client
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(status.value());
        response.setStatusMessage(status.getReasonPhrase());
        response.setData(ex.getMessage());

        return new ResponseEntity<>(response, status);
    }

    private boolean isUnexpectedException(Exception ex) {
        return !(ex instanceof EntityNotFoundException ||
                ex instanceof BadRequestException ||
                ex instanceof UnAuthorizedException ||
                ex instanceof MaxUploadSizeExceededException ||
                ex instanceof HttpMessageNotReadableException);
    }*/

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public final ResponseEntity<BaseResponseDto> maxUploadSizeExceededException(MaxUploadSizeExceededException ex, WebRequest request) {
        BaseResponseDto response = new BaseResponseDto();
        response.setStatusCode(HttpStatus.BAD_REQUEST.value());
        response.setStatusMessage(HttpStatus.BAD_REQUEST.getReasonPhrase());
        response.setData(ex.getMessage().substring(0, ex.getMessage().indexOf(";") + 1) + "Please limit the file size to 4 MB");
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
}
