package com.sayone.etailbookit.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class TemperamentDto implements Serializable {
    private Integer id;
    private String name;
    private boolean active;

    // Default constructor
    public TemperamentDto() {
    }

    // All-args constructor
    public TemperamentDto(Integer id, String name, Boolean active) {
        this.id = id;
        this.name = name;
        this.active = (active != null ? active : false);
    }
    // private Integer indexValue;
}
