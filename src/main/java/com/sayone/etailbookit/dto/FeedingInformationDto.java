package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class FeedingInformationDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private boolean active;

   // private Integer id;

    private Boolean display;

    private Integer feedCount;

    private String[] grainFreeRecipesYes;

    private String[] grainFullRecipesYes;

    private String[] grainFreeRecipesNo;

    private String[] grainFullRecipesNo;

    private BigDecimal amountPerExtraCup;

    private String amountCurrency;


}