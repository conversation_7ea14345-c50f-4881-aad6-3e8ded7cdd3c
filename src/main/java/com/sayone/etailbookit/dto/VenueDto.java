package com.sayone.etailbookit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class VenueDto implements Serializable {

    private Integer id;

    private String internalName;

    private String publicName;

    private String locationType;

    private VenueAddressDto locationAddress;

    private Boolean supportSimultaneousBookings;

    private List<Integer> serviceTypeIds;

    private List<ServiceTypeDto> serviceTypes;

    private Integer participantLimitService;

    private BigDecimal extraCharge;

    private String extraCurrency;

    private String availabilityInterval;

    private String availabilityIntervalUnit;

    private List<AvailabilityDto> availabilityDays;

    private Integer ecommerceStoreId;

    private List<VenuePetTypeDto> petTypes ;

    private Boolean active;

    private List<String> mobileZipCode;

    private String timezone;

    private boolean deleted;

}
