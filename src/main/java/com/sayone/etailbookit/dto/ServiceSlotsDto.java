package com.sayone.etailbookit.dto;

import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

@Data
public class ServiceSlotsDto {
    private List<Integer> serviceId;
    private Integer venue;
    private List<Integer> attendants;
    private Integer duration;
    private String durationUnit;
    private OffsetDateTime slotStartTime;
    private List<String> availableDays;
    private Integer availabilityInterval;
    private String availabilityIntervalUnit;
    private String slotName;
    private String color;
}
