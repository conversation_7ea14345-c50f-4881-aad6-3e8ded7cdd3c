package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WeightRangeDto implements Serializable {
    private Integer id;
    private BigDecimal minValue;
    private BigDecimal maxValue;
    private String weightUnit;
    @JsonProperty
    private boolean deleted;

    public boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
}
