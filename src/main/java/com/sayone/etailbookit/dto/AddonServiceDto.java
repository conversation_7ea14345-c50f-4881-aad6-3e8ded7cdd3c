package com.sayone.etailbookit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AddonServiceDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private Integer tackOnExtraMinutes;

    private BigDecimal tackOnExtraAmount;

    private String tackOnExtraAmountCurrency;

    private Boolean isTaxable = Boolean.FALSE;

    private Boolean active = Boolean.FALSE;

   // private Integer indexValue;

    private boolean deleted;
    
}