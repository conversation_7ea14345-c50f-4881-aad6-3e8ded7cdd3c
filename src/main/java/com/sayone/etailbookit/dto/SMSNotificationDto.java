package com.sayone.etailbookit.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class SMSNotificationDto implements Serializable {
    private String code;
    private Integer customer_id;
    private Integer pet_id;
    private String service_type;
    private String service;
    private String pet_name;
    private String vendor_name;
    private String appointment_date;
    private String appointment_time;
  //  private String shampoo;
    //private String cologne;
    private String vaccination_name;
    private String vaccination_expiry;
    private String date_exist;
    private String time_exist;
    private String store_name;
    private String old_vendor_name;
    private Integer appointment_id;
    private String rejection_reason;
    private String message;
    private String subject;
    private String slot_start_time;

}
