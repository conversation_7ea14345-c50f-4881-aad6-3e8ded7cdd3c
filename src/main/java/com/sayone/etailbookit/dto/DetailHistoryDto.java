package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sayone.etailbookit.util.ServiceStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;

@Data
public class DetailHistoryDto {

    private Integer appointmentId;
    private String petName;
    private String petType;
    private String orderId;
    private String serviceType;
    private String serviceName;
    private String date;
    private String time;
    private String duration;
    private String durationInterval;
    private String venue;
    private String attendantName;
    private ServiceStatus status;
    private String hairLength;
    private String hairTexture;
    private List<DesiredHairLengthDto> desiredHairLengths;
    private WeightRangeDto weight;
    private List<VaccinationRecordsDto> vaccination;
    private List<AllergiesDto> allergies;
    private String temperament;
    private List<AddonServiceDto> addOnServices;
    private List<DocumentOptionDto> documents;
    private List<VetInformationDto>vetInformation;
    private List<EmergencyContactInfoDto> emergencyContact;
    private List<PersonalityParameterDto> petPersonality;
    private List<UnfriendlyBehaviourTriggerDto> unfriendlyBehaviour;
    private BigDecimal amount;
    private BigDecimal tipAmount;
    private PetShampooDto shamppo;
    private PetCologneDto cologne;
    private OffsetDateTime serviceStartAt;
    private OffsetDateTime serviceEndAt;
    private LocalDateTime createdAt;
    private String startDateAndTime;
    private String EndDateAndTime;
}
