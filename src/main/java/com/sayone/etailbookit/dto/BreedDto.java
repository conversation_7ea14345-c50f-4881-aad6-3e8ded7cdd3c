package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BreedDto implements Serializable {
    private Integer id;
    private String name;
    private boolean active;
    @JsonProperty
    private boolean deleted;

    public boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
   // private boolean requireBreedsInfo;
}
