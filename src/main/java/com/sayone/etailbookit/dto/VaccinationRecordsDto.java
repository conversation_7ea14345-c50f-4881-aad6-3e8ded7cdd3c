package com.sayone.etailbookit.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
public class VaccinationRecordsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private MultipartFile file;

    private String fileURL;

    private String dateAdministrated;

    private String dateExpires;

    private boolean requireDateAdministrated;

    private boolean requireDateExpires;

    private boolean requireVaccinationDocument;

    private boolean requireUploadEsign;

    private boolean active;

   // private Integer indexValue;

}