package com.sayone.etailbookit.dto;

import com.sayone.etailbookit.model.BlockDateInfo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.OffsetDateTime;
import java.util.List;

@Data
public class BlockDateTimeDto {
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime date;
    private List<BlockTimeDto> blockTimes;
    private boolean blockFullDay;
    private Integer attendantId;
    private BlockDateInfo.BlockedTimeType blockType;
    
    // Recurring block fields
    private boolean recurringEnabled;
    private String recurringPattern; // "DAILY", "WEEKLY", "MONTHLY"
    private Integer recurringInterval; // e.g., 1 for every week, 2 for every 2 weeks
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime recurringEndDate; // End date for recurring blocks

}
