package com.sayone.etailbookit.dto;

import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.Service;
import com.sayone.etailbookit.model.Venue;
import lombok.Data;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
public class CancelledAppointmentsDto {
    private String customerName;
    private String petName;
    private String appointmentDateTime;
    private String venue;
    private String service;
    private BigDecimal appointmentCancellationRate;

}
