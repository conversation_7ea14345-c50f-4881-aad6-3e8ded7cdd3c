package com.sayone.etailbookit.dto;

import com.sayone.etailbookit.model.BittingHistory;
import com.sayone.etailbookit.model.PersonalityParameter;
import com.sayone.etailbookit.model.ThreatReaction;
import com.sayone.etailbookit.model.UnfriendlyBehaviourTrigger;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExtendedBehaviourDto implements Serializable {
    private boolean active;
    private List<BittingHistory> bittingHistory;
    private List<PersonalityParameter> personalityParameters;
    private List<ThreatReaction> threatReactions;
    private List<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers;
}