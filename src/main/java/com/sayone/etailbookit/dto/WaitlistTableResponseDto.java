package com.sayone.etailbookit.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WaitlistTableResponseDto {
    private String customerName;
    private Integer customerId;
    private String phone;
    private String email;
    private TimeSlotWaitListDto timeSlotWaitListDto;
}
