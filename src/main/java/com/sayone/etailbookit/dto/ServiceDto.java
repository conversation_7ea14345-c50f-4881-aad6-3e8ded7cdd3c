package com.sayone.etailbookit.dto;

import com.sayone.etailbookit.model.PetCologne;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ServiceDto {

    private Integer serviceId;
    private String name;
    private ServiceTypeDto serviceType;
    private PetTypeDto petType;
    private List<GeneralPetSizeDto> generalSizes;
    private List<VenueDto> venues;
    private List<AttendantDto> attendants;
    private List<TemperamentDto> temperaments;
    private List<ServiceBreedInfoDto> serviceBreedInfos;
    private Boolean petParentCanSelectAttendant;

    private String scheduleType;
    private Integer fixedScheduleValue;
    private String fixedScheduleUnit;

    private String variableScheduleMinValue;
    private String variableScheduleMaxValue;
    private Integer maxOvernights;

    private BigDecimal preBookingValue;
    private String preBookingUnit;

    private Boolean petparentCanSelectDatetime;
    private Boolean petparentCanSelectRecurringOptions;
    private Integer pre_buffer_mins;
    private Integer postBufferMins;

    private BigDecimal amountPerUnit;
    private String amount_currency;
    private String serviceUnit;
    private Boolean isTipsAllowed;
    private BigDecimal minimumFee;
    private Boolean enableLateOnboardingConfig;
    private Integer lateOnboardingDays;
    private String lateOnboardingChargeInterval;
    private BigDecimal lateOnboardingHourlyCharge;
    private Boolean isServiceLateDiscountAllowed;
    private Integer serviceLateDiscountDays;
    private BigDecimal serviceLateDiscountPercent;
    private Boolean isSiblingDiscountAllowed;
    private String siblingDiscountType;
    private BigDecimal siblingDiscountValue;
    private Boolean isTaxable;
    private List<AddonServiceDto> addonServicesOffered;
    private Boolean paymentAtTimeOfBooking;
    private Boolean paymentAtBeginningOfService;
    private Boolean paymentAfterServiceCompleted;
    private Boolean needDepositAtBooking;
    private String depositAmountType;
    private BigDecimal depositAmountValue;
    private Boolean chargeCancelationFee;
    private String cancelationAmountType;
    private BigDecimal cancelationAmountValue;
    private Integer cancelationBufferValue;
    private String cancelationBufferUnit;
    private Boolean requireCreditCardOnFile;
    private Boolean requireParticipantPetName;
    private Boolean requireParticipantPetType;
    private String color;

    private List<VaccinationRecordsDto> availableParticipantVaccinations;
    private List<DocumentOptionDto> availableParticipantDocuments;

    private Boolean availableParticipantWeights;
    private Boolean availableParticipantTemperaments;
    private Boolean availableParticipantAllergies;

    private Boolean requirePetPersonality;
    private Boolean requirePetBehaviour;
    private Boolean requireBitingInfo;
    private Boolean requireThreatReactions;
    private Boolean requireHairLengthInfo;
    private Boolean requireVetInfo;
    private Boolean requireNotes;
    private Boolean offerRouteTracking;
    private Boolean requireNotesPostService;

    private Boolean requireCombsAndBladesUsedPostService;
    private Boolean requireShampooUsedPostService;
    private Boolean requireCologneUsedPostService;
    private Boolean requireFoodFedPostService;
    private Boolean sendPictureToPetParentPostService;

    private Boolean shampoosOffered;
    private Boolean colognesOffered;
    private Boolean addonsOffered;
    private Boolean requireFeedingInfo;
    private Boolean requireDesiredHairLength;
    private List<PetShampooDto> shampoos;
    private List<PetCologneDto> colognes;

    private Boolean sendReportCard;
    private Boolean includeReportCardPeedTimes;
    private Boolean includeReportCardPoopedTimes;
    private Boolean includeReportCardPoopDescription;
    private Boolean includeReportCardTimesFed;
    private Boolean includeReportCardWaterBowlFilled;
    private Boolean includeReportCardGeneralNotes;

    private Boolean isActive;

    private List<AvailabilityDto> availabilityDays;
    private String availabilityInterval;

    private String availabilityIntervalUnit;
    private String retailer;
    private boolean deleted;
}
