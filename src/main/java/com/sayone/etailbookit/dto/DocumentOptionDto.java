package com.sayone.etailbookit.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
public class DocumentOptionDto implements Serializable {
    private Integer id;
    private String name;
    private boolean requireDescription;
    private boolean requireUpload;
    private boolean requireUploadEsign;
    private String description;
    private MultipartFile file;
    private boolean active;
    private String fileURL;
    private String retailer;
   // private Integer indexValue;
}
