package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sayone.etailbookit.model.WeightRange;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PetCustomerDto implements Serializable {
    private Integer id;
    private Integer customerId;
    private String name;
    private String dob;
    private Integer petTypeId;
    private BigDecimal exactWeight;
    private Integer weightRangeId;
    private Integer breedId;
    private String weightUnit;
}
