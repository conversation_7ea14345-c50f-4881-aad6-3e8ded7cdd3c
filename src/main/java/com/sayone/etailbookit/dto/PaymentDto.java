package com.sayone.etailbookit.dto;


import com.sayone.etailbookit.util.PaymentType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PaymentDto implements Serializable {

    private String retailer;
    private Integer customerId;
    private Integer appointmentId;
    private BigDecimal serviceAmount;
    private Boolean tipEnabled = Boolean.FALSE;
    private BigDecimal tipAmount;
    private BigDecimal minimumFee;
    private Boolean hasDiscount = Boolean.FALSE;
    private DiscountDto discount;
    private Boolean isTaxable = Boolean.FALSE;
    private Boolean depositEnabled = Boolean.FALSE;
    private BigDecimal depositAmount;
    private Double shampooPrice;
    private Double colognePrice;
    private PaymentType paymentType;
    private List<AddonServiceDto> addOns;
    private List<ServiceBreedInfoDto> serviceBreedInfoDtoList;
    private String cardDetails;
    private String billingAddress;
    private BigDecimal payableAmount;
    private BigDecimal serviceAmountAlone;
    private String serviceName;
    private Boolean isRefund = Boolean.FALSE;
    private Long orderId;
    private String duration;
    private String date;
    private String time;
}