package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class POSCustomerResponseDto {
    
    @JsonProperty("responseData")
    private ResponseData responseData;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("status")
    private Integer status;
    
    @Data
    public static class ResponseData {
        @JsonProperty("data")
        private List<POSCustomerData> data;
    }
    
    @Data
    public static class POSCustomerData {
        @JsonProperty("firstName")
        private String firstName;
        
        @JsonProperty("lastName")
        private String lastName;
        
        @JsonProperty("phone")
        private String phone;
        
        @JsonProperty("customerId")
        private Integer customerId;
        
        @JsonProperty("email")
        private String email;
    }
} 