package com.sayone.etailbookit.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class GeneralPetSizeDto implements Serializable {
    private Integer id;
    private String size;
    private Integer weightValue;
    private String weightUnit;
    private boolean deleted;
    // Default constructor
    public GeneralPetSizeDto() {
    }

    // All-args constructor
    public GeneralPetSizeDto(Integer id, String size, Integer weightValue, String weightUnit) {
        this.id = id;
        this.size = size;
        this.weightValue = weightValue;
        this.weightUnit = weightUnit;
    }


}
