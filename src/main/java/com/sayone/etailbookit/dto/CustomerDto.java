package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CustomerDto {

    private Integer id;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    @JsonProperty
    private boolean isActive;

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }
}

