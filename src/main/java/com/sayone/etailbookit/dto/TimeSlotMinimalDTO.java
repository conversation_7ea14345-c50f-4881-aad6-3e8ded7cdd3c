package com.sayone.etailbookit.dto;

import lombok.Data;

import java.time.OffsetDateTime;
@Data
public class TimeSlotMinimalDTO {
    private OffsetDateTime slotStartTime;
    private OffsetDateTime slotEndTime;
    private Integer clusterId;
    private Boolean attendantDeleted;

    public TimeSlotMinimalDTO(OffsetDateTime slotStartTime, OffsetDateTime slotEndTime, Integer clusterId, Boolean attendantDeleted) {
        this.slotStartTime = slotStartTime;
        this.slotEndTime = slotEndTime;
        this.clusterId = clusterId;
        this.attendantDeleted = attendantDeleted;
    }
}
