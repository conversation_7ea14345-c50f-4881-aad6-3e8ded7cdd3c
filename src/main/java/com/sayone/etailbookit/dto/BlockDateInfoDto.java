package com.sayone.etailbookit.dto;

import com.sayone.etailbookit.model.BlockDateInfo;
import lombok.Data;

@Data
public class BlockDateInfoDto {

    private Integer id;
    private String blockDate;
    private String blockStartTime;
    private String blockEndTime;

   // private String offsetBlockDate;
    private boolean blockFullDay;
    private String retailer;
    private Integer attendantId;
    private BlockDateInfo.BlockedTimeType blockType;
    private String recurringGroupId;
}
