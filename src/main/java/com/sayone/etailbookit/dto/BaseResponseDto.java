package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sayone.etailbookit.util.Status;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BaseResponseDto<T>  implements Serializable {
    @JsonProperty("statusCode")
    private Integer statusCode;
    @JsonProperty("statusMessage")
    private String statusMessage;
    @JsonProperty("detailedMessages")
    private List<String> detailedMessages;
    @JsonProperty("data")
    private T data;
    @JsonProperty("totalCount")
    private Integer totalCount;

    public BaseResponseDto() {
    }

    public BaseResponseDto(Status status, T data) {
        this.statusCode = status.getCode();
        this.statusMessage = status.getMessage();
        this.data = data;
    }

    public BaseResponseDto(Status status) {
        this.statusCode = status.getCode();
        this.statusMessage = status.getMessage();
    }

    public BaseResponseDto(Status status, T data, Integer totalCount) {
        this.statusCode = status.getCode();
        this.statusMessage = status.getMessage();
        this.data = data;
        this.totalCount = totalCount;
    }
}
