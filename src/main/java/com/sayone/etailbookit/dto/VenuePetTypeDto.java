package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VenuePetTypeDto implements Serializable {
    private Integer id;
    private String name;
    private List<TemperamentDto> temperaments ;
    private List<GeneralPetSizeDto> generalPetSizes ;
    private List<Integer> temperamentIds ;
    private List<Integer> generalPetSizeIds ;

}