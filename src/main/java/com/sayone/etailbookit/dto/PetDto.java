package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PetDto implements Serializable {
    private Integer id;
    private Integer customerId;
    private String name;
    private String dob;
    private Integer petTypeId;
    private String sex;
    private boolean spayed;
    private Integer hairLengthId;
    private Integer hairTextureId;
    private String color;
    private Integer weightRangeId;
    private BigDecimal exactWeight;
    private Integer temperamentId;
    private Integer feedCount;
    private String allergiesText;
    private List<String> photos;
    private List<Integer> allergyIds;
    private List<AllergiesDto> allergies;
    private List<VetInformationDto> vetInformation;
    private List<EmergencyContactInfoDto> emergencyContactInfo;
    private List<VaccinationRecordsDto> vaccinationRecords;
    private List<PersonalityParameterDto> personalityParameters;
    private List<UnfriendlyBehaviourTriggerDto> unfriendlyBehaviourTriggers;
    private List<ThreatReactionDto> threatReactions;
    private List<BittingHistoryDto> bitingHistories;
    private List<MultipartFile> pictures;
    private List<DocumentOptionDto> documents;
    private WaiverOfLiabilityDto signedWaiverOfLiabilityInfo;

    private List<Integer> personalityParameterIds;
    private List<Integer> unfriendlyBehaviourTriggerIds;
    private List<Integer> threatReactionIds;
    private List<Integer> bitingHistoryIds;
    private List<PetBreedsInfoDto> petBreedsInfos;

    private String deceaseDate;

    private String[] grainFreeRecipes;
    private String[] grainFullRecipes;

    private PetTypeDto petTypeDetails;
    private TemperamentDto temperamentDetails;
    private WeightRangeDto weightRangeDetails;

    private HairLengthDto hairLengthDetails;
    private HairTextureDto hairTextureDetails;
    private boolean bringFood;
    private String weightUnit;
    private String retailer;
    private boolean deleted;

}