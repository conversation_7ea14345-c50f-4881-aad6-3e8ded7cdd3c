package com.sayone.etailbookit.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentDetailsDto {

    private Integer appointmentId;
    private Integer customerId;
    private String service;
    private Long internalItemNumber;
    private String attendant;
    private Integer attendantId;
    private String pet;
    private Integer petId;
    private BigDecimal serviceAmount;
    private Boolean isTaxable;
    private Boolean isTipsAllowed;
    private String appointmentStatus;
    private BigDecimal cancellationFee;

}
