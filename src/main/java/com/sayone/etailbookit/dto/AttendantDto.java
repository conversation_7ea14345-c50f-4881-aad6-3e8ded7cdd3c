package com.sayone.etailbookit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AttendantDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer attendantId;
    private String firstName;
    private String lastName;
    private String email;
    private String phoneNo;
    private String title;
    private String color;
    private String[] preferredCommunications;
    private List<Integer> serviceTypeIds;
    private List<Integer> addonServiceIds;
    private Boolean revenueShare;
    private BigDecimal amountPerHour;
    private String amountCurrency;
    private BigDecimal amountPerEvent;
    private Integer revenuePercent;
    private Boolean tipEligible;
    private Integer capacityLimit;
    private List<PetSizeConstraintDto> petSizeConstraints;
    private List<PetSizeLimitDto> petSizeLimits;
    private List<PetTypeDto> petTypes ;
    private List<ServiceTypeDto> serviceTypes;
    private List<AddonServiceDto> addonServices;
    private List<AttendantPetTypeDto> attendantPetTypes ;
    private Boolean active = Boolean.FALSE;
    private List<AvailabilityDto> availabilityDays;
    private String availabilityInterval;
    private String availabilityIntervalUnit;
    private VenueDto venue;
    private boolean deleted;
    private Boolean googleCalendarSyncEnabled;
    private Boolean calendarSyncInProgress;
    private Boolean googleCalendarAuthorized;

}