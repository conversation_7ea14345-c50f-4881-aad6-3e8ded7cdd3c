package com.sayone.etailbookit.dto;

import lombok.Data;
import java.time.OffsetDateTime;
import java.util.List;

@Data
public class TimeSlotsDto {
    private Integer id;
    private Integer attendantId;
    private String attendantName;
    private List<Integer> serviceId;
    private List<String> serviceName;
    private Integer venueId;
    private String venueName;
    private OffsetDateTime slotStartTime;
    private OffsetDateTime slotEndTime;
    private  String slotName;
    private List<String> availableDays;
    private String availableDay;
    private Integer availabilityInterval;
    private String availabilityIntervalUnit;
    private Integer duration;
    private String durationUnit;
    private String color;
    private Integer timeSlotClusterId;
    private boolean slotBooked;
}
