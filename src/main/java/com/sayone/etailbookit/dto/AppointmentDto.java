package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sayone.etailbookit.util.ServiceStatus;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Data
public class AppointmentDto {

    private Integer id;
    private Integer customerId;
    private Integer addressId;
    private String appoinmentNo;
    private ServiceTypeDto serviceType;
    private ServiceDto service;
    private AttendantDto attendant;
    private VenueDto venue;
    private PetShampooDto shamppo;
    private PetCologneDto cologne;
   // private String date;
   @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
   private OffsetDateTime startTime;
   @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime endTime;
   @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
   private OffsetDateTime slotEndTime;
   private boolean enableSlot;
    private String dateTimeOnboarding;
    private Boolean recurringEnabled;
    private Boolean haveSibilings;
    private Integer sibilingCount;
    private List<AddonServiceDto> addOnService;
    private List<QuoteAdjustmentDto> adjustmentQuotes;
    private PetDto pet;
    private PetTypeDto petTypeDetails;
    private WeightRangeDto weightRangeDetails;
    private BigDecimal exactWeight;
    private String weightUnit;
    private TemperamentDto temperamentDetails;

    private HairLengthDto hairLengthDetails;
    private HairTextureDto hairTextureDetails;
    private List<DesiredHairLengthDto> desiredHairLengths;
    private List<AllergiesDto> allergies;
    private Integer feedingCount;
    private Boolean bringYourFood;
    private String[] grainFreeRecipes;
    private String[] grainFullRecipes;
    private BigDecimal amount;
    private BigDecimal serviceCost;
    private String amountCurrency;
    private BigDecimal tipAmount;
    private String tipAmountCurrency;
    private String allergiesText;
    private List<VetInformationDto> vetInformation;
    private List<VaccinationRecordsDto> vaccinationInfo;
    private List<PersonalityParameterDto> personalityParameters;
    private List<UnfriendlyBehaviourTriggerDto> unfriendlyBehaviourTriggers;
    private List<EmergencyContactInfoDto> emergencyInfo;
    private List<DocumentOptionDto> otherDoc;
    private WaiverOfLiabilityDto waiverOfLiabilityDoc;
    private ServiceStatus serviceStatus;
    private ServiceHistoryDto serviceDetails;
    private Long orderReference;
    private String paymentStatus;
    private Integer variableScheduleDuration;
    private Integer overnights;
    private BigDecimal discountAmount;

    private String serviceStartAt;
    private String serviceEndAt;
    private String duration;
    private String retailer;
    private String address;

    private String note;
    private String customerName;
    private Boolean optInForSms;
    private String smsPhoneNumber;
    private boolean deleted;
    @JsonProperty
    private boolean appointmentOverride;
    private boolean isEditable;
    private boolean bookedViaWaitList;
    private Integer timeSlotId;
    private String createdCustomerName;
    private Integer createdCustomerId;
    private String updatedCustomerName;
    private Integer updatedCustomerId;
    private String firstName;
    private String lastName;
    private String  paymentmethodId;
    private boolean waiverOfLiabilityAcknowledged;

    private Long storeId;

    public boolean getAppointmentOverride() {
        return appointmentOverride;
    }

    public boolean getEnableSlot() {
        return enableSlot;
    }

    public void setAppointmentOverride(boolean appointmentOverride) {
        this.appointmentOverride = appointmentOverride;
    }

    public boolean getIsEditable(){
        return isEditable;
    }

    public void setIsEditable(boolean isEditable){
        this.isEditable = isEditable;
    }

    public boolean getBookedViaWaitList(){
        return bookedViaWaitList;
    }

    public void setBookedViaManualSlot(boolean bookedViaWaitList){
        this.bookedViaWaitList = bookedViaWaitList;
    }

    public boolean isWaiverOfLiabilityAcknowledged() {
        return waiverOfLiabilityAcknowledged;
    }
    public void setWaiverOfLiabilityAcknowledged(boolean isWaiverOfLiabilityAcknowledged) {
        this.waiverOfLiabilityAcknowledged = isWaiverOfLiabilityAcknowledged;
    }
}
