package com.sayone.etailbookit.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PetTypeDto implements Serializable {
    private Integer id;
    private String name;
    private PetTypeConfigDto<HairLengthDto> hairLengths;
    private PetTypeConfigDto<HairTextureDto> hairTextures;
    private CombsBladesDto combsBlades;
    private PetTypeConfigDto<VetInformationDto> vetInformation;
    private PetTypeConfigDto<AllergiesDto> allergies;
    private PetTypeConfigDto<BreedDto> breeds;
    private PetTypeConfigDto<DesiredHairLengthDto> desiredHairLengths;
    private PetTypeConfigDto<TemperamentDto> temperaments;
    private PetTypeConfigDto<VaccinationRecordsDto> vaccinationRecords;
    private PetTypeConfigDto<EmergencyContactInfoDto> emergencyContactInfo;
    private PetTypeConfigDto<DocumentOptionDto> documentOptions;
    private PetTypeConfigDto<WeightRangeDto> weightRanges;
    private PetTypeConfigDto<GeneralPetSizeDto> generalPetSizes;
    private PetTypeConfigDto deceaseDate;
    private List<Integer> temperamentIds ;
    private List<Integer> generalPetSizeIds ;
    private String fileUrl;
    private Boolean active;
    private String retailer;
    private boolean deleted;
}