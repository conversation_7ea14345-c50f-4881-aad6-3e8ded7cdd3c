package com.sayone.etailbookit.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RecurringBlockResponseDto {
    private List<BlockDateInfoDto> created;
    private List<String> skipped;
    private Integer totalCreated;
    private Integer totalSkipped;
    private String recurringGroupId; // UUID generated by backend for this recurring series

    public RecurringBlockResponseDto() {
        this.created = new ArrayList<>();
        this.skipped = new ArrayList<>();
        this.totalCreated = 0;
        this.totalSkipped = 0;
    }
}

