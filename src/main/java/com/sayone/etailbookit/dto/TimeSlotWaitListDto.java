package com.sayone.etailbookit.dto;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;


@Data
public class TimeSlotWaitListDto {
    private Integer timeSlotId;
    private OffsetDateTime slotStartTime;
    private OffsetDateTime slotEndTime;
    private String slotName;
    private Integer attendantId;
    private String attendantFirstName;
    private List<ServiceInfoDto> serviceInfoDtos;
    private Integer venueId;
    private String venueName;
    private boolean slotBooked;

}
