package com.sayone.etailbookit.dto;

import com.sayone.etailbookit.util.Enums;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ServiceSyncDto {

    private String id;
    private String title;
    private Boolean isTipsAllowed;
    private Boolean isTaxable;
    private String petType;
    private String serviceType;
    private Long internalItemNumber; //src/main/java/com/etailpos/batch_process/service/ProductService.java:549  start with 3
    private BigDecimal price = BigDecimal.ZERO;
    private Boolean isActive = Boolean.TRUE;
    private Boolean isArchive=Boolean.FALSE;
    private String schema;
  //  private Enums.ItemType itemType = Enums.ItemType.SERVICE;
   // private Enums.ProductType productType = Enums.ProductType.PRODUCT;
    private Enums.PriceType priceType = Enums.PriceType.VARIABLE;
   // private Boolean isPrivate = false;
    //List<Integer> availableStores;

   // private String createdAt;
   // private String updatedAt;

}
