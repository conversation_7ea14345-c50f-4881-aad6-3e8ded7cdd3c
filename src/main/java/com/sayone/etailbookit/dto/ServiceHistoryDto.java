package com.sayone.etailbookit.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ServiceHistoryDto {

    private Integer combId;

    private Integer bladeId;

    private BladesDto blade;

    private CombsDto comb;

    private PetCologneDto petCologne;

    private PetShampooDto petShampoo;

    private Integer petShampooId;

    private Integer petCologneId;

    private String foodFed;

    private Integer timesFed;

    private Integer timesPeed;

    private Integer timesPooped;

    private String poopDescription;

    private List<MultipartFile> photos;

    private String notes;

    private boolean isFilledWaterBowl;

    private boolean picturesSent;

    private String appointmentNo;

    private String serviceStartAt;

    private String serviceEndAt;

    private Integer actualVariableScheduleDuration;

    private Integer actualOvernights;

    private Integer cupFedPerTime;

    private List<QuoteAdjustmentDto> adjustmentQuotes;

    private List<Integer> quoteAdjustmentRemoveIds;

    private BigDecimal totalCost;

    private  Integer updatedCustomerId;

    private String updatedCustomerName;
}
