package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.ServiceTypeDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.ServiceTypeProjection;
import org.springframework.data.domain.Page;


import java.io.IOException;
import java.util.List;

public interface IServiceTypeService {

    ServiceTypeDto addServiceType(ServiceTypeDto serviceTypeDto) throws EtailBookItException, IOException , Exception;

    ServiceTypeDto updateServiceType(ServiceTypeDto serviceTypeDto, int serviceTypeId) throws EtailBookItException, IOException, Exception;

    void deleteServiceType(int serviceTypeId) throws EtailBookItException, IOException;

    ServiceTypeDto getServiceTypeById(int serviceTypeId) throws EtailBookItException;

    Page<ServiceTypeProjection> getServiceTypes(Integer pageNo, Integer pageSize, String sortBy, String search);

    List<ServiceTypeProjection> getServiceTypesWithServices();

    void bulkUpdateServiceType(List<ServiceTypeDto> serviceTypeDtos) throws BadRequestException, EntityNotFoundException, EtailBookItException;

    Page<ServiceTypeDto> getActiveServiceType(Integer pageNo, Integer pageSize, String sortBy, String search) throws EtailBookItException;

    List<ServiceTypeDto> getServiceTypeFromService(Integer serviceId, Integer venueId, Integer attendantId)throws EtailBookItException;
}
