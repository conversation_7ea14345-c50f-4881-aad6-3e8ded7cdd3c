package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.WaitlistEntryDto;
import com.sayone.etailbookit.dto.WaitlistFromAppointmentRequest;

import com.sayone.etailbookit.dto.WaitlistBulkUpdateRequest;
import com.sayone.etailbookit.dto.WaitlistTableResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.data.domain.Page;

import java.util.List;

public interface WaitListEntryService {

    BaseResponseDto addToWaitList(WaitlistFromAppointmentRequest waitlistFromAppointmentRequest, String retailer) throws EtailBookItException;
    
    List<WaitlistEntryDto> getWaitlistEntriesByTimeSlot(Integer timeSlotId, String retailer) throws EtailBookItException;
    
    BaseResponseDto removeCustomerFromWaitlist(Integer waitlistEntryId, Integer customerEcomId, String retailer) throws EtailBookItException;
    
    BaseResponseDto markWaitlistAsNotified(Integer waitlistEntryId, String retailer) throws EtailBookItException;
    
    BaseResponseDto markWaitlistAsBooked(Integer waitlistEntryId, String retailer) throws EtailBookItException;
    
    BaseResponseDto bulkUpdateWaitlist(WaitlistBulkUpdateRequest request, String retailer) throws EtailBookItException;

    Page<WaitlistTableResponseDto> findAllWaitListedEntries(String retailer, int pageNo, int pageSize, String sortBy);
    
    /**
     * Notify all waitlist customers when a time slot becomes available
     * @param timeSlotId The ID of the time slot that became available
     * @param retailer The retailer context
     * @param timeZone The timezone for notification formatting
     * @return BaseResponseDto with notification status
     * @throws EtailBookItException if notification fails
     */
    BaseResponseDto notifyWaitlistCustomersForSlotAvailability(Integer timeSlotId, String retailer, String timeZone) throws EtailBookItException;
}
