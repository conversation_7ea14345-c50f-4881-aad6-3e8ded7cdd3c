package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.ServiceHistoryAddNotes;
import com.sayone.etailbookit.projections.AppointmentHistoryListingProjection;
import org.springframework.data.domain.Page;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;

import java.util.Optional;

public interface IServiceHistoryService {
    PaymentDto startService(int appointmentId, String serviceStartAt,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException;

    PaymentDto endService(ServiceHistoryDto serviceHistoryDto, int appointmentId) throws EtailBookItException;

    Page<AppointmentHistoryListingProjection> appointmentHistory(Integer customer, Integer pageNo, Integer pageSize, String sortBy,
                                                                 Optional<String> search, String startDate, String endDate,
                                                                 Optional<Integer> petType, Optional<String> serviceStatus,Optional<String> service) throws EtailBookItException;

    DetailHistoryDto getDetailedHistory(Integer id) throws EtailBookItException;

    ServiceHistoryDto getDetailedNote(Integer id) throws EtailBookItException;

    Boolean cancelService(int appointmentId,String timeZone, String cancellationReason,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException;

    void addNotes(Integer id, NoteDto noteDto) throws EtailBookItException;

    ServiceHistoryNotesListingDto listNotes(Integer appoinmentId) throws EtailBookItException;

    Page<ServiceHistoryNotesDto> listNotesByPetId(Integer petId, Integer pageNo, Integer pageSize) throws EtailBookItException;

    void updateNotes(Integer id, NoteDto noteDto)throws EtailBookItException;

    Boolean waiveService(Integer appointmentId,Integer updatedCustomerId,String updatedCustomerName)throws EtailBookItException;

    Boolean changeServiceStatus(Integer appointmentId,String serviceStatus,String timeZone,String cancellationReason,String rejectionReason,Integer updatedCustomerId,String updatedCustomerName)throws EtailBookItException;
}
