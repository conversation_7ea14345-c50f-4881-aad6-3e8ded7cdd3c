package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.DocumentOptionDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.DocumentsProjection;
import com.sayone.etailbookit.projections.PetDocumentsProjection;
import org.springframework.data.domain.Page;

import java.util.List;

public interface IDocumentService {

    BaseResponseDto createDocument(DocumentOptionDto documentOptionDto) throws EtailBookItException,Exception;

    Page<DocumentsProjection> getAllDocument(Integer pageNo, Integer pageSize, String sortBy);

    BaseResponseDto updateDocument(DocumentOptionDto documentOptionDto, Integer id) throws Exception;

    BaseResponseDto deleteDocument(Integer id) throws BadRequestException, EntityNotFoundException;

    List<PetDocumentsProjection> getPetDocumentsByCustomer(Integer customerId);

    List<DocumentsProjection> getAllActiveDocument();
}
