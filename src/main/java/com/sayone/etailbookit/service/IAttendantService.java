package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.AttendantDto;
import com.sayone.etailbookit.dto.AttendantInfo;
import com.sayone.etailbookit.dto.SearchDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.AttendantListingProjection;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface IAttendantService {
    void addAttendant(AttendantDto attendantDto) throws EtailBookItException;

    AttendantDto updateAttendant(AttendantDto attendantDto, Integer attendantId) throws EtailBookItException;

    void deleteAttendant(int attendantId) throws EtailBookItException;

    AttendantDto getAttendantById(int attendantId) throws EtailBookItException;

    List<AttendantListingProjection> getAttendants();

    Page<AttendantListingProjection> getAttendantsByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search);

    List<AttendantDto> getAttendantsForService(SearchDto searchDto) throws EntityNotFoundException;

    List<AttendantListingProjection> getActiveAttendants() throws EntityNotFoundException;

    List<AttendantDto> getAttendantsByVenue(Integer venueId,Integer serviceId)throws EtailBookItException;

    List<AttendantInfo> fetchAttendantIdAndName(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search,String retailer)throws EtailBookItException;

    List<AttendantDto> filterAttendantsByVenue(Integer venueId, String retailer) throws EntityNotFoundException;
}
