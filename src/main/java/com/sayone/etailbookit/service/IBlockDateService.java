package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BlockDateInfoDto;
import com.sayone.etailbookit.dto.BlockDateTimeDto;
import com.sayone.etailbookit.dto.BlockTimeDto;
import com.sayone.etailbookit.dto.RecurringBlockResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.BlockDateInfo;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;

public interface IBlockDateService {

    void saveBlockDates(OffsetDateTime startTime, OffsetDateTime endTime) throws EtailBookItException;

    BlockDateInfo saveBlockDates(OffsetDateTime blockDate,String timeZone) throws EtailBookItException;

    List<BlockDateInfoDto> getBlockDatesByMonth(OffsetDateTime startDate,OffsetDateTime endDate,String timeZone) throws EtailBookItException;

    void deleteBlockDate(int blockDateId,String timeZone) throws EtailBookItException;

    List<BlockDateInfoDto> saveBlockDayWithTime(BlockDateTimeDto blockDateTimeDto, String timeZone)throws EtailBookItException;

    RecurringBlockResponseDto saveRecurringBlockDayWithTime(BlockDateTimeDto blockDateTimeDto, String timeZone) throws EtailBookItException;

    void addDataToOffsetBlockDate(String retailer);

    List<BlockDateInfoDto> findBlockTimesOfSelectedDate(OffsetDateTime selectedDate, String timeZone);

    void unblockSelectedTimes(List<BlockDateInfoDto> blockTimeDtos, String timeZone);
}
