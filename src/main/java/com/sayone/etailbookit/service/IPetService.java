package com.sayone.etailbookit.service;


import com.fasterxml.jackson.databind.node.ObjectNode;
import com.itextpdf.text.DocumentException;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.PetDetailsProjection;
import com.sayone.etailbookit.projections.PetDropdownProjection;
import com.sayone.etailbookit.projections.PetListingProjection;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.security.GeneralSecurityException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface IPetService {

    void addPet(PetDto petDto) throws EtailBookItException;

    void updatePet(PetDto petDto, Integer petId) throws EtailBookItException;

    void deletePet(int petId) throws EtailBookItException;

    PetDetailsProjection getPetById(int petId) throws EtailBookItException;

    List<PetDropdownProjection> getPets();

    Page<PetListingProjection> getPetsByPagination(Integer customer, Integer pageNo, Integer pageSize, String sortBy, Optional<String> search) throws EntityNotFoundException;

    List<PetDropdownProjection> getPetsByCustomer(Integer customer,Optional<Boolean> withoutDeceasedDate, Optional<String> search) throws EtailBookItException;

    Boolean checkEligibility(Integer pet, Integer attendant, Integer venue) throws EtailBookItException;

    void updatePetWithCustomer(PetCustomerDto petCustomerDto, Integer petId, Integer customerId)throws EtailBookItException;

    void deletePetIdAndCustomerId(int petId ,int customerId) throws EtailBookItException;

    void createPetWithCustomer(PetCustomerDto petCustomerDto, int customerId)throws EtailBookItException;

    void mergePetProfile(List<PetLegacyRetailerListDto> petLegacyRetailerListDtos)throws EtailBookItException;

    void mergeAtbPetProfile(List<AtbpetProfileMergeDto> atBpetProfileMergeDtoList) throws EtailBookItException, ParseException;

    String signPetDocument(String fileUrl, Integer petId, Integer documentId,String name) throws IOException, URISyntaxException, GeneralSecurityException, DocumentException, EtailBookItException;

    PaginatedResponse<PetBirthdayDto> getPetBirthdayReport(Integer pageNo, Integer pageSize, String sortBy, Optional<String> petName, Integer petDob, List<Integer> petIds, List<Integer> cusIds)throws EtailBookItException;

    List<ObjectNode> importExcelAndSave(MultipartFile file) throws EtailBookItException, IOException, ParseException;

    List<Integer> bulkUpdateGeneralSizes(List<String> retailer);

    List<PetBirthdayDto> getPetReportForEcomm(Integer pageNo, Integer pageSize, String sortBy, Optional<String> petName, Integer petDob, List<Integer> petIds, List<Integer> cusIds)throws EtailBookItException;

    BaseResponseDto sendEmailToSignWaiver(Integer customerId,Integer petId) throws EtailBookItException;

    void mergeArizonawagnwash(List<ArizonawagnwashPetProfileMergeDto> arizonawagnwashPetProfileMergeDtoList)throws EtailBookItException;

    void mergePetPRofile(List<MergePetProfileDto> petProfileMergeDtoList)throws EtailBookItException;

    void deletePetsFromBadImport(List<MergePetProfileDto> petProfileMergeDtoList)throws EtailBookItException;
}
