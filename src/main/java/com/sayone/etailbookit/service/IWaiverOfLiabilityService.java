package com.sayone.etailbookit.service;

import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.WaiverOfLiability;

public interface IWaiverOfLiabilityService {
    void updateWaiverOfLiabilityInformation(WaiverOfLiability waiverOfLiability) throws EtailBookItException;

    WaiverOfLiability getWaiverOfLiabilityInformation();

    String getWaiverOfLiabilityInformationByPetId (Integer petId);

    String getWaiverOfLiabilityInformationByAppointmentId(Integer appointmentId);
}
