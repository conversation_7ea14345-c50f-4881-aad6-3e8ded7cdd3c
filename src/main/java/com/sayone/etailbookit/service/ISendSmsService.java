package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.AccessTokenDto;
import com.sayone.etailbookit.dto.SMSdto;
import com.sayone.etailbookit.dto.TwilioServiceDTO;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.TwilioService;
import com.sayone.etailbookit.projections.TwilioServiceProjection;
import com.twilio.jwt.accesstoken.AccessToken;

import java.io.IOException;
import java.util.List;

public interface ISendSmsService {
    String sendSMS(SMSdto smSdto) throws IOException;

    AccessTokenDto generateAccessToken(String serviceSID)throws EtailBookItException;

    TwilioService createConversation(TwilioServiceDTO twilioServiceDTO) throws EntityNotFoundException;

    List<TwilioServiceProjection> getConversation();

    void deleteConversation(String conversationSID);

    void deleteCustomerChatFromDatabase();
}
