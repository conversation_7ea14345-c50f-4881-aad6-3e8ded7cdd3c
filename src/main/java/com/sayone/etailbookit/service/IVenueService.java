package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.SearchDto;
import com.sayone.etailbookit.dto.VenueDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.VenueDetailProjection;
import com.sayone.etailbookit.projections.VenueDropdownProjection;
import com.sayone.etailbookit.projections.VenueListingProjection;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface IVenueService {
    VenueDto addVenue(VenueDto venueDto) throws EtailBookItException;

    void updateVenue(VenueDto venueDto, Integer venueId) throws EtailBookItException;

    void deleteVenue(int venueId) throws EtailBookItException;

    VenueDto getVenueById(int venueId) throws EtailBookItException;

    List<VenueDropdownProjection> getVenues();

    Page<VenueListingProjection> venuesByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search);

    List<VenueDto> getVenuesForService(SearchDto searchDto) throws EntityNotFoundException;
}
