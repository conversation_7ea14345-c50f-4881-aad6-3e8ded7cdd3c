package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.dto.PetCologneDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface IPetCologneService {

    PetCologneDto addPetCologne(PetCologneDto petCologneDto) throws EtailBookItException;

    PetCologneDto updatePetCologne(PetCologneDto petCologneDto, int petCologneId) throws EtailBookItException;

    void deletePetCologne(int petCologneId) throws EtailBookItException;

    PetCologneDto getPetCologneById(int petCologneId) throws EtailBookItException;

    ConfigurationDto<PetCologneDto> getPetColognes(Integer pageNo, Integer pageSize, String sortBy, String search);

    void bulkUpdateCologne(ConfigurationDto<PetCologneDto> petCologneDtos) throws EtailBookItException;

    Page<PetCologneDto> getActivePetCologne(Integer pageNo, Integer pageSize, String sortBy, String search) throws EtailBookItException;

    Page<PetCologneDto> getcologneByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search)throws EtailBookItException;
}
