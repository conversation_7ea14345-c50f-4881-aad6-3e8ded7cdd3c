package com.sayone.etailbookit.service;

import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Attendant;

import java.util.Map;

/**
 * Interface for Google Calendar synchronization service
 */
public interface IGoogleCalendarService {

    /**
     * Generate OAuth2 authorization URL for attendant to authorize Google Calendar access
     * @param attendantId The attendant ID
     * @param retailer The retailer name
     * @return Authorization URL
     * @throws EtailBookItException if attendant not found or URL generation fails
     */
    String getAuthorizationUrl(Integer attendantId, String retailer) throws EtailBookItException;


    /**
     * Invalidate Google Calendar sync for an attendant
     * Disables sync and clears all stored Google credentials (tokens, calendar ID)
     * @param attendantId The attendant ID
     * @throws EtailBookItException if attendant not found or invalidation fails
     */
    Attendant invalidateGoogleCalendarSync(Integer attendantId) throws EtailBookItException;

    /**
     * Send Google Calendar sync invitation email to attendant
     * @param attendantId The attendant ID
     * @throws EtailBookItException if attendant not found, no email, or sync not enabled
     */
    void sendCalendarSyncInvitation(Integer attendantId) throws EtailBookItException;

    /**
     * Accept Google Calendar invitation and generate authorization URL
     * Called by frontend when user clicks accept on the invitation
     * @param attendantId The attendant ID from the email link
     * @return Authorization URL for Google OAuth
     * @throws EtailBookItException if attendant not found or URL generation fails
     */
    String acceptCalendarInvitation(Integer attendantId) throws EtailBookItException;

    /**
     * Handle OAuth2 callback and store refresh token for attendant
     * State parameter format: "attendantId:retailer"
     * @param code Authorization code from Google
     * @param attendantId The attendant ID
     * @param retailer The retailer name
     * @throws EtailBookItException if callback processing fails
     */
    void handleOAuthCallback(String code, Integer attendantId, String retailer) throws EtailBookItException;

    /**
     * Trigger async appointment sync - validates and publishes event
     * @param attendantId The attendant ID
     * @throws EtailBookItException if validation fails
     */
    void triggerAsyncSync(Integer attendantId) throws EtailBookItException;

    /**
     * Sync appointments to Google Calendar
     * @param attendant The attendant (pre-fetched)
     * @param retailer The retailer name
     * @param appointments Appointments to sync (pre-fetched)
     * @return Number synced
     * @throws EtailBookItException if sync fails
     */
    int syncAppointments(Attendant attendant, String retailer, java.util.List<Appointment> appointments) throws EtailBookItException;

    /**
     * Get attendant by ID for verification
     * @param attendantId The attendant ID
     * @return Attendant object
     * @throws EtailBookItException if attendant not found
     */
    Attendant getAttendantById(Integer attendantId) throws EtailBookItException;


    /**
     * Create a Google Calendar event for an appointment
     * @param appointment The appointment to create event for
     */
    void createCalendarEvent(Appointment appointment);

    /**
     * Update a Google Calendar event for an appointment
     * @param appointment The appointment to update event for
     */
    void updateCalendarEvent(Appointment appointment);

    /**
     * Delete a Google Calendar event for an appointment
     * @param appointment The appointment to delete event for
     */
    void deleteCalendarEvent(Appointment appointment);

    /**
     * Reset the calendarSyncInProgress flag for an attendant
     * Runs in a separate transaction to ensure it's always reset
     */
    void resetSyncFlag(Integer attendantId);
}
