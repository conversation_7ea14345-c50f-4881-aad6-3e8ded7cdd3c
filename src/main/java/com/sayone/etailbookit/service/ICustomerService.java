package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.CustomerDto;
import com.sayone.etailbookit.dto.MergeCustomerDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Customer;
import com.sayone.etailbookit.projections.CustomerProjection;

import java.util.List;

public interface ICustomerService {

    void postCustomerDetails(List<CustomerDto> customers) throws EtailBookItException;

    void updateCustomerDetails(List<CustomerDto> customers)throws EtailBookItException;

    CustomerProjection getCustomerDetails(Integer customerId);

    MergeCustomerDto getPetandAppointmentDetails(Integer sourceCustomerId)throws EtailBookItException;

    void mergeCustomers(Integer destinationCustomerId, Integer sourceCustomerId) throws EtailBookItException;

    List<Customer> getAllCustomers();
}
