package com.sayone.etailbookit.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.itextpdf.signatures.PdfSigner;
import com.itextpdf.text.DocumentException;
import com.sayone.etailbookit.exception.EtailBookItException;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;
import java.security.cert.Certificate;

public interface IEsignSerive {
    String sign(String src, String dest, Certificate[] chain, PrivateKey pk, String digestAlgorithm, String provider, PdfSigner.CryptoStandard signatureType, String initialName, Integer petId,String documentType) throws GeneralSecurityException, IOException, DocumentException, EtailBookItException;
}
