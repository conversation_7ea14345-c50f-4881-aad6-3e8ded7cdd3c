package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.BookingDto;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.OffsetDateTime;

public interface ITimeslotService {
    BaseResponseDto generateServiceAvailability(ServiceSlotsDto serviceSlotsDto,String tz) throws EtailBookItException;

    BaseResponseDto getSlotById(Integer id,String timeZone) throws EtailBookItException;

    BaseResponseDto deleteSlotsById(Integer id, boolean changeEntireRecurringSlots) throws EtailBookItException;

    BaseResponseDto updateSlots(int id, ServiceSlotsDto serviceSlotsDto,boolean changeEntireRecurringSlots,String timeZone)throws EtailBookItException;

    BaseResponseDto getSlotsByDate(OffsetDateTime slotDateTime, String timeZone)throws EtailBookItException;

    BaseResponseDto getAllTimeSlots(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone);

    BaseResponseDto getTimeSlotsOfADay(BookingDto bookingInfo, String timeZone) throws BadRequestException;

   // BaseResponseDto fetchAvailableSlotsOfAMonth(Integer serviceId,Integer venueId,Integer attendantId,OffsetDateTime startDate,OffsetDateTime endDate,String timeZone) throws BadRequestException;

    BaseResponseDto getSlotsByDate1(OffsetDateTime slotDateTime, String timeZone, Integer pageNo, Integer pageSize, String sortBy);

    BaseResponseDto fetchSlotsByAttendant(Integer serviceId, Integer venueId, OffsetDateTime slotStartTime, String timeZone)throws EtailBookItException;

    //BaseResponseDto fetchSlotsByAttendantOfaMonth(Integer serviceId, Integer venueId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException;

    BaseResponseDto getAllTimeSlotsofAMonth(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone);

    BaseResponseDto getAllTimeSlotsofAWeek(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone);

    BaseResponseDto deleteEntriesOfARetailer(String retailer);

    BaseResponseDto generateServiceAvailabilityBackgroundProcessing(ServiceSlotsDto serviceSlotsDto)throws EtailBookItException;

    BaseResponseDto fetchSlotsByAttendantOfaMonthNew(Integer serviceId, Integer venueId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException;

    BaseResponseDto getSlotsByAttendantOfaMonth(Integer serviceId, Integer venueId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException;

    BaseResponseDto fetchSlotsByAttendantNew(Integer serviceId, Integer venueId, OffsetDateTime slotStartTime, String timeZone) throws EtailBookItException;

    BaseResponseDto getAllTimeSlots1(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws EtailBookItException;

    BaseResponseDto getSlotsByDateNew(OffsetDateTime slotDateTime, String timeZone, Integer pageNo, Integer pageSize, String sortBy);
}
