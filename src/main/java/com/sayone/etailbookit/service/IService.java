package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.ServiceDetailsDto;
import com.sayone.etailbookit.dto.ServiceDto;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.ServiceListingProjection;
import com.sayone.etailbookit.projections.ServiceNameListing;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;


public interface IService {

    Boolean addService(ServiceDto serviceDto) throws EtailBookItException;

    ServiceDetailsDto getServiceById(int serviceId) throws EtailBookItException;

    void deleteServiceById(int serviceId) throws EtailBookItException;

    Boolean updateService(ServiceDto serviceDto) throws EtailBookItException;

    List<ServiceNameListing> getAllService() throws EtailBookItException;

    Page<ServiceListingProjection> getServiceByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search);

    List<ServiceListingProjection> getServiceByServiceType(Integer serviceType) throws EtailBookItException;

    List<ServiceDto> getActiveService() throws EtailBookItException;

    void syncServices();
    Integer updateInternalItemNumber() throws EtailBookItException;

    Page<ServiceListingProjection> getServiceByRetailer(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search, String retailer)throws EtailBookItException;
}
