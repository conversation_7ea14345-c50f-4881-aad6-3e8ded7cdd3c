package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Pet;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.kinesis.endpoints.internal.Value;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface IPetTypeService {

    void addPetType(PetTypeDto petTypeDto , MultipartFile multipartFile) throws EtailBookItException, IOException,Exception;

    void updatePetType(PetTypeDto petTypeDto, int petTypeId, Optional<MultipartFile> multipartFile) throws Exception;

    void deletePetType(int petTypeId) throws EtailBookItException, IOException;

    PetTypeDto getPetTypeById(int petTypeId) throws EtailBookItException;

    Page<PetTypeDto> getPetTypes(Integer pageNo, Integer pageSize, String sortBy, String search);

    PetTypeDto getActiveDetails(int petTypeId) throws EtailBookItException;

    List<TemperamentDto> getTemperaments(int petTypeId) throws EntityNotFoundException;

    List<VaccinationRecordsDto> getVaccinationRecords(int petTypeId) throws EntityNotFoundException;

    List<GeneralPetSizeDto> getSizes(int petTypeId) throws EntityNotFoundException;

    void updateGeneralPetSize(UpdateGeneralPetSizeDto generalPetSizeDto, int petTypeId);

    void insertPetConfiguration(Integer petTypeId)throws EtailBookItException;

    void changeValuesOfGeneralPetSize()throws EtailBookItException;

    List<PetAndPetTypeMappingDto> getPetList(Integer serviceId,Integer customerId)throws EtailBookItException;
}
