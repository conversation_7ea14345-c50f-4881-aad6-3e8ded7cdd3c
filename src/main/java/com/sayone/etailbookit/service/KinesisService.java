package com.sayone.etailbookit.service;

import com.amazonaws.services.kinesis.AmazonKinesis;
import com.amazonaws.services.kinesis.model.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayone.etailbookit.controller.AppointmentController;
import com.sayone.etailbookit.dto.KinesisDataDto;
import com.sayone.etailbookit.dto.ServiceSyncDto;
import com.sayone.etailbookit.dto.SyncDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

@Service
public class KinesisService implements Runnable {

    private Thread thread;

    private String stream;

    private AmazonKinesis kinesis;

    private ObjectMapper objectMapper;

    private String servicesShardId;
    private static Logger LOGGER = LoggerFactory.getLogger(AppointmentController.class);

    public KinesisService(
        AmazonKinesis kinesis, ObjectMapper objectMapper,@Value("${aws.kinesis.stream}") String stream,
        @Value("${aws.kinesis.shard.id.servicing}") String servicesShardId
    ) {
        this.servicesShardId = servicesShardId;
        this.kinesis = kinesis;
        this.objectMapper = objectMapper;
        this.stream = stream;
    }

    public void produce(KinesisDataDto kinesisDataDTO, String shardId) {
        try {
            String explicitHashKey = null;
            ListShardsResult listShardsResult = kinesis.listShards(new ListShardsRequest().withStreamName(stream));
            for (Shard shard : listShardsResult.getShards()) {
                if (shard.getShardId().equals(shardId)) {
                    explicitHashKey = shard.getHashKeyRange().getStartingHashKey();
                }
            }
            List<PutRecordsRequestEntry> entries = new ArrayList<>();
            PutRecordsRequestEntry entry = new PutRecordsRequestEntry();
            entry.setData(ByteBuffer.wrap(objectMapper.writeValueAsBytes(kinesisDataDTO)));
            entry.setPartitionKey(shardId);
            entry.setExplicitHashKey(explicitHashKey);
            entries.add(entry);
            PutRecordsRequest createRecordsRequest = new PutRecordsRequest();
            createRecordsRequest.setStreamName(stream);
            createRecordsRequest.setRecords(entries);
            PutRecordsResult putRecordsResult = kinesis.putRecords(createRecordsRequest);
            LOGGER.info("Data send to Kinesis"+createRecordsRequest);
            LOGGER.info("KinesisService.produce :: putRecordsResult = " + putRecordsResult);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void syncService(String schema, List<ServiceSyncDto> dto) {
        KinesisDataDto kinesisDataDTO = new KinesisDataDto();
        SyncDto syncDto = new SyncDto();
        syncDto.setSyncProducts(dto);
        syncDto.setSchema(schema);
        kinesisDataDTO.setAction("serviceSync");
        kinesisDataDTO.setData(syncDto);
        kinesisDataDTO.setSchema(schema);
        produce(kinesisDataDTO, "shardId-00000000000"+servicesShardId);
    }

    @Override
    public void run() {

    }
}
