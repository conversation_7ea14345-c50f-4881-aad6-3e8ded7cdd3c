package com.sayone.etailbookit.service.impl;

import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.googleapis.batch.BatchRequest;
import com.google.api.client.googleapis.batch.json.JsonBatchCallback;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.http.EmptyContent;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpHeaders;
import com.google.api.client.http.HttpRequest;
import com.google.api.client.http.HttpRequestFactory;
import com.google.api.client.http.HttpResponse;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.client.util.DateTime;
import com.google.api.services.calendar.Calendar;
import com.google.api.services.calendar.CalendarScopes;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.EventDateTime;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.model.GoogleCalendarEvent;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.AttendantRepository;
import com.sayone.etailbookit.repository.GoogleCalendarEventRepository;
import com.sayone.etailbookit.component.CustomerSMSNotification;
import com.sayone.etailbookit.event.AppointmentSyncEvent;
import com.sayone.etailbookit.service.IGoogleCalendarService;
import com.sayone.etailbookit.util.RetailerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class GoogleCalendarService implements IGoogleCalendarService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleCalendarService.class);
    private static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();
    private static final List<String> SCOPES = Collections.singletonList(CalendarScopes.CALENDAR);
    private static final int BATCH_SIZE_LIMIT = 100; // Google Calendar API batch limit

    @Value("${google.calendar.client-id}")
    private String clientId;

    @Value("${google.calendar.client-secret}")
    private String clientSecret;

    @Value("${google.calendar.redirect-uri}")
    private String redirectUri;

    @Value("${google.calendar.application-name}")
    private String applicationName;

    @Value("${google.calendar.frontend-url}")
    private String frontendUrl;

    @Autowired
    private AttendantRepository attendantRepository;

    @Autowired
    private GoogleCalendarEventRepository googleCalendarEventRepository;

    @Autowired
    private CustomerSMSNotification customerSMSNotification;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * Generate OAuth2 authorization URL for attendant to authorize Google Calendar access
     * State parameter format: "attendantId:retailer"
     */
    public String getAuthorizationUrl(Integer attendantId, String retailer) throws EtailBookItException {
        // Validate attendant exists
        Attendant attendant = attendantRepository.findByAttendantId(attendantId);
        if (attendant == null) {
            LOGGER.error("Cannot generate authorization URL - attendant {} not found", attendantId);
            throw new EtailBookItException("Attendant not found with ID: " + attendantId);
        }

        LOGGER.info("Initiating calendar sync for attendant {} with retailer {}", attendantId, retailer);

        try {
            NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                    httpTransport, JSON_FACTORY,
                    clientId,
                    clientSecret,
                    SCOPES)
                    .setAccessType("offline")
                    .setApprovalPrompt("force")
                    .build();

            // Encode both attendantId and retailer in state parameter
            String stateParam = attendantId + ":" + retailer;
            
            String authorizationUrl = flow.newAuthorizationUrl()
                    .setRedirectUri(redirectUri)
                    .setState(stateParam)
                    .build();

            LOGGER.info("Generated authorization URL for attendant {} ({}) with retailer {}", 
                       attendantId, attendant.getEmail(), retailer);
            return authorizationUrl;
        } catch (Exception e) {
            LOGGER.error("Error generating authorization URL for attendant {}: {}", attendantId, e.getMessage());
            throw new EtailBookItException("Failed to generate Google Calendar authorization URL: " + e.getMessage());
        }
    }


    /**
     * Invalidate Google Calendar sync for an attendant
     * Disables sync flag and clears all stored Google credentials for security
     * This forces the attendant to re-authorize if they want to enable sync again
     */
    @Transactional
    public Attendant invalidateGoogleCalendarSync(Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered invalidateGoogleCalendarSync for attendant: {}", attendantId);

        try {
            Attendant attendant = attendantRepository.findByAttendantId(attendantId);
            if (attendant == null) {
                throw new EtailBookItException("Attendant not found with ID: " + attendantId);
            }

            if (attendant.getCalendarSyncInProgress()){
                throw new EtailBookItException("Calendar sync is already in progress for this attendant. Please wait for the current sync to complete.");
            }

            // Step 1: Delete calendar from Google Calendar FIRST (requires valid token)
            if (attendant.getGoogleCalendarId() != null && !attendant.getGoogleCalendarId().trim().isEmpty()) {
                try {
                    Calendar service = getCalendarService(attendant);
                    String calendarId = attendant.getGoogleCalendarId();
                    
                    // Verify calendar exists before attempting deletion
                    try {
                        service.calendarList().get(calendarId).execute();
                        // Calendar exists, proceed with deletion
                        service.calendars().delete(calendarId).execute();
                        LOGGER.info("Successfully deleted Google Calendar {} for attendant {}", calendarId, attendantId);
                    } catch (Exception e) {
                        LOGGER.warn("Calendar {} not found or already deleted for attendant {}", calendarId, attendantId);
                    }
                } catch (Exception e) {
                    LOGGER.warn("Failed to delete Google Calendar for attendant {} (may already be deleted): {}", 
                               attendantId, e.getMessage());
                }
            }

            // Step 2: Revoke token from Google AFTER calendar deletion (if refresh token exists)
            if (attendant.getGoogleRefreshToken() != null && !attendant.getGoogleRefreshToken().trim().isEmpty()) {
                try {
                    String revokeUrl = "https://oauth2.googleapis.com/revoke?token=" + attendant.getGoogleRefreshToken();
                    NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
                    
                    HttpRequestFactory requestFactory = httpTransport.createRequestFactory();
                    
                    // Create empty content for POST request (automatically sets Content-Length: 0)
                    EmptyContent emptyContent = new EmptyContent();
                    HttpRequest request = requestFactory.buildPostRequest(
                        new GenericUrl(revokeUrl), emptyContent);
                    
                    HttpResponse response = request.execute();
                    
                    if (response.getStatusCode() == 200) {
                        LOGGER.info("Successfully revoked Google token for attendant {}", attendantId);
                    } else {
                        LOGGER.warn("Failed to revoke token for attendant {}: HTTP {}", attendantId, response.getStatusCode());
                    }
                } catch (Exception e) {
                    LOGGER.warn("Failed to revoke Google token for attendant {} (may already be revoked): {}", 
                               attendantId, e.getMessage());
                }
            }

            // Step 3: Clear all Google credentials from database and return updated attendant
            attendant.setGoogleCalendarSyncEnabled(false);
            attendant.setGoogleCalendarAuthorized(false);
            attendant.setGoogleRefreshToken(null);
            attendant.setGoogleAccessToken(null);
            attendant.setGoogleTokenExpiry(null);
            attendant.setGoogleCalendarId(null);
            attendant.setCalendarSyncInProgress(false);
            Attendant savedAttendant = attendantRepository.save(attendant);

            LOGGER.info("@@@@Successfully invalidated Google Calendar sync for attendant {}", attendantId);
            
            return savedAttendant;

        } catch (EtailBookItException e) {
            LOGGER.error("Business logic error while invalidating calendar sync for attendant {}: {}",
                    attendantId, e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("Unexpected error while invalidating calendar sync for attendant {}: {}",
                    attendantId, e.getMessage(), e);
            throw new EtailBookItException("Failed to invalidate Google Calendar sync: " + e.getMessage());
        }
    }

    /**
     * Send Google Calendar sync invitation email to attendant
     * Only works if Google Calendar sync is enabled for the attendant
     * Email contains only frontend URL with attendantId and retailer parameters
     */
    @Transactional
    public void sendCalendarSyncInvitation(Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered sendCalendarSyncInvitation for attendant: {}", attendantId);

        Attendant attendant = attendantRepository.findByAttendantId(attendantId);
        if (attendant == null) {
            throw new EtailBookItException("Attendant not found with ID: " + attendantId);
        }

        // Check if Google Calendar sync is enabled
        if (!Boolean.TRUE.equals(attendant.getGoogleCalendarSyncEnabled())) {
            throw new EtailBookItException("Google Calendar sync is not enabled for this attendant. Please enable sync first.");
        }

        // Check if attendant has email address
        if (attendant.getEmail() == null || attendant.getEmail().trim().isEmpty()) {
            throw new EtailBookItException("Attendant does not have an email address configured. Please add an email address to enable Google Calendar sync.");
        }

        // Get retailer from context
        String retailer = RetailerContext.getRetailer();
        if (retailer == null || retailer.trim().isEmpty()) {
            throw new EtailBookItException("Retailer context not found. Please ensure the retailer header is set.");
        }

        // Build frontend invitation link with only attendantId and retailer
        // Frontend will call accept-invite API to get the authorization URL
        String invitationLink = frontendUrl + 
                              "?attendantId=" + attendantId + 
                              "&retailer=" + retailer;

        LOGGER.info("Generated invitation link: {}", invitationLink);

        // Use the CustomerSMSNotification service for template-based notification
        customerSMSNotification.sendGoogleCalendarSyncInvitation(attendant, invitationLink);

        LOGGER.info("@@@@Successfully sent Google Calendar sync invitation to attendant {} at email {} for retailer {}",
                attendantId, attendant.getEmail(), retailer);
    }

    /**
     * Accept Google Calendar invitation and generate authorization URL
     * This endpoint is called by frontend when user clicks accept on the invitation
     * 
     * @param attendantId The attendant ID from the email link
     * @return Authorization URL for Google OAuth
     * @throws EtailBookItException if attendant not found or URL generation fails
     */
    @Transactional
    public String acceptCalendarInvitation(Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered acceptCalendarInvitation for attendant: {}", attendantId);

        // Validate attendant exists
        Attendant attendant = attendantRepository.findByAttendantId(attendantId);
        if (attendant == null) {
            throw new EtailBookItException("Attendant not found with ID: " + attendantId);
        }

        // Check if Google Calendar sync is enabled
        if (!Boolean.TRUE.equals(attendant.getGoogleCalendarSyncEnabled())) {
            throw new EtailBookItException("Google Calendar sync is not enabled for this attendant. Please enable sync first.");
        }

        // Check if attendant already has valid credentials
        if (attendant.getGoogleRefreshToken() != null && 
            !attendant.getGoogleRefreshToken().trim().isEmpty() &&
            attendant.getGoogleCalendarId() != null &&
            !attendant.getGoogleCalendarId().trim().isEmpty()) {
            
            LOGGER.info("@@@@Attendant {} already has valid Google Calendar credentials", attendantId);
            throw new EtailBookItException("Google Calendar is already connected for this attendant. Use the invalidate-sync API to disconnect first.");
        }

        // Get retailer from context
        String retailer = RetailerContext.getRetailer();
        if (retailer == null || retailer.trim().isEmpty()) {
            throw new EtailBookItException("Retailer context not found. Please ensure the retailer header is set.");
        }

        // Generate authorization URL with retailer
        String authorizationUrl = getAuthorizationUrl(attendantId, retailer);

        LOGGER.info("@@@@Generated authorization URL for attendant {} with retailer {}", attendantId, retailer);
        return authorizationUrl;
    }


    /**
     * Handle OAuth2 callback and store refresh token for attendant
     * State parameter format: "attendantId:retailer"
     */
    @Transactional
    public void handleOAuthCallback(String code, Integer attendantId, String retailer) throws EtailBookItException {
        try {
            NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                    httpTransport, JSON_FACTORY,
                    clientId,
                    clientSecret,
                    SCOPES)
                    .setAccessType("offline")
                    .build();

            GoogleTokenResponse tokenResponse = flow.newTokenRequest(code)
                    .setRedirectUri(redirectUri)
                    .execute();

            Attendant attendant = attendantRepository.findByAttendantId(attendantId);
            if (attendant == null) {
                throw new EtailBookItException("Attendant not found with ID: " + attendantId);
            }

            // Store tokens
            attendant.setGoogleRefreshToken(tokenResponse.getRefreshToken());
            attendant.setGoogleAccessToken(tokenResponse.getAccessToken());
            attendant.setGoogleTokenExpiry(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresInSeconds()));
            attendant.setGoogleCalendarSyncEnabled(true);
            attendant.setGoogleCalendarAuthorized(true);

            // Set retailer context if not already set
            if (RetailerContext.getRetailer() == null && retailer != null) {
                RetailerContext.setRetailer(retailer);
                LOGGER.info("Set retailer '{}' from OAuth callback state", retailer);
            }

            // Fetch appointments and publish event for async sync
            List<Appointment> appointments = appointmentRepository.findByAttendantAndRetailer(attendantId, retailer);
            LOGGER.info("Found {} appointments for attendant {} after OAuth", appointments.size(), attendantId);

            // Check if sync is already in progress (BEFORE publishing event)
            if (Boolean.TRUE.equals(attendant.getCalendarSyncInProgress())) {
                LOGGER.warn("Sync already in progress for attendant {}. Skipping initial OAuth sync.", attendantId);
                throw new EtailBookItException("Calendar sync is already in progress for this attendant. Please wait for the current sync to complete.");
            }
            
            // Set sync in progress flag (BEFORE publishing event)
            attendant.setCalendarSyncInProgress(true);
            attendantRepository.save(attendant);
            LOGGER.info("Set calendarSyncInProgress=true for attendant {} (OAuth initial sync)", attendantId);

            LOGGER.info("Publishing AppointmentSyncEvent for attendant {} after OAuth completion", attendantId);
            AppointmentSyncEvent syncEvent = new AppointmentSyncEvent(this, attendant, retailer, appointments);
            eventPublisher.publishEvent(syncEvent);
            
            LOGGER.info("OAuth callback completed successfully for attendant {} with retailer {}", attendantId, retailer);
        } catch (Exception e) {
            LOGGER.error("Error handling OAuth callback for attendant {}: {}", attendantId, e.getMessage(), e);
            throw new EtailBookItException("Failed to complete Google Calendar authorization: " + e.getMessage());
        }
    }

    /**
     * Trigger async appointment sync
     * Validates attendant and appointments exist, then publishes event
     */
    @Transactional
    public void triggerAsyncSync(Integer attendantId) throws EtailBookItException {
        String retailer = RetailerContext.getRetailer();
        if (retailer == null || retailer.trim().isEmpty()) {
            throw new EtailBookItException("Retailer context not found. Please ensure the retailer header is set.");
        }
        
        Attendant attendant = attendantRepository.findByAttendantId(attendantId);
        if (attendant == null) {
            throw new EtailBookItException("Attendant not found with ID: " + attendantId);
        }
        
        if (!Boolean.TRUE.equals(attendant.getGoogleCalendarSyncEnabled())) {
            throw new EtailBookItException("Google Calendar sync is not enabled for attendant: " + attendantId);
        }

        if (attendant.getGoogleRefreshToken() == null || attendant.getGoogleRefreshToken().trim().isEmpty()) {
            throw new EtailBookItException("Google Calendar is not connected. Please authorize access first.");
        }
        
        List<Appointment> appointments = appointmentRepository.findByAttendantAndRetailer(attendant.getAttendantId(), retailer);
        if (appointments == null || appointments.isEmpty()) {
            throw new EtailBookItException("No appointments found for this attendant. Nothing to sync.");
        }
        
        // Check if sync is already in progress (BEFORE publishing event)
        if (Boolean.TRUE.equals(attendant.getCalendarSyncInProgress())) {
            LOGGER.warn("Sync already in progress for attendant {}. Rejecting concurrent sync request.", attendantId);
            throw new EtailBookItException("Calendar sync is already in progress for this attendant. Please wait for the current sync to complete.");
        }
        
        // Set sync in progress flag (BEFORE publishing event)
        attendant.setCalendarSyncInProgress(true);
        attendantRepository.save(attendant);
        LOGGER.info("Set calendarSyncInProgress=true for attendant {} (manual sync)", attendantId);
        
        LOGGER.info("Publishing AppointmentSyncEvent for manual sync - attendant {} has {} appointments", 
            attendantId, appointments.size());
        AppointmentSyncEvent syncEvent = new AppointmentSyncEvent(this, attendant, retailer, appointments);
        eventPublisher.publishEvent(syncEvent);
        LOGGER.info("Successfully published sync event for attendant {}", attendantId);
    }

    /**
     * Sync appointments to Google Calendar
     * @param attendant The attendant object (pre-fetched)
     * @param retailer The retailer name
     * @param appointments List of appointments to sync (pre-fetched)
     */
    @Transactional
    public int syncAppointments(Attendant attendant, String retailer, List<Appointment> appointments) throws EtailBookItException {
        String calendarId = getOrCreateCalendar(attendant, retailer);

        LOGGER.info("Syncing {} appointments for attendant {} in retailer {}", 
                   appointments.size(), attendant.getAttendantId(), retailer);
        
        // OPTIMIZATION: Batch fetch all existing mappings to avoid N+1 query problem
        // Filter out null IDs to ensure proper SQL matching
        Set<Integer> appointmentIds = appointments.stream()
            .map(Appointment::getId)
            .filter(id -> id != null)
            .collect(Collectors.toSet());
        
        Map<Integer, GoogleCalendarEvent> existingMappings = googleCalendarEventRepository
            .findByAppointmentIdIn(appointmentIds)
            .stream()
            .collect(Collectors.toMap(
                GoogleCalendarEvent::getAppointmentId,
                Function.identity()
            ));
        
        LOGGER.debug("Fetched {} existing calendar event mappings", existingMappings.size());
        
        // OPTIMIZATION: Lazy-create Calendar service (only when needed)
        // This avoids creating service if all appointments are skipped
        Calendar service = null;
        
        int foundCount = 0;
        int skippedCount = 0;
        
        // Collect appointments that need to be created
        List<Appointment> appointmentsToCreate = new ArrayList<>();

        for (Appointment appointment : appointments) {
            LOGGER.debug("Processing appointment {} with status: {}", 
                        appointment.getId(), appointment.getServiceStatus());
            try {
                // Only sync active appointments (skip null, CANCELLED, REJECTED, PENDING_APPROVAL)
                if (appointment.getServiceStatus() == null ||
                        appointment.getServiceStatus().toString().equals("CANCELLED") ||
                        appointment.getServiceStatus().toString().equals("REJECTED") ||
                        appointment.getServiceStatus().toString().equals("PENDING_APPROVAL")) {
                    LOGGER.debug("Skipping appointment {} due to status: {}", 
                               appointment.getId(), appointment.getServiceStatus());
                    skippedCount++;
                    continue;
                }

                // Lazy initialization: Create service only when first needed
                // This maintains original behavior where service creation failure is per-appointment
                if (service == null) {
                    service = getCalendarService(attendant);
                }

                // Check if event exists in database (using pre-fetched map)
                GoogleCalendarEvent existingMapping = existingMappings.get(appointment.getId());

                if (existingMapping != null) {
                    // Event mapping exists in DB - verify it exists in the CURRENT calendar
                    try {
                        // Try to get the event from Google Calendar
                        service.events().get(calendarId, existingMapping.getGoogleEventId()).execute();
                        LOGGER.debug("Event {} already exists in current calendar for appointment {}", 
                                   existingMapping.getGoogleEventId(), appointment.getId());
                        foundCount++;
                        continue; // Event exists, skip creation
                    } catch (Exception e) {
                        // Event doesn't exist in current calendar - remove mapping and check by appointment ID
                        LOGGER.debug("Event {} not found in current calendar for appointment {} - checking by appointment ID", 
                                  existingMapping.getGoogleEventId(), appointment.getId());
                        googleCalendarEventRepository.delete(existingMapping);
                        existingMappings.remove(appointment.getId()); // Remove from cache
                    }
                }

                // No DB mapping or mapping was invalid - check Google Calendar by appointment ID
                Event existingEvent = findEventByAppointmentId(service, calendarId, appointment.getId());
                
                if (existingEvent != null) {
                    // Found event by appointment ID - recreate mapping
                    GoogleCalendarEvent newMapping = new GoogleCalendarEvent();
                    newMapping.setAppointmentId(appointment.getId());
                    newMapping.setAttendantId(attendant.getAttendantId());
                    newMapping.setGoogleEventId(existingEvent.getId());
                    newMapping.setCalendarId(calendarId);
                    newMapping.setRetailer(appointment.getRetailer());
                    googleCalendarEventRepository.save(newMapping);
                    existingMappings.put(appointment.getId(), newMapping); // Update cache
                    LOGGER.info("Recreated DB mapping for existing event {} and appointment {}", 
                               existingEvent.getId(), appointment.getId());
                    foundCount++;
                    continue; // Event exists, skip creation
                }

                // Need to create this appointment
                appointmentsToCreate.add(appointment);
                
            } catch (Exception e) {
                LOGGER.error("Failed to check appointment {}: {}",
                        appointment.getId(), e.getMessage());
                skippedCount++;
                // Continue with next appointment
            }
        }

        // OPTIMIZATION: Batch create events using Google Calendar API BatchRequest
        int createdCount = 0;
        if (!appointmentsToCreate.isEmpty() && service != null) {
            LOGGER.info("Creating {} new events using batch API", appointmentsToCreate.size());
            createdCount = batchCreateEvents(service, calendarId, attendant, retailer, appointmentsToCreate);
        }

        LOGGER.info("Sync completed for attendant {}: {} created, {} found, {} skipped (out of {} total)",
                attendant.getAttendantId(), createdCount, foundCount, skippedCount, appointments.size());
        
        return createdCount + foundCount;
    }

    /**
     * Batch create events using Google Calendar API BatchRequest
     * Handles batches of up to 100 items (Google's limit)
     * Automatically splits into multiple batches if needed
     * 
     * @param service Calendar service
     * @param calendarId Calendar ID to create events in
     * @param attendant Attendant
     * @param retailer Retailer name
     * @param appointments List of appointments to create events for
     * @return Number of events successfully created
     */
    private int batchCreateEvents(Calendar service, String calendarId, Attendant attendant, 
                                    String retailer, List<Appointment> appointments) {
        int totalCreated = 0;
        int totalAppointments = appointments.size();
        
        // Split into batches of BATCH_SIZE_LIMIT (100) if needed
        for (int i = 0; i < totalAppointments; i += BATCH_SIZE_LIMIT) {
            int batchEnd = Math.min(i + BATCH_SIZE_LIMIT, totalAppointments);
            List<Appointment> batchAppointments = appointments.subList(i, batchEnd);
            
            LOGGER.info("Processing batch {}/{}: appointments {} to {} ({} items)", 
                       (i / BATCH_SIZE_LIMIT) + 1, 
                       (totalAppointments + BATCH_SIZE_LIMIT - 1) / BATCH_SIZE_LIMIT,
                       i, batchEnd - 1, batchAppointments.size());
            
            long batchStartTime = System.currentTimeMillis();
            
            // Execute this batch
            final int[] successCount = {0};
            final int[] failureCount = {0};
            int queuedCount = 0;
            
            try {
                BatchRequest batch = service.batch();
                
                // Add all events in this batch
                for (Appointment appointment : batchAppointments) {
                    Event event = buildEventFromAppointment(appointment);
                    
                    // Skip if event could not be built (invalid appointment data)
                    if (event == null) {
                        LOGGER.debug("Skipping appointment {} in batch - event could not be built", appointment.getId());
                        continue;
                    }
                    
                    // Queue this event insertion
                    service.events().insert(calendarId, event).queue(batch, new JsonBatchCallback<Event>() {
                        @Override
                        public void onSuccess(Event createdEvent, HttpHeaders responseHeaders) {
                            try {
                                // Save mapping to database
                                GoogleCalendarEvent mapping = new GoogleCalendarEvent();
                                mapping.setAppointmentId(appointment.getId());
                                mapping.setAttendantId(attendant.getAttendantId());
                                mapping.setGoogleEventId(createdEvent.getId());
                                mapping.setCalendarId(calendarId);
                                mapping.setRetailer(retailer);
                                googleCalendarEventRepository.save(mapping);
                                
                                successCount[0]++;
                                LOGGER.debug("✓ Batch: Created event {} for appointment {}", 
                                            createdEvent.getId(), appointment.getId());
                            } catch (Exception e) {
                                LOGGER.error("✗ Batch: Failed to save mapping for appointment {}: {}", 
                                            appointment.getId(), e.getMessage());
                                failureCount[0]++;
                            }
                        }
                        
                        @Override
                        public void onFailure(GoogleJsonError error, HttpHeaders responseHeaders) {
                            failureCount[0]++;
                            LOGGER.error("✗ Batch: Failed to create event for appointment {}: {} - {}", 
                                        appointment.getId(), error.getCode(), error.getMessage());
                        }
                    });
                    queuedCount++;
                }
                
                // Only execute batch if there are queued requests
                if (queuedCount > 0) {
                    LOGGER.debug("Executing batch with {} queued events...", queuedCount);

                    batch.execute();
                    
                    long batchDuration = System.currentTimeMillis() - batchStartTime;
                    totalCreated += successCount[0];
                    
                    LOGGER.info("Batch completed: {} succeeded, {} failed in {} ms", 
                               successCount[0], failureCount[0], batchDuration);
                } else {
                    LOGGER.warn("Skipping batch execution - all {} appointments had invalid data", batchAppointments.size());
                }
                
            } catch (Exception e) {
                LOGGER.error("Batch execution failed completely: {}", e.getMessage(), e);
            }
        }
        
        return totalCreated;
    }

    /**
     * Get or create calendar for the attendant
     * If calendar ID exists in DB, verify it exists in Google Calendar
     * If not, create a new calendar and return the calendar ID
     * @return The calendar ID (existing or newly created)
     */
    private String getOrCreateCalendar(Attendant attendant, String retailer) throws EtailBookItException {
        try {
            Calendar service = getCalendarService(attendant);
            boolean needsNewCalendar = false;
            
            if (attendant.getGoogleCalendarId() != null && !attendant.getGoogleCalendarId().isEmpty()) {
                // Calendar ID exists in DB - verify it exists and is not deleted in Google Calendar
                try {
                    service.calendarList().get(attendant.getGoogleCalendarId()).execute();
                    LOGGER.info("Calendar {} exists and is not deleted in Google Calendar for attendant {}", 
                               attendant.getGoogleCalendarId(), attendant.getAttendantId());
                    // Calendar exists and is not deleted - no need to create new one
                } catch (Exception e) {
                    // Calendar doesn't exist or is deleted - need to create new one
                    LOGGER.warn("Calendar {} not found or deleted in Google Calendar: {}. Will create new calendar.", 
                               attendant.getGoogleCalendarId(), e.getMessage());
                    needsNewCalendar = true;
                }
            } else {
                // No calendar ID in DB - need to create new one
                LOGGER.info("No calendar ID stored for attendant {}, will create new calendar", attendant.getAttendantId());
                needsNewCalendar = true;
            }

            // Create calendar only if needed
            if (needsNewCalendar) {
                // Use attendant's first name for calendar, fallback to "Attendant" if not available
                String attendantName = (attendant.getFirstName() != null && !attendant.getFirstName().trim().isEmpty()) 
                    ? attendant.getFirstName() 
                    : "Attendant";
                String calendarName = "Appointments for " + attendantName;

                com.google.api.services.calendar.model.Calendar calendar = new com.google.api.services.calendar.model.Calendar();
                calendar.setSummary(calendarName);
                calendar.setDescription(calendarName);
                calendar.setTimeZone("UTC");

                com.google.api.services.calendar.model.Calendar createdCalendar = service.calendars().insert(calendar).execute();
                attendant.setGoogleCalendarId(createdCalendar.getId());
                LOGGER.info("Created new calendar '{}' with ID: {}", calendarName, createdCalendar.getId());
            }
            
            // Always save attendant (may have been modified by caller before this method)
            // This ensures any pending changes (like tokens) are persisted
            attendantRepository.save(attendant);
            
            return attendant.getGoogleCalendarId();
        } catch (Exception e) {
            LOGGER.error("Error getting or creating calendar for attendant {}: {}", attendant.getAttendantId(), e.getMessage(), e);
            throw new EtailBookItException("Failed to get or create calendar: " + e.getMessage());
        }
    }

    /**
     * Get attendant by ID for verification
     */
    public Attendant getAttendantById(Integer attendantId) throws EtailBookItException {
        Attendant attendant = attendantRepository.findByAttendantId(attendantId);
        if (attendant == null) {
            throw new EtailBookItException("Attendant not found with ID: " + attendantId);
        }
        return attendant;
    }

    /**
     * Get Google Calendar service with valid credentials
     */
    private Calendar getCalendarService(Attendant attendant) throws EtailBookItException {
        // Validate credentials exist before attempting any API calls
        if (attendant.getGoogleRefreshToken() == null || attendant.getGoogleRefreshToken().trim().isEmpty()) {
            LOGGER.warn("Google Calendar credentials not found for attendant {}. Authorization required.", 
                       attendant.getAttendantId());
            throw new EtailBookItException("Google Calendar is not connected. Please authorize access first.");
        }
        
        try {
            NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();

            // Check if access token needs refresh
            if (attendant.getGoogleTokenExpiry() == null ||
                    LocalDateTime.now().isAfter(attendant.getGoogleTokenExpiry().minusMinutes(5))) {
                refreshAccessToken(attendant);
            }

            GoogleCredential credential = new GoogleCredential.Builder()
                    .setTransport(httpTransport)
                    .setJsonFactory(JSON_FACTORY)
                    .setClientSecrets(clientId, clientSecret)
                    .build()
                    .setAccessToken(attendant.getGoogleAccessToken())
                    .setRefreshToken(attendant.getGoogleRefreshToken());

            return new Calendar.Builder(httpTransport, JSON_FACTORY, credential)
                    .setApplicationName(applicationName)
                    .build();
        } catch (Exception e) {
            LOGGER.error("Error creating Calendar service for attendant {}: {}",
                    attendant.getAttendantId(), e.getMessage());
            throw new EtailBookItException("Failed to access Google Calendar: " + e.getMessage());
        }
    }

    /**
     * Refresh access token using refresh token
     */
    @Transactional
    private void refreshAccessToken(Attendant attendant) throws EtailBookItException {
        try {
            NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            GoogleCredential credential = new GoogleCredential.Builder()
                    .setTransport(httpTransport)
                    .setJsonFactory(JSON_FACTORY)
                    .setClientSecrets(clientId, clientSecret)
                    .build()
                    .setRefreshToken(attendant.getGoogleRefreshToken());

            if (credential.refreshToken()) {
                attendant.setGoogleAccessToken(credential.getAccessToken());
                attendant.setGoogleTokenExpiry(LocalDateTime.now().plusSeconds(3600)); // 1 hour
                attendantRepository.save(attendant);
                LOGGER.info("Refreshed access token for attendant {}", attendant.getAttendantId());
            }
        } catch (Exception e) {
            LOGGER.error("Error refreshing token for attendant {}: {}",
                    attendant.getAttendantId(), e.getMessage());
            
            // Provide user-friendly error message for common issues
            if (e.getMessage() != null && e.getMessage().contains("invalid_grant")) {
                throw new EtailBookItException("Google Calendar authorization has expired or been revoked. Please reconnect your Google Calendar.");
            }
            
            throw new EtailBookItException("Failed to refresh Google Calendar access token. Please reconnect your Google Calendar.");
        }
    }

    /**
     * Create a Google Calendar event for an appointment
     * Note: @Async without @Transactional to avoid anti-pattern where transaction 
     * may commit before async method completes
     */
    @Async
    public void createCalendarEvent(Appointment appointment) {
        try {
            Attendant attendant = appointment.getAttendant();
            if (attendant == null) {
                LOGGER.debug("No attendant for appointment {}", appointment.getId());
                return;
            }

            // Check if Google Calendar sync is enabled for this attendant
            if (!Boolean.TRUE.equals(attendant.getGoogleCalendarSyncEnabled())) {
                LOGGER.debug("Google Calendar sync not enabled for attendant {} (appointment {})", 
                           attendant.getAttendantId(), appointment.getId());
                return;
            }

            // Ensure calendar exists in Google Calendar
            String calendarId;
            try {
                calendarId = getOrCreateCalendar(attendant, appointment.getRetailer());
            } catch (Exception e) {
                LOGGER.error("Failed to get or create calendar for appointment {}: {}", 
                           appointment.getId(), e.getMessage());
                return;
            }

            // Check if event already exists in database
            Optional<GoogleCalendarEvent> existingEvent = googleCalendarEventRepository
                    .findByAppointmentId(appointment.getId());
            
            Calendar service = getCalendarService(attendant);
            
            if (existingEvent.isPresent()) {
                // Event mapping exists in DB - verify it exists in Google Calendar
                GoogleCalendarEvent calendarEvent = existingEvent.get();
                try {
                    // Try to get the event from Google Calendar
                    service.events().get(calendarId, calendarEvent.getGoogleEventId()).execute();
                    LOGGER.debug("Calendar event {} already exists in Google Calendar for appointment {}",
                               calendarEvent.getGoogleEventId(), appointment.getId());
                    return; // Event exists, don't create
                } catch (Exception e) {
                    // Event doesn't exist in Google Calendar - remove mapping
                    LOGGER.debug("Event {} not found in Google Calendar for appointment {}. Checking by appointment ID.",
                              calendarEvent.getGoogleEventId(), appointment.getId());
                    googleCalendarEventRepository.delete(calendarEvent);
                }
            }

            // No DB mapping or mapping was invalid - check Google Calendar by appointment ID
            Event foundEvent = findEventByAppointmentId(service, calendarId, appointment.getId());
            
            if (foundEvent != null) {
                // Found event by appointment ID - recreate mapping
                GoogleCalendarEvent newMapping = new GoogleCalendarEvent();
                newMapping.setAppointmentId(appointment.getId());
                newMapping.setAttendantId(attendant.getAttendantId());
                newMapping.setGoogleEventId(foundEvent.getId());
                newMapping.setCalendarId(calendarId);
                newMapping.setRetailer(appointment.getRetailer());
                googleCalendarEventRepository.save(newMapping);
                LOGGER.debug("Recreated DB mapping for existing event {} and appointment {}", 
                           foundEvent.getId(), appointment.getId());
                return; // Event exists, don't create
            }
            // Build new event
            Event event = buildEventFromAppointment(appointment);

            // Skip if event could not be built (invalid appointment data)
            if (event == null) {
                LOGGER.debug("Skipping calendar sync for appointment {} due to invalid data", appointment.getId());
                return;
            }

            // Create event in Google Calendar
            Event createdEvent = service.events().insert(calendarId, event).execute();

            // Save mapping
            GoogleCalendarEvent calendarEvent = new GoogleCalendarEvent();
            calendarEvent.setAppointmentId(appointment.getId());
            calendarEvent.setAttendantId(attendant.getAttendantId());
            calendarEvent.setGoogleEventId(createdEvent.getId());
            calendarEvent.setCalendarId(calendarId);
            calendarEvent.setRetailer(appointment.getRetailer());
            googleCalendarEventRepository.save(calendarEvent);

            LOGGER.info("Created Google Calendar event {} for appointment {} in calendar {}",
                    createdEvent.getId(), appointment.getId(), calendarId);
        } catch (Exception e) {
            LOGGER.error("Error creating calendar event for appointment {}: {}",
                    appointment.getId(), e.getMessage(), e);
        }
    }

    /**
     * Update a Google Calendar event for an appointment
     * Note: @Async without @Transactional to avoid anti-pattern where transaction 
     * may commit before async method completes
     */
    @Async
    public void updateCalendarEvent(Appointment appointment) {
        try {
            Attendant attendant = appointment.getAttendant();
            if (attendant == null) {
                LOGGER.debug("No attendant for appointment {}", appointment.getId());
                return;
            }

            // Check if Google Calendar sync is enabled for this attendant
            if (!Boolean.TRUE.equals(attendant.getGoogleCalendarSyncEnabled())) {
                LOGGER.debug("Google Calendar sync not enabled for attendant {} (appointment {})", 
                           attendant.getAttendantId(), appointment.getId());
                return;
            }

            // Ensure calendar exists in Google Calendar (validates or creates new calendar)
            String calendarId;
            try {
                calendarId = getOrCreateCalendar(attendant, appointment.getRetailer());
            } catch (Exception e) {
                LOGGER.error("Failed to get or create calendar for appointment {}: {}", 
                           appointment.getId(), e.getMessage());
                return;
            }

            Optional<GoogleCalendarEvent> calendarEventOpt = googleCalendarEventRepository
                    .findByAppointmentId(appointment.getId());

            if (!calendarEventOpt.isPresent()) {
                LOGGER.debug("No calendar event found for appointment {}, creating new one", appointment.getId());
                createCalendarEvent(appointment);
                return;
            }

            GoogleCalendarEvent calendarEvent = calendarEventOpt.get();
            Calendar service = getCalendarService(attendant);
            
            // Check if old mapping has different calendar ID (calendar was recreated)
            if (calendarEvent.getCalendarId() != null && !calendarEvent.getCalendarId().equals(calendarId)) {
                LOGGER.warn("Calendar ID changed from {} to {} for appointment {} - deleting old mapping and creating new event", 
                           calendarEvent.getCalendarId(), calendarId, appointment.getId());
                googleCalendarEventRepository.delete(calendarEvent);
                createCalendarEvent(appointment);
                return;
            }
            
            // Calendar is valid - now verify the event exists
            try {
                // Check if event exists in Google Calendar
                service.events().get(calendarId, calendarEvent.getGoogleEventId()).execute();
                LOGGER.info("Event {} exists in Google Calendar for appointment {} - proceeding with update", 
                           calendarEvent.getGoogleEventId(), appointment.getId());
            } catch (Exception e) {
                // Event doesn't exist in Google Calendar - check by appointment ID as fallback
                LOGGER.warn("Event {} not found in Google Calendar for appointment {} - checking by appointment ID", 
                           calendarEvent.getGoogleEventId(), appointment.getId());
                
                // Remove the old mapping
                googleCalendarEventRepository.delete(calendarEvent);
                
                // Fallback: Search by appointment ID
                Event foundEvent = findEventByAppointmentId(service, calendarId, appointment.getId());
                if (foundEvent != null) {
                    // Found event by appointment ID - recreate mapping and update
                    calendarEvent = new GoogleCalendarEvent();
                    calendarEvent.setAppointmentId(appointment.getId());
                    calendarEvent.setAttendantId(attendant.getAttendantId());
                    calendarEvent.setGoogleEventId(foundEvent.getId());
                    calendarEvent.setCalendarId(calendarId);
                    calendarEvent.setRetailer(appointment.getRetailer());
                    googleCalendarEventRepository.save(calendarEvent);
                    LOGGER.info("Recreated DB mapping for event {} and appointment {}", 
                               foundEvent.getId(), appointment.getId());
                } else {
                    // Event doesn't exist - create new one instead of updating
                    LOGGER.info("Event not found by appointment ID either - creating new event for appointment {}", 
                               appointment.getId());
                    createCalendarEvent(appointment);
                    return;
                }
            }

            Event event = buildEventFromAppointment(appointment);

            // Skip if event could not be built (invalid appointment data)
            if (event == null) {
                LOGGER.debug("Skipping calendar update for appointment {} due to invalid data", appointment.getId());
                return;
            }

            service.events().update(calendarId, calendarEvent.getGoogleEventId(), event).execute();

            LOGGER.info("Updated Google Calendar event {} for appointment {}",
                    calendarEvent.getGoogleEventId(), appointment.getId());
        } catch (Exception e) {
            LOGGER.error("Error updating calendar event for appointment {}: {}",
                    appointment.getId(), e.getMessage(), e);
            // Don't throw exception - calendar sync should not break appointment updates
        }
    }

    /**
     * Delete a Google Calendar event for an appointment
     * Note: @Async without @Transactional to avoid anti-pattern where transaction 
     * may commit before async method completes
     */
    @Async
    public void deleteCalendarEvent(Appointment appointment) {
        try {
            Attendant attendant = appointment.getAttendant();
            if (attendant == null) {
                LOGGER.debug("No attendant for appointment {}", appointment.getId());
                return;
            }

            // Check if Google Calendar sync is enabled for this attendant
            if (!Boolean.TRUE.equals(attendant.getGoogleCalendarSyncEnabled())) {
                LOGGER.debug("Google Calendar sync not enabled for attendant {} (appointment {}) - skipping calendar deletion", 
                           attendant.getAttendantId(), appointment.getId());
                return;
            }

            Optional<GoogleCalendarEvent> calendarEventOpt = googleCalendarEventRepository
                    .findByAppointmentId(appointment.getId());

            if (!calendarEventOpt.isPresent()) {
                LOGGER.debug("No calendar event found for appointment {}", appointment.getId());
                return;
            }

            GoogleCalendarEvent calendarEvent = calendarEventOpt.get();

            // Sync is enabled - verify calendar and event exist in Google Calendar before deleting
            Calendar service = getCalendarService(attendant);
            String calendarId = calendarEvent.getCalendarId() != null ?
                    calendarEvent.getCalendarId() : attendant.getGoogleCalendarId();
            
            // First, verify the calendar itself exists
            try {
                service.calendarList().get(calendarId).execute();
                LOGGER.debug("Calendar {} exists for appointment {} deletion", calendarId, appointment.getId());
            } catch (Exception e) {
                // Calendar doesn't exist or is deleted - delete orphaned mapping
                LOGGER.warn("Calendar {} not found or deleted for appointment {} - deleting orphaned DB mapping: {}", 
                           calendarId, appointment.getId(), e.getMessage());
                googleCalendarEventRepository.delete(calendarEvent);
                return;
            }
            
            // Calendar exists - now verify the event exists
            try {
                // Verify event exists in Google Calendar
                service.events().get(calendarId, calendarEvent.getGoogleEventId()).execute();
                LOGGER.info("Event {} exists in Google Calendar for appointment {} - proceeding with deletion", 
                           calendarEvent.getGoogleEventId(), appointment.getId());
            } catch (Exception e) {
                // Event doesn't exist in Google Calendar - check by appointment ID as fallback
                LOGGER.warn("Event {} not found in Google Calendar for appointment {} - checking by appointment ID", 
                           calendarEvent.getGoogleEventId(), appointment.getId());
                
                // Fallback: Search by appointment ID
                Event foundEvent = findEventByAppointmentId(service, calendarId, appointment.getId());
                if (foundEvent != null) {
                    // Found event by appointment ID - delete it
                    service.events().delete(calendarId, foundEvent.getId()).execute();
                    LOGGER.info("Deleted Google Calendar event {} (found by appointment ID) for appointment {} from calendar {}",
                            foundEvent.getId(), appointment.getId(), calendarId);
                } else {
                    LOGGER.warn("Event not found by appointment ID either - deleting orphaned DB mapping for appointment {}", 
                               appointment.getId());
                    googleCalendarEventRepository.delete(calendarEvent);
                }
                return;
            }

            service.events().delete(calendarId, calendarEvent.getGoogleEventId()).execute();

            LOGGER.info("Deleted Google Calendar event {} for appointment {} from calendar {}",
                    calendarEvent.getGoogleEventId(), appointment.getId(), calendarId);
            
            // Delete the DB mapping after successful deletion from Google Calendar
            googleCalendarEventRepository.delete(calendarEvent);
            LOGGER.info("Deleted DB mapping for appointment {}", appointment.getId());
        } catch (Exception e) {
            LOGGER.error("Error deleting calendar event for appointment {}: {}",
                    appointment.getId(), e.getMessage(), e);
            // Don't throw exception - calendar sync should not break appointment deletion
        }
    }

    /**
     * Find event in Google Calendar by appointment ID using extended properties
     */
    private Event findEventByAppointmentId(Calendar service, String calendarId, Integer appointmentId) {
        try {
            String query = "appointmentId=" + appointmentId;
            java.util.List<String> privateExtendedProperty = java.util.Collections.singletonList(query);
            com.google.api.services.calendar.model.Events events = service.events().list(calendarId)
                    .setPrivateExtendedProperty(privateExtendedProperty)
                    .setMaxResults(1)
                    .execute();
            
            if (events.getItems() != null && !events.getItems().isEmpty()) {
                Event event = events.getItems().get(0);
                LOGGER.info("Found existing event {} for appointment {} in calendar {}", 
                        event.getId(), appointmentId, calendarId);
                return event;
            }
            
            LOGGER.info("No existing event found for appointment {} in calendar {}", appointmentId, calendarId);
            return null;
        } catch (Exception e) {
            LOGGER.warn("Error searching for event by appointment ID {}: {}", appointmentId, e.getMessage());
            return null;
        }
    }

    /**
     * Build Google Calendar Event object from Appointment
     */
    private Event buildEventFromAppointment(Appointment appointment) {
        // Validate critical appointment data first
        if (appointment == null) {
            LOGGER.warn("Appointment is null, skipping calendar sync");
            return null;
        }
        
        if (appointment.getServiceType() == null) {
            LOGGER.warn("Appointment {} has null serviceType, skipping calendar sync", appointment.getId());
            return null;
        }
        
        Event event = new Event();

        // Set title
        String title = String.format("%s - %s",
                appointment.getServiceType().getName(),
                appointment.getPet() != null ? appointment.getPet().getName() : "Pet");
        event.setSummary(title);

        // Set description
        StringBuilder description = new StringBuilder();
        description.append("Service: ").append(appointment.getServiceType().getName()).append("\n");
        if (appointment.getPet() != null) {
            description.append("Pet: ").append(appointment.getPet().getName()).append("\n");
        }
        if (appointment.getCustomerName() != null) {
            description.append("Customer: ").append(appointment.getCustomerName()).append("\n");
        }
        if (appointment.getVenue() != null) {
            String venueName = appointment.getVenue().getPublicName() != null ?
                    appointment.getVenue().getPublicName() :
                    appointment.getVenue().getInternalName();
            description.append("Venue: ").append(venueName).append("\n");
        }
        description.append("Appointment No: ").append(appointment.getAppoinmentNo()).append("\n");
        if (appointment.getServiceStatus() != null) {
            description.append("Status: ").append(appointment.getServiceStatus().toString());
        }
        event.setDescription(description.toString());

        // Set location
        if (appointment.getVenue() != null && appointment.getVenue().getLocationAddress() != null) {
            event.setLocation(appointment.getVenue().getLocationAddress().toString());
        }

        // Set start time - use the calculated string time from appointment
        String startTimeStr = appointment.getStartTime();
        if (startTimeStr == null || startTimeStr.trim().isEmpty()) {
            LOGGER.warn("Appointment {} has null or empty start time, skipping calendar sync", appointment.getId());
            return null; // Return null to indicate this appointment should be skipped
        }

        OffsetDateTime startOffsetDateTime;
        try {
            startOffsetDateTime = OffsetDateTime.parse(startTimeStr);
        } catch (Exception e) {
            LOGGER.warn("Appointment {} has invalid start time format '{}', skipping calendar sync: {}",
                    appointment.getId(), startTimeStr, e.getMessage());
            return null;
        }

        EventDateTime start = new EventDateTime()
                .setDateTime(new DateTime(Date.from(startOffsetDateTime.toInstant())))
                .setTimeZone("UTC");
        event.setStart(start);

        // Set end time - use the calculated string time from appointment
        String endTimeStr = appointment.getEndTime();
        if (endTimeStr == null || endTimeStr.trim().isEmpty()) {
            LOGGER.warn("Appointment {} has null or empty end time, skipping calendar sync", appointment.getId());
            return null;
        }

        OffsetDateTime endOffsetDateTime;
        try {
            endOffsetDateTime = OffsetDateTime.parse(endTimeStr);
        } catch (Exception e) {
            LOGGER.warn("Appointment {} has invalid end time format '{}', skipping calendar sync: {}",
                    appointment.getId(), endTimeStr, e.getMessage());
            return null;
        }

        // Validate time range BEFORE creating EventDateTime objects
        if (endOffsetDateTime.isBefore(startOffsetDateTime)) {
            LOGGER.warn("Appointment {} has end time before start time, skipping calendar sync", appointment.getId());
            return null;
        }

        // If start and end times are the same, add 1 hour to end time to create a valid event
        if (endOffsetDateTime.isEqual(startOffsetDateTime)) {
            LOGGER.info("Appointment {} has same start and end time, extending end time by 1 hour", appointment.getId());
            endOffsetDateTime = endOffsetDateTime.plusHours(1);
        }

        EventDateTime end = new EventDateTime()
                .setDateTime(new DateTime(Date.from(endOffsetDateTime.toInstant())))
                .setTimeZone("UTC");
        event.setEnd(end);

        // Set color based on attendant's calendar color if available
        if (appointment.getAttendant() != null && appointment.getAttendant().getColor() != null) {
            // Google Calendar supports color IDs 1-11
            event.setColorId("9"); // Default blue color
        }

        // Add extended properties for duplicate detection and tracking
        Event.ExtendedProperties extendedProps = new Event.ExtendedProperties();
        Map<String, String> privateProps = new HashMap<>();
        privateProps.put("appointmentId", appointment.getId().toString());
        extendedProps.setPrivate(privateProps);
        event.setExtendedProperties(extendedProps);

        return event;
    }

    /**
     * Reset the calendarSyncInProgress flag for an attendant
     * This method runs in a SEPARATE transaction to ensure the flag is always reset,
     * even if the main sync transaction fails and rolls back
     * 
     * @param attendantId - ID of the attendant
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void resetSyncFlag(Integer attendantId) {
        try {
            Attendant attendant = attendantRepository.findByAttendantId(attendantId);
            if (attendant != null) {
                attendant.setCalendarSyncInProgress(false);
                attendantRepository.save(attendant);
                LOGGER.info("Successfully reset calendarSyncInProgress flag for attendant {}", attendantId);
            } else {
                LOGGER.warn("Cannot reset sync flag - attendant {} not found", attendantId);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to reset sync flag for attendant {}: {}", attendantId, e.getMessage(), e);
            // Don't throw - we don't want flag reset failure to affect the sync result
        }
    }
}

