package com.sayone.etailbookit.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.event.SlotCreationEvent;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.kinesis.KinesisClient;
import software.amazon.awssdk.services.kinesis.model.PutRecordRequest;
import software.amazon.awssdk.services.kinesis.model.PutRecordResponse;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class SlotCreationPublisher {

    private final KinesisClient kinesisClient;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Value("${kinesis.stream-name}")
    private String streamName;

    @Value("${kinesis.shard.id.slots}")
    private String slotGenerationShardId; // The shard ID for slot generation
    private final AtomicInteger recordCount = new AtomicInteger(0); // Counter for sent records
    public SlotCreationPublisher(ObjectMapper objectMapper, ApplicationEventPublisher eventPublisher) {
        this.kinesisClient = KinesisClient.create();
        this.objectMapper = objectMapper;
        this.eventPublisher = eventPublisher;
    }

    public void publishSlotCreationEvent(ServiceSlotsDto serviceSlotsDto, Integer attendantId, Integer timeSlotClusterId,String retailer) throws EtailBookItException {
        try {
            // Serialize the payload
            String message = objectMapper.writeValueAsString(Map.of(
                    "serviceSlotsDto", serviceSlotsDto,
                    "attendantId", attendantId,
                    "timeSlotClusterId", timeSlotClusterId,
                    "retailer",retailer,
                    "eventType", "SLOT_CREATE"
            ));

            String formattedShardId = "shardId-" + String.format("%012d", Integer.parseInt(slotGenerationShardId));
            // Build the PutRecordRequest
            PutRecordRequest request = PutRecordRequest.builder()
                    .streamName(streamName)
                    .partitionKey(formattedShardId) // Ensures routing to the configured shard
                    .data(SdkBytes.fromUtf8String(message))
                    .build();

            // Publish the record
            PutRecordResponse response = kinesisClient.putRecord(request);

            if (response.sdkHttpResponse().isSuccessful()) {
                int count = recordCount.incrementAndGet(); // Increment counter
                System.out.println("Record successfully sent. Sequence Number: " + response.sequenceNumber());
                System.out.println("Total Records Sent: " + count);
            } else {
                throw new EtailBookItException("Failed to publish to Kinesis. HTTP Status: "
                        + response.sdkHttpResponse().statusCode());
            }
            // trigger the event
            eventPublisher.publishEvent(new SlotCreationEvent(this, serviceSlotsDto, attendantId, timeSlotClusterId, retailer));
        } catch (Exception e) {
            // Wrap exceptions and throw a custom exception
            throw new EtailBookItException("Failed to publish slot creation event: " + e.getMessage());
        }
    }
}
