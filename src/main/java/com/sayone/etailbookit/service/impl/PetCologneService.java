package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.dto.PetCologneDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.PetCologneMapper;
import com.sayone.etailbookit.mapper.PetShampooMapper;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Configuration;
import com.sayone.etailbookit.model.PetCologne;
import com.sayone.etailbookit.model.PetShampoo;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.ConfigurationRepository;
import com.sayone.etailbookit.repository.PetCologneRepository;
import com.sayone.etailbookit.service.IPetCologneService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class PetCologneService implements IPetCologneService {

    @Autowired
    PetCologneRepository petCologneRepository;

    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    private static final String PET_COLOGNE = "pet_cologne";

    @Override
    public PetCologneDto addPetCologne(PetCologneDto petCologneDto) throws EtailBookItException {
        Validator.validatePetCologne(petCologneDto, null, petCologneRepository);
        PetCologne petCologne = PetCologneMapper.toPetCologneEntity(petCologneDto);
        //commenting out this code as per tge requirement of the ticket BKI-1213
       /* if (petCologne.getIndexValue() == null) {
            Integer lastIndexId = petCologneRepository.getByLastIndexValueByRetailerAndDeleted(petCologne.getRetailer(),false);
            if(lastIndexId != null){
                petCologne.setIndexValue(lastIndexId+1);
            }else{
                petCologne.setIndexValue(1);
            }
        }*/
        petCologne = petCologneRepository.save(petCologne);
        return PetCologneMapper.toPetCologneDto(petCologne);
    }

    @Override
    public PetCologneDto updatePetCologne(PetCologneDto petCologneDto, int petCologneId) throws EtailBookItException {
        Validator.validatePetCologne(petCologneDto, petCologneId, petCologneRepository);
        PetCologne petCologne = PetCologneMapper.toPetCologneEntity(petCologneDto);
        petCologne.setId(petCologneId);
        petCologne = petCologneRepository.save(petCologne);
        return PetCologneMapper.toPetCologneDto(petCologne);
    }

    @Override
    public void deletePetCologne(int petCologneId) throws EtailBookItException {
        Optional<PetCologne> petCologne = petCologneRepository.findById(petCologneId);
        if (!petCologne.isPresent()) {
            throw new EntityNotFoundException("Pet Cologne not found");
        }
        else {
            List<Appointment> existingAppointment = appointmentRepository.findByCologne(petCologne.get());
            if(!existingAppointment.isEmpty()) {
                throw new BadRequestException("Cologne is used in an appointment");
            }
            else {
                petCologneRepository.deleteById(petCologneId);
            }
        }
    }

    @Override
    public PetCologneDto getPetCologneById(int petCologneId) throws EtailBookItException {
        Validator.validatePetCologne(null, petCologneId, petCologneRepository);
        PetCologne petCologne = petCologneRepository.getOne(petCologneId);
        return PetCologneMapper.toPetCologneDto(petCologne);
    }

    @Override
    public ConfigurationDto<PetCologneDto> getPetColognes(Integer pageNo, Integer pageSize, String sortBy, String search) {
        Pageable paging = PageRequest.of(pageNo, pageSize,Sort.by((Sort.Order.desc("active")),Sort.Order.desc(sortBy)));
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.asc(sortBy)));
        }
        Configuration configuration = configurationRepository.findByNameAndRetailer(PET_COLOGNE, RetailerContext.getRetailer());
        ConfigurationDto<PetCologneDto> configurationDto = new ConfigurationDto<>();
        if(configuration!=null) {
            configurationDto.setActive(configuration.isActive());
        }
        Page<PetCologne> petColognes = petCologneRepository.getPetColognes(RetailerContext.getRetailer(), false, search.toLowerCase(), paging);
        if(Utils.isNotEmpty(petColognes.getContent()))
            configurationDto.setOptionsPage(new PageImpl<>(PetCologneMapper.toPetCologneDtoList(petColognes.getContent()), paging, petColognes.getTotalElements()));
        return configurationDto;
    }

    @Override
    @Transactional
    public void bulkUpdateCologne(ConfigurationDto<PetCologneDto> petCologneDtos) throws EtailBookItException {
        List<String> cologneNames = new ArrayList<>();
        if(petCologneDtos.getOptions() != null) {
            for(PetCologneDto petCologneDto : petCologneDtos.getOptions()) {
                Validator.validatePetCologne(petCologneDto, petCologneDto.getId(), petCologneRepository);
                if(cologneNames.contains(petCologneDto.getName().toLowerCase())) {
                    throw new BadRequestException("Cologne with name " + petCologneDto.getName() + " already exists");
                }
                cologneNames.add(petCologneDto.getName().toLowerCase());
            }
        }
        else if(petCologneDtos.isActive()) {
            throw new BadRequestException("No cologne options created");
        }
        Configuration configuration = configurationRepository.findByNameAndRetailer(PET_COLOGNE, RetailerContext.getRetailer());
        if (configuration == null) {
            configuration=new Configuration();
            configuration.setName(PET_COLOGNE);
        }
        configuration.setRetailer(RetailerContext.getRetailer());
        configuration.setActive(petCologneDtos.isActive());
        configurationRepository.save(configuration);
        if (petCologneDtos.getOptions() != null) {
            for (PetCologneDto petCologneDto : petCologneDtos.getOptions()) {
                Validator.validatePetCologne(petCologneDto, petCologneDto.getId(), petCologneRepository);
            }
            List<PetCologne> petColognes = PetCologneMapper.toPetCologneList(petCologneDtos.getOptions());
            petCologneRepository.saveAll(petColognes);
        }
    }

    @Override
    public Page<PetCologneDto> getActivePetCologne(Integer pageNo, Integer pageSize, String sortBy, String search) throws EtailBookItException {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<PetCologne> petColognes = null;
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        if(!search.isEmpty() && search.hashCode()!=0){
            petColognes=petCologneRepository.findByKeywordActiveRetailerAndDeleted(search.toLowerCase(),true, RetailerContext.getRetailer(),false,paging);
        }else {
            petColognes = petCologneRepository.findByActiveAndRetailerAndDeleted(true, RetailerContext.getRetailer(), false, paging);
        }
        return new PageImpl<>(PetCologneMapper.toPetCologneDtoList(petColognes.getContent()), paging, petColognes.getTotalElements());

    }

    @Override
    public Page<PetCologneDto> getcologneByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search) throws EtailBookItException{

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<PetCologne> cologneList ;
        if(search.isPresent() && search.hashCode() != 0){
            cologneList= petCologneRepository.findByKeyword(search.get().toLowerCase(), RetailerContext.getRetailer(),false,paging);
        } else{
            cologneList= petCologneRepository.getAll(RetailerContext.getRetailer(), false,paging);
        }
        return new PageImpl<>(PetCologneMapper.toPetCologneDtoList(cologneList.getContent()), paging, cologneList.getTotalElements());
    }
}
