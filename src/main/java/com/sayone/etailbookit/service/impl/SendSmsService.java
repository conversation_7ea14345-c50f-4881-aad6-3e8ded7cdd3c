package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.AccessTokenDto;
import com.sayone.etailbookit.dto.SMSdto;
import com.sayone.etailbookit.dto.TwilioServiceDTO;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.model.Customer;
import com.sayone.etailbookit.model.TwilioService;
import com.sayone.etailbookit.projections.TwilioServiceProjection;
import com.sayone.etailbookit.repository.CustomerRepository;
import com.sayone.etailbookit.repository.TwilioServiceRepository;
import com.sayone.etailbookit.service.ISendSmsService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.SmsProperties;
import com.twilio.Twilio;
import com.twilio.jwt.accesstoken.ChatGrant;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import net.bytebuddy.implementation.bytecode.Throw;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.twilio.jwt.accesstoken.AccessToken;
import com.twilio.rest.conversations.v1.Conversation;
import com.twilio.rest.conversations.v1.conversation.Participant;

import java.io.IOException;
import java.util.List;

@Service
public class SendSmsService implements ISendSmsService {

    @Autowired
    SmsProperties smsProperties;
    @Autowired
    CustomerRepository customerRepository;
    @Autowired
    TwilioServiceRepository twilioServiceRepository;

    @Override
    public String sendSMS(SMSdto smSdto) throws IOException {


        Twilio.init(smsProperties.getTwilioAccountSid(),smsProperties.getTwilioAuthToken());
        Message.creator(new PhoneNumber(smSdto.getTo()),new PhoneNumber(smsProperties.getTwilioNumber()),"Appointment Created").create();
        return null;
    }

    @Override
    public AccessTokenDto generateAccessToken(String serviceSID) {
        // Required for all types of tokens
        String twilioAccountSid = smsProperties.getTwilioAccountSid();
        String twilioApiKey = smsProperties.getTwilioApiKey();
        String twilioApiSecret = smsProperties.getTwilioSecretKey();

        String identity = RetailerContext.getRetailer();

        ChatGrant grant = new ChatGrant();
        grant.setServiceSid(serviceSID);

        AccessTokenDto accessTokenDto=new AccessTokenDto();
        AccessToken token = new AccessToken.Builder(twilioAccountSid, twilioApiKey, twilioApiSecret)
                .identity(identity).grant(grant).build();
        accessTokenDto.setAccessToken(token);
        accessTokenDto.setJwt(token.toJwt());
        System.out.println(token.toJwt());
        return accessTokenDto;
    }

    @Override
    public TwilioService createConversation(TwilioServiceDTO twilioServiceDTO) throws EntityNotFoundException {

        Customer customer=customerRepository.findByEcomIdandRetailer(twilioServiceDTO.getCustomerID(), twilioServiceDTO.getRetailer());
        if(customer==null){
            throw  new EntityNotFoundException("Customer not found with id::"+twilioServiceDTO.getCustomerID());
        }
        Twilio.init(smsProperties.getTwilioAccountSid(),smsProperties.getTwilioAuthToken());

        Conversation conversation = Conversation.creator()
                .setFriendlyName(customer.getFirstName()).create();

        String serviceSID=conversation.getChatServiceSid();
         String conversationSID=conversation.getSid();
        Participant participant =
                Participant.creator(conversationSID)
                        .setMessagingBindingAddress(customer.getPhoneNumber()).setIdentity(twilioServiceDTO.getRetailer())
                        .setMessagingBindingProxyAddress(
                                smsProperties.getTwilioNumber())
                        .create();
        System.out.println("Participant SID::"+participant.getSid());
        TwilioService twilioService=new TwilioService();
        twilioService.setServiceSID(serviceSID);
        twilioService.setCustomerID(twilioServiceDTO.getCustomerID());
        twilioService.setRetailer(twilioServiceDTO.getRetailer());
        twilioService=twilioServiceRepository.save(twilioService);
        return twilioService;
    }

    @Override
    public List<TwilioServiceProjection> getConversation() {
        Pageable paging = PageRequest.of(0, 3, Sort.by("createdAt").descending());
        List<TwilioServiceProjection> twilioServiceProjections=twilioServiceRepository.getLatestConversation(RetailerContext.getRetailer(),paging);
        return twilioServiceProjections;
    }

    @Override
    public void deleteConversation(String conversationSID) {
        Twilio.init(smsProperties.getTwilioAccountSid(),smsProperties.getTwilioAuthToken());
        Conversation.deleter(conversationSID).delete();
    }

    @Override
    public void deleteCustomerChatFromDatabase() {
        twilioServiceRepository.deleteAll();
    }


}
