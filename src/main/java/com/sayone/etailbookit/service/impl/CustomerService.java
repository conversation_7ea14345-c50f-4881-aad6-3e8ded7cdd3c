package com.sayone.etailbookit.service.impl;


import com.sayone.etailbookit.controller.CustomerController;
import com.sayone.etailbookit.dto.CustomerDto;
import com.sayone.etailbookit.dto.MergeCustomerDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Customer;
import com.sayone.etailbookit.projections.CustomerProjection;

import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.projections.PetNameAndCountProjection;
import com.sayone.etailbookit.repository.AppointmentRepository;

import com.sayone.etailbookit.repository.CustomerRepository;
import com.sayone.etailbookit.repository.PetRepository;
import com.sayone.etailbookit.service.ICustomerService;
import com.sayone.etailbookit.util.RetailerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CustomerService implements ICustomerService {
    private static Logger LOGGER = LoggerFactory.getLogger(CustomerService.class);

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Override
    public void postCustomerDetails(List<CustomerDto> customers) throws EtailBookItException {
        createOrUpdateCustomer(customers);
    }

    @Override
    public void updateCustomerDetails(List<CustomerDto> customers) throws EtailBookItException {
        createOrUpdateCustomer(customers);
    }

    private void createOrUpdateCustomer(List<CustomerDto> customers){
        for(CustomerDto customerDto:customers){
            Customer customer = customerRepository.findByEcomIdandRetailer(customerDto.getId(), RetailerContext.getRetailer());
            if(customer==null){
                LOGGER.info("Customer dto in the create section"+customerDto.toString());
                Customer customerModel = new Customer();
                customerModel.setEcom_id(customerDto.getId());
                customerModel.setFirstName(customerDto.getFirstName());
                customerModel.setLastName(customerDto.getLastName());
                customerModel.setPhoneNumber(customerDto.getPhoneNumber());
                customerModel.set_active(customerDto.getIsActive());
                customerModel.setRetailer(RetailerContext.getRetailer());
                customerRepository.save(customerModel);
                LOGGER.info("Customer created with details"+customerModel.toString());
            }else{
                LOGGER.info("Customer dto in the update section"+customerDto.toString());
                customer.setFirstName(customerDto.getFirstName());
                customer.setLastName(customerDto.getLastName());
                customer.setPhoneNumber(customerDto.getPhoneNumber());
                customer.set_active(customerDto.getIsActive());
                customer.setRetailer(RetailerContext.getRetailer());
                customerRepository.save(customer);
                LOGGER.info("Customer updated with details"+customer.toString());
            }
        }
    }

    @Override
    public CustomerProjection getCustomerDetails(Integer customerId) {
        CustomerProjection customer=customerRepository.getByEcomIdandRetailer(customerId,RetailerContext.getRetailer());
        return customer;
    }

    @Override
    public MergeCustomerDto getPetandAppointmentDetails(Integer sourceCustomerId) throws EtailBookItException{
        MergeCustomerDto mergeCustomerDto = new MergeCustomerDto();
        List<PetNameAndCountProjection> petNameAndCountList = petRepository.getPetNameAndCount(sourceCustomerId, RetailerContext.getRetailer());
        if (!petNameAndCountList.isEmpty()) {
            mergeCustomerDto.setPetNameAndCountList(petNameAndCountList);
        }
        List<String> appointmentNumbers = appointmentRepository.getAppointmentName(sourceCustomerId, RetailerContext.getRetailer());
        if (!appointmentNumbers.isEmpty()) {
            mergeCustomerDto.setAppointmentNumber(appointmentNumbers);
            mergeCustomerDto.setTotalNumberOfAppointments(appointmentNumbers.size());
        }

        return mergeCustomerDto;
    }

    @Override
    public void mergeCustomers(Integer destinationCustomerId, Integer sourceCustomerId) throws EtailBookItException{
        //merge pet
        List<Pet> petList = petRepository.findByCustomerIdAndRetailer(sourceCustomerId, RetailerContext.getRetailer());
        if (!petList.isEmpty()) {
            for (Pet pet : petList) {
                pet.setCustomerId(destinationCustomerId);
                petRepository.save(pet);
            }
        }
        //merge appointment
        List<Appointment> appointmentList = appointmentRepository.findByCustomerIdAndRetailer(sourceCustomerId,RetailerContext.getRetailer());
        if (!appointmentList.isEmpty()){
            for(Appointment appointment:appointmentList){
                appointment.setCustomerId(destinationCustomerId);
                appointmentRepository.save(appointment);
            }
        }

    }

    @Override
    public List<Customer> getAllCustomers() {
        List<Customer> customers=customerRepository.findAll();
        return customers;
    }

}

