package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.component.CustomerEmailNotification;
import com.sayone.etailbookit.dto.ServiceTypeDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.AddonServiceMapper;
import com.sayone.etailbookit.mapper.ServiceTypeMapper;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.ServiceType;
import com.sayone.etailbookit.projections.ServiceTypeProjection;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.ServiceTypeRepository;
import com.sayone.etailbookit.service.IServiceTypeService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.IOException;
import java.util.List;

@Service
public class ServiceTypeService implements IServiceTypeService {

    @Autowired
    ServiceTypeRepository serviceTypeRepository;

    @Autowired
    private DataFileProperties dataFileDetails;

    @Autowired
    private AmazonS3 s3client;

    @Autowired
    CustomerEmailNotification customerEmailNotification;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Override
    public ServiceTypeDto addServiceType(ServiceTypeDto serviceTypeDto) throws Exception{

        Validator.validateServiceType(serviceTypeDto, null, serviceTypeRepository);
        //commented out this code to temporarily to get rid of the file not found exception. Need to fix this and uncomment the code.
        /*if(serviceTypeDto.getFile() != null)
            serviceTypeDto.setFileUrl( FileUploadUtil.uploadFile(serviceTypeDto.getFile(),dataFileDetails.getBucketName(),s3client,dataFileDetails.getAwsEndpoint(),dataFileDetails.getServiceTypePrefix(), FileType.Photos));
        else
            throw new EntityNotFoundException("Service_Type Image not found ");*/
        ServiceType serviceType = ServiceTypeMapper.toServiceTypeEntity(serviceTypeDto);
        serviceType = serviceTypeRepository.save(serviceType);
        return ServiceTypeMapper.toServiceTypeDto(serviceType);

    }

    @Override
    public ServiceTypeDto updateServiceType(ServiceTypeDto serviceTypeDto, int serviceTypeId) throws Exception{

        Validator.validateServiceType(serviceTypeDto, serviceTypeId, serviceTypeRepository);
        ServiceType existingValue = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
        if (existingValue == null) {
            throw new EntityNotFoundException("Service Type not found with id " + serviceTypeId);
        }
        if(existingValue.getName().equalsIgnoreCase("Grooming DEV")){
           throw new EtailBookItException("This is test data and can not be updated");
        }
        if (serviceTypeDto.getFile() != null) {
            serviceTypeDto.setFileUrl( FileUploadUtil.uploadFile(serviceTypeDto.getFile(),dataFileDetails.getBucketName(),s3client,dataFileDetails.getAwsEndpoint(),dataFileDetails.getServiceTypePrefix(),FileType.Photos));
            FileUploadUtil.deleteFile(existingValue.getFileUrl(),s3client,dataFileDetails.getBucketName(),dataFileDetails.getServiceTypePrefix());
        } else
            serviceTypeDto.setFileUrl(existingValue.getFileUrl());

        ServiceType serviceType = ServiceTypeMapper.toServiceTypeEntity(serviceTypeDto);
        serviceType.setServiceTypeId(serviceTypeId);
        serviceType = serviceTypeRepository.save(serviceType);
        return ServiceTypeMapper.toServiceTypeDto(serviceType);
    }

    @Transactional
    @Override
    public void deleteServiceType(int serviceTypeId) throws EtailBookItException, IOException {
        Validator.validateServiceType(null, serviceTypeId, serviceTypeRepository);
        ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
        if(serviceType.getName().equalsIgnoreCase("Grooming DEV")){
            throw new EtailBookItException("This is test data and can not be deleted");
        }
        if(serviceType.getFileUrl()!=null) {
            if (!serviceType.getFileUrl().isEmpty())
                FileUploadUtil.deleteFile(serviceType.getFileUrl(), s3client, dataFileDetails.getBucketName(), dataFileDetails.getServiceTypePrefix());

        }
      /*  if (Utils.isNotEmpty(serviceType.getAttendants())) {
            throw new BadRequestException("Service Type is currently used by Attendants");
        } else if (Utils.isNotEmpty(serviceType.getVenues())) {
            throw new BadRequestException("Service Type is currently used by Venues");
        } else if (Utils.isNotEmpty(serviceType.getServices())) {
            throw new BadRequestException("Service Type is currently used by Services");
        } else {
            serviceTypeRepository.delete(serviceType);
        }*/
        // Unlink attendants
        if (serviceType.getAttendants() != null) {
            serviceType.getAttendants().forEach(att -> att.getServiceTypes().remove(serviceType));
            serviceType.getAttendants().clear();
        }

        // Unlink venues
        if (serviceType.getVenues() != null) {
            serviceType.getVenues().forEach(venue -> venue.getServiceTypes().remove(serviceType));
            serviceType.getVenues().clear();
        }

        // Unlink services (set foreign key to null)
        if (serviceType.getServices() != null) {
            serviceType.getServices().forEach(service -> service.setServiceType(null));
            serviceType.getServices().clear();
        }
        // Unlink from appointments
        List<Appointment> appointments = appointmentRepository.findByServiceType(serviceType);
        for (Appointment appt : appointments) {
            appt.setServiceType(null);
        }

        // Now safely delete the ServiceType
        serviceTypeRepository.delete(serviceType);
    }

    @Override
    public ServiceTypeDto getServiceTypeById(int serviceTypeId) throws EtailBookItException {
        Validator.validateServiceType(null, serviceTypeId, serviceTypeRepository);
        ServiceType serviceType = serviceTypeRepository.getOne(serviceTypeId);
        return ServiceTypeMapper.toServiceTypeDto(serviceType);
    }

    @Override
    public Page<ServiceTypeProjection> getServiceTypes(Integer pageNo, Integer pageSize, String sortBy, String search) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.desc(sortBy)));
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.asc(sortBy)));
        }
        Page<ServiceTypeProjection> serviceTypes = serviceTypeRepository.getAll(RetailerContext.getRetailer(), false, search.toLowerCase(),paging);
        return serviceTypes;
    }

    @Override
    public List<ServiceTypeProjection> getServiceTypesWithServices() {
        return serviceTypeRepository.findAllByRetailer(RetailerContext.getRetailer());
    }

    @Override
    public void bulkUpdateServiceType(List<ServiceTypeDto> serviceTypeDtos) throws EtailBookItException {
        for (ServiceTypeDto serviceTypeDto : serviceTypeDtos) {
            Validator.validateServiceType(serviceTypeDto, serviceTypeDto.getId(), serviceTypeRepository);
        }
        List<ServiceType> serviceTypes = ServiceTypeMapper.toServiceTypeList(serviceTypeDtos);
        serviceTypeRepository.saveAll(serviceTypes);
    }

    @Override
    public Page<ServiceTypeDto> getActiveServiceType(Integer pageNo, Integer pageSize, String sortBy, String search) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<ServiceType> serviceTypeList;
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        if(!search.isEmpty() && search.hashCode() != 0){
            serviceTypeList =serviceTypeRepository.findByKeywordActiveRetailerAndDeleted(search.toLowerCase(),true,RetailerContext.getRetailer(),false,paging);
        }
        else {
            serviceTypeList = serviceTypeRepository.findByActiveAndRetailerAndDeleted(true, RetailerContext.getRetailer(), false,paging);
        }
        return new PageImpl<>(ServiceTypeMapper.toServiceTypeDtoList(serviceTypeList.getContent()), paging, serviceTypeList.getTotalElements());
    }

    @Override
    public List<ServiceTypeDto> getServiceTypeFromService(Integer serviceId, Integer venueId, Integer attendantId) throws EtailBookItException {

        List<ServiceType> serviceTypes=serviceTypeRepository.findByServiceVenueAttendant(serviceId,venueId,attendantId);
        return ServiceTypeMapper.toServiceTypeDtoList(serviceTypes);

    }

}
