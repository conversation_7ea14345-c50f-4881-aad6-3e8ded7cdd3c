package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.DocumentOptionDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.model.DocumentOption;
import com.sayone.etailbookit.projections.DocumentsProjection;
import com.sayone.etailbookit.projections.PetDocumentsProjection;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IDocumentService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class DocumentService implements IDocumentService {

    @Autowired
    DocumentOptionRepository documentOptionRepository;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    PetDocumentRepository petDocumentRepository;

    @Autowired
    AppointmentDocumentRepository appointmentDocumentRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    WaiverOfLiabilityRepository waiverOfLiabilityRepository;


    @Override
    public Page<DocumentsProjection> getAllDocument(Integer pageNo, Integer pageSize, String sortBy) {

        String retailer = RetailerContext.getRetailer();
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.desc(sortBy)));
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize,Sort.by((Sort.Order.desc("active")),Sort.Order.asc(sortBy)));
        }
        return documentOptionRepository.findAllDocuments(retailer, paging);
    }


    @Override
    public BaseResponseDto createDocument(DocumentOptionDto documentOptionDto) throws Exception {
        Validator.validateDocument(true, documentOptionDto);
        DocumentOption documentOption = new DocumentOption();
        documentOption.setName(documentOptionDto.getName());
        documentOption.setActive(documentOptionDto.isActive());
        documentOption.setRequireDescription(documentOptionDto.isRequireDescription());
        documentOption.setRequireUpload(documentOptionDto.isRequireUpload());
        documentOption.setRequireUploadEsign(documentOptionDto.isRequireUploadEsign());
        documentOption.setRetailer(RetailerContext.getRetailer());
      //  documentOption.setIndexValue(documentOptionDto.getIndexValue());
        if (documentOptionDto.getFile() != null) {
            String awsUrl = FileUploadUtil.uploadFile(documentOptionDto.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.DocumentsORPhotos);
            documentOption.setFileURL(awsUrl);
        }
        documentOptionRepository.save(documentOption);

        return new BaseResponseDto(Status.SUCCESS);
    }



    @Override
    public BaseResponseDto updateDocument(DocumentOptionDto documentOptionDto, Integer id) throws Exception {

        Validator.validateDocument(true, documentOptionDto);
        DocumentOption documentOption = documentOptionRepository.findByDocumentOptionId(id);
        documentOption.setName(documentOptionDto.getName());
        documentOption.setActive(documentOptionDto.isActive());
        documentOption.setRequireDescription(documentOptionDto.isRequireDescription());
        documentOption.setRequireUpload(documentOptionDto.isRequireUpload());
        documentOption.setRequireUploadEsign(documentOptionDto.isRequireUploadEsign());
      //  documentOption.setIndexValue(documentOptionDto.getIndexValue());
        if (!ObjectUtils.isEmpty(documentOptionDto.getFile())) {
            if (!ObjectUtils.isEmpty(documentOption.getFileURL()) && !documentOption.getFileURL().isEmpty()) {
                List<String> file = new ArrayList<>();
                file.add(documentOption.getFileURL());
                FileUploadUtil.deleteMultipleFiles(file, amazonS3, dataFileProperties.getBucketName(), dataFileProperties.getPetDocumentPrefix());
            }
            String awsUrl = FileUploadUtil.uploadFile(documentOptionDto.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.DocumentsORPhotos);
            documentOption.setFileURL(awsUrl);
        }
        documentOptionRepository.save(documentOption);
        return new BaseResponseDto(Status.SUCCESS);
    }

    @Override
    @Transactional
    public BaseResponseDto deleteDocument(Integer id) throws BadRequestException, EntityNotFoundException {
        Optional<DocumentOption> optionalDocumentOption = documentOptionRepository.findById(id);
        if (!optionalDocumentOption.isPresent()) {
            throw new EntityNotFoundException("Document option not found.");
        }
        if (appointmentDocumentRepository.countAllByDocumentOptionDocumentOptionId(id) > 0 || appointmentDocumentRepository.countAllByDocumentOptionId(id) > 0) {
            throw new BadRequestException("Please delete associated appointments for deleting this document option.");
        }
        if (petDocumentRepository.countAllByDocumentOptionDocumentOptionId(id) > 0) {
            throw new BadRequestException("Please delete associated pet documents for deleting this document option.");
        }
        if (petTypeRepository.countAllByDocumentOptionsContains(optionalDocumentOption.get()) > 0) {
            throw new BadRequestException("Please delete associated pet types for deleting this document option.");
        }
        if (serviceRepository.countAllByAvailableParticipantDocumentsContains(optionalDocumentOption.get()) > 0) {
            throw new BadRequestException("Please delete associated services for deleting this document option.");
        }
        documentOptionRepository.deleteById(id);
        return new BaseResponseDto(Status.SUCCESS);
    }

    @Override
    public List<PetDocumentsProjection> getPetDocumentsByCustomer(Integer customerId) {
        List<PetDocumentsProjection> petDocuments = petDocumentRepository.findByCustomerId(customerId);
        return petDocuments;
    }

    @Override
    public List<DocumentsProjection> getAllActiveDocument() {
        String retailer = RetailerContext.getRetailer();
        return documentOptionRepository.findAllActiveDocuments(retailer);
    }
}
