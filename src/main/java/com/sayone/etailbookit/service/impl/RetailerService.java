package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.RetailerListDto;
import com.sayone.etailbookit.mapper.RetailerMapper;
import com.sayone.etailbookit.model.Schema;
import com.sayone.etailbookit.repository.SchemaRepository;
import com.sayone.etailbookit.service.IRetailerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RetailerService implements IRetailerService {

    @Autowired
    SchemaRepository schemaRepository;

    @Override
    public void addRetailer(List<RetailerListDto> retailerListDtoList) {
       List<Schema> retailer= RetailerMapper.toSchemaEntityList(retailerListDtoList);
        schemaRepository.saveAll(retailer);
    }
}
