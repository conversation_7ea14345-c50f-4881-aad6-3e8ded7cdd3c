package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.wafv2.model.All;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IResetConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ResetConfiguration implements IResetConfiguration {

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    AppointmentVetInfoRepository appointmentVetInfoRepository;

    @Autowired
    AppointmentDesiredHairlengthRepository appointmentDesiredHairlengthRepository;

    @Autowired
    AppointmentDocumentRepository appointmentDocumentRepository;

    @Autowired
    AppointmentEmergencyContactInfoRpository appointmentEmergencyContactInfoRpository;

    @Autowired
    AppointmentVaccinationInformationRepository appointmentVaccinationInformationRepository;

    @Autowired
    AppointmentWaiverOfLiabilityInformationRepository  appointmentWaiverOfLiabilityInformationRepository;

    @Autowired
    ServiceHistoryAddNotesRepository serviceHistoryAddNotesRepository;

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    AttendantAvailabilityRepository attendantAvailabilityRepository;

    @Autowired
    AttendantPetTypesRepository attendantPetTypesRepository;

    @Autowired
    PetDocumentRepository petDocumentRepository;

    @Autowired
    PetEmergencyContactRepository petEmergencyContactRepository;

    @Autowired
    PetVetInformationRepository petVetInformationRepository;

    @Autowired
    PetWaiverOfLiabilityInfoRepository petWaiverOfLiabilityInfoRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    ServiceAvailabilityRepository serviceAvailabilityRepository;

    @Autowired
    ServiceHistoryRepository serviceHistoryRepository;

    @Autowired
    ServiceTypeRepository serviceTypeRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    ThreatReactionRepository threatReactionRepository;

    @Autowired
    UnfriendlyBehaviourTriggerRepository unfriendlyBehaviourTriggerRepository;

    @Autowired
    VaccinationRecordsRepository vaccinationRecordsRepository;

    @Autowired
    PetVaccinationRecordRepository petVaccinationRecordRepository;

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    VenueAvailabilityRepository venueAvailabilityRepository;

    @Autowired
    VenuePetTypesRepository venuePetTypesRepository;

    @Autowired
    VenueAddressRepository venueAddressRepository;

    @Autowired
    VetInformationRepository vetInformationRepository;

    @Autowired
    WaiverOfLiabilityRepository waiverOfLiabilityRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    AddonServiceRepository addonServiceRepository;

    @Autowired
    AllergiesRepository allergiesRepository;

    @Autowired
    DesiredHairLengthRepository desiredHairLengthRepository;

    @Autowired
    DocumentOptionRepository documentOptionRepository;

    @Autowired
    EmergencyContactInfoRepository emergencyContactInfoRepository;

    @Autowired
    PersonalityParameterRepository personalityParameterRepository;

    @Autowired
    BittingHistoryRepository bittingHistoryRepository;

    @Autowired
    BladesRepository bladesRepository;

    @Autowired
    BlockDatesRepository blockDatesRepository;

    @Autowired
    CombsRepository combsRepository;

    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    FeedingInformationRepository feedingInformationRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    HairLengthRepository hairLengthRepository;

    @Autowired
    HairTextureRepository hairTextureRepository;

    @Autowired
    PetCologneRepository petCologneRepository;

    @Autowired
    PetShampooRepository petShampooRepository;

    @Autowired
    PetTypeConfigurationRepository petTypeConfigurationRepository;

    @Autowired
    QuoteAdjustmentRepository quoteAdjustmentRepository;

    @Autowired
    TimeSlotRepository timeSlotRepository;

    @Autowired
    PetSizeConstraintRepository petSizeConstraintRepository;

    @Autowired
    PetSizeLimitRepository petSizeLimitRepository;

    @Autowired
    PetBreedsInformationRepository petBreedsInformationRepository;


    @Override
    public void resetRetailerConfiguration(String retailer) throws BadRequestException {


        List<ServiceHistory> serviceHistoryList=serviceHistoryRepository.findByRetailer(retailer);
        if(!serviceHistoryList.isEmpty()){
            /*for (ServiceHistory serviceHistory:serviceHistoryList) {
                serviceHistoryRepository.deleteById(serviceHistory.getId());
            }*/
            serviceHistoryRepository.deleteAll(serviceHistoryList);
        }

        List<PetSizeLimit> petSizeLimits=petSizeLimitRepository.findByRetailer(retailer);
        if(!petSizeLimits.isEmpty()){
            petSizeLimitRepository.deleteAll(petSizeLimits);
        }
        List<ServiceHistoryAddNotes> serviceHistoryAddNotesList=serviceHistoryAddNotesRepository.findByRetailer(retailer);
        if(!serviceHistoryAddNotesList.isEmpty()){
            /*for(ServiceHistoryAddNotes serviceHistoryAddNotes:serviceHistoryAddNotesList){
                serviceHistoryAddNotesRepository.deleteById(serviceHistoryAddNotes.getId());
            }*/
            serviceHistoryAddNotesRepository.deleteAll(serviceHistoryAddNotesList);
        }
        List<AppointmentDesiredHairLengths> appointmentDesiredHairLengths=appointmentDesiredHairlengthRepository.findByRetailer(retailer);
        if(!appointmentDesiredHairLengths.isEmpty()){
           /* for (AppointmentDesiredHairLengths appointmentDesiredHairLength:appointmentDesiredHairLengths){
                appointmentDesiredHairlengthRepository.deleteById(appointmentDesiredHairLength.getId());
            }*/
            appointmentDesiredHairlengthRepository.deleteAll(appointmentDesiredHairLengths);
        }
        List<AppointmentVaccinationInformation> appointmentVaccinationInformationList=appointmentVaccinationInformationRepository.findByRetailer(retailer);
        if(!appointmentVaccinationInformationList.isEmpty()){
            appointmentVaccinationInformationRepository.deleteAll(appointmentVaccinationInformationList);
        }
        List<AppointmentVetInformation> appointmentVetInformationList=appointmentVetInfoRepository.findByRetailer(retailer);
        if(!appointmentVetInformationList.isEmpty()){
           /* for(AppointmentVetInformation appointmentVetInformation:appointmentVetInformationList){
                appointmentVetInfoRepository.deleteById(appointmentVetInformation.getId());
            }*/
            appointmentVetInfoRepository.deleteAll(appointmentVetInformationList);
        }
        List<AppointmentWaiverOfLiabilityInformation> appointmentWaiverOfLiabilityInformationList=appointmentWaiverOfLiabilityInformationRepository.findByRetailer(retailer);
        if(!appointmentWaiverOfLiabilityInformationList.isEmpty()){
           /* for(AppointmentWaiverOfLiabilityInformation appointmentWaiverOfLiabilityInformation:appointmentWaiverOfLiabilityInformationList){
                appointmentWaiverOfLiabilityInformationRepository.deleteById(appointmentWaiverOfLiabilityInformation.getId());
            }*/
            appointmentWaiverOfLiabilityInformationRepository.deleteAll(appointmentWaiverOfLiabilityInformationList);
        }

        List<QuoteAdjustments> quoteAdjustments=quoteAdjustmentRepository.findByRetailer(retailer);
        if(!quoteAdjustments.isEmpty()){
            quoteAdjustmentRepository.deleteAll(quoteAdjustments);
        }
        List<PetSizeConstraint> petSizeConstraints=petSizeConstraintRepository.findByRetailer(retailer);
        if(!petSizeConstraints.isEmpty()){
            petSizeConstraintRepository.deleteAll(petSizeConstraints);
        }
        List<Appointment> appointments=appointmentRepository.findByRetailer(retailer);
        if(!appointments.isEmpty()){
            for (Appointment appointment:appointments){
                    appointmentVetInfoRepository.deleteByAppointmentId(appointment.getId(),retailer);
                    appointmentDesiredHairlengthRepository.deleteByAppointmentId(appointment.getId(),retailer);
                    appointmentDocumentRepository.deleteByAppointmentId(appointment.getId(),retailer);
                    appointmentEmergencyContactInfoRpository.deleteByAppointmentId(appointment.getId(),retailer);
                    appointmentVaccinationInformationRepository.deleteByAppointmentId(appointment.getId());
                    appointmentWaiverOfLiabilityInformationRepository.deleteByAppointmentId(appointment.getId(),retailer);
                    serviceHistoryAddNotesRepository.deleteByAppointmentId(appointment.getId(),retailer);
                    serviceHistoryRepository.deleteByAppointmentId(appointment.getId(),retailer);
               //     quoteAdjustmentRepository.deleteByAppointmentId(appointment.getId(),retailer);
                    appointmentRepository.deleteById(appointment.getId());

            }
        }

        List<TimeSlots> timeSlots=timeSlotRepository.findByRetailer(retailer);
        if(!timeSlots.isEmpty()){
            timeSlotRepository.deleteAll(timeSlots);
        }
        List<Pet> pets=petRepository.findByRetailer(retailer);
        if(!pets.isEmpty()){
            for (Pet pet:pets){
                petDocumentRepository.deleteByPetId(pet.getId(),retailer);
                petEmergencyContactRepository.deleteByPetId(pet.getId(),retailer);
                List<PetVetInformation> petVetInformation=petVetInformationRepository.findByPetIdAndRetailer(pet.getId(),retailer);
                if(!petVetInformation.isEmpty()){
                    petVetInformationRepository.deleteAll(petVetInformation);
                }

                petWaiverOfLiabilityInfoRepository.deleteByPetId(pet.getId(),retailer);
                petVaccinationRecordRepository.deleteByPetId(pet.getId(),retailer);
                appointmentRepository.deleteByPetId(pet.getId(),retailer);
                petBreedsInformationRepository.deleteByPetId(pet.getId(),retailer);
                petRepository.deleteById(pet.getId());
            }
        }
        List<WeightRange> weightRanges=weightRangeRepository.findByRetailer(retailer);
        if(!weightRanges.isEmpty()){
            for (WeightRange weightRange:weightRanges){
                petRepository.deleteByWeightRangeId(weightRange.getWeightRangeId(),retailer);
                appointmentRepository.deleteByWeightRangeId(weightRange.getWeightRangeId(),retailer);
                weightRangeRepository.deleteById(weightRange.getWeightRangeId());
            }
        }

        List<com.sayone.etailbookit.model.Service> services=serviceRepository.getAllServices(retailer);
        if(!services.isEmpty()) {
            for (com.sayone.etailbookit.model.Service service : services) {
                serviceAvailabilityRepository.deleteByServiceId(service.getServiceId(),retailer);
                appointmentRepository.deleteByServiceId(service.getServiceId(),retailer);
                serviceRepository.deleteById(service.getServiceId());
            }
        }
    List<Attendant> attendants=attendantRepository.findByRetailer(retailer);
    if(!attendants.isEmpty()){
        for (Attendant attendant:attendants){
            ///attendantAvailabilityRepository.findByattendantId(attendant.getAttendantId());
            attendantAvailabilityRepository.deleteByAttendantId(attendant.getAttendantId(),retailer);
            attendantPetTypesRepository.deleteByAttendantId(attendant.getAttendantId(),retailer);
            petSizeConstraintRepository.deleteByAttendantId(attendant.getAttendantId(),retailer);
            attendantRepository.updateAttendantDeleted(attendant.getAttendantId(),true);
        }
    }

    List<ServiceType> serviceTypeList=serviceTypeRepository.findByRetailer(retailer);
        if(!serviceTypeList.isEmpty()){
            for (ServiceType serviceType:serviceTypeList){
                serviceRepository.deleteByServiceType(serviceType.getServiceTypeId(),retailer);
                serviceTypeRepository.deleteById(serviceType.getServiceTypeId());
            }
        }
        List<Temperament> temperamentList=temperamentRepository.findByRetailer(retailer);
        if(!temperamentList.isEmpty()){
            for (Temperament temperament:temperamentList){
                petRepository.deleteByTemperamentId(temperament.getTemperamentId(),retailer);
                appointmentRepository.deleteByTemperamentId(temperament.getTemperamentId(),retailer);
                temperamentRepository.deleteById(temperament.getTemperamentId());
            }
        }
        List<ThreatReaction> threatReactionRepositoryList=threatReactionRepository.findByRetailer(retailer);
        if(!threatReactionRepositoryList.isEmpty()){
            /*for (ThreatReaction threatReaction:threatReactionRepositoryList){
                threatReactionRepository.deleteById(threatReaction.getId());
            }*/
            threatReactionRepository.deleteAll(threatReactionRepositoryList);
        }

        List<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggerList=unfriendlyBehaviourTriggerRepository.findByRetailer(retailer);
        if(!unfriendlyBehaviourTriggerList.isEmpty()){
           /* for (UnfriendlyBehaviourTrigger unfriendlyBehaviourTrigger:unfriendlyBehaviourTriggerList){
                unfriendlyBehaviourTriggerRepository.deleteById(unfriendlyBehaviourTrigger.getId());
            }*/
            unfriendlyBehaviourTriggerRepository.deleteAll(unfriendlyBehaviourTriggerList);
        }
        List<VaccinationRecords> vaccinationRecordsList=vaccinationRecordsRepository.findByRetailer(retailer);
        if(!vaccinationRecordsList.isEmpty()){
            for(VaccinationRecords vaccinationRecords:vaccinationRecordsList){
                appointmentVaccinationInformationRepository.deleteByVaccinationInfoId(vaccinationRecords.getVaccinationRecordId(),retailer);
                petVaccinationRecordRepository.deleteByVaccinationInfoId(vaccinationRecords.getVaccinationRecordId());
                vaccinationRecordsRepository.deleteById(vaccinationRecords.getVaccinationRecordId());
            }
        }
        List<AttendantPetTypes> attendantPetTypesList=attendantPetTypesRepository.findByRetailer(retailer);
        if(!attendantPetTypesList.isEmpty()){
           /* for(AttendantPetTypes attendantPetTypes:attendantPetTypesList){
                attendantPetTypesRepository.deleteById(attendantPetTypes.getAttendantPetTypeId());
            }*/
            attendantPetTypesRepository.deleteAll(attendantPetTypesList);
        }
        List<Venue> venueList=venueRepository.findByRetailer(retailer);
        if(!venueList.isEmpty()){
            for (Venue venue:venueList){
                venueAvailabilityRepository.deleteByVenueId(venue.getVenueId(),retailer);
                venuePetTypesRepository.deleteByVenueId(venue.getVenueId(),retailer);
                attendantRepository.deleteByVenueIdAndDeleted(venue.getVenueId(),retailer,true);
               // venueRepository.deleteById(venue.getVenueId());
                venueRepository.deleteVenue(venue.getVenueId(),retailer,true);
            }
        }
        List<VenueAddress> venueAddresses=venueAddressRepository.findByRetailer(retailer);
        if(!venueAddresses.isEmpty()){
            /*for(VenueAddress venueAddress:venueAddresses){
                venueAddressRepository.deleteById(venueAddress.getId());
            }*/
            venueAddressRepository.deleteAll(venueAddresses);
        }
        List<VenueAvailability> venueAvailabilities=venueAvailabilityRepository.findByRetailer(retailer);
        if(!venueAvailabilities.isEmpty()){
            /*for(VenueAvailability venueAvailability:venueAvailabilities){
                venueAvailabilityRepository.deleteById(venueAvailability.getId());
            }*/
            venueAvailabilityRepository.deleteAll(venueAvailabilities);
        }
        List<VenuePetTypes> venuePetTypes=venuePetTypesRepository.findByRetailer(retailer);
        if(!venuePetTypes.isEmpty()){
            /*for(VenuePetTypes venuePetType:venuePetTypes){
                venuePetTypesRepository.deleteById(venuePetType.getVenuePetTypeId());
            }*/
            venuePetTypesRepository.deleteAll(venuePetTypes);
        }
        List<VetInformation> vetInformationList=vetInformationRepository.findByRetailer(retailer);
        if(!vetInformationList.isEmpty()){
            for(VetInformation vetInformation:vetInformationList){
                appointmentVetInfoRepository.deleteByVetInformationId(vetInformation.getId(),retailer);
                petVetInformationRepository.deleteByVetInformationId(vetInformation.getId(),retailer);
                vetInformationRepository.deleteById(vetInformation.getId());
            }
        }
        WaiverOfLiability waiverOfLiability=waiverOfLiabilityRepository.findByRetailer(retailer);
        if(waiverOfLiability!=null){
            petWaiverOfLiabilityInfoRepository.deleteByWaiverId(waiverOfLiability.getWaiverOfLiabilityId(),retailer);
            appointmentWaiverOfLiabilityInformationRepository.deleteByWaiverId(waiverOfLiability.getWaiverOfLiabilityId(),retailer);
            waiverOfLiabilityRepository.deleteById(waiverOfLiability.getWaiverOfLiabilityId());
        }

        List<AddonService>addonServices=addonServiceRepository.findByRetailerwithoutPagination(retailer);
        if(!addonServices.isEmpty()){
            /*for(AddonService addonService:addonServices){
                addonServiceRepository.deleteById(addonService.getAddonServiceId());
            }*/
            addonServiceRepository.deleteAll(addonServices);
        }
        List<Allergies> allergies=allergiesRepository.findByRetailer(retailer);
        if(!allergies.isEmpty()){
            /*for(Allergies allergy:allergies){
                allergiesRepository.deleteById(allergy.getAllergyId());
            }*/
            allergiesRepository.deleteAll(allergies);
        }
        List<DesiredHairLength> desiredHairLengths=desiredHairLengthRepository.findByRetailer(retailer);
        if(!desiredHairLengths.isEmpty()){
            for(DesiredHairLength desiredHairLength:desiredHairLengths) {
                appointmentDesiredHairlengthRepository.deleteByDesiredHairLengthId(desiredHairLength.getDesiredHairLengthId(),retailer);
                desiredHairLengthRepository.deleteById(desiredHairLength.getDesiredHairLengthId());
            }
        }

        List<DocumentOption> documentOptions=documentOptionRepository.findByRetailer(retailer);
        if(!documentOptions.isEmpty()){
            for(DocumentOption documentOption:documentOptions){
                appointmentDocumentRepository.deleteByDocumentOptionId(documentOption.getDocumentOptionId(),retailer);
                petDocumentRepository.deleteByDocumnetOptionId(documentOption.getDocumentOptionId(),retailer);
                documentOptionRepository.deleteById(documentOption.getDocumentOptionId());
            }
        }
        List<AppointmentDocuments> appointmentDocuments=appointmentDocumentRepository.findByRetailer(retailer);
        if(!appointmentDocuments.isEmpty()){
           /* for (AppointmentDocuments appointmentDocument:appointmentDocuments){
                appointmentDocumentRepository.deleteById(appointmentDocument.getId());
            }*/
            appointmentDocumentRepository.deleteAll(appointmentDocuments);
        }
        List<AppointmentEmergencyContactInfo> appointmentEmergencyContactInfos=appointmentEmergencyContactInfoRpository.findByRetailer(retailer);
        if(!appointmentEmergencyContactInfos.isEmpty()){
            /*for(AppointmentEmergencyContactInfo appointmentEmergencyContactInfo:appointmentEmergencyContactInfos){
                appointmentEmergencyContactInfoRpository.deleteById(appointmentEmergencyContactInfo.getId());
            }*/
            appointmentEmergencyContactInfoRpository.deleteAll(appointmentEmergencyContactInfos);
        }
        List<EmergencyContactInfo>emergencyContactInfos=emergencyContactInfoRepository.findByRetailer(retailer);
        if(!emergencyContactInfos.isEmpty()){
            for(EmergencyContactInfo emergencyContactInfo:emergencyContactInfos){
                petEmergencyContactRepository.deleteByEmergencyContactInfoId(emergencyContactInfo.getEmergencyContactInfoId(),retailer);
                appointmentEmergencyContactInfoRpository.deleteByEmergencyContactInfoId(emergencyContactInfo.getEmergencyContactInfoId(),retailer);
                emergencyContactInfoRepository.deleteById(emergencyContactInfo.getEmergencyContactInfoId());
            }
        }
        List<PersonalityParameter> personalityParameterList=personalityParameterRepository.findByRetailer(retailer);
        if(!personalityParameterList.isEmpty()){
           /* for (PersonalityParameter personalityParameter:personalityParameterList){
                personalityParameterRepository.deleteById(personalityParameter.getId());
            }*/
            personalityParameterRepository.deleteAll(personalityParameterList);
        }

        List<AttendantAvailability> attendantAvailabilityList=attendantAvailabilityRepository.findByRetailer(retailer);
        if(!attendantAvailabilityList.isEmpty()){
            /*for(AttendantAvailability attendantAvailability:attendantAvailabilityList){
                attendantAvailabilityRepository.deleteById(attendantAvailability.getId());
            }*/
            attendantAvailabilityRepository.deleteAll(attendantAvailabilityList);
        }

        List<BittingHistory> bittingHistoryList=bittingHistoryRepository.findByRetailer(retailer);
        if(!bittingHistoryList.isEmpty()){
           /* for(BittingHistory bittingHistory:bittingHistoryList){
                bittingHistoryRepository.deleteById(bittingHistory.getId());
            }*/
            bittingHistoryRepository.deleteAll(bittingHistoryList);
        }
        List<Blades> blades=bladesRepository.findByRetailer(retailer);
        if(!blades.isEmpty()){
            for(Blades blade:blades){
                serviceHistoryRepository.deleteByBladeId(blade.getId(),retailer);
                bladesRepository.deleteById(blade.getId());
            }
        }
        List<BlockDateInfo> blockDateInfoList=blockDatesRepository.findByRetailer(retailer);
        if(!blockDateInfoList.isEmpty()){
            /*for(BlockDateInfo blockDateInfo:blockDateInfoList){
                blockDatesRepository.deleteById(blockDateInfo.getId());
            }*/
            blockDatesRepository.deleteAll(blockDateInfoList);
        }
        List<Combs> combsList=combsRepository.findByRetailer(retailer);
        if(!combsList.isEmpty()){
            for(Combs combs:combsList){
                serviceHistoryRepository.deleteByCombsId(combs.getId(),retailer);
                combsRepository.deleteById(combs.getId());
            }
        }
        List<Configuration> configurationList=configurationRepository.findByRetailer(retailer);
        if(!configurationList.isEmpty()) {
            /*for (Configuration configuration : configurationList) {
                configurationRepository.deleteById(configuration.getConfigId());
            }*/
            configurationRepository.deleteAll(configurationList);
        }
         FeedingInformation feedingInformations=feedingInformationRepository.findByRetailer(retailer);
        if(feedingInformations!=null){
            feedingInformationRepository.deleteById(feedingInformations.getFeedingInformationId());
        }
        List<GeneralPetSize> generalPetSizeList=generalPetSizeRepository.findByRetailer(retailer);
        if(!generalPetSizeList.isEmpty()){
            for(GeneralPetSize generalPetSize:generalPetSizeList){
                generalPetSizeRepository.updateDeleted(generalPetSize.getGeneralPetSizeId(),true);
            }
        }
        List<HairLength> hairLengths=hairLengthRepository.findByRetailer(retailer);
        if(!hairLengths.isEmpty()){
            for(HairLength hairLength:hairLengths){
                appointmentRepository.deleteByHairLengthId(hairLength.getHairLengthId(),retailer);
                petRepository.deleteByHairLengthId(hairLength.getHairLengthId(),retailer);
                //hairLengthRepository.deleteById(hairLength.getHairLengthId());
                hairLengthRepository.deleteHairLength(hairLength.getHairLengthId(),retailer,true);
            }
        }
        List<HairTexture> hairTextureList=hairTextureRepository.findByRetailer(retailer);
        if(!hairTextureList.isEmpty()){
            for(HairTexture hairTexture:hairTextureList){
                appointmentRepository.deleteByHaiTextureId(hairTexture.getHairTextureId(),retailer);
                petRepository.deleteByHairTextureId(hairTexture.getHairTextureId(),retailer);
                hairTextureRepository.deleteById(hairTexture.getHairTextureId());
            }
        }
        List<PetCologne> petCologneList=petCologneRepository.findByRetailerWithoutPagination(retailer);
        if(!petCologneList.isEmpty()){
            for(PetCologne petCologne:petCologneList) {
                serviceHistoryRepository.deleteByCologneId(petCologne.getId(),retailer);
                appointmentRepository.deleteByPetCologneId(petCologne.getId(),retailer);
                petCologneRepository.deleteById(petCologne.getId());
            }
        }
        List<PetDocuments>petDocumentsList=petDocumentRepository.findByRetailer(retailer);
        if(!petDocumentsList.isEmpty()){
            /*for(PetDocuments petDocuments:petDocumentsList){
                petDocumentRepository.deleteById(petDocuments.getId());
            }*/
            petDocumentRepository.deleteAll(petDocumentsList);
        }
        List<PetEmergencyContactInfo>petEmergencyContactInfoList=petEmergencyContactRepository.findByRetailer(retailer);
        if(!petEmergencyContactInfoList.isEmpty()){
            /*for(PetEmergencyContactInfo petEmergencyContactInfo:petEmergencyContactInfoList){
                petEmergencyContactRepository.deleteById(petEmergencyContactInfo.getId());
            }*/
            petEmergencyContactRepository.deleteAll(petEmergencyContactInfoList);
        }
        List<PetShampoo> petShampooList=petShampooRepository.findByRetailer(retailer);
        if(!petShampooList.isEmpty()){
            for(PetShampoo petShampoo:petShampooList){
                appointmentRepository.deleteByPetShampooId(petShampoo.getId(),retailer);
                serviceHistoryRepository.deleteByPetShampooId(petShampoo.getId(),retailer);
                petShampooRepository.deleteById(petShampoo.getId());
            }
        }

        List<PetTypeConfiguration> petTypeConfigurationList=petTypeConfigurationRepository.findByRetailer(retailer);
        if(!petTypeConfigurationList.isEmpty()){
           /* for (PetTypeConfiguration petTypeConfiguration:petTypeConfigurationList){
                petTypeConfigurationRepository.deleteById(petTypeConfiguration.getConfigurationId());
            }*/
            petTypeConfigurationRepository.deleteAll(petTypeConfigurationList);
        }
        List<PetVaccinationRecords> petVaccinationRecordsList=petVaccinationRecordRepository.findByRetailer(retailer);
        if(!petVaccinationRecordsList.isEmpty()){
           /* for(PetVaccinationRecords petVaccinationRecords:petVaccinationRecordsList){
                petVaccinationRecordRepository.deleteById(petVaccinationRecords.getId());
            }*/
            petVaccinationRecordRepository.deleteAll(petVaccinationRecordsList);
        }
        List<PetWaiverOfLiabilityInfo> petWaiverOfLiabilityInfoList=petWaiverOfLiabilityInfoRepository.findByRetailer(retailer);
        if(!petWaiverOfLiabilityInfoList.isEmpty()){
            /*for(PetWaiverOfLiabilityInfo petWaiverOfLiabilityInfo:petWaiverOfLiabilityInfoList){
                petWaiverOfLiabilityInfoRepository.deleteById(petWaiverOfLiabilityInfo.getId());
            }*/
            petWaiverOfLiabilityInfoRepository.deleteAll(petWaiverOfLiabilityInfoList);
        }

        List<ServiceAvailability> serviceAvailabilityList=serviceAvailabilityRepository.findByRetailer(retailer);
        if(!serviceAvailabilityList.isEmpty()){
            /*for(ServiceAvailability serviceAvailability:serviceAvailabilityList){
                serviceAvailabilityRepository.deleteById(serviceAvailability.getId());
            }*/
            serviceAvailabilityRepository.deleteAll(serviceAvailabilityList);
        }

        List<PetType> petTypes=petTypeRepository.findByRetailer(retailer);
        if(!petTypes.isEmpty()){
            for (PetType petType:petTypes){
                attendantPetTypesRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                temperamentRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                generalPetSizeRepository.updateDeletedByPetTypeId(petType.getPetTypeId(),retailer,true);
                desiredHairLengthRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                hairLengthRepository.deleteByHairLengthByPetTypeId(petType.getPetTypeId(),retailer,true);
                bladesRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                combsRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                vaccinationRecordsRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                venuePetTypesRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                vetInformationRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                weightRangeRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                allergiesRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                emergencyContactInfoRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                petRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                appointmentRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                hairTextureRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                petTypeConfigurationRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
                serviceRepository.deleteByPetTypeId(petType.getPetTypeId(),retailer);
               // petTypeRepository.deleteById(petType.getPetTypeId());
                petTypeRepository.deletePetType(petType.getPetTypeId(),retailer,true);
            }
        }

    }

}
