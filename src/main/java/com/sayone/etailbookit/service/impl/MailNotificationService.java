package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.model.EmailRequest;
import com.sayone.etailbookit.service.IMailNotificationService;
import com.sendgrid.*;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class MailNotificationService implements IMailNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(MailNotificationService.class);

    @Value("${spring.sendgrid.api-key}")
    private String sendgridApiKey;

    private static Logger LOGGER = LoggerFactory.getLogger(MailNotificationService.class);

    @Override
    public String sendMail(EmailRequest emailRequest) {
        LOGGER.info("@@@@Enterd sentMail function");
        SendGrid sg = new SendGrid(sendgridApiKey);

        Mail mail = new Mail();
        Email fromEmail = new Email();
        fromEmail.setEmail(emailRequest.getMailFrom());
        mail.setFrom(fromEmail);
        mail.setSubject(emailRequest.getSubject());

        Personalization personalization = new Personalization();
        Email to = new Email();
        to.setEmail(emailRequest.getMailTo());
        personalization.addTo(to);

        Email cc = new Email();
        cc.setEmail(emailRequest.getCc());
        personalization.addCc(cc);
        mail.addPersonalization(personalization);

        Content content = new Content();
        content.setType("text/html");
        content.setValue("<html lang=\"en\">\n" +
                "\n" +
                "<head>\n" +
                "\n" +
                "<meta charset=\"utf-8\">\n" +
                "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n" +
                "<meta name=\"description\" content=\"\">\n" +
                "<meta name=\"author\" content=\"\"> \n" +
                "\n" +
                "<title>EtailBookit email template</title> \n" +
                "<link href=\"https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,900&display=swap\" rel=\"stylesheet\">\n" +
                "<style>\n" +
                "body{background:#fff; margin: 0; padding: 0; font-family: 'Source Sans Pro', sans-serif; line-height:32px; font-size: 17px; }\n" +
                "\t\n" +
                "</style>\n" +
                "</head>\n" +
                "\n" +
                "<body >\n" +
                "<div style=\"width: 100%; height: 100vh; float: left; margin: 0; padding: 0;\">\n" +
                "<table style=\"max-width:750px; height: auto; padding:0; position: relative; text-align: left; border-radius: 3px; margin: 0px auto;\">\n" +
                "<tr>\t\n" +
                "<td>\n" +
                "<div style=\"width: 275px; height: auto;margin-left: 20px;\">\n" +
                "</td></tr>\n" +
                "\t\n" +
                "<tr>\n" +
                "<td style=\"background:#f1f1f1; padding:30px 40px; text-align:left\">\n" +
                emailRequest.getText()+"<br>\n" +
                "\n" +
                "\t\n" +
                "\n" +
                "\n" +
                "\n" +
                "</td></tr>\n" +
                "</table>\t\n" +
                "</div>\t\t\n" +
                "</body>\n" +
                "\n" +
                "</html>\n");
        mail.addContent(content);

        Request request = new Request();
        try {
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());
            Response response = sg.api(request);
            logger.info("@@@@"+response.getStatusCode());
            LOGGER.info("@@@@Exited sentMail function");
            return "Success";
        } catch (IOException ex) {
            ex.printStackTrace();
            return "Fail";
        }
    }
}