package com.sayone.etailbookit.service.impl;


import com.amazonaws.services.cloudformation.model.StackInstance;
import com.amazonaws.services.healthlake.model.transform.ImportJobPropertiesJsonUnmarshaller;
import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.Batch.AppointmentReaderConfiguration;
import com.sayone.etailbookit.Batch.AppointmentWriterConfiguration;
import com.sayone.etailbookit.component.AuthenticationEcom;
import com.sayone.etailbookit.component.CustomerEmailNotification;
import com.sayone.etailbookit.component.CustomerSMSNotification;
import com.sayone.etailbookit.component.ScheduleAppointment;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.AppointmentMapper;
import com.sayone.etailbookit.mapper.PetTypeMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.AppointmentService;
import com.sayone.etailbookit.service.WaitListEntryService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import com.zaxxer.hikari.HikariDataSource;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tomcat.jni.Local;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.zone.ZoneRules;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;

import static java.lang.String.format;
import static org.apache.poi.ss.util.CellUtil.createCell;

@Service
@Slf4j
public class AppointmentServiceImpl implements AppointmentService {
    private static Logger LOGGER = LoggerFactory.getLogger(AppointmentServiceImpl.class);
    @Autowired
    ServiceTypeRepository serviceTypeRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    PetShampooRepository petShampooRepository;

    @Autowired
    PetCologneRepository petCologneRepository;

    @Autowired
    AddonServiceRepository addonServiceRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    VaccinationRecordsRepository vaccinationRecordsRepository;

    @Autowired
    AllergiesRepository allergiesRepository;

    @Autowired
    DocumentOptionRepository documentOptionRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    DesiredHairLengthRepository desiredHairLengthRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    EmergencyContactInfoRepository emergencyContactInfoRepository;

    @Autowired
    ServiceHistoryRepository serviceHistoryRepository;

    @Autowired
    UnfriendlyBehaviourTriggerRepository unfriendlyBehaviourTriggerRepository;

    @Autowired
    PersonalityParameterRepository personalityParameterRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    HairLengthRepository hairLengthRepository;

    @Autowired
    HairTextureRepository hairTextureRepository;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    VetInformationRepository vetInformationRepository;

    @Autowired
    BlockDatesRepository blockDatesRepository;

    @Autowired
    WaiverOfLiabilityRepository waiverOfLiabilityRepository;

    @Autowired
    CustomerEmailNotification customerEmailNotification;

    @Autowired
    PetVaccinationRecordRepository petVaccinationRecordRepository;

    @Autowired
    PetDocumentRepository petDocumentRepository;

    @Autowired
    CustomerSMSNotification customerSMSNotification;

    @Autowired
    PetWaiverOfLiabilityInfoRepository petWaiverOfLiabilityInfoRepository;

    @Autowired
    QuoteAdjustmentRepository quoteAdjustmentRepository;

    @Autowired
    AppointmentWaiverOfLiabilityInformationRepository appointmentWaiverOfLiabilityInformationRepository;

    @Autowired
    AppointmentVetInfoRepository appointmentVetInfoRepository;

    @Autowired
    AppointmentEmergencyContactInfoRpository appointmentEmergencyContactInfoRpository;

    @Autowired
    AppointmentDesiredHairlengthRepository appointmentDesiredHairlengthRepository;

    @Autowired
    PetVetInformationRepository petVetInformationRepository;

    @Autowired
    PetBreedsInformationRepository petBreedsInformationRepository;

    @Autowired
    BreedRepository breedRepository;

    @Autowired
    OffsetRetailerMappingRepository offsetRetailerMappingRepository;

    @Autowired
    ServiceBreedsInformationRepository serviceBreedsInformationRepository;

    @Autowired
    PetTypeConfigurationRepository petTypeConfigurationRepository;

    @Autowired
    VacationRepository vacationRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    AuthenticationEcom authenticationEcom;

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private Job appointmentJob;

    @Autowired
    AppointmentWriterConfiguration appointmentWriterConfiguration;

    @Autowired
    AppointmentReaderConfiguration appointmentReaderConfiguration;

    @Autowired
    AppointmentExcelGenerator appointmentExcelGenerator;

    @Autowired
    AttendantAvailabilityRepository attendantAvailabilityRepository;

    @Autowired
    TimeSlotRepository timeSlotRepository;

    @Autowired
    TimeSlotClusterRepository timeSlotClusterRepository;

    @Value("${ecom.auth.uri}")
    String auth_Uri;

    @Value("${appointment.slot.interval}")
    Integer slotInterval;

    @Value("${appointment.lookAheadPeriod}")
    Integer lookAhead;

    @Autowired
    ExcelExportService excelExportService;

    @Autowired
    GoogleCalendarService googleCalendarService;

    WaitListEntryRepository waitListEntryRepository;

    WaitListEntryService waitListEntryService;
    // OPTIMIZATION: Cache for timezone offset calculations
    private final Map<String, Integer> timezoneOffsetCache = new ConcurrentHashMap<>();
    private static final int CACHE_SIZE_LIMIT = 1000;


    private static DateTimeFormatter monthDateYearFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH.mm.ss");
    
    /**
     * Trims seconds and nanoseconds from an OffsetDateTime to maintain consistency
     * in appointmentStartDateAndTime field across all appointment types
     */
    private OffsetDateTime trimSecondsFromDateTime(OffsetDateTime dateTime) {
        return dateTime.withSecond(0).withNano(0);
    }

    @Override
    public PaymentDto createAppointment(AppointmentDto appointmentDto, String timeZone) throws EtailBookItException {
        Validator.validateAppointment(appointmentDto, appointmentRepository);
        Boolean reopened=Boolean.FALSE;
        Appointment appointment = generateAppointmentDetails(appointmentDto, timeZone,reopened);
        if(appointmentDto.getTimeSlotId()!=null){
            Optional<TimeSlots> timeSlots= timeSlotRepository.findById(appointmentDto.getTimeSlotId());
            timeSlots.ifPresent(slots -> slots.setSlotBooked(Boolean.TRUE));
            timeSlotRepository.save(timeSlots.get());
        }
        if(appointmentDto.getIsEditable()){
            List<Appointment> existingAppointments = appointmentRepository.getAppointmentsByDateTimeAndPetAndServiceStatus(
                    appointment.getPet(), appointment.getDate(), appointment.getTime(), ServiceStatus.CANCELLED
            );

            if (!existingAppointments.isEmpty()) {
                throw new EtailBookItException("Selected pet has another appointment at selected time");
            }
        }
        if(appointmentDto.getBookedViaWaitList() && appointmentDto.getTimeSlotId()!=null){
            Integer timeSlotId=appointmentDto.getTimeSlotId();
            Integer customerEcomId=appointmentDto.getCustomerId();
            List<WaitlistEntry> entries = waitListEntryRepository.findByTimeSlotAndCustomer(timeSlotId, customerEcomId, RetailerContext.getRetailer());

            for (WaitlistEntry entry : entries) {
                Set<Customer> customers = entry.getCustomers();
                Customer customerToRemove = customers.stream()
                        .filter(c -> c.getEcom_id().equals(customerEcomId))
                        .findFirst()
                        .orElse(null);

                if (customerToRemove != null) {
                    customers.remove(customerToRemove);

                    // If no customers left, delete the entire entry
                    if (customers.isEmpty()) {
                        waitListEntryRepository.delete(entry);
                    } else {
                        entry.setCustomers(customers);
                        waitListEntryRepository.save(entry);
                    }
                }
            }
        }
        //Check if pet already has an appointment at the same time as newly created appointment

        if (appointment.getService().getPaymentAtTimeOfBooking()) {
            appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
        }
        if(appointmentDto.getPaymentmethodId()!=null){
            appointment.setPaymentId(appointmentDto.getPaymentmethodId());
        }
        if(!appointmentDto.getFirstName().isEmpty() && !appointmentDto.getLastName().isEmpty()){
            String carHolderName =appointmentDto.getFirstName().concat( " "+appointmentDto.getLastName());
            appointment.setCardHolderName(carHolderName);
            saveCardToEcom(appointmentDto);

        }
        appointment = appointmentRepository.save(appointment);
        if(RequestTypeContext.getRequestType().equals("Customer") && appointment.getServiceStatus().equals(ServiceStatus.PENDING_APPROVAL)){
            customerSMSNotification.appointmentApprove(appointment);
        }else {
            try {
                //customerEmailNotification.setAppointmentMailNotification(appointmentDto, appointment);
                //  customerSMSNotification.setAppointmentSmsNotiication(appointmentDto,appointment,"BOOKING_CONFIRMATION_STORE");
                customerSMSNotification.setAppointmentSmsNotiication(appointment, "BOOKIT_APPOINTMENT_CONFIRMATION", timeZone);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        appointment.setAppoinmentNo("#"+appointment.getId());
        appointment=appointmentRepository.save(appointment);
        
        // Sync to Google Calendar if attendant has it enabled
        try {
            googleCalendarService.createCalendarEvent(appointment);
        } catch (Exception e) {
            LOGGER.error("Failed to sync appointment to Google Calendar: {}", e.getMessage());
            // Don't fail appointment creation if calendar sync fails
        }
        
        if (Optional.ofNullable(appointment.getId()).orElse(0) != 0) {
            PaymentDto paymentDto = new PaymentDto();
            paymentDto.setAppointmentId(appointment.getId());
            paymentDto.setDate(appointment.getDate().toString());
            paymentDto.setTime(appointment.getTime().toString());
            PaymentDto paymentDetails = AmountCalculation.getPaymentDetails(appointment);
            return paymentDetails;
        }
        throw new EtailBookItException("Error occurred  during create appointment");

    }

    @Override
    @Transactional
    public PaymentDto reopenedAppointment(int id, AppointmentDto appointmentDto, String timeZone) throws EtailBookItException {
        Optional<Appointment> optionalAppointment = appointmentRepository.findById(id);
        if (!optionalAppointment.isPresent()) {
            throw new EntityNotFoundException("Details for the selected appointment is not found");
        }
        Appointment existingAppointment = optionalAppointment.get();
        if(appointmentDto.getEnableSlot()){
            Integer timeSlotId= existingAppointment.getTimeSlotId();
            if(timeSlotId!=null){
                if(!timeSlotId.equals(appointmentDto.getTimeSlotId())){
                    Optional<TimeSlots> timeSlots=timeSlotRepository.findById(timeSlotId);
                    if(timeSlots.isPresent()){
                        TimeSlots timeSlots1= timeSlots.get();
                        timeSlots1.setSlotBooked(Boolean.FALSE);
                        timeSlotRepository.save(timeSlots1);

                        try {
                            // Notify waitlist customers if slot was released
                            try {
                                waitListEntryService.notifyWaitlistCustomersForSlotAvailability(
                                    timeSlotId, 
                                    RetailerContext.getRetailer(), 
                                    timeZone
                                );
                            } catch (Exception e) {
                                LOGGER.warn("Failed to notify waitlist customers for slot {}: {}", timeSlotId, e.getMessage());
                            }
                        } catch (Exception e) {
                            LOGGER.warn("Failed to send slot release notification for appointment {}: {}", existingAppointment.getId(), e.getMessage());
                        }
                    }
                }
            }
        }
        if(appointmentDto.getTimeSlotId()!=null){
            Optional<TimeSlots> timeSlots= timeSlotRepository.findById(appointmentDto.getTimeSlotId());
            timeSlots.ifPresent(slots -> slots.setSlotBooked(Boolean.TRUE));
            timeSlotRepository.save(timeSlots.get());
        }
        Validator.validateAppointment(appointmentDto, appointmentRepository);
        Appointment appointment = generateAppointmentDetails(appointmentDto, timeZone,true);
        appointment.setId(id);
        //Check if pet already has an appointment at the same time as newly created appointment
        List<Appointment> existingAppointments = appointmentRepository.getAppointmentsByDateTimeAndPetAndServiceStatus(
                appointment.getPet(), appointment.getDate(), appointment.getTime(), ServiceStatus.CANCELLED
        );

        if (existingAppointments != null && existingAppointments.size() == 1 && !existingAppointments.get(0).getId().equals(id)) {
            throw new EtailBookItException("Selected pet has another appointment at selected time");
        }
        if (optionalAppointment.get().getAppoinmentNo() != null) {
            appointment.setAppoinmentNo(optionalAppointment.get().getAppoinmentNo());
        }
        if (appointmentDto.getWaiverOfLiabilityDoc() != null && appointmentDto.getWaiverOfLiabilityDoc().getId() != null) {
            appointment.setWaiverOfLiabilityDoc(generateWaiverOfLiabilityInformation(appointment, appointmentDto));

        } else {
            appointment.setWaiverOfLiabilityDoc(null);
        }
        if (optionalAppointment.get().getAppointmentEmergencyContactInfo() != null) {
            for (AppointmentEmergencyContactInfo appointmentEmergencyContactInfo1 : optionalAppointment.get().getAppointmentEmergencyContactInfo()) {
                boolean found = false;
                for (AppointmentEmergencyContactInfo appointmentEmergencyContactInfo2 : appointment.getAppointmentEmergencyContactInfo()) {
                    if (appointmentEmergencyContactInfo2.getEmergencyContactInfo().getEmergencyContactInfoId().equals(appointmentEmergencyContactInfo1.getEmergencyContactInfo().getEmergencyContactInfoId())) {
                        appointmentEmergencyContactInfo2.setId(appointmentEmergencyContactInfo1.getId());
                        found = true;
                        break;
                    }
                }
                if (!found)
                    appointmentEmergencyContactInfoRpository.deleteById(appointmentEmergencyContactInfo1.getId());
            }
        } else {
            appointmentEmergencyContactInfoRpository.deleteByAppointmentId(id, RetailerContext.getRetailer());
        }

        if (optionalAppointment.get().getAppointmentVetInformation() != null) {
            for (AppointmentVetInformation appointmentVetInformation1 : optionalAppointment.get().getAppointmentVetInformation()) {
                boolean found = false;
                for (AppointmentVetInformation appointmentVetInformation2 : appointment.getAppointmentVetInformation()) {
                    if (appointmentVetInformation2.getVetInformation().getId().equals(appointmentVetInformation1.getVetInformation().getId())) {
                        appointmentVetInformation2.setId(appointmentVetInformation1.getId());
                        found = true;
                        break;
                    }
                }
                if (!found)
                    appointmentVetInfoRepository.deleteById(appointmentVetInformation1.getId());
            }
        } else {
            appointmentVetInfoRepository.deleteByAppointmentId(id, RetailerContext.getRetailer());
        }

        if (optionalAppointment.get().getDesiredHairLengths() != null) {
            for (AppointmentDesiredHairLengths appointmentDesiredHairLengths1 : optionalAppointment.get().getDesiredHairLengths()) {
                boolean found = false;
                for (AppointmentDesiredHairLengths appointmentDesiredHairLengths2 : appointment.getDesiredHairLengths()) {
                    if (appointmentDesiredHairLengths2.getDesiredHairLength().getDesiredHairLengthId().equals(appointmentDesiredHairLengths1.getDesiredHairLength().getDesiredHairLengthId())) {
                        appointmentDesiredHairLengths2.setId(appointmentDesiredHairLengths1.getId());
                        found = true;
                        break;
                    }
                }
                if (!found)
                    appointmentDesiredHairlengthRepository.deleteById(appointmentDesiredHairLengths1.getId());
            }
        } else {
            appointmentDesiredHairlengthRepository.deleteByAppointmentId(id, RetailerContext.getRetailer());
        }
        if (appointment.getService().getPaymentAtTimeOfBooking()) {
            appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
        }
        appointment.setModifiedAt(LocalDateTime.now());
        appointment = appointmentRepository.save(appointment);

        try {
            //customerEmailNotification.appointmentUpdateMailNotification(appointment);
            customerSMSNotification.serviceChangeSMSNotification(existingAppointment, appointment, timeZone);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Optional.ofNullable(appointment.getId()).orElse(0) != 0) {
            PaymentDto paymentDto = new PaymentDto();
            paymentDto.setAppointmentId(appointment.getId());
            PaymentDto paymentDetails = AmountCalculation.getPaymentDetails(appointment);
            return paymentDetails;
        }
        throw new EtailBookItException("Error occurred  during create appointment");

    }

    @Override
    public AppointmentDetailsProjection getAppointmentById(Integer appointmentId) throws EtailBookItException {
        AppointmentDetailsProjection appointment = appointmentRepository.getAppointmentDetails(appointmentId);
        if (appointment == null) {
            throw new EntityNotFoundException("Specified appointment not found");
        }
        return appointment;
    }

    @Override
    public List<AppointmentDetailsProjection> getAllAppointmentsByDate(OffsetDateTime date, String timeZone) throws EtailBookItException {
        long startTime = System.currentTimeMillis();
        
        try {
            // OPTIMIZATION 1: Calculate timezone offset once
            OffsetDateTime tempDate = date;
            Integer offsetTimeDifference = calculateOffset(timeZone, tempDate);
            OffsetDateTime adjustedDate = offsetTimeDifference > 0 ? date.plusMinutes(offsetTimeDifference) : date;
            LocalDate targetLocalDate = adjustedDate.toLocalDate();
            
            // OPTIMIZATION 2: Single database query for all required dates
            Set<LocalDate> requiredDates = new HashSet<>();
            requiredDates.add(targetLocalDate);
            
            // Add adjacent dates if needed for timezone boundary cases
            if (offsetTimeDifference != 0) {
                requiredDates.add(tempDate.toLocalDate());
                if (offsetTimeDifference > 0) {
                    requiredDates.add(tempDate.toLocalDate().plusDays(1));
                } else {
                    requiredDates.add(tempDate.toLocalDate().minusDays(1));
                }
            }
            
            // OPTIMIZATION 3: Single query to get all appointment IDs for all required dates
            List<Integer> allAppointmentIds = appointmentRepository.getAppointmentsIdByDateRange(
                requiredDates, RetailerContext.getRetailer());
            
            if (allAppointmentIds.isEmpty()) {
                LOGGER.debug("No appointments found for dates: {}", requiredDates);
                return new ArrayList<>();
            }
            
            // OPTIMIZATION 4: Batch processing for large result sets
            List<AppointmentDetailsProjection> allAppointments;
            if (allAppointmentIds.size() > 1000) {
                // Process in batches for large datasets
                allAppointments = processAppointmentsInBatches(allAppointmentIds);
            } else {
                // Single query for smaller datasets
                allAppointments = appointmentRepository.getAppointmentDetailsByIds(allAppointmentIds);
            }
            
            // OPTIMIZATION 5: Efficient filtering and processing in single pass
            List<AppointmentDetailsProjection> filteredAppointments = allAppointments.stream()
                .filter(appointment -> {
                    // Apply timezone adjustment
                    LocalDate appointmentDate = appointment.getOffsetStartTime()
                        .plusMinutes(offsetTimeDifference)
                        .toLocalDate();
                    
                    // Check if appointment belongs to target date
                    return appointmentDate.equals(targetLocalDate);
                })
                .peek(appointment -> {
                    // Set offset appointment date efficiently
                    appointment.setOffsetAppointmentDate(
                        appointment.getOffsetStartTime()
                            .plusMinutes(offsetTimeDifference)
                            .toLocalDate()
                    );
                })
                .collect(Collectors.toList());
            
            long executionTime = System.currentTimeMillis() - startTime;
            LOGGER.debug("getAllAppointmentsByDate completed in {}ms, found {} appointments", 
                executionTime, filteredAppointments.size());
            
            return filteredAppointments;
            
        } catch (Exception e) {
            LOGGER.error("Error in getAllAppointmentsByDate: {}", e.getMessage(), e);
            throw new EtailBookItException("Failed to retrieve appointments: " + e.getMessage());
        }
    }
    
    /**
     * OPTIMIZATION: Process appointments in batches for large datasets
     */
    private List<AppointmentDetailsProjection> processAppointmentsInBatches(List<Integer> appointmentIds) {
        List<AppointmentDetailsProjection> allAppointments = new ArrayList<>();
        int batchSize = 500; // Optimal batch size for database queries
        
        for (int i = 0; i < appointmentIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, appointmentIds.size());
            List<Integer> batch = appointmentIds.subList(i, endIndex);
            
            List<AppointmentDetailsProjection> batchResults = appointmentRepository.getAppointmentDetailsByIds(batch);
            allAppointments.addAll(batchResults);
            
            LOGGER.debug("Processed batch {}/{}: {} appointments", 
                (i / batchSize) + 1, (appointmentIds.size() + batchSize - 1) / batchSize, batchResults.size());
        }
        
        return allAppointments;
    }

    public BaseResponseDto getAllAppointment(
            Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer petType, Integer pet,
            Integer attendant, Integer venue, OffsetDateTime startDateOffset, OffsetDateTime endDateOffset, String timeZone
    ) throws EtailBookItException {
        long startTime = System.currentTimeMillis();
        
        try {
            LOGGER.debug("getAllAppointment called with pageNo: {}, pageSize: {}, customer: {}, petType: {}, pet: {}, attendant: {}, venue: {}", 
                pageNo, pageSize, customer, petType, pet, attendant, venue);
            
            // OPTIMIZATION 1: Validate input parameters early
            if (pageNo == null || pageNo < 0) pageNo = 0;
            if (pageSize == null || pageSize <= 0) pageSize = 10;
            if (pageSize > 1000) pageSize = 1000; // Prevent excessive memory usage
            
            Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
            
            // OPTIMIZATION 2: Handle different request types efficiently
            if (RequestTypeContext.getRequestType().equals("Retailer")) {
                if (startDateOffset != null && endDateOffset != null) {
                    // OPTIMIZATION 3: Optimize date range processing
                    endDateOffset = endDateOffset.withHour(23).withMinute(59).withSecond(59);
                    
                    // Calculate timezone offset once
                    ZoneId zoneId = ZoneId.of(timeZone);
                    ZoneOffset offset = zoneId.getRules().getOffset(java.time.Instant.now());
                    int offsetSeconds = offset.getTotalSeconds();
                    
                    if (offsetSeconds < 0) {
                        endDateOffset = endDateOffset.plusSeconds(Math.abs(offsetSeconds));
                    } else {
                        endDateOffset = endDateOffset.minusSeconds(offsetSeconds);
                    }
                    
                    String startDate = startDateOffset.toString();
                    String endDate = endDateOffset.toString();
                    
                    LOGGER.debug("Fetching appointments with date range: {} to {}", startDate, endDate);
                    
                    // OPTIMIZATION 4: Single optimized query for date range
                    List<AppointmentListingProjection> dataListWithTimeRange = appointmentRepository.getAllAppointments(
                        customer, startDate, endDate, pet, attendant, venue, ServiceStatus.CANCELLED, RetailerContext.getRetailer()
                    );
                    
                    long executionTime = System.currentTimeMillis() - startTime;
                    LOGGER.debug("getAllAppointment with date range completed in {}ms, found {} appointments", 
                        executionTime, dataListWithTimeRange.size());
                    
                    return new BaseResponseDto(Status.SUCCESS, dataListWithTimeRange);
                    
                } else {
                    // OPTIMIZATION 5: Optimized query without date range
                    LOGGER.debug("Fetching appointments without date range");
                    
                    Page<AppointmentListingProjection> dataList = appointmentRepository.getAllAppointmentsWithoutDate(
                        customer, petType, RetailerContext.getRetailer(), paging
                    );
                    
                    long executionTime = System.currentTimeMillis() - startTime;
                    LOGGER.debug("getAllAppointment without date range completed in {}ms, found {} appointments", 
                        executionTime, dataList.getTotalElements());
                    
                    return new BaseResponseDto(Status.SUCCESS, dataList);
                }
            } else {
                // OPTIMIZATION 6: Optimized query for non-retailer requests
                LOGGER.debug("Fetching appointments for non-retailer request");
                
                Page<AppointmentListingProjection> dataList = appointmentRepository.getAllAppointmentsWithoutDate(
                    customer, petType, RetailerContext.getRetailer(), paging
                );
                
                long executionTime = System.currentTimeMillis() - startTime;
                LOGGER.debug("getAllAppointment non-retailer completed in {}ms, found {} appointments", 
                    executionTime, dataList.getTotalElements());
                
                return new BaseResponseDto(Status.SUCCESS, dataList);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error in getAllAppointment: {}", e.getMessage(), e);
            throw new EtailBookItException("Failed to retrieve appointments: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Boolean updateAppointment(int id, OffsetDateTime date, OffsetDateTime startTime, OffsetDateTime endTime, String timeZone,Integer createdCustomerId,String createdCustomerName,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException {
        Appointment existingAppointment;
        if (Optional.ofNullable(id).orElse(0) != 0) {
            Optional<Appointment> appointment = appointmentRepository.findById(id);
            if (!appointment.isPresent())
                throw new EntityNotFoundException("Details for the selected appointment is not found");
            existingAppointment = appointment.get();
            LocalDate dateExist = existingAppointment.getDate();
            OffsetTime timeExist = existingAppointment.getTime();
            Appointment existingAppointment2 = SerializationUtils.clone(appointment.get());
            if (existingAppointment.getServiceStatus().ordinal() != ServiceStatus.valueOf("CREATED").ordinal()) {
                String stage = ServiceStatus.valueOf(Utils.serviceStatusValues.get(existingAppointment.getServiceStatus().ordinal())).toString();
                throw new EtailBookItException("The appointment cannot be updated because its in " + stage + " Stage");
            }
            LocalDate appointmentDate = startTime.toLocalDate();
          /*  LocalDate appointmentDate=  ZonedDateTime.parse(
                    LocalDate.parse(date, monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(startTime, timeFormatter).toString() +
                            OffsetContext.getOffset()
            ).toInstant().atOffset(ZoneOffset.UTC).toLocalDate();*/
            OffsetTime appointmentStartTime = startTime.toOffsetTime();
          /*  OffsetTime appointmentStartTime=   ZonedDateTime.parse(
                    LocalDate.parse(date, monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(startTime, timeFormatter).toString() +
                            OffsetContext.getOffset()
            ).toInstant().atOffset(ZoneOffset.UTC).toOffsetTime();*/
            OffsetTime appointmentEndTime = endTime.toOffsetTime();
            /*OffsetTime appointmentEndTime= ZonedDateTime.parse(
                    LocalDate.parse(date, monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(endTime, timeFormatter).toString() +
                            OffsetContext.getOffset()
            ).toInstant().atOffset(ZoneOffset.UTC).toOffsetTime();*/

            Duration duration = Duration.between(appointmentStartTime, appointmentEndTime);
            existingAppointment.setDate(
                    appointmentDate);
            existingAppointment.setEndDate(appointmentDate);
            existingAppointment.setTime(appointmentStartTime);
            existingAppointment.setEndTime(appointmentEndTime);
            // Trim seconds from the UTC timestamps to maintain consistency
            existingAppointment.setAppointmentStartDateAndTime(trimSecondsFromDateTime(startTime).toString());
            existingAppointment.setAppointmentEndDateAndTime(trimSecondsFromDateTime(endTime).toString());
            existingAppointment.setIsOverride(Boolean.TRUE);
            existingAppointment.setDuration(duration.getSeconds() + "(s)");
            existingAppointment.setModifiedAt(LocalDateTime.now());
            if(createdCustomerId!=null && createdCustomerName !=null){
                existingAppointment.setCreatedCustomerId(createdCustomerId);
                existingAppointment.setCreatedCustomerName(createdCustomerName);
            }
            if(updatedCustomerId!=null && updatedCustomerName!=null){
                existingAppointment.setUpdatedCustomerId(updatedCustomerId);
                existingAppointment.setUpdatedCustomerName(updatedCustomerName);
            }
            Appointment appointment1 = appointmentRepository.save(existingAppointment);
            
            // Sync update to Google Calendar if attendant has it enabled
            try {
                googleCalendarService.updateCalendarEvent(appointment1);
            } catch (Exception e) {
                LOGGER.error("Failed to update Google Calendar event: {}", e.getMessage());
                // Don't fail appointment update if calendar sync fails
            }
            
            try {
                //customerEmailNotification.serviceChangeMailNotification(existingAppointment,dateExist,timeExist,existingAppointment.getDate(),existingAppointment.getTime());
                customerSMSNotification.serviceChangeSMSNotification(existingAppointment2, appointment1, timeZone);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }
        return false;
    }

//    @Override
//    public Page<AppointmentListingProjection> getAppointmentsByDate(OffsetDateTime date, Boolean withCancel, Integer pageNo, Integer pageSize, String sortBy, Optional<String> search,String timeZone) throws EtailBookItException {
//        OffsetDateTime tempDate=date;
//        Integer offsetDifference=calculateOffset(timeZone);
//        if(offsetDifference>0){
//            date=date.plusMinutes(offsetDifference);
//        }
//        OffsetDateTime offsetAddedDate=date;
//        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
//        Page<AppointmentListingProjection> appointments;
//        LocalDate localDate=date.toLocalDate();
//        if (search.isPresent() && search.hashCode() != 0) {
//            appointments = appointmentRepository.getAppointmentsByDate(localDate
//                    /*LocalDate.parse(date, monthDateYearFormat)*/,
//                    search.get(),
//                    RetailerContext.getRetailer(), paging
//            );
//        } else {
//            if (!withCancel) {
//                appointments = appointmentRepository.getAppointmentsByDate(localDate
//                      /*  LocalDate.parse(date, monthDateYearFormat)*/,
//                        RetailerContext.getRetailer(), ServiceStatus.CANCELLED, paging
//                );
//            } else {
//                appointments = appointmentRepository.getAppointmentsByDate(localDate
//                        /*LocalDate.parse(date, monthDateYearFormat)*/,
//                        RetailerContext.getRetailer(), paging
//                );
//            }
//        }
//      //  appointments=appointments.map(this::transformStartDateAndEndDate);
//        appointments.stream().map(a->a.setOffsetAppointmentDate(a.getOffsetStartTime().plusMinutes(offsetDifference).toLocalDate())).collect(Collectors.toList());
//        List<AppointmentListingProjection> appointmentListingProjectionList=appointments.get().collect(Collectors.toList());
//        List<AppointmentListingProjection> appointmentListingProjectionsRemovalList = new ArrayList<>();
//        appointmentListingProjectionsRemovalList=filterOutAppointments(appointmentListingProjectionList,offsetDifference,offsetAddedDate,tempDate);
//        if (offsetDifference > 0) {
//        if(localDate!=tempDate.toLocalDate()) {
//
//                Page<AppointmentListingProjection> appointmentPreviousDate = appointmentRepository.getAppointmentsByDate(tempDate.toLocalDate(), RetailerContext.getRetailer(),ServiceStatus.CANCELLED, paging);
//            appointmentPreviousDate.stream().map(a->a.setOffsetAppointmentDate(a.getOffsetStartTime().plusMinutes(offsetDifference).toLocalDate())).collect(Collectors.toList());
//                appointmentPreviousDate.stream().forEach(a -> {
//                    if (a.getOffsetStartTime().plusMinutes(offsetDifference).isAfter(offsetAddedDate)||(a.getOffsetStartTime().plusMinutes(offsetDifference).isEqual(offsetAddedDate))) {
//                        appointmentListingProjectionList.add(a);
//                    }
//                });
//            filterOutAppointments(appointmentListingProjectionList,offsetDifference,offsetAddedDate,tempDate);
//            }
//        }
//        else {
//                Page<AppointmentListingProjection> appointmentAfterDate=appointmentRepository.getAppointmentsByDate(tempDate.toLocalDate().plusDays(1),RetailerContext.getRetailer(),ServiceStatus.CANCELLED,paging);
//                appointmentAfterDate.stream().map(a->a.setOffsetAppointmentDate(a.getOffsetStartTime().plusMinutes(offsetDifference).toLocalDate())).collect(Collectors.toList());
//                appointmentAfterDate.stream().forEach(a->{
//                    if(a.getOffsetStartTime().plusMinutes(offsetDifference).toLocalDate().isEqual(tempDate.toLocalDate())){
//                        appointmentListingProjectionList.add(a);
//                    }
//                });
//            filterOutAppointments(appointmentListingProjectionList,offsetDifference,offsetAddedDate,tempDate);
//            }
//
//        appointmentListingProjectionList.removeAll(appointmentListingProjectionsRemovalList);
////        final int start = (int)paging.getOffset();
////        final int end = Math.min((start + paging.getPageSize()), appointmentListingProjectionList.size());
//        final int start = 0;
//        final int end = appointmentListingProjectionList.size();
//        final Page<AppointmentListingProjection> page = new PageImpl<>(appointmentListingProjectionList.subList(start, end), paging, appointments.getTotalElements());
//        return page;
//
//    }

    @Override
    public Page<AppointmentListingProjection> getAppointmentsByDate1(OffsetDateTime date, Boolean withCancel, Integer pageNo, Integer pageSize, String sortBy, Optional<String> search, String timeZone) throws EtailBookItException {
        String startDateTime = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm'Z'"));
        String endDateTime = date.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm'Z'"));
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<AppointmentListingProjection> appointmentListingProjections;
        String searchValue = search.isPresent() && !search.get().isEmpty() ? search.get() : null;
        
        LOGGER.debug("OnDate API - Searching appointments between {} and {} for retailer: {}", startDateTime, endDateTime, RetailerContext.getRetailer());
        
        appointmentListingProjections = appointmentRepository.getAppointmentsByDateTimeRange(startDateTime, endDateTime, searchValue, RetailerContext.getRetailer(), withCancel ? null : ServiceStatus.CANCELLED, paging);
        
        LOGGER.debug("OnDate API - Found {} appointments", appointmentListingProjections.getTotalElements());
        
        return appointmentListingProjections;
    }

    private List<AppointmentListingProjection> filterOutAppointments(List<AppointmentListingProjection> appointmentListingProjectionList, Integer offsetDifference, OffsetDateTime offsetAddedDate, OffsetDateTime tempDate) {
        List<AppointmentListingProjection> appointmentListingProjectionsRemovalList = new ArrayList<>();
        if (offsetAddedDate.toLocalDate() == tempDate.toLocalDate()) {
            if (offsetDifference > 0) {
                appointmentListingProjectionList.stream().forEach(a -> {
                    if (a.getOffsetAppointmentDate().equals(offsetAddedDate.toLocalDate().plusDays(1))) {
                        appointmentListingProjectionsRemovalList.add(a);
                    }
                });
            } else {
                appointmentListingProjectionList.stream().forEach(a -> {
                    if (a.getOffsetAppointmentDate().equals(offsetAddedDate.toLocalDate().minusDays(1))) {
                        appointmentListingProjectionsRemovalList.add(a);
                    }
                });
            }
            return appointmentListingProjectionsRemovalList;
        } else if (offsetDifference > 0) {
            OffsetDateTime startOffsetDate = offsetAddedDate.plusDays(1);
            appointmentListingProjectionList.stream().forEach(a -> {
                if (a.getOffsetStartTime().plusMinutes(offsetDifference).isAfter(startOffsetDate)) {
                    appointmentListingProjectionsRemovalList.add(a);
                }
            });
        } else {
            OffsetDateTime startOffsetDate = tempDate.plusDays(1);
            appointmentListingProjectionList.stream().forEach(a -> {
                if (a.getOffsetStartTime().plusMinutes(offsetDifference).isBefore(tempDate)) {
                    appointmentListingProjectionsRemovalList.add(a);
                }
            });
            appointmentListingProjectionList.stream().forEach(a -> {
                if (a.getOffsetStartTime().plusMinutes(offsetDifference).isAfter(startOffsetDate)) {
                    appointmentListingProjectionsRemovalList.add(a);
                }
            });
        }
        return appointmentListingProjectionsRemovalList;
    }
  /* private AppointmentListingProjection transformStartDateAndEndDate(final AppointmentListingProjection appointmentListingProjection){
        appointmentListingProjection.setDate(appointmentListingProjection.getOffsetStartTime().plusMinutes(330).toLocalDate());
        //System.out.println(appointmentListingProjection.getOffsetStartTime().plusMinutes(330).toLocalDate());
        return appointmentListingProjection;
    }*/

    public Integer calculateOffset(String timeZone, OffsetDateTime appointmentDateTime) {
        // OPTIMIZATION: Use caching for timezone offset calculations
        String cacheKey = timeZone + "_" + appointmentDateTime.toLocalDate();
        
        return timezoneOffsetCache.computeIfAbsent(cacheKey, k -> {
            // Calculate offset if not in cache
            try {
                ZonedDateTime zonedDateTime = appointmentDateTime.atZoneSameInstant(ZoneId.of(timeZone));
                int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
                int offset = offsetInSeconds / 60;
                
                // OPTIMIZATION: Limit cache size to prevent memory issues
                if (timezoneOffsetCache.size() > CACHE_SIZE_LIMIT) {
                    timezoneOffsetCache.clear();
                    LOGGER.debug("Timezone offset cache cleared due to size limit");
                }
                
                return offset;
            } catch (Exception e) {
                LOGGER.warn("Error calculating timezone offset for {}: {}", timeZone, e.getMessage());
                return 0;
            }
        });
    }

    public Integer checkDayLightSavings(String timeZone, OffsetDateTime apppointmentDateTime) {
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDateTime localDateTime = apppointmentDateTime.toLocalDateTime();
        ZoneOffset dstOffset = zoneId.getRules().getOffset(localDateTime);
        ZoneOffset standardOffset = zoneId.getRules().getStandardOffset(apppointmentDateTime.toInstant());
        int daylightSavingOffsetMinutes = (dstOffset.getTotalSeconds() - standardOffset.getTotalSeconds()) / 60;
        return daylightSavingOffsetMinutes;
    }
    public Integer checkDST(String timeZone, OffsetDateTime apppointmentDateTime){
        ZoneId zoneId = ZoneId.of(timeZone);

        // Get the current time in that zone
        ZonedDateTime now = apppointmentDateTime.atZoneSameInstant(zoneId);

        // Get the zone rules
        ZoneRules rules = zoneId.getRules();

        // Get the current and standard offsets
        ZoneOffset currentOffset = now.getOffset(); // Current offset (with DST if applicable)
        ZoneOffset standardOffset = rules.getStandardOffset(now.toInstant()); // Standard offset (without DST)

        // Determine the DST adjustment
        int adjustmentMinutes = currentOffset.getTotalSeconds() - standardOffset.getTotalSeconds();

         return adjustmentMinutes;
    }
    @Autowired
    private HikariDataSource dataSource;
    @Override
    public BaseResponseDto getAppointments(Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer pet, Integer attendant, Integer venue, String serviceStatus, Integer petType, String search, Integer service, OffsetDateTime startDateOffset, OffsetDateTime endDateOffset, String timeZone) throws EtailBookItException {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        ServiceStatus status = (serviceStatus != null) ? ServiceStatus.valueOf(serviceStatus) : null;
        Page<AppointmentsListing> appointments;
        if (startDateOffset != null && endDateOffset != null) {
            LOGGER.info("MAXIMUM POOL SIZE"+dataSource.getMaximumPoolSize());
            LOGGER.info("CONNECTION TIME OUT"+dataSource.getConnectionTimeout());
            endDateOffset = endDateOffset.withHour(23).withMinute(59).withSecond(59);
            ZoneId zoneId = ZoneId.of(timeZone);
            ZoneOffset offset = zoneId.getRules().getOffset(java.time.Instant.now());
            int offsetSeconds = offset.getTotalSeconds();
            if (offsetSeconds < 0)
                endDateOffset = endDateOffset.plusSeconds(Math.abs(offsetSeconds));
            else
                endDateOffset = endDateOffset.minusSeconds(offsetSeconds);
            String startDate = startDateOffset.toString();
            String endDate = endDateOffset.toString();
            appointments = appointmentRepository.getAllAppointments(
                    customer, pet, attendant, venue, search, service, startDate, endDate, status, petType,
                    RetailerContext.getRetailer(), paging
            );
        }
        /*else if(endDateOffset != null) {
            appointments = appointmentRepository.getAllAppointmentsBefore(
                    customer, pet, attendant, venue, search, service,endDateOffset, status, petType,
                    RetailerContext.getRetailer(), paging
            );
        }
        else if(startDateOffset != null) {
            appointments = appointmentRepository.getAllAppointmentsAfter(
                    customer, pet, attendant, venue, search,service, startDateOffset, status, petType,
                    RetailerContext.getRetailer(), paging
            );
        }*/
        else {
            appointments = appointmentRepository.getAllAppointments(
                    customer, pet, attendant, venue, search, service, status, petType, RetailerContext.getRetailer(), paging
            );
        }
        return new BaseResponseDto<>(Status.SUCCESS, appointments);
    }

    @Override
    @Transactional
    public Boolean updateOrderDetails(AppointmentDto order) throws EtailBookItException {
        Appointment existingAppointment;
        if (Optional.ofNullable(order.getId()).orElse(0) != 0 &&
                order.getOrderReference() != null && !order.getPaymentStatus().isEmpty()) {
            Optional<Appointment> appointment = appointmentRepository.findById(order.getId());
            if (!appointment.isPresent())
                throw new EntityNotFoundException("There is no appointment for this id: " + order.getId());
            existingAppointment = appointment.get();
            existingAppointment.setOrderReference(order.getOrderReference());
            existingAppointment.setPaymentStatus(order.getPaymentStatus());
            existingAppointment.setTipAmount(order.getTipAmount());
            appointmentRepository.save(existingAppointment);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public void bulkUpdateOrderDetails(List<PaymentUpdateDto> orders) throws EtailBookItException {
        try {
            for (PaymentUpdateDto appointmentDto : orders) {
                Appointment existingAppointment;
                Optional<Appointment> appointment = appointmentRepository.findById(appointmentDto.getId());
                //Optional<Appointment> appointment=appointmentRepository.findByIdAndOrderReference(appointmentDto.getId(),appointmentDto.getOrderReference());
                if (!appointment.isPresent())
                    throw new EntityNotFoundException("Appointment not found with id : " + appointmentDto.getId());
                existingAppointment = appointment.get();
                existingAppointment.setOrderReference(appointmentDto.getOrderReference());
                existingAppointment.setPaymentStatus(appointmentDto.getPaymentStatus());
                //  existingAppointment.setTipAmount(appointmentDto.getTipAmount());
                appointmentRepository.save(existingAppointment);
            }
        } catch (EntityNotFoundException e) {
            throw new EtailBookItException(e.getMessage());
        } catch (Exception e) {
            throw new EtailBookItException("Error updating order details");
        }
    }

    @Override
    public PaymentDto calculateServiceAmount(AppointmentDto appointmentDto, String timeZone) throws EtailBookItException {

        return AmountCalculation.serviceAmount(generateAppointmentDetails(appointmentDto, timeZone,true));

    }

    @Override
    public PaymentDetailsDto calculateServiceAmount(Integer appointmentId) {
        Appointment appointment = appointmentRepository.getOne(appointmentId);
        PaymentDto paymentDto = AmountCalculation.serviceAmount(appointment);
        PaymentDetailsDto paymentDetailsDto = new PaymentDetailsDto();
        paymentDetailsDto.setAppointmentId(appointmentId);
        paymentDetailsDto.setCustomerId(appointment.getCustomerId());
        paymentDetailsDto.setService(appointment.getService().getName());
        paymentDetailsDto.setAttendantId(appointment.getAttendant().getAttendantId());
        paymentDetailsDto.setAttendant(appointment.getAttendant().getFirstName() + " " + appointment.getAttendant().getLastName());
        paymentDetailsDto.setPet(appointment.getPet().getName());
        paymentDetailsDto.setPetId(appointment.getPet().getId());
        paymentDetailsDto.setServiceAmount(paymentDto.getServiceAmount());
        paymentDetailsDto.setIsTipsAllowed(appointment.getService().getIsTipsAllowed());
        paymentDetailsDto.setIsTaxable(paymentDto.getIsTaxable());
        paymentDetailsDto.setInternalItemNumber(appointment.getService().getInternalItemNumber());
        paymentDetailsDto.setAppointmentStatus(appointment.getServiceStatus().name());
        if (appointment.getService().getCancelationAmountValue() == null) {
            paymentDetailsDto.setCancellationFee(BigDecimal.ZERO);
        } else {
            paymentDetailsDto.setCancellationFee(appointment.getService().getCancelationAmountValue());
        }
        return paymentDetailsDto;
    }

    @Override
    public List<AppointmentPaymentProjection> getAppointmentsForCustomer(Integer customer) {
        return appointmentRepository.getUnpaidAppointmentsForCustomer(customer, "READY_TO_PAY", RetailerContext.getRetailer());
    }

    @Override
    public void makeReadyToPay(Integer appointmentId) {
        Appointment appointment = appointmentRepository.getOne(appointmentId);
        appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
        appointmentRepository.save(appointment);
    }

    private Appointment generateAppointmentDetails(AppointmentDto appointmentDto, String timeZone,boolean reopened) throws EtailBookItException {

        Integer offsetTimeDifference = calculateOffset(timeZone,appointmentDto.getStartTime());

        if (appointmentDto.getVenue() == null) {
            throw new BadRequestException("Venue not specified");
        }

        com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(appointmentDto.getService().getServiceId());

        if (appointmentDto.getAttendant() == null) {
            if (service.getPetParentCanSelectAttendant() == null || service.getPetParentCanSelectAttendant()) {
                throw new BadRequestException("Attendant not specified");
            }
        }

        Appointment appointment = AppointmentMapper.toAppointmentEntity(appointmentDto);
        if (appointmentDto.getId() != null) appointment.setId(appointmentDto.getId());

        ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(appointmentDto.getServiceType().getId());
        if (serviceType != null)
            appointment.setServiceType(serviceType);
        else
            throw new EntityNotFoundException("Service Type Not Found for Id :: " + appointmentDto.getServiceType().getId());

        if (service != null)
            appointment.setService(service);
        else
            throw new EntityNotFoundException("Service Not Found for Id :: " + appointmentDto.getService().getServiceId());

        Venue venue = venueRepository.findByVenueId(appointmentDto.getVenue().getId());
        if (venue != null)
            appointment.setVenue(venue);
        else
            throw new EntityNotFoundException("Venue Not Found for Id :: " + appointmentDto.getVenue().getId());

        if (appointmentDto.getShamppo() != null) {
            Optional<PetShampoo> petShampoo = petShampooRepository.findById(appointmentDto.getShamppo().getId());
            petShampoo.ifPresent(appointment::setShamppo);
        }

        if (appointmentDto.getCologne() != null) {
            Optional<PetCologne> petCologne = petCologneRepository.findById(appointmentDto.getCologne().getId());
            petCologne.ifPresent(appointment::setCologne);
        }

        if (service.getScheduleType().equals("FIXED")) {
            appointment.setDuration(service.getFixedScheduleValue() + " " + service.getFixedScheduleUnit() + "(s)");
        } else {
            appointment.setDuration(service.getVariableScheduleMinValue() + " - "
                    + service.getVariableScheduleMaxValue() + " " + service.getFixedScheduleUnit() + "(s)");
        }

        if (appointmentDto.getAttendant() != null) {
            //todo: add validation for when PetParentCanSelectAttendant
            Attendant attendant = attendantRepository.findByAttendantId(appointmentDto.getAttendant().getAttendantId());
            if (attendant != null)
                appointment.setAttendant(attendant);
            else
                throw new EntityNotFoundException("Specified Attendant Not Found");
        } else {
            if (service.getPetParentCanSelectAttendant()) {
                throw new BadRequestException("Attendant not selected for appointment");
            }
        }
        if (appointmentDto.getAppointmentOverride()) {
            LocalDate appointmentDate = appointmentDto.getStartTime().toLocalDate();
            //todo:change this to end date  implementation overnight service duration is done.
            LocalDate appointmentEndDate = appointmentDto.getEndTime().toLocalDate();
               /*     ZonedDateTime.parse(
                    LocalDate.parse(appointmentDto.getDate(), monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(appointmentDto.getStartTime(), timeFormatter).toString() +
                            OffsetContext.getOffset()
            ).toInstant().atOffset(ZoneOffset.UTC).toLocalDate();*/
            LOGGER.debug("APPOINTMENT OVERRIDE - Date: {}", appointmentDate);
            OffsetTime appointmentStartTime = appointmentDto.getStartTime().toOffsetTime();
               /*     ZonedDateTime.parse(
                    LocalDate.parse(appointmentDto.getDate(), monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(appointmentDto.getStartTime(), timeFormatter).toString() +
                            OffsetContext.getOffset()
            ).toInstant().atOffset(ZoneOffset.UTC).toOffsetTime();*/
            LOGGER.debug("APPOINTMENT OVERRIDE - Start Time: {}", appointmentStartTime);
            OffsetTime appointmentEndTime = appointmentDto.getEndTime().toOffsetTime();
               /*     ZonedDateTime.parse(
                    LocalDate.parse(appointmentDto.getDate(), monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(appointmentDto.getEndTime(), timeFormatter).toString() +
                            OffsetContext.getOffset()
            ).toInstant().atOffset(ZoneOffset.UTC).toOffsetTime();*/
            LOGGER.debug("APPOINTMENT OVERRIDE - End Time: {}", appointmentEndTime);

            Duration duration = Duration.between(appointmentStartTime, appointmentEndTime);
            appointment.setDate(
                    appointmentDate);
            appointment.setEndDate(appointmentEndDate);
            appointment.setTime(appointmentStartTime);
            appointment.setEndTime(appointmentEndTime);
            
            // Trim seconds from the UTC timestamps to maintain consistency with manual slots
            appointment.setAppointmentStartDateAndTime(trimSecondsFromDateTime(appointmentDto.getStartTime()).toString());
            appointment.setAppointmentEndDateAndTime(trimSecondsFromDateTime(appointmentDto.getEndTime()).toString());
            appointment.setIsOverride(Boolean.TRUE);
            appointment.setDuration(duration.getSeconds() + "(s)");
            
            LOGGER.debug("APPOINTMENT OVERRIDE - Stored appointmentStartDateAndTime: {}", appointment.getAppointmentStartDateAndTime());

        } else if (appointmentDto.getEnableSlot()) {
            LocalDate appointmentDate = appointmentDto.getStartTime().toLocalDate();
            LocalDate appointmentEndDate = appointmentDto.getSlotEndTime().toLocalDate();
            OffsetTime appointmentStartTime = appointmentDto.getStartTime().toOffsetTime();
            OffsetTime appointmentEndTime = appointmentDto.getSlotEndTime().toOffsetTime();

            Duration duration = Duration.between(appointmentStartTime, appointmentEndTime);
            appointment.setDate(appointmentDate);
            appointment.setEndDate(appointmentEndDate);
            appointment.setTime(appointmentStartTime);
            appointment.setEndTime(appointmentEndTime);
            
            // Trim seconds from the UTC timestamps to maintain consistency
            appointment.setAppointmentStartDateAndTime(trimSecondsFromDateTime(appointmentDto.getStartTime()).toString());
            appointment.setAppointmentEndDateAndTime(trimSecondsFromDateTime(appointmentDto.getSlotEndTime()).toString());
            appointment.setDuration(duration.getSeconds() + "(s)");
            appointment.setIsManualSlots(Boolean.TRUE);
            appointment.setOffsetAppointmentDate(appointmentDto.getStartTime().toLocalDate());
            appointment.setTimeSlotId(appointmentDto.getTimeSlotId());
            
            LOGGER.debug("MANUAL SLOTS - Stored appointmentStartDateAndTime: {}", appointment.getAppointmentStartDateAndTime());
        } else if (
                (service.getPetparentCanSelectDatetime() != null && service.getPetparentCanSelectDatetime())
                        || RequestTypeContext.getRequestType().equals("Retailer")
        ) {

            appointment.setDate(appointmentDto.getStartTime().toLocalDate()
                  /*  ZonedDateTime.parse(
                        LocalDate.parse(appointmentDto.getDate(), monthDateYearFormat).toString() + "T" +
                        LocalTime.parse(appointmentDto.getStartTime(), timeFormatter).toString() +
                        OffsetContext.getOffset()
                    ).toInstant().atOffset(ZoneOffset.UTC).toLocalDate()*/
            );
            //todo:change this when overnight serviceDuration is completed
            appointment.setEndDate(calculateEndDateTime(appointmentDto.getStartTime(), service).toLocalDate());
            appointment.setTime(appointmentDto.getStartTime().toOffsetTime()
                   /* ZonedDateTime.parse(
                            LocalDate.parse(appointmentDto.getDate(), monthDateYearFormat).toString() + "T" +
                            LocalTime.parse(appointmentDto.getStartTime(), timeFormatter).toString() +
                            OffsetContext.getOffset()
                    ).toInstant().atOffset(ZoneOffset.UTC).toOffsetTime()*/
            );
            appointment.setEndTime(calculateEndDateTime(appointmentDto.getStartTime(), service).toOffsetTime());
            appointment.setAppointmentStartDateAndTime(appointmentDto.getStartTime().toString());
            appointment.setAppointmentEndDateAndTime(calculateEndDateTime(appointmentDto.getStartTime(), service).toString());
        } /*else {
            OffsetDateTime dateToBeChecked = Instant.now().atOffset(ZoneOffset.of(OffsetContext.getOffset()));
            int daysChecked = 0;
            List<Map<String,List<AppointmentSlotsDto>>> availableSlots;
            while (daysChecked <= (lookAhead * 31)) {
                BookingDto bookingInfo = BookingDto.builder()
                        .serviceId(appointment.getService().getServiceId())
                        .venueId(appointment.getVenue().getVenueId())
                        .attendantId(appointment.getAttendant() == null ? null : appointment.getAttendant().getAttendantId())
                        .appointmentDate(dateToBeChecked)
                        .appointmentDate(dateToBeChecked)
                        .build();
                availableSlots = getAvailableAppointmentSlots(bookingInfo, true, timeZone);
                if (availableSlots.isEmpty()) {
                    daysChecked++;
                    dateToBeChecked = dateToBeChecked.plusDays(1);
                } else {
                    if (!service.getPetParentCanSelectAttendant()) {
                        Attendant attendant = attendantRepository.findByAttendantId(availableSlots.get(0).getAttendantId());
                        if (attendant != null)
                            appointment.setAttendant(attendant);
                    }
                    ZonedDateTime dateTime = ZonedDateTime.parse(availableSlots.get(0).getSlotStartTime().toString()
                    ).toInstant().atOffset(ZoneOffset.UTC).toZonedDateTime();
                    appointment.setDate(dateTime.toLocalDate());
                    //todo:change this when overnight serviceDuration is completed
                    appointment.setEndDate(dateTime.toLocalDate());
                    appointment.setTime(dateTime.toOffsetDateTime().toOffsetTime());
                    appointment.setAppointmentStartDateAndTime(dateTime.toOffsetDateTime().toString());
                    appointment.setAppointmentEndDateAndTime(dateTime.toOffsetDateTime().toString());
                    break;
                }
            }
            if (daysChecked >= (lookAhead * 31)) {
                throw new BadRequestException("No timeslots are available for appointment");
            }
        }*/
        if (!appointmentDto.getIsEditable()) {
            List<Appointment> existingAppointments = appointmentRepository.getAppointmentsByDateTimeAndPetAndServiceStatus(
                    appointment.getPet(), appointment.getDate(), appointment.getTime(), ServiceStatus.CANCELLED
            );

            if (!existingAppointments.isEmpty()) {
                throw new EtailBookItException("Selected pet has another appointment at selected time");
            }
        }

        List<Appointment> existingAttendantsAppointments = appointmentRepository.findByDateTimeServiceStatusAttendantAndRetailer(appointmentDto.getStartTime().toString(), ServiceStatus.CANCELLED, appointment.getAttendant().getAttendantId(), RetailerContext.getRetailer());
        if (!existingAttendantsAppointments.isEmpty()) {
            Integer appointmentListSize = existingAttendantsAppointments.size();
            if (appointmentListSize + 1 > appointment.getAttendant().getCapacityLimit()) {
                throw new EtailBookItException("Selected Attendant has reached the maximum capacity to service the pet simultaneously");
            }
        }
        List<Appointment> existingVenueAppointments = appointmentRepository.findByDateTimeServiceStatusVenueAndRetailer(appointmentDto.getStartTime().toString(), ServiceStatus.CANCELLED, appointment.getVenue().getVenueId(), RetailerContext.getRetailer());
        if (!existingVenueAppointments.isEmpty()) {
            Integer appointmentListSize = existingVenueAppointments.size();
            if (appointmentListSize + 1 > appointment.getVenue().getParticipantLimitService()) {
                throw new EtailBookItException("Selected Venue has reached the maximum capacity to service the pet simultaneously");
            }
        }

        //checking the attendant pet size limit
        int newAppointmentSizeConstraintCapacity = 0;
        Attendant requiredAttendant = attendantRepository.findByAttendantId(appointmentDto.getAttendant().getAttendantId());
        List<Appointment> currentAppointments = appointmentRepository.findByDate(appointmentDto.getStartTime().plusMinutes(offsetTimeDifference).toLocalDate());
        for (Appointment currentappointment : currentAppointments) {
            if (currentappointment.getAttendant().getAttendantId().equals(requiredAttendant.getAttendantId())) {
                for (PetSizeLimit petSizeLimit : requiredAttendant.getPetSizeLimits()) {
                    for (GeneralPetSize generalPetSize : currentappointment.getPetType().getGeneralPetSizes()) {
                        if (petSizeLimit.getGeneralPetSize().equals(generalPetSize)) {
                            newAppointmentSizeConstraintCapacity = petSizeLimit.getCapacity();
                            newAppointmentSizeConstraintCapacity = newAppointmentSizeConstraintCapacity - 1;
                            if (newAppointmentSizeConstraintCapacity <= 0) {
                                throw new BadRequestException("Attendant has reached the pet size limit for this day");
                            }
                        }
                    }
                }
            }
        }
      /*  if (appointmentRepository.getLastAppointment(RetailerContext.getRetailer()) != null) {
            appointment.setAppoinmentNo(
                    "#" + (appointmentRepository.getLastAppointment(RetailerContext.getRetailer()) + 1)
            );
        } else {
            appointment.setAppoinmentNo("#1");
        }*/

        if (appointmentDto.getDateTimeOnboarding() != null && !appointmentDto.getDateTimeOnboarding().isEmpty())
            appointment.setDateTimeOnboarding(LocalDate.parse(appointmentDto.getDateTimeOnboarding(), monthDateYearFormat));

        if (appointmentDto.getAddOnService() != null) {
            Set<AddonService> addonServiceSet = new HashSet<>();
            for (AddonServiceDto addonServiceDto : appointmentDto.getAddOnService()) {
                AddonService addonService = addonServiceRepository.findByAddonServiceId(addonServiceDto.getId());
                addonServiceSet.add(addonService);
            }
            appointment.setAddOnService(addonServiceSet);
        }
        BigDecimal quoteAdjustmentPriceSum = BigDecimal.ZERO;
        if (appointmentDto.getAdjustmentQuotes() != null) {
            Validator.validateQuoteService(appointmentDto.getAdjustmentQuotes());
            Set<QuoteAdjustments> quoteAdjustmentsSet = new HashSet<>();
            if (appointmentDto.getId() != null) {
                Set<QuoteAdjustments> existingQuoteAdjustmentsSet = quoteAdjustmentRepository.findAllByAppointmentId(appointmentDto.getId());
                for (QuoteAdjustments existingQuoteAdjustments : existingQuoteAdjustmentsSet) {
                    boolean found = false;
                    for (QuoteAdjustmentDto quoteAdjustmentDto : appointmentDto.getAdjustmentQuotes()) {
                        if (quoteAdjustmentDto.getQuoteAdjustmentId() != null
                                && quoteAdjustmentDto.getQuoteAdjustmentId().equals(existingQuoteAdjustments.getQuoteAdjustmentId())) {
                            found = true;
                            break;
                        }
                    }
                    if (!found) quoteAdjustmentRepository.deleteById(existingQuoteAdjustments.getQuoteAdjustmentId());
                }

            }
            for (QuoteAdjustmentDto quoteAdjustmentDto : appointmentDto.getAdjustmentQuotes()) {
                QuoteAdjustments quoteAdjustments = new QuoteAdjustments();
                if (quoteAdjustmentDto.getQuoteAdjustmentId() != null)
                    quoteAdjustments.setQuoteAdjustmentId(quoteAdjustmentDto.getQuoteAdjustmentId());
                quoteAdjustments.setQuoteName(quoteAdjustmentDto.getQuotename());
                quoteAdjustments.setQuotePrice(quoteAdjustmentDto.getQuoteprice());
                quoteAdjustments.setRetailer(RetailerContext.getRetailer());
                quoteAdjustments.setAppointment(appointment);
                quoteAdjustmentsSet.add(quoteAdjustments);
                quoteAdjustmentPriceSum = quoteAdjustmentPriceSum.add(quoteAdjustmentDto.getQuoteprice());
            }
            appointment.setQuoteAdjustments(quoteAdjustmentsSet);
            appointment.setQuoteAdjustmentPrice(quoteAdjustmentPriceSum);
        }

        Optional<Pet> pet = petRepository.findById(appointmentDto.getPet().getId());
        if (pet.isPresent()) {
            Set<PetTypeConfiguration> petTypeConfiguration=pet.get().getPetType().getPetTypeConfigurations();
            for(PetTypeConfiguration petTypeConfiguration1:petTypeConfiguration){
                if(petTypeConfiguration1.getName().equals(PetTypeConfig.WEIGHT_RANGE)){
                    if(!petTypeConfiguration1.getDisplayType().equals(DisplayType.NONE)){
                        if(appointmentDto.getExactWeight()==null && (appointmentDto.getWeightRangeDetails()==null || appointmentDto.getWeightRangeDetails().getId()==null)){
                            throw new BadRequestException("Please enter pet weight");
                        }
                    }
                }
            }
            Set<PetBreedsInformation> petBreedsInformations = new HashSet<>();
            if (appointmentDto.getPet().getPetBreedsInfos() != null && RequestTypeContext.getRequestType().equals("Retailer")) {
                List<Integer> validIds = new ArrayList<>();
                appointmentDto.getPet().getPetBreedsInfos().forEach(value -> {
                    if (value.getId() != null) validIds.add(value.getId());
                });
                if (validIds.size() == 0) {
                    petBreedsInformationRepository.deleteAllByPetId(appointmentDto.getPet().getId());
                } else {
                    petBreedsInformationRepository.deleteAllByIdNotInAndPetId(validIds, appointmentDto.getPet().getId());
                }
                for (PetBreedsInfoDto petBreedsInfoDto : appointmentDto.getPet().getPetBreedsInfos()) {
                    Optional<Breed> optionalBreed = breedRepository.findById(petBreedsInfoDto.getBreedId());
                    if (!optionalBreed.isPresent()) {
                        throw new EntityNotFoundException("Breed information not found");
                    }
                    PetBreedsInformation newPetBreedsInformation = new PetBreedsInformation();
                    if (petBreedsInfoDto.getId() != null) {
                        newPetBreedsInformation.setId(petBreedsInfoDto.getId());
                    }
                    newPetBreedsInformation.setBreed(optionalBreed.get());
                    newPetBreedsInformation.setRetailer(RetailerContext.getRetailer());
                    newPetBreedsInformation.setPet(pet.get());
                    petBreedsInformations.add(newPetBreedsInformation);
                }
            } else if (RequestTypeContext.getRequestType().equals("Retailer")) {
                petBreedsInformationRepository.deleteAllByPetId(appointmentDto.getPet().getId());
            }
                pet.get().setPetBreedsInformations(petBreedsInformations);
                if (appointmentDto.getWeightUnit() != null) {
                    appointment.setWeightUnit(appointmentDto.getWeightUnit());
                    pet.get().setWeightUnit(appointmentDto.getWeightUnit());
                }
                if (appointmentDto.getWeightRangeDetails() != null && appointmentDto.getWeightRangeDetails().getId() != null) {
                    Optional<WeightRange> weightRange = weightRangeRepository.findById(appointmentDto.getWeightRangeDetails().getId());
                    if(weightRange.isPresent()){
                        appointment.setWeightRange(weightRange.get());
                        appointment.setExactWeight(BigDecimal.ZERO);
                        pet.get().setExactWeight(BigDecimal.ZERO);
                        pet.get().setWeightRange(weightRange.get());
                    }

                } else if(appointmentDto.getExactWeight() != null) {
                        appointment.setExactWeight(appointmentDto.getExactWeight());
                        pet.get().setExactWeight(appointmentDto.getExactWeight());
                }
                petRepository.save(pet.get());

            appointment.setPet(pet.get());
        } else
            throw new EntityNotFoundException("Pet not found");

        if (appointmentDto.getPetTypeDetails() != null && appointmentDto.getPetTypeDetails().getId() != null) {
            PetProjection petTypeName = petTypeRepository.getPetTypeName(appointmentDto.getPetTypeDetails().getId());
            if (petTypeName == null)
                throw new EntityNotFoundException("Pet type not found with ID :::" + appointmentDto.getPetTypeDetails().getId());

            PetType petType = new PetType();
            petType.setPetTypeId(appointmentDto.getPetTypeDetails().getId());
            petType.setName(petTypeName.getPetTypeName());
            appointment.setPetType(petType);
        }

        if (appointmentDto.getTemperamentDetails() != null && appointmentDto.getTemperamentDetails().getId() != null) {
            Temperament temperament = temperamentRepository.findByTemperamentId(appointmentDto.getTemperamentDetails().getId());
            if (temperament == null) {
                throw new EntityNotFoundException("Temperament not found with ID :::" + appointmentDto.getTemperamentDetails().getId());
            }
            appointment.setTemperament(temperament);
        }

        if (appointmentDto.getHairLengthDetails() != null && appointmentDto.getHairLengthDetails().getId() != null) {
            Optional<HairLength> hairLength = hairLengthRepository.findById(appointmentDto.getHairLengthDetails().getId());
            if (!hairLength.isPresent()) {
                throw new EntityNotFoundException("Hair Length not found with ID :::" + appointmentDto.getHairLengthDetails().getId());
            }
            appointment.setHairLength(hairLength.get());
        }

        if (appointmentDto.getHairTextureDetails() != null && appointmentDto.getHairTextureDetails().getId() != null) {
            Optional<HairTexture> hairTexture = hairTextureRepository.findById(appointmentDto.getHairTextureDetails().getId());
            if (!hairTexture.isPresent()) {
                throw new EntityNotFoundException("Hair Texture not found with ID :::" + appointmentDto.getHairTextureDetails().getId());
            }
            appointment.setHairTexture(hairTexture.get());
        }

        if (appointmentDto.getDesiredHairLengths() != null) {
            Set<AppointmentDesiredHairLengths> appDesiredHair = new HashSet<>();
            for (DesiredHairLengthDto dto : appointmentDto.getDesiredHairLengths()) {
                if (dto.getId() != null) {
                    Optional<DesiredHairLength> hairLength = desiredHairLengthRepository.findById(dto.getId());
                    AppointmentDesiredHairLengths newApp = new AppointmentDesiredHairLengths();
                    newApp.setAppointment(appointment);
                    hairLength.ifPresent(newApp::setDesiredHairLength);
                    newApp.setValue(dto.getValue());
                    newApp.setRetailer(RetailerContext.getRetailer());
                    appDesiredHair.add(newApp);
                }

            }

            appointment.setDesiredHairLengths(appDesiredHair);

        }

        if (appointmentDto.getAllergies() != null) {
            Set<Allergies> allergiesSet = new HashSet<>();
            for (AllergiesDto allergiesDto : appointmentDto.getAllergies()) {
                Allergies allergies = allergiesRepository.findByAllergyId(allergiesDto.getId());
                allergiesSet.add(allergies);
            }
            appointment.setAllergies(allergiesSet);
        }

        if (appointmentDto.getVaccinationInfo() != null) {
            Set<AppointmentVaccinationInformation> appointmentVaccinationInformationSet = new HashSet<>();
            for (VaccinationRecordsDto vaccinationRecordsDto : appointmentDto.getVaccinationInfo()) {
                PetVaccinationRecords petVaccinationRecords = new PetVaccinationRecords();
                Optional<VaccinationRecords> vaccinationRecords = vaccinationRecordsRepository.findById(vaccinationRecordsDto.getId());
                Optional<PetVaccinationRecords> petVaccinationRecordsOptional = petVaccinationRecordRepository.findByPetIdAndVaccinationRecordsVaccinationRecordId(pet.get().getId(), vaccinationRecordsDto.getId());
                if (petVaccinationRecordsOptional.isPresent()) {
                    petVaccinationRecords = petVaccinationRecordsOptional.get();
                }
                AppointmentVaccinationInformation appointmentVaccinationInformation = new AppointmentVaccinationInformation();
                appointmentVaccinationInformation.setAppointment(appointment);
                if (vaccinationRecords.isPresent()) {
                    appointmentVaccinationInformation.setVaccinationRecords(vaccinationRecords.get());
                    petVaccinationRecords.setVaccinationRecords(vaccinationRecords.get());
                    petVaccinationRecords.setPet(pet.get());
                } else {
                    throw new EntityNotFoundException("VaccinationRecord not found with ID :::" + vaccinationRecordsDto.getId());
                }
                if (vaccinationRecords.get().getRequireDateAdministrated() && ObjectUtils.isEmpty(vaccinationRecordsDto.getDateAdministrated()))
                    throw new BadRequestException("Vaccination Administrated Date is required");
                if (vaccinationRecords.get().getRequireDateExpires() && ObjectUtils.isEmpty(vaccinationRecordsDto.getDateExpires()))
                    throw new BadRequestException("Vaccination Expires Date is required");
                if (vaccinationRecords.get().getRequireVaccinationDocument() && ObjectUtils.isEmpty(vaccinationRecordsDto.getFile()) && ObjectUtils.isEmpty(vaccinationRecordsDto.getFileURL()))
                    throw new BadRequestException("Vaccination Document is required");
                if (vaccinationRecords.get().getRequireDateAdministrated()) {
                    try {
                        appointmentVaccinationInformation.setDateAdministrated(LocalDate.parse(vaccinationRecordsDto.getDateAdministrated()));
                        petVaccinationRecords.setDateAdministrated(LocalDate.parse(vaccinationRecordsDto.getDateAdministrated()));
                    } catch (Exception e) {
                        throw new BadRequestException("Invalid date administrated format");
                    }
                }
                if (vaccinationRecords.get().getRequireDateExpires()) {
                    try {
                        appointmentVaccinationInformation.setDateExpires(LocalDate.parse(vaccinationRecordsDto.getDateExpires()));
                        petVaccinationRecords.setDateExpires(LocalDate.parse(vaccinationRecordsDto.getDateExpires()));
                    } catch (Exception e) {
                        throw new BadRequestException("Invalid date expires format");
                    }
                }

                if (vaccinationRecords.get().getRequireVaccinationDocument()) {
                    try {
                        if (vaccinationRecordsDto.getFile() != null) {
                            String awsUrl = FileUploadUtil.uploadFile(vaccinationRecordsDto.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.DocumentsORPhotos);
                            appointmentVaccinationInformation.setFile(awsUrl);
                            petVaccinationRecords.setFile(awsUrl);
                        } else if (vaccinationRecordsDto.getFileURL() != null) {
                            appointmentVaccinationInformation.setFile(vaccinationRecordsDto.getFileURL());
                            petVaccinationRecords.setFile(vaccinationRecordsDto.getFileURL());
                        }
                    } catch (Exception e) {
                        throw new EtailBookItException("Exception occurred while file upload");
                    }
                }
                appointmentVaccinationInformationSet.add(appointmentVaccinationInformation);
                petVaccinationRecordRepository.save(petVaccinationRecords);
            }
            appointment.setVaccinationInfo(appointmentVaccinationInformationSet);
            if(!service.getAvailableParticipantVaccinations().isEmpty()){
                if(appointmentDto.getVaccinationInfo().size()!=service.getAvailableParticipantVaccinations().size()){
                    throw new EtailBookItException("Require all the vacciantions configured in the service");
                }
            }
        } else if (!service.getAvailableParticipantVaccinations().isEmpty()) {
            throw new EtailBookItException("Vaccination information is required as it is configured in service");
        }
        LOGGER.info("VALUE Of boolean reopened "+ reopened);
        if(!reopened){
            if (appointmentDto.getWaiverOfLiabilityDoc() != null && appointmentDto.getWaiverOfLiabilityDoc().getId() != null) {
                appointment.setWaiverOfLiabilityDoc(generateWaiverOfLiabilityInformation(appointment, appointmentDto));

            } else {
                appointment.setWaiverOfLiabilityDoc(null);
            }
        }
        if(!appointmentDto.isWaiverOfLiabilityAcknowledged() && RequestTypeContext.getRequestType().equals("Retailer")) {
            appointment.setWaiverAcknowledged(appointmentDto.isWaiverOfLiabilityAcknowledged());
        }

        if (appointmentDto.getOtherDoc() != null) {
            Set<AppointmentDocuments> documentOptions = new HashSet<>();
            for (DocumentOptionDto documentOptionDto : appointmentDto.getOtherDoc()) {
                AppointmentDocuments otherDocuments = new AppointmentDocuments();
                // PetDocuments petDocuments=new PetDocuments();
                PetDocuments petDocuments = petDocumentRepository.findByDocumentOptionIdAndPetId(documentOptionDto.getId(), pet.get().getId());
                Optional<DocumentOption> documentOption = documentOptionRepository.findById(documentOptionDto.getId());
                otherDocuments.setAppointment(appointment);
                if (documentOption.isPresent()) {
                    otherDocuments.setDocumentOption(documentOption.get());
                    petDocuments.setDocumentOption(documentOption.get());
                } else {
                    throw new EntityNotFoundException("Document not found with ID :::" + documentOptionDto.getId());
                }

                if (ObjectUtils.isEmpty(documentOptionDto.getFile()) && ObjectUtils.isEmpty(documentOptionDto.getFileURL()) && documentOption.get().getRequireUpload()) {
                    throw new EntityNotFoundException("Other documents file or file url not found");
                }

                if (documentOption.get().getRequireDescription() && ObjectUtils.isEmpty(documentOptionDto.getDescription()))
                    throw new EntityNotFoundException("Other documents description not found");

                try {
                    if (!ObjectUtils.isEmpty(documentOptionDto.getFile())) {
                        String awsUrl = FileUploadUtil.uploadFile(documentOptionDto.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.DocumentsORPhotos);
                        otherDocuments.setFile(awsUrl);
                        petDocuments.setFile(awsUrl);
                    } else if (!ObjectUtils.isEmpty(documentOptionDto.getFileURL())) {
                        otherDocuments.setFile(documentOptionDto.getFileURL());
                        petDocuments.setFile(documentOptionDto.getFileURL());
                    }
                } catch (Exception e) {
                    throw new EtailBookItException("Exception occurred while file upload");
                }

                if (!ObjectUtils.isEmpty(documentOptionDto.getDescription()))
                    otherDocuments.setDescription(documentOptionDto.getDescription());
                petDocuments.setDescription(documentOptionDto.getDescription());
                documentOptions.add(otherDocuments);
                petDocumentRepository.save(petDocuments);
            }
            appointment.setOtherDoc(documentOptions);
        }

        if (appointmentDto.getVetInformation() != null) {
            Set<AppointmentVetInformation> appointmentVetInformations = new HashSet<>();
            for (VetInformationDto vetInformationDto : appointmentDto.getVetInformation()) {
                Optional<VetInformation> vetInformation = vetInformationRepository.findById(vetInformationDto.getId());
                Optional<PetVetInformation> petVetInformations = petVetInformationRepository.findByPetIdAndVetInformationId(pet.get().getId(), vetInformationDto.getId());
                if (petVetInformations.isPresent()) {
                    PetVetInformation petVetInformation = petVetInformations.get();
                    petVetInformation.setValue(vetInformationDto.getValue());
                    petVetInformation.setRetailer(RetailerContext.getRetailer());
                    if (vetInformation.isPresent()) {
                        petVetInformation.setVetInformation(vetInformation.get());
                    }
                    petVetInformationRepository.save(petVetInformation);
                }
                AppointmentVetInformation appointmentVetInformation = new AppointmentVetInformation();
                appointmentVetInformation.setAppointment(appointment);
                if (vetInformation.isPresent()) {
                    appointmentVetInformation.setVetInformation(vetInformation.get());
                    // petVetInformation.setVetInformation(vetInformation.get());
                } else {
                    throw new EntityNotFoundException("VetInformation not found with ID :::" + vetInformationDto.getId());
                }
                appointmentVetInformation.setValue(vetInformationDto.getValue());
                appointmentVetInformation.setRetailer(RetailerContext.getRetailer());
                appointmentVetInformations.add(appointmentVetInformation);
                // petVetInformation.setValue(vetInformationDto.getValue());
                //petVetInformation.setRetailer(RetailerContext.getRetailer());
                // petVetInformationRepository.save(petVetInformation);
            }
            appointment.setAppointmentVetInformation(appointmentVetInformations);
        }

        if (appointmentDto.getEmergencyInfo() != null) {
            Set<AppointmentEmergencyContactInfo> emergencyContactInfos = new HashSet<>();
            for (EmergencyContactInfoDto emergencyContactInfo : appointmentDto.getEmergencyInfo()) {
                EmergencyContactInfo contactInfo = emergencyContactInfoRepository.findByEmergencyContactInfoId(emergencyContactInfo.getId());
                AppointmentEmergencyContactInfo appointmentEmergencyContactInfo = new AppointmentEmergencyContactInfo();
                appointmentEmergencyContactInfo.setAppointment(appointment);
                if (contactInfo != null) {
                    appointmentEmergencyContactInfo.setEmergencyContactInfo(contactInfo);
                } else {
                    throw new EntityNotFoundException("EmergencyContactInfo not found with ID :::" + emergencyContactInfo.getId());
                }
                appointmentEmergencyContactInfo.setRetailer(RetailerContext.getRetailer());
                appointmentEmergencyContactInfo.setValue(emergencyContactInfo.getValue());
                emergencyContactInfos.add(appointmentEmergencyContactInfo);
            }
            appointment.setAppointmentEmergencyContactInfo(emergencyContactInfos);
        }

        if (appointmentDto.getFeedingCount() != null) {
            appointment.setFeedingCount(appointmentDto.getFeedingCount());
        }

        if (appointmentDto.getBringYourFood() != null) {
            appointment.setBringYourFood(appointmentDto.getBringYourFood());
        }

        if (appointmentDto.getGrainFreeRecipes() != null) {
            appointment.setGrainFreeRecipes(appointment.getGrainFreeRecipes());
        }

        if (appointmentDto.getGrainFullRecipes() != null) {
            appointment.setGrainFullRecipes(appointment.getGrainFullRecipes());
        }

        if (appointmentDto.getOptInForSms() != null && appointmentDto.getOptInForSms()) {
            appointment.setOptInForSms(appointmentDto.getOptInForSms());
            if (appointmentDto.getSmsPhoneNumber() == null)
                throw new BadRequestException("Phone number is required for sending sms");
            if (!appointmentDto.getSmsPhoneNumber().matches("^\\+1\\d{10}$") && !appointmentDto.getSmsPhoneNumber().matches("^[0-9]{10}"))
                throw new BadRequestException("Phone number is not valid");
            appointment.setSmsPhoneNumber(appointmentDto.getSmsPhoneNumber());
        }

        if (appointmentDto.getPersonalityParameters() != null) {
            Set<PersonalityParameter> personalityParameters = new HashSet<>();
            for (PersonalityParameterDto personalityParameterDto : appointmentDto.getPersonalityParameters()) {
                Optional<PersonalityParameter> personalityParameter = personalityParameterRepository.findById(personalityParameterDto.getId());
                if (personalityParameter.isPresent()) {
                    personalityParameters.add(personalityParameter.get());
                } else {
                    throw new EntityNotFoundException("PersonalityParameter not found with ID :::" + personalityParameterDto.getId());
                }
            }
            appointment.setPersonalityParameters(personalityParameters);
        }

        if (appointmentDto.getUnfriendlyBehaviourTriggers() != null) {
            Set<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers = new HashSet<>();
            for (UnfriendlyBehaviourTriggerDto unfriendlyBehaviourTriggerDto : appointmentDto.getUnfriendlyBehaviourTriggers()) {
                Optional<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTrigger = unfriendlyBehaviourTriggerRepository.findById(unfriendlyBehaviourTriggerDto.getId());
                if (unfriendlyBehaviourTrigger.isPresent()) {
                    unfriendlyBehaviourTriggers.add(unfriendlyBehaviourTrigger.get());
                } else {
                    throw new EntityNotFoundException("UnfriendlyBehaviourTrigger not found with ID :::" + unfriendlyBehaviourTriggerDto.getId());
                }
            }
            appointment.setUnfriendlyBehaviourTriggers(unfriendlyBehaviourTriggers);
        }
        if (appointmentDto.getNote() != null) {
            appointment.setNote(appointmentDto.getNote());
        }
        if (appointmentDto.getCustomerName() != null) {
            appointment.setCustomerName(appointmentDto.getCustomerName());
        }
        if (appointmentDto.getServiceCost() != null) {
            appointment.setServiceCost(appointmentDto.getServiceCost());
        }
        appointment.setServiceStatus(ServiceStatus.CREATED);
        if(RequestTypeContext.getRequestType().equals("Customer")){
            appointment.setServiceStatus(ServiceStatus.PENDING_APPROVAL);
        }
        appointment.setPaymentStatus(OrderStatus.PENDING.name());
        appointment.setAddress(appointmentDto.getAddress());
        appointment.setSource("Bookit Appointment");
        appointment.setRetailer(RetailerContext.getRetailer());
        if(appointmentDto.getCreatedCustomerId()!=null && appointmentDto.getCreatedCustomerName() !=null){
            appointment.setCreatedCustomerId(appointmentDto.getCreatedCustomerId());
            appointment.setCreatedCustomerName(appointmentDto.getCreatedCustomerName());
        }
        if(appointmentDto.getUpdatedCustomerId()!=null && appointmentDto.getUpdatedCustomerName()!=null){
            appointment.setUpdatedCustomerId(appointmentDto.getUpdatedCustomerId());
            appointment.setUpdatedCustomerName(appointmentDto.getUpdatedCustomerName());
        }


        return appointment;

    }

    RestTemplate restTemplate = new RestTemplate();

    public void saveCardToEcom(AppointmentDto appointmentDto) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
       // String url = auth_Uri + RetailerContext.getRetailer() + "/api/v1/messages/send/";
        String url= auth_Uri + RetailerContext.getRetailer() +"/api/v1/customer/"+appointmentDto.getCustomerId()+"/cards/"+appointmentDto.getStoreId()+"/create/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        CardSavingDto cardSavingDto=new CardSavingDto();
        cardSavingDto.setName(appointmentDto.getFirstName().concat(" " +appointmentDto.getLastName()));
        cardSavingDto.setCustomer_id(appointmentDto.getCustomerId());
        cardSavingDto.setStore_id(appointmentDto.getStoreId());
        cardSavingDto.setCard_token(appointmentDto.getPaymentmethodId());
        cardSavingDto.setIs_default(Boolean.FALSE);
        cardSavingDto.setCard_expiry("none");

        HttpEntity<CardSavingDto> request = new HttpEntity<>(cardSavingDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    public Integer getAvailableSlotsCounterByDate(Integer serviceId,
                                                  Integer venueId,
                                                  // Integer attendantId,
                                                  Integer serviceDuration,
                                                  OffsetDateTime offsetDateTime,
                                                  List<BlockDateInfo> blockDateInfoList, String timeZone) throws BadRequestException {
       List<AppointmentSlotsDto> allFreeSlots=new ArrayList<>();
        Venue currentVenue = venueRepository.findByVenueId(venueId);
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        LocalTime venueStartTime = null;
        LocalTime venueEndTime = null;
        DaysOfWeek daysOfWeek = new DaysOfWeek();

        if (currentVenue.getParticipantLimitService() <= 0) {
            if (RequestTypeContext.getRequestType().equals("Retailer")) {
                throw new BadRequestException("Venue capacity is improperly configured");
            } else {
                throw new BadRequestException("Venue is unavailable");
            }
        }

        // Goes through venues availability and sets the opening and closing time of the venue for the required day of the week
        for (VenueAvailability day : currentVenue.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                venueStartTime = day.getAvailabilityOpenTime().toLocalTime();
                venueEndTime = day.getAvailabilityCloseTime().toLocalTime();
                break;
            }
        }
        if (venueStartTime == null) {
            return 0;
        }

        //Checks the schedule type of the service to set the required duration
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(serviceId);
        if (requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }
        if (ServiceScheduleType.FIXED.toString().equals(requiredService.getScheduleType())) {
            serviceDuration = requiredService.getFixedScheduleValue();
        } else if (ServiceScheduleType.VARIABLE.toString().equals(requiredService.getScheduleType())) {
            serviceDuration = Integer.parseInt(requiredService.getVariableScheduleMaxValue());
        } else {
            if (serviceDuration == null) {
                throw new BadRequestException("Service duration Not specified");
            }
        }

        LocalTime serviceStartTime = null;
        LocalTime serviceEndTime = null;

        // Goes through service availability and sets the opening and closing time of the service for the required day of the week
        for (ServiceAvailability day : requiredService.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                serviceStartTime = day.getAvailabilityOpenTime().toLocalTime();
                serviceEndTime = day.getAvailabilityCloseTime().toLocalTime();
                break;
            }
        }
        if (serviceStartTime == null) {
            return 0;
        }

        LocalTime temp1 = serviceStartTime.compareTo(venueStartTime) > 0 ?
                serviceStartTime
                :
                serviceStartTime.compareTo(venueStartTime) < 0 ? venueStartTime : serviceStartTime;
        LocalTime temp2 = serviceEndTime.compareTo(venueEndTime) < 0 ?
                serviceEndTime
                :
                serviceEndTime.compareTo(venueEndTime) > 0 ? venueEndTime : serviceEndTime;

        String appointmentDay=daysOfWeek.getKey(offsetDateTime.getDayOfWeek().toString());

        OffsetTime tempStartTime=OffsetTime.of(temp1,ZoneOffset.UTC).truncatedTo(ChronoUnit.MINUTES);
        OffsetTime tempEndTime=OffsetTime.of(temp2,ZoneOffset.UTC).truncatedTo(ChronoUnit.MINUTES);

        PetType petType=requiredService.getPetType();
        Integer requiredServiceId=requiredService.getServiceId();
        ServiceType serviceType=requiredService.getServiceType();

        //List<AttendantAvailability> attendantAvailability = attendantAvailabilityRepository.findByAvailabilityTimRangeAndRetailer(appointmentDay.toString(),tempStartTime,tempEndTime, RetailerContext.getRetailer(),false,petType.getPetTypeId());
        List<AttendantAvailability> attendantAvailability=attendantAvailabilityRepository.findByAvailableDayServiceTypeVenueAndPetType(appointmentDay.toString(),currentVenue.getVenueId(),petType.getPetTypeId(),RetailerContext.getRetailer(),false);
        if(!attendantAvailability.isEmpty()) {
            for (AttendantAvailability attendantAvailability1 : attendantAvailability) {

               // AttendantModelAvailabilityProjections requiredAttendant = attendantRepository.findAttendantAndAvailability(attendantAvailability1.getAttendantId());
               /* if (requiredAttendant == null && attendantAvailability1.getAttendant().getAttendantId() != null && RequestTypeContext.getRequestType().equals("Retailer")) {
                    throw new BadRequestException("Selected attendant is invalid");
                }*/
                Attendant requiredAttendant=attendantAvailability1.getAttendant();
                Set<ServiceType> requiredServiceTypes=requiredAttendant.getServiceTypes();
                if(!requiredServiceTypes.contains(requiredService.getServiceType())){
                    continue;
                }
                LocalTime attendantStartTime = null;
                LocalTime attendantEndTime = null;

                //todo:  currently does not consider the attendant capacity because it just fetches appointments on that venue
                //Gets all the appointments between specified DateTime interval
                //Need to add multiple day support. Currently, we are only checking one day interval
                List<Appointment> currentAppointments = appointmentRepository.getAppointmentsByVenueAndDate(
                        currentVenue,
                        offsetDateTime.toLocalDate(),
                        offsetDateTime.plusDays(1).toLocalDate()
                );

                List<AppointmentSlotsDto> freeTimeSlots = new ArrayList<>();

                if (requiredAttendant != null) {

                   /* if (requiredAttendant.getCapacityLimit() <= 0) {
                        if (RequestTypeContext.getRequestType().equals("Retailer")) {
                            throw new BadRequestException("Attendant is improperly configured");
                        } else {
                            throw new BadRequestException("Attendant is unavailable");
                        }
                    }*/

                    // Goes through service availability and sets the opening and closing time of the service for the required day of the week
                   // for (AttendantModelAvailabilityProjections.Availability day : requiredAttendant.getAvailabilityDays()) {
                      //  if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                            attendantStartTime = attendantAvailability1.getAvailabilityOpenTime().toLocalTime();
                            attendantEndTime = attendantAvailability1.getAvailabilityCloseTime().toLocalTime();
                         //   break;
                       // }
                  //  }
                    if (attendantStartTime == null) {
                        return 0;
                    }
                    LocalTime startTime = attendantStartTime.compareTo(temp1) > 0 ?
                            attendantStartTime
                            :
                            attendantStartTime.compareTo(temp1) < 0 ? temp1 : attendantStartTime;
                    LocalTime endTime = attendantEndTime.compareTo(temp2) < 0 ?
                            attendantEndTime
                            :
                            attendantEndTime.compareTo(temp2) > 0 ? temp2 : attendantEndTime;

                   /* OffsetTime startTime = attendantStartTime.isAfter(temp1) ?
                            attendantStartTime
                            :
                            attendantStartTime.isBefore(temp1) ? temp1 : attendantStartTime;
                    OffsetTime endTime = attendantEndTime.isBefore(temp2) ?
                            attendantEndTime
                            :
                            attendantEndTime.isAfter(temp2) ? temp2 : attendantEndTime;
*/
                    freeTimeSlots = SchedulerUtils.generateFreeTimeSlotsForDay(
                            currentVenue,
                            requiredAttendant,
                            requiredService,
                            currentAppointments,
                            serviceDuration,
                            requiredService.getFixedScheduleUnit(),
                            OffsetDateTime.of(
                                    offsetDateTime.toLocalDate(),
                                    startTime,
                                    offsetDateTime.getOffset()
                            ),
                            OffsetDateTime.of(
                                    offsetDateTime.toLocalDate(),
                                    endTime,
                                    offsetDateTime.getOffset()
                            )
                           /* OffsetDateTime.parse(
                                    offsetDateTime.toLocalDate().toString() + "T" +
                                            startTime.toString().split("[+\\-Z]")[0] + "Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
                            ,
                            OffsetDateTime.parse(
                                    offsetDateTime.toLocalDate().toString() + "T" +
                                            endTime.toString().split("[+\\-Z]")[0] + "Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()*/,
                            slotInterval
                    );

                }
                List<AppointmentSlotsDto> newFreeSlots = getUnblockedTimeSlotsForCalendar(blockDateInfoList, freeTimeSlots, false);
                allFreeSlots.addAll(newFreeSlots);
            }
            ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(TimeZoneContext.getTimeZone()));
            return (int) allFreeSlots.stream().filter(t ->
                    t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).count();
        }
        return 0;
    }
    public Integer getAvailableSlotsCounterByDate1(Integer serviceId,
                                                  Integer venueId,
                                                 Integer attendantId,
                                                  Integer serviceDuration,
                                                  OffsetDateTime offsetDateTime,
                                                  List<BlockDateInfo> blockDateInfoList, String timeZone) throws BadRequestException {
        Venue currentVenue = venueRepository.findByVenueId(venueId);
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        LocalTime venueStartTime = null;
        LocalTime venueEndTime = null;
        DaysOfWeek daysOfWeek = new DaysOfWeek();

        if (currentVenue.getParticipantLimitService() <= 0) {
            if (RequestTypeContext.getRequestType().equals("Retailer")) {
                throw new BadRequestException("Venue capacity is improperly configured");
            } else {
                throw new BadRequestException("Venue is unavailable");
            }
        }

        // Goes through venues availability and sets the opening and closing time of the venue for the required day of the week
        for (VenueAvailability day : currentVenue.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                venueStartTime = day.getAvailabilityOpenTime().toLocalTime();
                venueEndTime = day.getAvailabilityCloseTime().toLocalTime();
                break;
            }
        }
        if (venueStartTime == null) {
            return 0;
        }

        //Checks the schedule type of the service to set the required duration
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(serviceId);
        if (requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }
        if (ServiceScheduleType.FIXED.toString().equals(requiredService.getScheduleType())) {
            serviceDuration = requiredService.getFixedScheduleValue();
        } else if (ServiceScheduleType.VARIABLE.toString().equals(requiredService.getScheduleType())) {
            serviceDuration = Integer.parseInt(requiredService.getVariableScheduleMaxValue());
        } else {
            if (serviceDuration == null) {
                throw new BadRequestException("Service duration Not specified");
            }
        }

        LocalTime serviceStartTime = null;
        LocalTime serviceEndTime = null;

        // Goes through service availability and sets the opening and closing time of the service for the required day of the week
        for (ServiceAvailability day : requiredService.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                serviceStartTime = day.getAvailabilityOpenTime().toLocalTime();
                serviceEndTime = day.getAvailabilityCloseTime().toLocalTime();
                break;
            }
        }
        if (serviceStartTime == null) {
            return 0;
        }

        Attendant requiredAttendant = attendantRepository.findByAttendantId(attendantId);
        if (requiredAttendant == null && attendantId != null && RequestTypeContext.getRequestType().equals("Retailer")) {
            throw new BadRequestException("Selected attendant is invalid");
        }

                LocalTime attendantStartTime = null;
                LocalTime attendantEndTime = null;

                //todo:  currently does not consider the attendant capacity because it just fetches appointments on that venue
                //Gets all the appointments between specified DateTime interval
                //Need to add multiple day support. Currently, we are only checking one day interval
                List<Appointment> currentAppointments = appointmentRepository.getAppointmentsByVenueAndDate(
                        currentVenue,
                        offsetDateTime.toLocalDate(),
                        offsetDateTime.plusDays(1).toLocalDate()
                );

                List<AppointmentSlotsDto> freeTimeSlots = new ArrayList<>();

                LocalTime temp1 = serviceStartTime.compareTo(venueStartTime) > 0 ?
                        serviceStartTime
                        :
                        serviceStartTime.compareTo(venueStartTime) < 0 ? venueStartTime : serviceStartTime;
                LocalTime temp2 = serviceEndTime.compareTo(venueEndTime) < 0 ?
                        serviceEndTime
                        :
                        serviceEndTime.compareTo(venueEndTime) > 0 ? venueEndTime : serviceEndTime;

                ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(TimeZoneContext.getTimeZone()));

                if (requiredAttendant != null) {

                    if (requiredAttendant.getCapacityLimit() <= 0) {
                        if (RequestTypeContext.getRequestType().equals("Retailer")) {
                            throw new BadRequestException("Attendant is improperly configured");
                        } else {
                            throw new BadRequestException("Attendant is unavailable");
                        }
                    }

                    // Goes through service availability and sets the opening and closing time of the service for the required day of the week
                    for (AttendantAvailability day : requiredAttendant.getAvailabilityDays()) {
                        if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                            attendantStartTime = day.getAvailabilityOpenTime().toLocalTime();
                            attendantEndTime = day.getAvailabilityCloseTime().toLocalTime();
                            break;
                        }
                    }
                    if (attendantStartTime == null) {
                        return 0;
                    }
                    LocalTime startTime = attendantStartTime.compareTo(temp1) > 0 ?
                            attendantStartTime
                            :
                            attendantStartTime.compareTo(temp1) < 0 ? temp1 : attendantStartTime;
                    LocalTime endTime = attendantEndTime.compareTo(temp2) < 0 ?
                            attendantEndTime
                            :
                            attendantEndTime.compareTo(temp2) > 0 ? temp2 : attendantEndTime;

                    //todo :: just commenting out this code as this code change may affect the changes made on the required attendant field based on the ticket BKi-1362.
                  /*  freeTimeSlots = SchedulerUtils.generateFreeTimeSlotsForDay(
                            currentVenue,
                            requiredAttendant,
                            requiredService,
                            currentAppointments,
                            serviceDuration,
                            requiredService.getFixedScheduleUnit(),
                            OffsetDateTime.of(
                                    offsetDateTime.toLocalDate(),
                                    startTime,
                                    offsetDateTime.getOffset()
                            ),
                            OffsetDateTime.of(
                                    offsetDateTime.toLocalDate(),
                                    endTime,
                                    offsetDateTime.getOffset()
                            ),
                            slotInterval
                    );*/

                } else if (attendantId == null) {
            //System should choose the first available attendant that is eligible to perform the appointment.
            Set<Attendant> attendants = requiredService.getAttendants();
            for (Attendant attendant : attendants) {
                for (AttendantAvailability day : attendant.getAvailabilityDays()) {
                    if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(offsetDateTime.getDayOfWeek())) {
                        attendantStartTime = day.getAvailabilityOpenTime().toLocalTime();
                        attendantEndTime = day.getAvailabilityCloseTime().toLocalTime();
                        break;
                    }
                }
                if (attendantStartTime == null) {
                    continue;
                }

                LocalTime startTime = attendantStartTime.compareTo(temp1) > 0 ?
                        attendantStartTime
                        :
                        attendantStartTime.compareTo(temp1) < 0 ? temp1 : attendantStartTime;
                LocalTime endTime = attendantEndTime.compareTo(temp2) > 0 ?
                        attendantEndTime
                        :
                        attendantEndTime.compareTo(temp1) < 0 ? temp1 : attendantEndTime;

                //todo just commenting out this code as this may affect the changes made to the attendant field attendant in the ticket BKI-1362

                /*List<AppointmentSlotsDto> timeSlotCollection = SchedulerUtils.generateFreeTimeSlotsForDay(
                        currentVenue,
                        attendant,
                        requiredService,
                        currentAppointments,
                        serviceDuration,
                        requiredService.getFixedScheduleUnit(),
                        OffsetDateTime.of(
                                offsetDateTime.toLocalDate(),
                                startTime,
                                offsetDateTime.getOffset()
                        ),
                        OffsetDateTime.of(
                                offsetDateTime.toLocalDate(),
                                endTime,
                                offsetDateTime.getOffset()
                        ),
                        slotInterval
                );
                if (!timeSlotCollection.isEmpty()) {
                    List<AppointmentSlotsDto> newTimeSlotCollection = getUnblockedTimeSlotsForCalendar(blockDateInfoList, timeSlotCollection, false);
                    newTimeSlotCollection.forEach(
                            t -> t.setAttendantId(attendant.getAttendantId())
                    );
                    return (int) newTimeSlotCollection.stream().filter(t ->
                            t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).count();
                }*/
            }
            return 0;
        }
                List<AppointmentSlotsDto> newFreeSlots = getUnblockedTimeSlotsForCalendar(blockDateInfoList, freeTimeSlots, false);
                return (int) newFreeSlots.stream().filter(t ->
                        t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).count();
     /*    List<AppointmentSlotsDto> newFreeSlotsFiltered= newFreeSlots.stream().filter(t ->
                t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).collect(Collectors.toList());
        List<AppointmentSlotsDto> removedSlots=new ArrayList<>();
        Integer offsetDifference=calculateOffset(timeZone);
        OffsetDateTime startOffsetDate=offsetDateTime;
        if(offsetDifference<0){
            startOffsetDate=startOffsetDate.minusDays(1);
        }else{
            startOffsetDate=startOffsetDate.plusDays(1);
        }

        for(AppointmentSlotsDto appointmentSlotsDto:newFreeSlotsFiltered){
            OffsetDateTime slotStartTime=appointmentSlotsDto.getSlotStartTime();
            if(offsetDifference<0){
                if(slotStartTime.plusMinutes(offsetDifference).isBefore(startOffsetDate)){
                    removedSlots.add(appointmentSlotsDto);
                }
            }else{
                OffsetDateTime tempDate=offsetDateTime.minusMinutes(offsetDifference);
                if(tempDate.toLocalDate().equals(offsetDateTime.toLocalDate())) {
                    if (slotStartTime.plusMinutes(offsetDifference).toLocalDate().equals(startOffsetDate.toLocalDate())) {
                        removedSlots.add(appointmentSlotsDto);
                    }
                } else if (slotStartTime.plusMinutes(offsetDifference).toLocalDate().isAfter(startOffsetDate.toLocalDate())) {
                    removedSlots.add(appointmentSlotsDto);
                }
            }
        }
        newFreeSlotsFiltered.removeAll(removedSlots);
        return  (int) newFreeSlotsFiltered.stream().count();*/
    }

    @Override
    public List<AvailableSlotsCounterDto> getAvailableAppointmentSlotsCounterByDateRange(Integer serviceId,
                                                                                         Integer venueId,
                                                                                        // Integer attendantId,
                                                                                         Integer serviceDuration,
                                                                                         OffsetDateTime offsetStartDate,
                                                                                         OffsetDateTime offsetEndDate, String timeZone) throws EtailBookItException {
        List<AvailableSlotsCounterDto> availableSlotsCounterList = new ArrayList<>();
        Integer offsetDifference = calculateOffset(timeZone,offsetStartDate);
        //Integer dayLightSavings = checkDayLightSavings(timeZone, offsetStartDate);
       // Integer totalDifference = offsetDifference + dayLightSavings;
        //offsetStartDate=offsetStartDate.plusMinutes(offsetTimeDifference);
        //offsetEndDate=offsetEndDate.plusMinutes(offsetTimeDifference);

        OffsetDateTime startDate = offsetStartDate;
        OffsetDateTime endDate = offsetEndDate;
        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByRetailerAndOffsetBlockDateBetween(RetailerContext.getRetailer(), startDate.toString(), endDate.toString());
        OffsetDateTime currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            AvailableSlotsCounterDto availableSlotsCounterDto = new AvailableSlotsCounterDto();
            availableSlotsCounterDto.setDate(currentDate);
            availableSlotsCounterDto.setAvailableSlots(0);
            boolean blocked = false;
            if (!blockDateInfoList.isEmpty()) {
                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (blockDateInfo.getBlockStartTime() == null
                            && blockDateInfo.getBlockEndTime() == null
                            && currentDate.plusMinutes(offsetDifference).toLocalDate().isEqual(OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetDifference).toLocalDate())
                        //  && !OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).isBefore(currentDate)
                        // &&!OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).isAfter(currentDate)
                    ) {
                        blocked = true;
                        availableSlotsCounterDto.setBlockFullDay(true);
                        break;
                    }
                }
            }

            if (!blocked) {
                availableSlotsCounterDto.setAvailableSlots(getAvailableSlotsCounterByDate(
                        serviceId,
                        venueId,
                      //  attendantId,
                        serviceDuration,
                        //  currentDate.atTime(OffsetTime.of(0, 0, 0, 0, ZoneOffset.of(OffsetContext.getOffset()))),
                        //todo :remove adding offset time difference once availability revamp is completed;
                        currentDate.plusMinutes(offsetDifference),
                        blockDateInfoList, timeZone));
                availableSlotsCounterDto.setBlockTimes(0);

                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (blockDateInfo.getBlockStartTime() != null && blockDateInfo.getBlockEndTime() != null) {
                        LocalDate localBlockStartDate = LocalDate.of(blockDateInfo.getBlockStartTime().plusMinutes(offsetDifference).getYear(), blockDateInfo.getBlockStartTime().plusMinutes(offsetDifference).getMonth(), blockDateInfo.getBlockStartTime().plusMinutes(offsetDifference).getDayOfMonth());
                        LocalDate localBlockEndDate = LocalDate.of(blockDateInfo.getBlockEndTime().plusMinutes(offsetDifference).getYear(), blockDateInfo.getBlockEndTime().plusMinutes(offsetDifference).getMonth(), blockDateInfo.getBlockEndTime().plusMinutes(offsetDifference).getDayOfMonth());
                        if (blockDateInfo.getBlockStartTime() != null
                                && blockDateInfo.getBlockEndTime() != null
                                && !localBlockStartDate.isBefore(currentDate.plusMinutes(offsetDifference).toLocalDate())
                                && !localBlockEndDate.isAfter(currentDate.plusMinutes(offsetDifference).toLocalDate()))
                        // && !OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).isBefore(currentDate.plusMinutes(offsetTimeDifference))
                        //&&!OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).isAfter(currentDate.plusMinutes(offsetTimeDifference)))
                        {
                            availableSlotsCounterDto.setBlockTimes(availableSlotsCounterDto.getBlockTimes() + 1);
                        }
                    }
                }
            }

            availableSlotsCounterList.add(availableSlotsCounterDto);
            currentDate = currentDate.plusDays(1);
        }
        return availableSlotsCounterList;
    }

    public List<AppointmentSlotsDto> getAvailableAppointmentSlots(BookingDto appointmentDetails, Boolean autoBooking,String timeZone) throws EtailBookItException {
        OffsetDateTime appointmentOffsetDateTime=appointmentDetails.getAppointmentDate();
        //To get blocked dates of a particular retailer and checking with the appointment date
        LocalDate localDate = appointmentOffsetDateTime.toLocalDate();
        OffsetDateTime offsetDateTime=appointmentDetails.getUtcAppointmentDate();
        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByBlockDateAndRetailer(offsetDateTime.toLocalDate(), RetailerContext.getRetailer());
        if(!blockDateInfoList.isEmpty()){
            for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                if(blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null){
                    if(autoBooking)
                        return new ArrayList<>();
                    throw new BadRequestException("Bookings are not available for the selected date and time");
                }
            }
        }

        if(appointmentDetails.getVenueId() == null) {
            throw new BadRequestException("No venue selected");
        }
        Venue currentVenue = venueRepository.findByVenueId(appointmentDetails.getVenueId());
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        OffsetTime venueStartTime = null;
        OffsetTime venueEndTime = null;
        DaysOfWeek daysOfWeek = new DaysOfWeek();

        if(currentVenue.getParticipantLimitService() <= 0) {
            if(RequestTypeContext.getRequestType().equals("Retailer")) {
                throw new BadRequestException("Venue capacity is improperly configured");
            }
            else {
                throw new BadRequestException("Venue is unavailable");
            }
        }

        // Goes through venues availability and sets the opening and closing time of the venue for the required day of the week
        for (VenueAvailability day : currentVenue.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                venueStartTime = day.getAvailabilityOpenTime();
                venueEndTime = day.getAvailabilityCloseTime();
                break;
            }
        }
        if(venueStartTime == null) {
            if(autoBooking)
                return new ArrayList<>();
            throw new BadRequestException("Venue is not available on selected date");
        }

        //Checks the schedule type of the service to set the required duration
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(appointmentDetails.getServiceId());
        if(requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }
        if (ServiceScheduleType.FIXED.toString().equals(requiredService.getScheduleType())) {
            appointmentDetails.setServiceDuration(requiredService.getFixedScheduleValue());
        } else if (ServiceScheduleType.VARIABLE.toString().equals(requiredService.getScheduleType())) {
            appointmentDetails.setServiceDuration(Integer.parseInt(requiredService.getVariableScheduleMaxValue()));
        } else {
            if (appointmentDetails.getServiceDuration() == null) {
                throw new BadRequestException("Service duration Not specified");
            }
        }

        OffsetTime serviceStartTime = null;
        OffsetTime serviceEndTime = null;

        // Goes through service availability and sets the opening and closing time of the service for the required day of the week
        for (ServiceAvailability day : requiredService.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                serviceStartTime = day.getAvailabilityOpenTime();
                serviceEndTime = day.getAvailabilityCloseTime();
                break;
            }
        }
        if(serviceStartTime == null) {
            if(autoBooking)
                return new ArrayList<>();
            throw new BadRequestException("Service is not available on selected date");
        }

        Attendant requiredAttendant = attendantRepository.findByAttendantId(appointmentDetails.getAttendantId());
        if (
                requiredAttendant == null && appointmentDetails.getAttendantId() != null
                        && RequestTypeContext.getRequestType().equals("Retailer")
        ) {
            throw new BadRequestException("Selected attendant is invalid");
        }

        OffsetTime attendantStartTime = null;
        OffsetTime attendantEndTime = null;
        int newAppointmentSizeConstraintCapacity=0;

        //todo:  currently does not consider the attendant capacity because it just fetches appointments on that venue
        //Gets all the appointments between specified DateTime interval
        //Need to add multiple day support. Currently, we are only checking one day interval
        List<Appointment> currentAppointments = appointmentRepository.getAppointmentsByVenueAndDate(
                currentVenue,
                appointmentDetails.getAppointmentDate().toLocalDate(),
                appointmentDetails.getAppointmentDate().plusDays(1).toLocalDate()
        );

        List<AppointmentSlotsDto> freeTimeSlots = new ArrayList<>();

       /* LocalTime temp1 = serviceStartTime.compareTo(venueStartTime) > 0 ?
                serviceStartTime
                :
                serviceStartTime.compareTo(venueStartTime) < 0 ? venueStartTime : serviceStartTime;
        LocalTime temp2 = serviceEndTime.compareTo(venueEndTime) < 0 ?
                serviceEndTime
                :

                serviceEndTime.compareTo(venueEndTime) > 0 ? venueEndTime : serviceEndTime;*/
         OffsetTime temp1 = serviceStartTime.isAfter(venueStartTime) ?
                serviceStartTime
                :
                serviceStartTime.isBefore(venueStartTime) ? venueStartTime : serviceStartTime;
        OffsetTime temp2 = serviceEndTime.isBefore(venueEndTime)?
                serviceEndTime
                :

                serviceEndTime.isAfter(venueEndTime) ? venueEndTime : serviceEndTime;


    //    ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(TimeZoneContext.getTimeZone()));
        ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(ZoneOffset.UTC.toString()));

        if(requiredAttendant != null) {

            if(requiredAttendant.getCapacityLimit() <= 0) {
                if(RequestTypeContext.getRequestType().equals("Retailer")) {
                    throw new BadRequestException("Attendant is improperly configured");
                }
                else {
                    throw new BadRequestException("Attendant is unavailable");
                }
            }
         /*   for(Appointment appointment:currentAppointments) {
                if (appointment.getAttendant().getAttendantId().equals(requiredAttendant.getAttendantId())) {
                    for (PetSizeLimit petSizeLimit : requiredAttendant.getPetSizeLimits()) {
                        for (GeneralPetSize generalPetSize : appointment.getPetType().getGeneralPetSizes()) {
                            if (petSizeLimit.getGeneralPetSize().equals(generalPetSize)) {
                                newAppointmentSizeConstraintCapacity = petSizeLimit.getCapacity();
                                newAppointmentSizeConstraintCapacity = newAppointmentSizeConstraintCapacity - 1;
                                if (newAppointmentSizeConstraintCapacity <= 0) {
                                    throw new BadRequestException("Attendant has reached the pet size limit for this day");
                                }
                            }
                        }
                    }
                }
            }*/

            // Goes through service availability and sets the opening and closing time of the service for the required day of the week
            for (AttendantAvailability day : requiredAttendant.getAvailabilityDays()) {
                if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                    attendantStartTime = day.getAvailabilityOpenTime();
                    attendantEndTime = day.getAvailabilityCloseTime();
                    break;
                }
            }
            if(attendantStartTime == null) {
                if(autoBooking)
                    return new ArrayList<>();
                throw new BadRequestException("Attendant is not available on selected date");
            }
          /*  LocalTime startTime = attendantStartTime.compareTo(temp1) > 0 ?
                    attendantStartTime
                    :
                    attendantStartTime.compareTo(temp1) < 0 ? temp1 : attendantStartTime;
            LocalTime endTime = attendantEndTime.compareTo(temp2) < 0 ?
                    attendantEndTime
                    :
                    attendantEndTime.compareTo(temp2) > 0 ? temp2 : attendantEndTime;*/
            OffsetTime startTime = attendantStartTime.isAfter(temp1)  ?
                    attendantStartTime
                    :
                    attendantStartTime.isBefore(temp1) ? temp1 : attendantStartTime;
            OffsetTime endTime = attendantEndTime.isBefore(temp2)?
                    attendantEndTime
                    :
                    attendantEndTime.isAfter(temp2) ? temp2 : attendantEndTime;
          //  OffsetDateTime attendantStartTime = OffsetDateTime.parse(attendantAvailability.getDate().toString()+"T"+attendantAvailability.getAvailabilityOpenTime().toString().split("[+\\-Z]")[0]+"Z").atZoneSimilarLocal(ZoneId.of(TimeZoneContext.getTimeZone())).toOffsetDateTime();

         //todo just commenting out this code bcz change are made to required attendant to the the type attenantModelProjection from attenda t based on the requirement in ticket BKi-1362
           /* freeTimeSlots = SchedulerUtils.generateFreeTimeSlotsForDay(
                    currentVenue,
                    requiredAttendant,
                    requiredService,
                    currentAppointments,
                    appointmentDetails.getServiceDuration(),
                    requiredService.getFixedScheduleUnit(),
                    OffsetDateTime.parse(
                            appointmentDetails.getAppointmentDate().toLocalDate().toString()+"T"+
                            startTime.toString().split("[+\\-Z]")[0]+"Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
            ,
                    OffsetDateTime.parse(
                            appointmentDetails.getAppointmentDate().toLocalDate().toString()+"T"+
                                    endTime.toString().split("[+\\-Z]")[0]+"Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
                    ,
                    //System.out.println("++++++++++++++++++++++"+OffsetDateTime.parse(localDate.toString()+"T"+offsetTime));
                    slotInterval
            );*/

        }
        else if(appointmentDetails.getAttendantId() == null) {
            //System should choose the first available attendant that is eligible to perform the appointment.
            Set<Attendant> attendants = requiredService.getAttendants();
            for (Attendant attendant : attendants) {
                for (AttendantAvailability day : attendant.getAvailabilityDays()) {
                    if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                        attendantStartTime = day.getAvailabilityOpenTime();
                        attendantEndTime = day.getAvailabilityCloseTime();
                        break;
                    }
                }
                if(attendantStartTime == null) {
                    continue;
                }

             /*   LocalTime startTime = attendantStartTime.compareTo(temp1) > 0 ?
                        attendantStartTime
                        :
                        attendantStartTime.compareTo(temp1) < 0 ? temp1 : attendantStartTime;
                LocalTime endTime = attendantEndTime.compareTo(temp2) > 0 ?
                        attendantEndTime
                        :
                        attendantEndTime.compareTo(temp1) < 0 ? temp1 : attendantEndTime;*/
                OffsetTime startTime = attendantStartTime.isAfter(temp1)  ?
                        attendantStartTime
                        :
                        attendantStartTime.isBefore(temp1) ? temp1 : attendantStartTime;
                OffsetTime endTime = attendantEndTime.isAfter(temp2)?
                        attendantEndTime
                        :
                        attendantEndTime.isBefore(temp1)  ? temp1 : attendantEndTime;
//todo just commenting out this code bcz change are made to required attendant to the the type attenantModelProjection from attenda t based on the requirement in ticket BKi-1362
                  /*  List<AppointmentSlotsDto> timeSlotCollection = SchedulerUtils.generateFreeTimeSlotsForDay(
                            currentVenue,
                            attendant,
                            requiredService,
                            currentAppointments,
                            appointmentDetails.getServiceDuration(),
                            requiredService.getFixedScheduleUnit(),
                            OffsetDateTime.parse(
                                    appointmentDetails.getAppointmentDate().toLocalDate().toString()+"T"+
                                            startTime.toString().split("[+\\-Z]")[0]+"Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
                            ,
                            OffsetDateTime.parse(
                                    appointmentDetails.getAppointmentDate().toLocalDate().toString()+"T"+
                                            endTime.toString().split("[+\\-Z]")[0]+"Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
                            ,
                            slotInterval
                    );
                    if (!timeSlotCollection.isEmpty()) {
                        List<AppointmentSlotsDto> newTimeSlotCollection = getUnblockedTimeSlots(blockDateInfoList, timeSlotCollection, autoBooking);
                        newTimeSlotCollection.forEach(
                                t -> t.setAttendantId(attendant.getAttendantId())
                        );
                        for (AppointmentSlotsDto appointmentSlotsDto:newTimeSlotCollection){
                            appointmentSlotsDto.setSlotStartIsoDateTime(appointmentSlotsDto.getSlotStartTime().minusMinutes(calculateOffset(timeZone)).atZoneSameInstant(ZoneId.of(timeZone)).format(DateTimeFormatter.ISO_DATE_TIME));
                            appointmentSlotsDto.setSlotEndIsoDateTime(appointmentSlotsDto.getSlotEndTime().minusMinutes(calculateOffset(timeZone)).atZoneSameInstant(ZoneId.of(timeZone)).format(DateTimeFormatter.ISO_DATE_TIME));
                        }
                        return newTimeSlotCollection.stream().filter(t ->
                                t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).collect(Collectors.toList()
                        );
                    }*/
                }
                if(!autoBooking)
                    throw new BadRequestException("No attendants available on selected date");
        }
        List<AppointmentSlotsDto> newFreeSlots = getUnblockedTimeSlots(blockDateInfoList, freeTimeSlots, autoBooking);
        List<AppointmentSlotsDto> filteredSlots= newFreeSlots.stream().filter(t ->t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).collect(Collectors.toList());

      for (AppointmentSlotsDto appointmentSlotsDto:filteredSlots){
       appointmentSlotsDto.setSlotStartIsoDateTime(appointmentSlotsDto.getSlotStartTime().minusMinutes(calculateOffset(timeZone,appointmentSlotsDto.getSlotStartTime())).atZoneSameInstant(ZoneId.of(timeZone)).format(DateTimeFormatter.ISO_DATE_TIME));
      appointmentSlotsDto.setSlotEndIsoDateTime(appointmentSlotsDto.getSlotEndTime().minusMinutes(calculateOffset(timeZone,appointmentSlotsDto.getSlotEndTime())).atZoneSameInstant(ZoneId.of(timeZone)).truncatedTo(ChronoUnit.MINUTES).format(DateTimeFormatter.ISO_DATE_TIME));
      }

      /*  List<AppointmentSlotsDto> removedSlots=new ArrayList<>();
        Integer offsetDifference=calculateOffset(timeZone);
        OffsetDateTime startOffsetDate=appointmentDetails.getAppointmentDate();
        if(offsetDifference<0){
            startOffsetDate=startOffsetDate.minusDays(1);
        }else{
            startOffsetDate=startOffsetDate.plusDays(1);
        }

        for(AppointmentSlotsDto appointmentSlotsDto:filteredSlots){
            OffsetDateTime slotStartTime=appointmentSlotsDto.getSlotStartTime();
            if(offsetDifference<0){
                if(slotStartTime.plusMinutes(offsetDifference).isBefore(startOffsetDate)){
                    removedSlots.add(appointmentSlotsDto);
                }
            }else{
                OffsetDateTime tempDate=appointmentDetails.getAppointmentDate().minusMinutes(offsetDifference);
                if(tempDate.toLocalDate().equals(appointmentDetails.getAppointmentDate().toLocalDate())) {
                    if (slotStartTime.plusMinutes(offsetDifference).toLocalDate().equals(startOffsetDate.toLocalDate())) {
                        removedSlots.add(appointmentSlotsDto);
                    }
                } else if (slotStartTime.plusMinutes(offsetDifference).toLocalDate().isAfter(startOffsetDate.toLocalDate())) {
                    removedSlots.add(appointmentSlotsDto);
                }
            }
        }
        filteredSlots.removeAll(removedSlots);*/
        return filteredSlots;
    }

    public List<AppointmentSlotsDto> getUnblockedTimeSlotsForCalendar(List<BlockDateInfo> blockDateInfoList, List<AppointmentSlotsDto> freeTimeSlots, boolean autoBooking) throws BadRequestException {
        List<AppointmentSlotsDto> newFreeSlots = new ArrayList<>();
        if(!blockDateInfoList.isEmpty()){
            for(AppointmentSlotsDto slot: freeTimeSlots) {
                boolean isBlocked = false;
                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (
                            blockDateInfo.getBlockStartTime() != null
                                    && blockDateInfo.getBlockEndTime() != null
                                    && ((slot.getSlotStartTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotStartTime().isBefore(blockDateInfo.getBlockEndTime())) || (slot.getSlotEndTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotEndTime().isBefore(blockDateInfo.getBlockEndTime())) )
                    ) {
                        isBlocked = true;
                    }
                };
                if (!isBlocked) newFreeSlots.add(slot);
            };
        }
        else {
            return freeTimeSlots;
        }
        return newFreeSlots;
    }

    public List<AppointmentSlotsDto> getUnblockedTimeSlots(List<BlockDateInfo> blockDateInfoList, List<AppointmentSlotsDto> freeTimeSlots, boolean autoBooking) throws BadRequestException {
        List<AppointmentSlotsDto> newFreeSlots = new ArrayList<>();
        if(!blockDateInfoList.isEmpty()){
            for(AppointmentSlotsDto slot: freeTimeSlots) {
                boolean isBlocked = false;
                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (
                            blockDateInfo.getBlockStartTime() != null
                                    && blockDateInfo.getBlockEndTime() != null
                                    && ((slot.getSlotStartTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotStartTime().isBefore(blockDateInfo.getBlockEndTime())) || (slot.getSlotEndTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotEndTime().isBefore(blockDateInfo.getBlockEndTime())) )
                    ) {
                        isBlocked = true;
                    }
                };
                if (!isBlocked) newFreeSlots.add(slot);
            };
        }
        else {
            return freeTimeSlots;
        }
        if (newFreeSlots.size() == 0) {
            if (autoBooking)
                return new ArrayList<>();
            else throw new BadRequestException("No timeslots are available for appointment");
        }
        return newFreeSlots;
    }
    public AppointmentWaiverOfLiabilityInformation generateWaiverOfLiabilityInformation(Appointment appointment,AppointmentDto appointmentDto){
        AppointmentWaiverOfLiabilityInformation appointmentWaiverOfLiabilityInformation = new AppointmentWaiverOfLiabilityInformation();
        if (appointmentDto.getId() != null) {
            AppointmentWaiverOfLiabilityInformation optionalAppointmentWaiverOfLiabilityInformation = appointmentWaiverOfLiabilityInformationRepository.findByAppointmentId(appointmentDto.getId(), RetailerContext.getRetailer());
            if (optionalAppointmentWaiverOfLiabilityInformation != null) {
                LOGGER.info("--------EXISTING WAIVER ID ::---"+optionalAppointmentWaiverOfLiabilityInformation.getId());
                appointmentWaiverOfLiabilityInformation.setId(optionalAppointmentWaiverOfLiabilityInformation.getId());
            }
        }
        // appointmentWaiverOfLiabilityInformation.setId(94);
        Optional<WaiverOfLiability> waiverOfLiability = waiverOfLiabilityRepository.findById(appointmentDto.getWaiverOfLiabilityDoc().getId());
        appointmentWaiverOfLiabilityInformation.setAppointment(appointment);
        appointmentWaiverOfLiabilityInformation.setRetailer(RetailerContext.getRetailer());
        if (waiverOfLiability.isPresent()) {
            appointmentWaiverOfLiabilityInformation.setWaiverOfLiability(waiverOfLiability.get());
        }
        PetWaiverOfLiabilityInfo petWaiverOfLiabilityInfo=petWaiverOfLiabilityInfoRepository.findByPet(appointmentDto.getPet().getId(),RetailerContext.getRetailer());
        if(petWaiverOfLiabilityInfo!=null){
            String fileUrl=petWaiverOfLiabilityInfo.getSignedFile();
            appointmentWaiverOfLiabilityInformation.setFileUrl(fileUrl);
        }
        LOGGER.info("APPOINTMENT WAIVER ID ::"+appointmentWaiverOfLiabilityInformation.getId());
        return appointmentWaiverOfLiabilityInformation;
    }


    @Override
    public List<AppointmentUploadedDocumentsProjection> getAllUploadedAppointmentDocumentsByCustomerId(Integer pageNo, Integer pageSize, String sortBy, Integer customerId) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        return appointmentRepository.findAllDocumentsByCustomerId(customerId, paging);
    }

    @Override
    public void changeAppointmentTimeWithOffset() {
        List<Appointment> appointmentsList=new ArrayList<>();
       List<OffsetRetailerMapping> offsetRetailerMappingList=offsetRetailerMappingRepository.findAll();
       OffsetTime appointmentTime=null;
       LocalDate appointmentDate=null;
       OffsetDateTime appointmentEndTime=null;
        OffsetDateTime changedAppointmentDateAndTime=null;
        OffsetDateTime changedAppointmentEndTime=null;
        OffsetTime endTime=null;
        LocalDate appointmentEndDate=null;
        String retailer=null;
        Integer offset=null;
        for(OffsetRetailerMapping offsetRetailerMapping:offsetRetailerMappingList) {
            retailer=offsetRetailerMapping.getSchemaName();
            offset=offsetRetailerMapping.getOffset();
            List<Appointment> appointments= appointmentRepository.findAppointmentsWithServiceStatus(retailer);
            for (Appointment appointment : appointments) {
                appointmentTime = appointment.getTime();
                appointmentDate = appointment.getDate();
                appointmentEndTime = appointment.getNewEndTime();
                if (appointmentEndTime != null) {
                        changedAppointmentEndTime = appointmentEndTime.plusHours(offset);
                        endTime = changedAppointmentEndTime.toOffsetTime();
                        appointment.setEndTime(endTime);
                        appointmentEndDate = changedAppointmentEndTime.toLocalDate();
                        appointment.setEndDate(appointmentEndDate);
                }
                OffsetDateTime appointmentDateTime = OffsetDateTime.of(appointmentDate, appointmentTime.toLocalTime(), ZoneOffset.UTC).toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()));
                changedAppointmentDateAndTime = appointmentDateTime.plusHours(offset);
                LocalDate newAppointmentDate = changedAppointmentDateAndTime.toLocalDate();
                OffsetTime newAppointmentTime = changedAppointmentDateAndTime.toOffsetTime();
                appointment.setDate(newAppointmentDate);
                appointment.setTime(newAppointmentTime);
                appointmentsList.add(appointment);
            }
            appointmentRepository.saveAll(appointments);
        }

    }

    @Override
    public void addValuesToEndDate() {
        LocalDate appointmentStartDate=null;
        List<Appointment> appointments=new ArrayList<>();
        List<Appointment>appointmentList=appointmentRepository.findAll();
        for (Appointment appointment:appointmentList){
            appointmentStartDate=appointment.getDate();
            if(appointment.getEndDate()==null){
                appointment.setEndDate(appointmentStartDate);
            }
            appointments.add(appointment);
        }
        appointmentRepository.saveAll(appointments);
    }

    @Override
    public void offsetRetailerMapping(List<OffsetRetailerMappingDto> offsetRetailerMappingDtos) {
        List<OffsetRetailerMapping> offsetRetailerMappingList=new ArrayList<>();
        for (OffsetRetailerMappingDto offsetRetailerMappingDto:offsetRetailerMappingDtos){
            OffsetRetailerMapping offsetRetailerMapping=new OffsetRetailerMapping();
            offsetRetailerMapping.setTimeZone(offsetRetailerMappingDto.getTimezone());
            offsetRetailerMapping.setOffset(offsetRetailerMappingDto.getOffset());
            offsetRetailerMapping.setSchemaName(offsetRetailerMappingDto.getSchemaName());
            offsetRetailerMappingList.add(offsetRetailerMapping);
        }
        offsetRetailerMappingRepository.saveAll(offsetRetailerMappingList);
    }

    @Override
    public void populateDataToAppointmentDateAndTime() {
        List<Appointment> appointments=appointmentRepository.findAll();
        for (Appointment appointment:appointments){
            appointment.setAppointmentStartDateAndTime(appointment.getStartTime());
            appointment.setAppointmentEndDateAndTime(appointment.getEndTime());
            appointmentRepository.save(appointment);
        }
    }

    @Override
    public PaymentDto insertGroomBarData(GroomBarAppointmentDto groomBarAppointmentDto) throws EtailBookItException {

        Appointment appointment =new Appointment();

        ServiceType serviceType=serviceTypeRepository.findByName("Groombar Service Type");
        if(serviceType!=null){
            appointment.setServiceType(serviceType);
            serviceType.setRetailer(RetailerContext.getRetailer());
            serviceTypeRepository.save(serviceType);
        }
        else {
            serviceType=createGroomBarServiceType();
            appointment.setServiceType(serviceType);
        }
        com.sayone.etailbookit.model.Service service = serviceRepository.findByName("Groombar Service");
        if(service!=null){
            appointment.setService(service);
            service.setFixedScheduleUnit("MINUTE");
            service.setFixedScheduleValue(20);
            service.setAmountPerUnit(groomBarAppointmentDto.getServiceCost());
            service.setServiceUnit("EVENT");
            service.setRetailer(RetailerContext.getRetailer());
            PetType petType=petTypeRepository.findByName("Groombar pet type");
            if(petType!=null){
                service.setPetType(petType);
            }
            else{
                petType=createGroomBarPetType();
                service.setPetType(petType);
            }
            serviceRepository.save(service);
        }else{
            service=createGroomBarService();
            appointment.setService(service);
        }
        //Todo:check the serviceDuration
      /*  if(service.getScheduleType().equals("FIXED")) {
            appointment.setDuration(service.getFixedScheduleValue() + " " + service.getFixedScheduleUnit() + "(s)");
        }

        else {
            appointment.setDuration(service.getVariableScheduleMinValue() + " - "
                    + service.getVariableScheduleMaxValue() + " " + service.getFixedScheduleUnit() + "(s)");
        }*/
        Venue venue=venueRepository.findByInternalName("Groombar Venue");
        if(venue!=null){
            appointment.setVenue(venue);
            venue.setRetailer(RetailerContext.getRetailer());
            venueRepository.save(venue);
        }
        else{
            venue=createGroomBarVenue();
            appointment.setVenue(venue);
        }
        Attendant attendant=attendantRepository.findByAttendantId(groomBarAppointmentDto.getAttendantId());
        if(attendant!=null){
            appointment.setAttendant(attendant);
        }
        else {
             attendant = attendantRepository.findByFirstName("Groombar Attendant");
            if (attendant != null) {
                appointment.setAttendant(attendant);
                attendant.setRetailer(RetailerContext.getRetailer());
                attendantRepository.save(attendant);
            } else {
                attendant = createGroomBarAttendant();
                appointment.setAttendant(attendant);
            }
        }
        Pet pet=petRepository.findByPetName("Groombar Pet");
        if(pet!=null){
            pet.setRetailer(RetailerContext.getRetailer());
            appointment.setPet(pet);
            petRepository.save(pet);
        }else {
            pet=createGroomBarPet();
            appointment.setPet(pet);
        }

      //  appointment.setQuoteAdjustmentPrice(groomBarAppointmentDto.getQuoteAdjustmentPrice());
        appointment.setRetailer(RetailerContext.getRetailer());
        appointment.setServiceStatus(ServiceStatus.COMPLETE);
        appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
        appointment.setDiscountAmount(groomBarAppointmentDto.getDiscountAmount());
       /* appointment.setTipAmount(groomBarAppointmentDto.getTipAmount());
        appointment.setTipAmountCurrency(groomBarAppointmentDto.getTipAmountCurrency());*/
        appointment.setServiceCost(groomBarAppointmentDto.getServiceCost());
        appointment.setSource("GroomBar");
        OffsetDateTime offsetDateTime=OffsetDateTime.parse(groomBarAppointmentDto.getStartTime());
        appointment.setDate(offsetDateTime.toLocalDate());
        appointment.setTime(offsetDateTime.toOffsetTime());
        appointment.setCustomerId(groomBarAppointmentDto.getCustomerId());
        if(groomBarAppointmentDto.getCustomerName()!=null){
            appointment.setCustomerName(groomBarAppointmentDto.getCustomerName());
        }
        //appointment.setAmount(service.get);
        appointment.setAppointmentStartDateAndTime(groomBarAppointmentDto.getStartTime());
        appointment.setAmount(groomBarAppointmentDto.getServiceCost());
        appointment.setBringYourFood(Boolean.FALSE);
        appointment.setRecurringEnabled(Boolean.FALSE);
        appointment.setHaveSibilings(Boolean.FALSE);
       appointment= appointmentRepository.save(appointment);

       // PaymentDto paymentDetails = new PaymentDto();
       PaymentDto paymentDetails= AmountCalculation.serviceAmount(appointment);
       paymentDetails.setPayableAmount(paymentDetails.getServiceAmount());
       paymentDetails.setRetailer(appointment.getRetailer());
       paymentDetails.setAppointmentId(appointment.getId());
       paymentDetails.setCustomerId(appointment.getCustomerId());
       paymentDetails.setServiceName("Groombar Service");
      /*  if (Optional.ofNullable(appointment.getId()).orElse(0) != 0){
            PaymentDto paymentDto = new PaymentDto();
            paymentDto.setAppointmentId(appointment.getId());
            paymentDto.setDate(appointment.getDate().toString());
            paymentDto.setTime(appointment.getTime().toString());
            // paymentDetails = AmountCalculation.getPaymentDetails(appointment);
             paymentDetails.setPaymentType(PaymentType.PAYMENT_AFTER_SERVICE_COMPLETE);
           // return paymentDetails;
        }*/
        paymentDetails.setPaymentType(PaymentType.PAYMENT_AFTER_SERVICE_COMPLETE);
        appointment = appointmentRepository.getOne(appointment.getId());
        appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
        appointmentRepository.save(appointment);

        return paymentDetails;
    }

    private Pet createGroomBarPet() {
        Pet pet=new Pet();
        pet.setName("Groombar Pet");
        pet.setRetailer(RetailerContext.getRetailer());
        PetType petType=petTypeRepository.findByName("Groombar pet type");
        if(petType!=null){
            pet.setPetType(petType);
        }else{
            petType=createGroomBarPetType();
            pet.setPetType(petType);
        }
        pet=petRepository.save(pet);
        return pet;
    }

    private PetType createGroomBarPetType(){
        PetType petType=new PetType();
        petType.setName("Groombar pet type");
        Set<PetTypeConfiguration> petTypeConfigurations=new HashSet<>();
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.HAIR_LENGTH, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.HAIR_TEXTURE, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.COMBS_BLADES, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.VET_INFO, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.ALLERGIES, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.DESIRED_HAIR_LENGTH, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.BREEDS, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.TEMPERAMENT, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.VACCINATION_RECORDS, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.DOCUMENT_OPTIONS, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.EMERGENCY_CONTACT_INFO, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.WEIGHT_RANGE,DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.GENERAL_PET_SIZE, DisplayType.NONE, petType));
            petTypeConfigurations.add(PetTypeMapper.toPetTypeConfigurationEntity(PetTypeConfig.DECEASE_DATE,DisplayType.NONE, petType));
        petType.setPetTypeConfigurations(petTypeConfigurations);
        petType.setRetailer(RetailerContext.getRetailer());
        petType= petTypeRepository.save(petType);
        return petType;
    }
    private Attendant createGroomBarAttendant() {
        Attendant attendant= new Attendant();
        attendant.setFirstName("Groombar Attendant");
        attendant.setActive(Boolean.TRUE);
        attendant.setRetailer(RetailerContext.getRetailer());
        Venue venue=venueRepository.findByInternalName("Groombar Venue");
        if(venue!=null){
            attendant.setVenue(venue);
        }
        else{
            venue=createGroomBarVenue();
            attendant.setVenue(venue);
        }
        attendant=attendantRepository.save(attendant);
        return attendant;
    }

    private Venue createGroomBarVenue() {
        Venue venue=new Venue();
        venue.setInternalName("Groombar Venue");
        venue.setActive(Boolean.TRUE);
        venue.setRetailer(RetailerContext.getRetailer());
        venue=venueRepository.save(venue);
        return venue;
    }

    private com.sayone.etailbookit.model.Service createGroomBarService() {
        com.sayone.etailbookit.model.Service service=new com.sayone.etailbookit.model.Service();
        service.setName("Groombar Service");
        service.setIsActive(Boolean.TRUE);
        ServiceType serviceType=serviceTypeRepository.findByName("Groombar Service Type");
        if(serviceType!=null){
            service.setServiceType(serviceType);
        }else{
            serviceType=createGroomBarServiceType();
            service.setServiceType(serviceType);
        }
        PetType petType=petTypeRepository.findByName("Groombar pet type");
        if(petType!=null){
            service.setPetType(petType);
        }else{
            petType=createGroomBarPetType();
            service.setPetType(petType);
        }
        service.setScheduleType("FIXED");
        service.setFixedScheduleUnit("MINUTE");
        service.setCancelationAmountType("DOLLAR");
        service.setCancelationBufferUnit("MINUTE");
        service.setDepositAmountType("DOLLAR");
        service.setLateOnboardingChargeInterval("HALF_DAY");
        service.setServiceUnit("EVENT");
        service.setSiblingDiscountType("DOLLAR");
        service.setRetailer(RetailerContext.getRetailer());
        service=serviceRepository.save(service);
        return service;
    }

    private ServiceType createGroomBarServiceType() {
        ServiceType serviceType=new ServiceType();
        serviceType.setName("Groombar Service Type");
        serviceType.setActive(Boolean.TRUE);
        serviceType.setRetailer(RetailerContext.getRetailer());
        serviceType=serviceTypeRepository.save(serviceType);
        return serviceType;
    }

    @Override
    public ByteArrayOutputStream getExportAppointments(Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer pet, Integer attendant, Integer venue, String status, Integer petType, String search, Integer service, OffsetDateTime startDate, OffsetDateTime endDate, boolean fetchAll,String timeZone) throws EtailBookItException, IOException {
        if (fetchAll) {
            appointmentReaderConfiguration.setCustomer(customer);
            appointmentReaderConfiguration.setPet(pet);
            appointmentReaderConfiguration.setAttendant(attendant);
            appointmentReaderConfiguration.setVenue(venue);
            appointmentReaderConfiguration.setStatus(status);
            appointmentReaderConfiguration.setPetType(petType);
            appointmentReaderConfiguration.setSearch(search);
            appointmentReaderConfiguration.setService(service);
            appointmentReaderConfiguration.setStartDate(startDate.toString());
            appointmentReaderConfiguration.setEndDate(endDate.toString());
            appointmentReaderConfiguration.setRetailer(RetailerContext.getRetailer());

            try {
                JobParameters jobParameters = new JobParametersBuilder()
                        .addString("timestamp", String.valueOf(System.currentTimeMillis()))
                        .toJobParameters();
                JobExecution jobExecution = jobLauncher.run(appointmentJob, jobParameters);
            } catch (Exception e) {
                throw new EtailBookItException(e.getMessage());
            }
            return appointmentWriterConfiguration.getGeneratedContent();
        } else {
            Page<AppointmentsListing> appointmentsListings = getAppointmentsForExcel(pageNo, pageSize, sortBy, customer, pet, attendant, venue, status, petType, search, service, startDate, endDate,timeZone);
            XSSFWorkbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Appointments");
            sheet.setDefaultColumnWidth(15);
            appointmentExcelGenerator.createHeaderRow(sheet);
            for (AppointmentsListing appointment : appointmentsListings) {
                BatchAppointmentDto batchAppointmentDto = new BatchAppointmentDto();
                batchAppointmentDto.setId(appointment.getAppointmentId());
                batchAppointmentDto.setCustomerName(appointment.getCustomerName());
                batchAppointmentDto.setService(appointment.getService());
                batchAppointmentDto.setPetName(appointment.getPetName());
                batchAppointmentDto.setOrderId(Optional.ofNullable(appointment.getOrderReference()).orElse(0L));
                batchAppointmentDto.setEventId(appointment.getAppointmentNo());
                batchAppointmentDto.setServiceStartAt(appointment.getServiceStartAt());
                batchAppointmentDto.setServiceEndAt(appointment.getServiceEndAt());
                batchAppointmentDto.setDuration(appointment.getDuration());
                batchAppointmentDto.setDate(appointment.getDate());
                batchAppointmentDto.setAmount(appointment.getAmount());
                appointmentExcelGenerator.createDataRow(sheet, batchAppointmentDto);
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            outputStream.close();
            return outputStream;
        }
    }

    @Override
    public Map<String, List<AppointmentSlotsDto>> getAvailableAppointmentSlots1(BookingDto appointmentDetails, boolean autoBooking, String timeZone, Integer daylightAddedOffsetMinutes)throws EtailBookItException {

        Map<String,List<AppointmentSlotsDto>> attendantSlotsMap=new HashMap<>();
        List<Map<String,List<AppointmentSlotsDto>>> attendantSlotList=new ArrayList<>();
        OffsetDateTime appointmentOffsetDateTime = appointmentDetails.getAppointmentDate();
        List<AppointmentSlotsDto> freeTimeSlots = new ArrayList<>();
        List<AppointmentSlotsDto> filteredSlots=new ArrayList<>();
        List<AppointmentSlotsDto> allTheSlots=new ArrayList<>();
        //To get blocked dates of a particular retailer and checking with the appointment date
        LocalDate localDate = appointmentOffsetDateTime.toLocalDate();
        OffsetDateTime offsetDateTime = appointmentDetails.getUtcAppointmentDate();
        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByBlockDateAndRetailer(offsetDateTime.toLocalDate(), RetailerContext.getRetailer());
        if (!blockDateInfoList.isEmpty()) {
            for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                if (blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null) {
                    /*if (autoBooking)
                        return new ArrayList<>();*/
                    throw new BadRequestException("Bookings are not available for the selected date and time");
                }
            }
        }

        //todo uncomment this portion of code when attendant vacation is implemented
       /* List<Vacation> vacationList=vacationRepository.findByAttendantAndRetailer(appointmentDetails.getAttendantId(),RetailerContext.getRetailer());
        if(!vacationList.isEmpty()){
            for(Vacation vacation:vacationList){
                if(!appointmentDetails.getUtcAppointmentDate().isBefore(vacation.getVacationStartTime())&& !appointmentDetails.getUtcAppointmentDate().isAfter(vacation.getVacationEndTime())){
                    throw new EtailBookItException("The selected attendant is on vacation during the time ::"+appointmentDetails.getUtcAppointmentDate());
                }
            }
        }*/

        if (appointmentDetails.getVenueId() == null) {
            throw new BadRequestException("No venue selected");
        }
        Venue currentVenue = venueRepository.findByVenueId(appointmentDetails.getVenueId());
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        LocalTime venueStartTime = null;
        LocalTime venueEndTime = null;
        DaysOfWeek daysOfWeek = new DaysOfWeek();

        if (currentVenue.getParticipantLimitService() <= 0) {
            if (RequestTypeContext.getRequestType().equals("Retailer")) {
                throw new BadRequestException("Venue capacity is improperly configured");
            } else {
                throw new BadRequestException("Venue is unavailable");
            }
        }

        // Goes through venues availability and sets the opening and closing time of the venue for the required day of the week
        for (VenueAvailability day : currentVenue.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                venueStartTime = day.getAvailabilityOpenTime().toLocalTime();
                venueEndTime = day.getAvailabilityCloseTime().toLocalTime();
                break;
            }
        }
        if (venueStartTime == null) {
            /*if (autoBooking)
                return new ArrayList<>();*/
            throw new BadRequestException("Venue is not available on selected date");
        }

        //Checks the schedule type of the service to set the required duration
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(appointmentDetails.getServiceId());
        if (requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }
        if (ServiceScheduleType.FIXED.toString().equals(requiredService.getScheduleType())) {
            appointmentDetails.setServiceDuration(requiredService.getFixedScheduleValue());
        } else if (ServiceScheduleType.VARIABLE.toString().equals(requiredService.getScheduleType())) {
            appointmentDetails.setServiceDuration(Integer.parseInt(requiredService.getVariableScheduleMaxValue()));
        } else {
            if (appointmentDetails.getServiceDuration() == null) {
                throw new BadRequestException("Service duration Not specified");
            }
        }

        LocalTime serviceStartTime = null;
        LocalTime serviceEndTime = null;

        // Goes through service availability and sets the opening and closing time of the service for the required day of the week
        for (ServiceAvailability day : requiredService.getAvailabilityDays()) {
            if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(day.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                serviceStartTime = day.getAvailabilityOpenTime().toLocalTime();
                serviceEndTime = day.getAvailabilityCloseTime().toLocalTime();
                break;
            }
        }
        if (serviceStartTime == null) {
           /* if (autoBooking)
                return new ArrayList<>();*/
            throw new BadRequestException("Service is not available on selected date");
        }

        LocalTime temp1 = serviceStartTime.isAfter(venueStartTime) ?
                serviceStartTime
                :
                serviceStartTime.isBefore(venueStartTime) ? venueStartTime : serviceStartTime;
        LocalTime temp2 = serviceEndTime.isBefore(venueEndTime) ?
                serviceEndTime
                :

                serviceEndTime.isAfter(venueEndTime) ? venueEndTime : serviceEndTime;

        OffsetTime tempStartTime=OffsetTime.of(temp1,ZoneOffset.UTC).truncatedTo(ChronoUnit.MINUTES);
        OffsetTime tempEndTime=OffsetTime.of(temp2,ZoneOffset.UTC).truncatedTo(ChronoUnit.MINUTES);

        String appointmentDay=daysOfWeek.getKey(appointmentDetails.getAppointmentDate().getDayOfWeek().toString());
        PetType petType=requiredService.getPetType();
        ServiceType serviceType=requiredService.getServiceType();

        //List<AttendantAvailability> attendantAvailability = attendantAvailabilityRepository.findByAvailabilityTimRangeAndRetailer(appointmentDay,tempStartTime ,tempEndTime, RetailerContext.getRetailer(),false,petType.getPetTypeId());
        List<AttendantAvailability> attendantAvailability=attendantAvailabilityRepository.findByAvailableDayServiceTypeVenueAndPetType(appointmentDay,currentVenue.getVenueId(),petType.getPetTypeId(),RetailerContext.getRetailer(),false);
        if (!attendantAvailability.isEmpty()) {
            for (AttendantAvailability attendantAvailability1 : attendantAvailability) {
                // AttendantModelAvailabilityProjections requiredAttendant = attendantRepository.findAttendantAndAvailability(attendantAvailability1.getAttendantId());
                Attendant requiredAttendant = attendantAvailability1.getAttendant();
                Set<ServiceType> requiredServiceTypes=requiredAttendant.getServiceTypes();
                if(!requiredServiceTypes.contains(requiredService.getServiceType())){
                    continue;
                }
                LocalTime attendantStartTime = null;
                LocalTime attendantEndTime = null;
                int newAppointmentSizeConstraintCapacity = 0;

                //todo:  currently does not consider the attendant capacity because it just fetches appointments on that venue
                //Gets all the appointments between specified DateTime interval
                //Need to add multiple day support. Currently, we are only checking one day interval
                List<Appointment> currentAppointments = appointmentRepository.getAppointmentsByVenueAndDate(
                        currentVenue,
                        appointmentDetails.getAppointmentDate().toLocalDate(),
                        appointmentDetails.getAppointmentDate().plusDays(1).toLocalDate()
                );


                //    ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(TimeZoneContext.getTimeZone()));
                ZonedDateTime currentTimeUTC = ZonedDateTime.now(ZoneId.of(ZoneOffset.UTC.toString()));

                ZonedDateTime currentTime=currentTimeUTC.plusMinutes(daylightAddedOffsetMinutes);

                // Goes through service availability and sets the opening and closing time of the service for the required day of the week
                // for (AttendantModelAvailabilityProjections.Availability day : requiredAttendant.getAvailabilityDays()) {
                // if (DayOfWeek.valueOf(daysOfWeek.getDayOfWeek(attendantAvailability1.getAvailableDay().toUpperCase())).equals(appointmentDetails.getAppointmentDate().getDayOfWeek())) {
                attendantStartTime = attendantAvailability1.getAvailabilityOpenTime().toLocalTime();
                attendantEndTime = attendantAvailability1.getAvailabilityCloseTime().toLocalTime();
                // break;
                // }
                //}
                LocalTime startTime = attendantStartTime.isAfter(temp1) ?
                        attendantStartTime
                        :
                        attendantStartTime.isBefore(temp1) ? temp1 : attendantStartTime;
                LocalTime endTime = attendantEndTime.isBefore(temp2) ?
                        attendantEndTime
                        :
                        attendantEndTime.isAfter(temp2) ? temp2 : attendantEndTime;
                //  OffsetDateTime attendantStartTime = OffsetDateTime.parse(attendantAvailability.getDate().toString()+"T"+attendantAvailability.getAvailabilityOpenTime().toString().split("[+\\-Z]")[0]+"Z").atZoneSimilarLocal(ZoneId.of(TimeZoneContext.getTimeZone())).toOffsetDateTime();
                freeTimeSlots = SchedulerUtils.generateFreeTimeSlotsForDay(
                        currentVenue,
                        requiredAttendant,
                        requiredService,
                        currentAppointments,
                        appointmentDetails.getServiceDuration(),
                        requiredService.getFixedScheduleUnit(),
                        OffsetDateTime.parse(
                                appointmentDetails.getAppointmentDate().toLocalDate().toString() + "T" +
                                        startTime.toString().split("[+\\-Z]")[0] + "Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
                        ,
                        OffsetDateTime.parse(
                                appointmentDetails.getAppointmentDate().toLocalDate().toString() + "T" +
                                        endTime.toString().split("[+\\-Z]")[0] + "Z").atZoneSimilarLocal(ZoneId.of(ZoneOffset.UTC.toString())).toOffsetDateTime()
                        ,
                        //System.out.println("++++++++++++++++++++++"+OffsetDateTime.parse(localDate.toString()+"T"+offsetTime));
                        slotInterval
                );


                List<AppointmentSlotsDto> newFreeSlots = getUnblockedTimeSlots(blockDateInfoList, freeTimeSlots, autoBooking);
                filteredSlots = newFreeSlots.stream().filter(t -> t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).collect(Collectors.toList());

               /* for (AppointmentSlotsDto appointmentSlotsDto : filteredSlots) {
                    appointmentSlotsDto.setSlotStartIsoDateTime(appointmentSlotsDto.getSlotStartTime().toString());
                    appointmentSlotsDto.setSlotEndIsoDateTime(appointmentSlotsDto.getSlotEndTime().toString());
                }*/

                allTheSlots.addAll(filteredSlots);
            }
        }
        attendantSlotsMap= allTheSlots.stream().collect(Collectors.toMap(
                AppointmentSlotsDto::getAttendantName,
                slot -> {
                    List<AppointmentSlotsDto> initialList = new ArrayList<>();
                    initialList.add(slot);
                    return initialList;
                },
                (existingList, replacementList) -> {
                    existingList.addAll(replacementList);
                    return existingList;
                }
        ));

        return attendantSlotsMap;
    }

    @Override
    public void fetchAppointmentsWithCorrectedTimeAfterDST() throws EtailBookItException {
        Integer count=0;
        LocalDate appointmentDate = LocalDate.parse("2024-11-03");
        LocalDate daylightStartDate =LocalDate.parse("2024-03-10");
        List<Appointment> appointmentsAfterDST = appointmentRepository.findAppointmentsAfterDateAndRetailer(appointmentDate,
                RetailerContext.getRetailer());
        for (Appointment appointment : appointmentsAfterDST) {
            Integer timeSlotId = appointment.getTimeSlotId();
            if(timeSlotId!=null){
                count=count+1;
                Optional<TimeSlots> timeSlots = timeSlotRepository.findById(timeSlotId);
                if (timeSlots.isPresent()) {
                    Integer timeSlotClusterId = timeSlots.get().getTimeSlotCluster().getClusterId();
                    List<TimeSlots> timeSlotsList = timeSlotClusterRepository.findSlotByClusterId(timeSlotClusterId);
                    TimeSlots timeSlot = timeSlotsList.get(0);
                    OffsetDateTime clusterSlotStartTime = timeSlot.getSlotStartTime();
                    LocalDate clusterStartDate = clusterSlotStartTime.toLocalDate();
                    if (clusterStartDate.isBefore(appointmentDate) && clusterStartDate.isAfter(daylightStartDate)) {
                        customerSMSNotification.sendCorrectedTimeAfterDST(appointment);
                    }
                }
            }
        }
    }

     @Override
    public BaseResponseDto fetchAppointmentsAfterDST() throws EtailBookItException {
        LocalDate appointmentDate=LocalDate.parse("2024-11-03");
        LocalDate daylightStartDate =LocalDate.parse("2024-03-10");
         List<Integer> appointmentIds=new ArrayList<>();
        List<Appointment> appointmentsAfterDST=appointmentRepository.findAppointmentsAfterDateAndRetailer(appointmentDate,RetailerContext.getRetailer());
        Integer count=0;
        for(Appointment appointment:appointmentsAfterDST) {
            Integer timeSlotId = appointment.getTimeSlotId();
            if(timeSlotId!=null){
                count=count+1;
            Optional<TimeSlots> timeSlots = timeSlotRepository.findById(timeSlotId);
            if (timeSlots.isPresent()) {

                Integer timeSlotClusterId = timeSlots.get().getTimeSlotCluster().getClusterId();
                List<TimeSlots> timeSlotsList = timeSlotClusterRepository.findSlotByClusterId(timeSlotClusterId);
                TimeSlots timeSlot = timeSlotsList.get(0);
                OffsetDateTime clusterSlotStartTime = timeSlot.getSlotStartTime();
                LocalDate clusterStartDate = clusterSlotStartTime.toLocalDate();
                if (clusterStartDate.isBefore(appointmentDate) && clusterStartDate.isAfter(daylightStartDate)) {
                    OffsetTime appointmentTime = appointment.getTime();
                    OffsetTime correctedAppointmentTime = appointmentTime.plusMinutes(60);
                    OffsetDateTime correctedAppointmentOffsetStartTime = appointment.getOffsetStartTime().plusMinutes(60);
                    OffsetDateTime correctedAppointmentOffsetEndTime = appointment.getOffsetEndTime().plusMinutes(60);
                    appointment.setAppointmentStartDateAndTime(correctedAppointmentOffsetStartTime.toString());
                    appointment.setAppointmentEndDateAndTime(correctedAppointmentOffsetEndTime.toString());
                    appointment.setTime(correctedAppointmentTime);
                    appointment.setEndTime(correctedAppointmentOffsetEndTime.toOffsetTime());
                    appointmentRepository.save(appointment);
                }
            }
            }
            else{
                appointmentIds.add(appointment.getId());

            }
        }
         System.out.println("COUNT ::"+count);
        return new BaseResponseDto<>(Status.SUCCESS,appointmentIds);
    }

    @Override
    public BaseResponseDto getAppointmentsOfMonthView( OffsetDateTime startDateOffset, OffsetDateTime endDateOffset, String timeZone) throws EtailBookItException {
        long startTime = System.currentTimeMillis();
        
        try {
            LOGGER.debug("getAppointmentsOfMonthView called with startDate: {}, endDate: {}, timeZone: {}", 
                startDateOffset, endDateOffset, timeZone);
            
            // OPTIMIZATION 1: Validate input parameters
            if (startDateOffset == null || endDateOffset == null) {
                LOGGER.warn("Start date or end date is null, returning empty result");
                return new BaseResponseDto(Status.SUCCESS, new ArrayList<>());
            }
            
            if (timeZone == null || timeZone.trim().isEmpty()) {
                LOGGER.warn("Timezone is null or empty, using default UTC");
                timeZone = "UTC";
            }
            
            // OPTIMIZATION 2: Calculate timezone offset once and cache it
            Integer offsetDifference = calculateOffset(timeZone, startDateOffset);
            
            // OPTIMIZATION 3: Optimize date range processing
            endDateOffset = endDateOffset.withHour(23).withMinute(59).withSecond(59);
            
            ZoneId zoneId = ZoneId.of(timeZone);
            ZoneOffset offset = zoneId.getRules().getOffset(java.time.Instant.now());
            int offsetSeconds = offset.getTotalSeconds();
            
            if (offsetSeconds < 0) {
                endDateOffset = endDateOffset.plusSeconds(Math.abs(offsetSeconds));
            } else {
                endDateOffset = endDateOffset.minusSeconds(offsetSeconds);
            }
            
            OffsetDateTime startDate = startDateOffset;
            OffsetDateTime endDate = endDateOffset;
            
            LOGGER.debug("Fetching month view appointments for range: {} to {}", startDate, endDate);
            
            // OPTIMIZATION 4: Single optimized query with proper error handling
            List<AppointmentMonthViewProjection> dataListWithTimeRange = appointmentRepository.getAppointmentsOfMonthView(
                startDate, endDate, ServiceStatus.CANCELLED, RetailerContext.getRetailer(), offsetDifference
            );
            
            long executionTime = System.currentTimeMillis() - startTime;
            LOGGER.debug("getAppointmentsOfMonthView completed in {}ms, found {} appointments", 
                executionTime, dataListWithTimeRange.size());
            
            return new BaseResponseDto(Status.SUCCESS, dataListWithTimeRange);
            
        } catch (Exception e) {
            LOGGER.error("Error in getAppointmentsOfMonthView: {}", e.getMessage(), e);
            throw new EtailBookItException("Failed to retrieve month view appointments: " + e.getMessage());
        }
    }

    @Override
    public void fetchAppointmentWithId(List<Integer> appointmentIds) {
        for(Integer appointmentId :appointmentIds){
            Optional<Appointment> appointment= appointmentRepository.findById(appointmentId);
            if(appointment.isPresent()) {
                Appointment appointment1=appointment.get();
                OffsetTime appointmentTime = appointment1.getTime();
                OffsetTime correctedAppointmentTime = appointmentTime.plusMinutes(60);
                OffsetDateTime correctedAppointmentOffsetStartTime = appointment1.getOffsetStartTime().plusMinutes(60);
                OffsetDateTime correctedAppointmentOffsetEndTime = appointment1.getOffsetEndTime().plusMinutes(60);
                appointment1.setAppointmentStartDateAndTime(correctedAppointmentOffsetStartTime.toString());
                appointment1.setAppointmentEndDateAndTime(correctedAppointmentOffsetEndTime.toString());
                appointment1.setTime(correctedAppointmentTime);
                appointment1.setEndTime(correctedAppointmentOffsetEndTime.toOffsetTime());
                appointmentRepository.save(appointment1);
            }
        }
    }


    @Override
    public BaseResponseDto manualResendOfAppointmentExport(String retailer,OffsetDateTime selectedDate,String timeZone) throws Exception {

        byte[] excelData= processAppointmentsForSelectedDate(retailer,timeZone,selectedDate);
        customerSMSNotification.exportAppointmentsForSelectedDate(excelData,retailer,selectedDate);

        return new BaseResponseDto<>(Status.SUCCESS);
    }

    @Override
    public BaseResponseDto addAppointmentToWaitList(OffsetDateTime currentDate, String timeZone, String retailer,Integer pageNo,Integer pageSize,String sortBy) {

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        List<ServiceStatus> allowedStatuses = List.of(ServiceStatus.CREATED, ServiceStatus.CANCELLED);
        String startTime=currentDate.toString();
        Page<AppointmentDetailsProjection> appointmentDetailsProjections=appointmentRepository.findByStartDateStatusRetailer(startTime, retailer, allowedStatuses, paging);
        BaseResponseDto responseDto=new BaseResponseDto<>();
        responseDto.setData(appointmentDetailsProjections);
        return responseDto;
    }

    private byte[] processAppointmentsForSelectedDate(String retailer, String timeZone, OffsetDateTime selectedDate) throws IOException {
            LocalDate today=LocalDate.now();
            int pageSize = 50; // Batch size
            int pageNumber = 0;
            Integer offsetDifference;
            if(timeZone!=null){
                TimeZone tz = TimeZone.getTimeZone(timeZone);
                offsetDifference= tz.getOffset(new Date().getTime()) / 1000 / 60;
            }
            else{
                offsetDifference=0;
            }
            List<Appointment> appointments;
            List<Appointment> allAppointments = new ArrayList<>();
            do {
                Pageable pageable = PageRequest.of(pageNumber, pageSize);
                appointments = appointmentRepository
                        .findByDateServiceStatusAndRetailer(selectedDate.toLocalDate(),retailer, offsetDifference,pageable).getContent();
                allAppointments.addAll(appointments);
                pageNumber++;
            } while (!appointments.isEmpty());
            return excelExportService.exportAppointments(allAppointments,timeZone);

    }

    private String getDefaultTimezoneOfARetailer(String retailer) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(retailer);
        String acccessToken = authToken.getBody().getAccess_token();
        String url=auth_Uri+retailer+"/api/v1/bookit/retailer/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema",retailer);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<RetailerProfileDto> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                RetailerProfileDto.class
        );
        if(responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody()!=null){
            RetailerProfileDto response = responseEntity.getBody();
            return response.getDefault_store_timezone();
        }
        else {
            LOGGER.error("ERROR FROM ECOM API RESPONSE TO GET THE DEFAULT TIME ZONE");
            return null;
        }

    }

    private Page<AppointmentsListing> getAppointmentsForExcel(Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer pet, Integer attendant, Integer venue, String serviceStatus, Integer petType, String search, Integer service, OffsetDateTime startDateOffset, OffsetDateTime endDateOffset, String timeZone) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        ServiceStatus status = (serviceStatus != null) ? ServiceStatus.valueOf(serviceStatus) : null;
        Page<AppointmentsListing> appointments;
        if(startDateOffset != null && endDateOffset != null) {
            endDateOffset = endDateOffset.withHour(23).withMinute(59).withSecond(59);
            ZoneId zoneId = ZoneId.of(timeZone);
            ZoneOffset offset = zoneId.getRules().getOffset(java.time.Instant.now());
            int offsetSeconds = offset.getTotalSeconds();
            if(offsetSeconds < 0)
                endDateOffset = endDateOffset.plusSeconds(Math.abs(offsetSeconds));
            else
                endDateOffset = endDateOffset.minusSeconds(offsetSeconds);
            String startDate = startDateOffset.toString();
            String endDate = endDateOffset.toString();
            appointments = appointmentRepository.getAllAppointments(
                    customer, pet, attendant, venue, search, service,startDate, endDate, status, petType,
                    RetailerContext.getRetailer(), paging
            );
        }
        else {
            appointments = appointmentRepository.getAllAppointments(
                    customer, pet, attendant, venue, search,service, status, petType, RetailerContext.getRetailer(), paging
            );
        }
        return appointments;
    }

    public void populateSource(){
        List<Appointment> appointments = appointmentRepository.findAll();
        for(Appointment appointment : appointments){
            if(appointment.getSource() == null) {
                appointment.setSource("Bookit Appointment");
            }
        }
        appointmentRepository.saveAll(appointments);
    }

    public OffsetDateTime calculateEndDateTime(OffsetDateTime startTime, com.sayone.etailbookit.model.Service service){
        OffsetDateTime endTime = null;
        switch (service.getFixedScheduleUnit()){
            case "DAY" :
                endTime = startTime.plusDays(service.getFixedScheduleValue());
                break;
            case "MINUTE" :
                endTime = startTime.plusMinutes(service.getFixedScheduleValue());
                break;
            case "HOUR" :
                endTime = startTime.plusHours(service.getFixedScheduleValue());
                break;
        }
        return endTime;
    }
}
