package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.component.CustomerEmailNotification;
import com.sayone.etailbookit.controller.AppointmentController;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.ServiceMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IService;
import com.sayone.etailbookit.service.KinesisService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Time;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;


@org.springframework.stereotype.Service
public class IServiceImpl implements IService {

    @Autowired
    ServiceTypeRepository serviceTypeRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    AddonServiceRepository addonServiceRepository;

    @Autowired
    VaccinationRecordsRepository vaccinationRecordsRepository;

    @Autowired
    DocumentOptionRepository documentOptionRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    ServiceAvailabilityRepository serviceAvailabilityRepository;

    @Autowired
    ServiceBreedsInformationRepository serviceBreedsInformationRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    AllergiesRepository allergiesRepository;

    @Autowired
    VetInformationRepository vetInformationRepository;

    @Autowired
    HairLengthRepository hairLengthRepository;

    @Autowired
    CustomerEmailNotification customerEmailNotification;

    @Autowired
    ServiceHistoryRepository serviceHistoryRepository;

    @Autowired
    PetShampooRepository petShampooRepository;

    @Autowired
    PetCologneRepository petCologneRepository;

    @Autowired
    PetTypeConfigurationRepository petTypeConfigurationRepository;

    @Autowired
    KinesisService kinesisService;

    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    BreedRepository breedRepository;

    @Autowired
    Validator validator;

    private static Logger LOGGER = LoggerFactory.getLogger(AppointmentController.class);

    private static final String EXTENDED_BEHAVIOUR = "extended_behaviour";

    @Override
    public Boolean addService(ServiceDto serviceDto) throws EtailBookItException {
        validator.validateService(null, serviceDto);

        Service service = generateServiceValue(serviceDto);
        try {
            service = serviceRepository.save(service);
            service.setInternalItemNumber(service.getInternalItemNumber()+service.getServiceId());
            service = serviceRepository.save(service);
        }catch (Exception e){
            throw new EtailBookItException(e);
        }

        ServiceSyncDto serviceSyncDto = new ServiceSyncDto();
        serviceSyncDto.setId("BKI-"+RetailerContext.getRetailer() + "-" + service.getServiceId().toString());
        serviceSyncDto.setTitle(service.getName());
        serviceSyncDto.setInternalItemNumber(service.getInternalItemNumber());
        serviceSyncDto.setServiceType(service.getServiceType().getName());
        serviceSyncDto.setIsTipsAllowed(service.getIsTipsAllowed());
        serviceSyncDto.setPetType(service.getPetType().getName());
        serviceSyncDto.setPrice(BigDecimal.ZERO);
        serviceSyncDto.setIsActive(true);
        serviceSyncDto.setIsArchive(false);
        serviceSyncDto.setIsTaxable(service.getIsTaxable());
        serviceSyncDto.setPriceType(Enums.PriceType.VARIABLE);
        serviceSyncDto.setSchema(RetailerContext.getRetailer());
        kinesisService.syncService(RetailerContext.getRetailer(), Collections.singletonList(serviceSyncDto));

        return Optional.ofNullable(service.getServiceId()).orElse(0) != 0;

    }



    @Override
    public ServiceDetailsDto getServiceById(int serviceId) throws EtailBookItException {
        Service service = serviceRepository.getServiceDetails(serviceId);
        if(service == null) {
            throw new EntityNotFoundException("Service not found");
        }
        return ServiceMapper.toServiceDtoNew(service);
    }

    @Override
    @Transactional
    public void deleteServiceById(int serviceId) throws EtailBookItException {
        //scenario : if service is already used in an appointmet and trying to delete the service by retailer.
        Optional<Service> service = serviceRepository.findById(serviceId);
        if(service.isPresent()){
            List<Appointment> existingAppointment = appointmentRepository.findByService(service.get());
            if(!existingAppointment.isEmpty()) {
                throw new BadRequestException("Service is used in an appointment");
            }
            if(service.get().getName().equalsIgnoreCase("Dog Grooming DEV")){
                throw new EtailBookItException("This is test data and can not be deleted");
            }
            Long iIN=310000000000L;
            ServiceSyncDto serviceSyncDto = new ServiceSyncDto();
            serviceSyncDto.setId("BKI-"+RetailerContext.getRetailer() + "-" + service.get().getServiceId().toString());
            serviceSyncDto.setTitle(service.get().getName());
            serviceSyncDto.setInternalItemNumber(iIN+service.get().getServiceId());
            serviceSyncDto.setServiceType(service.get().getServiceType().getName());
            serviceSyncDto.setIsTipsAllowed(service.get().getIsTipsAllowed());
            serviceSyncDto.setPetType(service.get().getPetType().getName());
            serviceSyncDto.setPrice(BigDecimal.ZERO);
            serviceSyncDto.setIsActive(true);
            serviceSyncDto.setIsArchive(true);
            serviceSyncDto.setIsTaxable(service.get().getIsTaxable());
            serviceSyncDto.setPriceType(Enums.PriceType.VARIABLE);
            serviceSyncDto.setSchema(RetailerContext.getRetailer());
            kinesisService.syncService(RetailerContext.getRetailer(), Collections.singletonList(serviceSyncDto));
            serviceRepository.deleteById(serviceId);
        }
        else {
            throw new EntityNotFoundException("Specified service is not found ");
        }
    }

    @Override
    @Transactional
    public Boolean updateService(ServiceDto serviceDto) throws EtailBookItException {
        validator.validateService(serviceDto.getServiceId(), serviceDto);
        Service service = generateServiceValue(serviceDto);
        Long iIN=310000000000L;
       // Service existingService = serviceRepository.getServiceDetails(serviceDto.getServiceId());
        service.setServiceId(serviceDto.getServiceId());
        if(Utils.isNotEmpty(serviceDto.getAvailabilityDays()))
            serviceAvailabilityRepository.deleteByService(service);
        try {
            serviceRepository.save(service);
         //   if(checkDifference(service, existingService)) {
                ServiceSyncDto serviceSyncDto = new ServiceSyncDto();
                serviceSyncDto.setId("BKI-"+RetailerContext.getRetailer() + "-" + service.getServiceId().toString());
                serviceSyncDto.setTitle(service.getName());
                serviceSyncDto.setInternalItemNumber(iIN+service.getServiceId());
                serviceSyncDto.setServiceType(service.getServiceType().getName());
                serviceSyncDto.setIsTipsAllowed(service.getIsTipsAllowed());
                serviceSyncDto.setPetType(service.getPetType().getName());
                serviceSyncDto.setPrice(BigDecimal.ZERO);
                serviceSyncDto.setIsActive(true);
                serviceSyncDto.setIsArchive(false);
                serviceSyncDto.setIsTaxable(service.getIsTaxable());
                serviceSyncDto.setPriceType(Enums.PriceType.VARIABLE);
                serviceSyncDto.setSchema(RetailerContext.getRetailer());
                kinesisService.syncService(RetailerContext.getRetailer(), Collections.singletonList(serviceSyncDto));
         //   }
            return true;
        }catch (Exception e){
            throw new EtailBookItException(e);
        }
    }

    @Override
    public List<ServiceNameListing> getAllService() {

        List<ServiceNameListing> serviceList =  serviceRepository.getAll(RetailerContext.getRetailer());
        return serviceList;

    }

    @Override
    public Page<ServiceListingProjection> getServiceByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<ServiceListingProjection> serviceList ;
        if(search.isPresent() && search.hashCode() != 0){
            serviceList = serviceRepository.findByKeyword(search.get().toLowerCase(), RetailerContext.getRetailer(),false,paging);
        } else{
            serviceList = serviceRepository.getAll(RetailerContext.getRetailer(), false,paging);
        }
        return serviceList;

    }

    @Override
    public List<ServiceListingProjection> getServiceByServiceType(Integer serviceTypeId) throws EtailBookItException{

        ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
        if( serviceType == null){
            throw new EntityNotFoundException("Service Type not found");
        }
        List<ServiceListingProjection> services = serviceRepository.findServicesByServiceType(serviceType, RetailerContext.getRetailer());
        return services;

    }

    @Override
    public List<ServiceDto> getActiveService() throws EtailBookItException {
        List<Service> serviceList =  serviceRepository.findByIsActiveAndRetailerAndDeleted(true, RetailerContext.getRetailer(),false);
        return ServiceMapper.toServiceDtoList(serviceList);

    }

    private Service generateServiceValue(ServiceDto serviceDto) throws BadRequestException, EntityNotFoundException {
        Service service = ServiceMapper.toServiceEntity(serviceDto);
        service.setServiceType(serviceTypeRepository.findByServiceTypeId(serviceDto.getServiceType().getId()));
        service.setPetType(petTypeRepository.findByPetTypeId(serviceDto.getPetType().getId()));
        service.setPetParentCanSelectAttendant(serviceDto.getPetParentCanSelectAttendant());
        service.setPre_buffer_mins(serviceDto.getPre_buffer_mins());
        service.setPostBufferMins(serviceDto.getPostBufferMins());

        // Get selected petsize
        Set<GeneralPetSize> generalPetSizeSet = new HashSet<>();
        if (serviceDto.getGeneralSizes() != null) {
            for (GeneralPetSizeDto generalPetSizeDto : serviceDto.getGeneralSizes()) {
                GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeDto.getId());
                generalPetSizeSet.add(generalPetSize);
            }
        }
        service.setGeneralPetSize(generalPetSizeSet);

        // Get selected venues
        Set<Venue> venuesSet = new HashSet<>();
        for (VenueDto venueDto : serviceDto.getVenues()) {
            Venue venue = venueRepository.findByVenueId(venueDto.getId());
            venuesSet.add(venue);
        }
        service.setVenues(venuesSet);

        // Get selected Attendants
        Set<Attendant> attendantSet = new HashSet<>();
        for (AttendantDto attendantDto : serviceDto.getAttendants()) {
            Attendant attendant = attendantRepository.findByAttendantId(attendantDto.getAttendantId());
            attendantSet.add(attendant);
        }
        service.setAttendants(attendantSet);
        //get Temperaments
        Set<Temperament> temperamentsSet=new HashSet<>();
        if (serviceDto.getTemperaments() != null) {
            for (TemperamentDto temperamentDto : serviceDto.getTemperaments()) {
                Temperament temperament = temperamentRepository.findByTemperamentId(temperamentDto.getId());
                temperamentsSet.add(temperament);
            }
        }
        service.setTemperaments(temperamentsSet);

        // Set Add-on services
        service.setAddonsOffered(serviceDto.getAddonsOffered());
        Set<AddonService> addonServiceSet = new HashSet<>();
        if(!ObjectUtils.isEmpty(serviceDto.getAddonServicesOffered())) {
            for (AddonServiceDto addonServiceDto : serviceDto.getAddonServicesOffered()) {
                AddonService addonService = addonServiceRepository.findByAddonServiceId(addonServiceDto.getId());
                addonServiceSet.add(addonService);
            }
        }

        service.setAddonServices(addonServiceSet);

        // Set Shampoos
        service.setShampoosOffered(serviceDto.getShampoosOffered());
        Set<PetShampoo> shampoos = new HashSet<>();
        if(!ObjectUtils.isEmpty(serviceDto.getShampoos())) {
            for (PetShampooDto shampooDto : serviceDto.getShampoos()) {
                Optional<PetShampoo> shampoo = petShampooRepository.findById(shampooDto.getId());
                shampoos.add(shampoo.get());
            }
        }

        service.setShampoos(shampoos);

        // Set Colognes
        service.setColognesOffered(serviceDto.getColognesOffered());
        Set<PetCologne> colognes = new HashSet<>();
        if(!ObjectUtils.isEmpty(serviceDto.getColognes())) {
            for (PetCologneDto cologneDto : serviceDto.getColognes()) {
                Optional<PetCologne> cologne = petCologneRepository.findById(cologneDto.getId());
                colognes.add(cologne.get());
            }
        }

        service.setColognes(colognes);

        // Get selected vaccinationRecordsSet
        Set<VaccinationRecords> vaccinationRecordsSet = new HashSet<>();
        for (VaccinationRecordsDto vaccinationRecordDto : serviceDto.getAvailableParticipantVaccinations()) {
            VaccinationRecords vaccinationRecords = vaccinationRecordsRepository.findByVaccinationRecordId(vaccinationRecordDto.getId());
            vaccinationRecordsSet.add(vaccinationRecords);
        }
        service.setAvailableParticipantVaccinations(vaccinationRecordsSet);

        // Get selected DocumentOption
        Set<DocumentOption> documentOptionSet = new HashSet<>();
        for (DocumentOptionDto documentOptionDto : serviceDto.getAvailableParticipantDocuments()) {
            DocumentOption documentOption = documentOptionRepository.findByDocumentOptionId(documentOptionDto.getId());
            documentOptionSet.add(documentOption);
        }
        service.setAvailableParticipantDocuments(documentOptionSet);
        service.setAvailabilityInterval(serviceDto.getAvailabilityInterval());
        service.setAvailabilityIntervalUnit(serviceDto.getAvailabilityIntervalUnit());


        //validating with pet type configuraion
        PetType petType = petTypeRepository.findByPetTypeId(serviceDto.getPetType().getId());
        List<PetTypeConfiguration> petTypeConfiguration = petTypeConfigurationRepository.findByPetTypePetTypeId(serviceDto.getPetType().getId());

        for (PetTypeConfiguration petTypeConfigurationList : petTypeConfiguration) {
            switch (petTypeConfigurationList.getName().name()) {
                case "WEIGHT_RANGE": {
                    if (petTypeConfigurationList.getDisplayType().ordinal() == 0 && serviceDto.getAvailableParticipantWeights() != null && serviceDto.getAvailableParticipantWeights()){
                        throw new EntityNotFoundException("Weight range is not configured for pet type :::" + petType.getName());
                    }
                }
                case "TEMPERAMENT": {
                    if (petTypeConfigurationList.getDisplayType().ordinal() == 0 && serviceDto.getAvailableParticipantTemperaments() != null && serviceDto.getAvailableParticipantTemperaments()){
                        throw new EntityNotFoundException("Temperament is not configured for pet type :::" + petType.getName());
                    }
                }
                case "ALLERGIES": {
                    if (petTypeConfigurationList.getDisplayType().ordinal() == 0 && serviceDto.getAvailableParticipantAllergies() != null && serviceDto.getAvailableParticipantAllergies()){
                        throw new EntityNotFoundException("Allergies is not configured for pet type :::" + petType.getName());
                    }
                }
                case "VET_INFO": {
                    if (petTypeConfigurationList.getDisplayType().ordinal() == 0 && serviceDto.getRequireVetInfo() != null && serviceDto.getRequireVetInfo()){
                        throw new EntityNotFoundException("Vet information is not configured for pet type :::" + petType.getName());
                    }
                }
                case "HAIR_LENGTH": {
                    if (petTypeConfigurationList.getDisplayType().ordinal() == 0 && serviceDto.getRequireHairLengthInfo() != null && serviceDto.getRequireHairLengthInfo()){
                        throw new EntityNotFoundException("Hair length is not configured for pet type :::" + petType.getName());
                    }
                }
            }
        }

        service.setAvailableParticipantWeights(serviceDto.getAvailableParticipantWeights());
        service.setAvailableParticipantTemperaments(serviceDto.getAvailableParticipantTemperaments());
        service.setAvailableParticipantAllergies(serviceDto.getAvailableParticipantAllergies());
        service.setRequireVetInfo(serviceDto.getRequireVetInfo());
        service.setRequirePetThreatReactions(serviceDto.getRequireThreatReactions());
        service.setRequireHairLengthInfo(serviceDto.getRequireHairLengthInfo());

        if(serviceDto.getRequirePetBehaviour().equals(true)){
            Configuration configuration = configurationRepository.findByNameAndRetailer(EXTENDED_BEHAVIOUR, RetailerContext.getRetailer());
            if(configuration.isActive() == false){
                throw new EntityNotFoundException("Pet behaviour is not configured for this pet type :::" + petType.getName());
            }
        }
        service.setRequirePetBehaviour(serviceDto.getRequirePetBehaviour());
        //check cancellation fee
        if(serviceDto.getChargeCancelationFee()){
            service.setChargeCancelationFee(Boolean.TRUE);
            service.setCancelationAmountValue(serviceDto.getCancelationAmountValue());
            service.setCancelationAmountType(serviceDto.getCancelationAmountType());
        }

        // getting available time of this service
        if (Utils.isNotEmpty(serviceDto.getAvailabilityDays())) {
            Set<ServiceAvailability> serviceAvailabilities = new HashSet();

            for(AvailabilityDto serviceAvailability : serviceDto.getAvailabilityDays()){
                ServiceAvailability availability = new ServiceAvailability();
                availability.setAvailabilityCloseTime(
                        OffsetTime.parse(serviceAvailability.getAvailabilityCloseTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailabilityOpenTime(
                        OffsetTime.parse(serviceAvailability.getAvailabilityOpenTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailableDay(serviceAvailability.getAvailableDay());
                availability.setService(service);
                availability.setRetailer(RetailerContext.getRetailer());
                serviceAvailabilities.add(availability);
            }
            service.setAvailabilityDays(serviceAvailabilities);
        }

        if(ObjectUtils.isEmpty(serviceDto.getServiceId())) {
            Long iIN = 310000000000L;
            service.setInternalItemNumber(iIN);
        }
        else {
            Long iIN = serviceRepository.getIINForId(serviceDto.getServiceId());
            service.setInternalItemNumber(iIN);
        }

        Set<ServiceBreedsInformation> serviceBreedsInformations = new HashSet<>();
        if(!ObjectUtils.isEmpty(serviceDto.getServiceBreedInfos())) {
            if (serviceDto.getServiceId() != null) {
                List<Integer> validIds = new ArrayList<>();
                serviceDto.getServiceBreedInfos().forEach(value -> validIds.add(value.getId()));
                serviceBreedsInformationRepository.deleteAllByIdNotInAndServiceServiceId(validIds, serviceDto.getServiceId());
            }
            for (ServiceBreedInfoDto serviceBreedInfoDto : serviceDto.getServiceBreedInfos()) {
                Optional<Breed> optionalBreed = breedRepository.findById(serviceBreedInfoDto.getBreedId());
                if (!optionalBreed.isPresent()) {
                    throw new EntityNotFoundException("Breed information not found");
                }
                ServiceBreedsInformation serviceBreedsInformation = new ServiceBreedsInformation();
//                if (serviceBreedInfoDto.getId() != null)
//                    serviceBreedsInformation.setId(serviceBreedInfoDto.getId());
                serviceBreedsInformation.setBreed(optionalBreed.get());
                serviceBreedsInformation.setDurationType(serviceBreedInfoDto.getDurationType());
                serviceBreedsInformation.setDuration(serviceBreedInfoDto.getDuration());
                serviceBreedsInformation.setChargeAmount(serviceBreedInfoDto.getChargeAmount());
                serviceBreedsInformation.setRetailer(RetailerContext.getRetailer());
                serviceBreedsInformation.setService(service);
                serviceBreedsInformations.add(serviceBreedsInformation);
            }
        } else if (serviceDto.getServiceId() != null) {
            serviceBreedsInformationRepository.deleteAllByServiceServiceId(serviceDto.getServiceId());
        }


        service.setServiceBreedsInformations(serviceBreedsInformations);

        service.setRetailer(RetailerContext.getRetailer());

        return service;
    }

    @Override
    public void syncServices() {
        List<Service> services = serviceRepository.getUnsyncedServices(RetailerContext.getRetailer());
        List<ServiceSyncDto> serviceSyncDtos = new ArrayList<>();
        Long iIN=310000000000L;
        for(Service service : services) {
            try{
            ServiceSyncDto serviceSyncDto = new ServiceSyncDto();
            serviceSyncDto.setId("BKI-"+RetailerContext.getRetailer() + "-" + service.getServiceId().toString());
            serviceSyncDto.setTitle(service.getName());
            service.setInternalItemNumber(iIN+service.getServiceId());
            serviceRepository.save(service);
            serviceSyncDto.setInternalItemNumber(iIN+service.getServiceId());
            serviceSyncDto.setServiceType(service.getServiceType() ==  null ? "" : service.getServiceType().getName());
            serviceSyncDto.setPetType(service.getPetType().getName());
            serviceSyncDto.setIsTipsAllowed(service.getIsTipsAllowed());
            serviceSyncDto.setPrice(BigDecimal.ZERO);
            serviceSyncDto.setIsActive(true);
            serviceSyncDto.setIsArchive(false);
            serviceSyncDto.setIsTaxable(service.getIsTaxable());
            serviceSyncDto.setPriceType(Enums.PriceType.VARIABLE);
            serviceSyncDto.setSchema(RetailerContext.getRetailer());
            serviceSyncDtos.add(serviceSyncDto);
            LOGGER.info("---------Each service sync dto-----"+serviceSyncDto);}
            catch (Exception e){
                LOGGER.info("Exception thrown while syncing service dto with service id::"+service.getServiceId()+" with Exception"+e);
            }
        };
        LOGGER.info("=======Service SyncDtos========="+serviceSyncDtos);
        kinesisService.syncService(RetailerContext.getRetailer(), serviceSyncDtos);
    }

    public boolean checkDifference(Service newService, Service oldService) {
        if(!newService.getName().equals(oldService.getName())) {
            return true;
        }
        if(!newService.getIsTaxable().equals(oldService.getIsTaxable())) {
            return true;
        }
        if(!newService.getServiceType().equals(oldService.getServiceType())) {
            return true;
        }
        if(!newService.getPetType().equals(oldService.getPetType())) {
            return true;
        }
        if(!newService.getIsTipsAllowed().equals(oldService.getIsTipsAllowed())) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Integer updateInternalItemNumber() throws EtailBookItException{

        try {
            List<Service> serviceList =  serviceRepository.getAllServices((RetailerContext.getRetailer()));
            for (Service services:serviceList){
                    services.setInternalItemNumber(null);
                    serviceRepository.save(services);
            }
        }catch (Exception e){
            throw new EtailBookItException(e);
        }
        List<Service> updatedServices=  serviceRepository.getUnsyncedServices(RetailerContext.getRetailer());
        return updatedServices.size();
    }

    @Override
    public Page<ServiceListingProjection> getServiceByRetailer(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search, String retailer)throws EtailBookItException {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<ServiceListingProjection> serviceList ;
        if(search.isPresent() && search.hashCode() != 0){
            serviceList = serviceRepository.findByKeyword(search.get().toLowerCase(), RetailerContext.getRetailer(),false,paging);
        } else{
            serviceList = serviceRepository.getAll(RetailerContext.getRetailer(), false,paging);
        }

        return serviceList;
    }
}
