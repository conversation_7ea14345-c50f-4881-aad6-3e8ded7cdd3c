package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.component.CustomerEmailNotification;
import com.sayone.etailbookit.component.CustomerSMSNotification;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.ServiceHistoryMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IServiceHistoryService;
import com.sayone.etailbookit.service.WaitListEntryService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class ServiceHistoryService implements IServiceHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceHistoryService.class);

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    CombsRepository combsRepository;

    @Autowired
    BladesRepository bladesRepository;

    @Autowired
    PetShampooRepository petShampooRepository;

    @Autowired
    PetCologneRepository petCologneRepository;

    @Autowired
    ServiceHistoryRepository serviceHistoryRepository;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    HairTextureRepository hairTextureRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    PetVetInformationRepository petVetInformationRepository;

    @Autowired
    PetEmergencyContactRepository petEmergencyContactRepository;

    @Autowired
    PersonalityParameterRepository personalityParameterRepository;

    @Autowired
    UnfriendlyBehaviourTriggerRepository unfriendlyBehaviourTrigger;

    @Autowired
    FeedingInformationRepository feedingInformationRepository;

    @Autowired
    ServiceHistoryAddNotesRepository serviceHistoryAddNotesRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    CustomerEmailNotification customerEmailNotification;

    @Autowired
    CustomerSMSNotification customerSMSNotification;

    @Autowired
    GoogleCalendarService googleCalendarService;

    @Autowired
    QuoteAdjustmentRepository quoteAdjustmentRepository;

    @Autowired
    WaitListEntryService waitListEntryService;

    @Autowired
    TimeSlotRepository timeSlotRepository;

    @Autowired
    WaiverOfLiabilityRepository waiverOfLiabilityRepository;

    private static DateTimeFormatter monthDateYearFormat = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public PaymentDto startService(int appointmentId, String serviceStartAt,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException {
        /*OffsetDateTime serviceUserTimezone = ZonedDateTime.parse(serviceStartAt + OffsetContext.getOffset()).
                toInstant().atOffset(ZoneOffset.UTC).toZonedDateTime().toOffsetDateTime();*/

        Optional<Appointment> appointmentOptional = appointmentRepository.findById(appointmentId);
        if (appointmentOptional.isPresent()) {
            Appointment appointment = appointmentOptional.get();
            LocalDateTime createdAT = appointment.getCreatedAt();
           /* OffsetDateTime createdTimezone = ZonedDateTime.parse(createdAT + OffsetContext.getOffset()).
                    toInstant().atOffset(ZoneOffset.UTC).toZonedDateTime().toOffsetDateTime();*/
         /*   if (serviceUserTimezone.isBefore(createdTimezone)) {
                throw new BadRequestException("Service start time should greater than created time");
            }*/

            if(appointment.getSource() == null)
                appointment.setSource("Bookit Appointment");
            if(!appointment.getWaiverAcknowledged()){
                WaiverOfLiability waiverOfLiability = waiverOfLiabilityRepository.findByRetailer(RetailerContext.getRetailer());
                if(waiverOfLiability!=null){
                    if(waiverOfLiability.getRequireCustomerSignature()){
                        throw new EtailBookItException("Waiver of Liability needs to be signed in order to start this appointment");
                    }
                }

            }
            appointment.setServiceStatus(ServiceStatus.STARTED);
            appointment.setServiceStartAt(OffsetDateTime.parse(serviceStartAt));
           // appointment.setServiceStartAt(ZonedDateTime.parse(serviceStartAt + OffsetContext.getOffset()).toInstant().atOffset(ZoneOffset.UTC).toZonedDateTime().toOffsetDateTime());
            if(appointment.getService().getPaymentAtBeginningOfService()) {
                appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
            }
            if(updatedCustomerId!=null && updatedCustomerName!=null){
                appointment.setUpdatedCustomerId(updatedCustomerId);
                appointment.setUpdatedCustomerName(updatedCustomerName);
            }
            appointment = appointmentRepository.save(appointment);
            
            // Update Google Calendar event status if attendant has it enabled
            try {
                googleCalendarService.updateCalendarEvent(appointment);
            } catch (Exception e) {
                logger.warn("Failed to update Google Calendar event: {}", e.getMessage());
                // Don't fail service start if calendar sync fails
            }
            
            return AmountCalculation.getPaymentDetails(appointment);
        } else {
            throw new EntityNotFoundException("Appointment not found with id :::" + appointmentId);
        }
    }

    @Override
    @Transactional
    public PaymentDto endService(ServiceHistoryDto serviceHistoryDto, int appointmentId) throws EtailBookItException {

        PaymentDto paymentDetails = new PaymentDto();
        Optional<Appointment> appointmentOptional = appointmentRepository.findById(appointmentId);
        if (appointmentOptional.isPresent()) {
            Appointment appointment = appointmentOptional.get();
            /*if (appointment.getServiceStartAt().isAfter(
                    OffsetDateTime.parse(serviceHistoryDto.getServiceEndAt() + OffsetContext.getOffset())
            )) {
                throw new BadRequestException("Service end date and time should be greater than start date and time");
            }*/
            if (appointment.getServiceStatus() == ServiceStatus.COMPLETE) {
                throw new BadRequestException("Service cannot be ended because it has already been completed");
            }
            if (appointment.getServiceStatus() == ServiceStatus.CANCELLED) {
                throw new BadRequestException("Service cannot be ended because it has been cancelled");
            }
            /*appointment.setServiceEndAt(ZonedDateTime.parse(serviceHistoryDto.getServiceEndAt() + OffsetContext.getOffset())
                    .toInstant().atOffset(ZoneOffset.UTC).toZonedDateTime().toOffsetDateTime());*/
            appointment.setServiceEndAt(OffsetDateTime.parse(serviceHistoryDto.getServiceEndAt()));
            Duration duration = Duration.between(appointment.getServiceStartAt(), appointment.getServiceEndAt());
            String stringValueDurationHour = String.valueOf(duration.toHours());
            String stringValueDurationDay = String.valueOf(duration.toDays());
            if ((duration.toHours()) >= 24) {
                appointment.setDuration(stringValueDurationDay + "  days");
            } else {
                appointment.setDuration(stringValueDurationHour + " hours");
            }
            ServiceHistory serviceHistory = ServiceHistoryMapper.toServiceHistoryEntity(serviceHistoryDto);
            ServiceHistory existingServiceHistory = serviceHistoryRepository.findByAppointmentId(appointmentId);
            if (existingServiceHistory != null) serviceHistory.setId(existingServiceHistory.getId());
            Optional<Appointment> appointmentService = appointmentRepository.findById(appointmentId);
            com.sayone.etailbookit.model.Service serviceData = new com.sayone.etailbookit.model.Service();
            if (appointmentService.isPresent()) {
                Integer serviceId = appointmentService.get().getService().getServiceId();
                serviceData = serviceRepository.findByServiceId(serviceId);
            }
            if (ObjectUtils.isNotEmpty(serviceHistoryDto.getBladeId())) {
                Optional<Blades> blades = bladesRepository.findById(serviceHistoryDto.getBladeId());
                if (blades.isPresent()) {
                    serviceHistory.setBlade(blades.get());
                } else {
                    throw new EntityNotFoundException("Blades not found with ID :::" + serviceHistoryDto.getBladeId());
                }
            } else {
                if (serviceData.getRequireCombsAndBladesUsedPostService().equals(true))
                    throw new EntityNotFoundException("Please add Blade information which is configured in service");
            }
            if(serviceHistoryDto.getNotes()!=null){
                List<ServiceHistoryAddNotes> serviceHistoryAddNotes = appointment.getAddNotes();

                ServiceHistoryAddNotes newNote = new ServiceHistoryAddNotes();
                newNote.setValue(serviceHistoryDto.getNotes());
                newNote.setAppointment(appointment);
                newNote.setRetailer(RetailerContext.getRetailer());
                newNote.setUserName("");
                serviceHistoryAddNotes.add(newNote);
                appointment.setAddNotes(serviceHistoryAddNotes);
            }
            if (ObjectUtils.isNotEmpty(serviceHistoryDto.getCombId())) {
                Optional<Combs> combs = combsRepository.findById(serviceHistoryDto.getCombId());
                if (combs.isPresent()) {
                    serviceHistory.setComb(combs.get());
                } else {
                    throw new EntityNotFoundException("Combs not found with ID :::" + serviceHistoryDto.getCombId());
                }
            } else {
                if (serviceData.getRequireCombsAndBladesUsedPostService().equals(true))
                    throw new EntityNotFoundException("Please add Combs information which is configured in service");
            }

            if (ObjectUtils.isNotEmpty(serviceHistoryDto.getPetShampooId())) {
                Optional<PetShampoo> petShampoo = petShampooRepository.findById(serviceHistoryDto.getPetShampooId());
                if (petShampoo.isPresent()) {
                    serviceHistory.setPetShampoo(petShampoo.get());
                } else {
                    throw new EntityNotFoundException("Pet Shampoo not found with ID :::" + serviceHistoryDto.getPetShampooId());
                }
            } else {
                if (serviceData.getRequireShampooUsedPostService().equals(true))
                    throw new EntityNotFoundException("Please add Shampoo information which is configured in service");
            }

            if (ObjectUtils.isNotEmpty(serviceHistoryDto.getPetCologneId())) {
                Optional<PetCologne> petCologne = petCologneRepository.findById(serviceHistoryDto.getPetCologneId());
                if (petCologne.isPresent()) {
                    serviceHistory.setPetCologne(petCologne.get());
                } else {
                    throw new EntityNotFoundException("Pet Cologne not found with ID :::" + serviceHistoryDto.getBladeId());
                }
            } else {
                if (serviceData.getRequireCologneUsedPostService().equals(true))
                    throw new EntityNotFoundException("Please add Cologne information which is configured in service");
            }
            //quote adjustment config
            BigDecimal quoteAdjustmentPriceSum=BigDecimal.ZERO;
            Set<QuoteAdjustments> quoteAdjustmentsSet=appointment.getQuoteAdjustments();
            if(ObjectUtils.isNotEmpty(serviceHistoryDto.getAdjustmentQuotes())){
                Validator.validateQuoteService(serviceHistoryDto.getAdjustmentQuotes());
                for(QuoteAdjustmentDto quoteAdjustmentDto:serviceHistoryDto.getAdjustmentQuotes()){
                    if(quoteAdjustmentDto.getQuoteAdjustmentId()!=null){
                        QuoteAdjustments quoteAdjustments=quoteAdjustmentRepository.findByQuoteAdjustmentId(quoteAdjustmentDto.getQuoteAdjustmentId());
                        if(quoteAdjustments!=null){
                            quoteAdjustments.setQuoteName(quoteAdjustmentDto.getQuotename());
                            quoteAdjustments.setQuotePrice(quoteAdjustmentDto.getQuoteprice());
                            quoteAdjustments.setAppointment(appointment);
                            quoteAdjustmentRepository.saveAndFlush(quoteAdjustments);
                            quoteAdjustmentPriceSum=quoteAdjustmentPriceSum.add(quoteAdjustmentDto.getQuoteprice());
                           // calculateQuoteAdjustmentPrice(appointment);
                        }
                    }else{
                        QuoteAdjustments quoteAdjustments=new QuoteAdjustments();
                        quoteAdjustments.setQuoteName(quoteAdjustmentDto.getQuotename());
                        quoteAdjustments.setQuotePrice(quoteAdjustmentDto.getQuoteprice());
                        quoteAdjustments.setAppointment(appointment);
                        quoteAdjustments.setRetailer(RetailerContext.getRetailer());
                       quoteAdjustments= quoteAdjustmentRepository.saveAndFlush(quoteAdjustments);
                        quoteAdjustmentsSet.add(quoteAdjustments);
                        quoteAdjustmentPriceSum=quoteAdjustmentPriceSum.add(quoteAdjustmentDto.getQuoteprice());
                    }
                }
            }
            if(ObjectUtils.isNotEmpty(serviceHistoryDto.getQuoteAdjustmentRemoveIds())){
                for(Integer removeIds:serviceHistoryDto.getQuoteAdjustmentRemoveIds()){
                    QuoteAdjustments quoteAdjustments=quoteAdjustmentRepository.findByQuoteAdjustmentId(removeIds);
                    if(quoteAdjustments!=null) {
                        quoteAdjustmentPriceSum = quoteAdjustmentPriceSum.subtract(quoteAdjustments.getQuotePrice());
                        quoteAdjustmentRepository.deleteById(removeIds);
                    }
                }
            }
            BigDecimal currentQuoteAdjustmentPrice=appointment.getQuoteAdjustmentPrice();
            if(currentQuoteAdjustmentPrice==null){
                appointment.setQuoteAdjustmentPrice(quoteAdjustmentPriceSum);
                BigDecimal currentServiceAmount=appointment.getAmount();
                if(currentServiceAmount!=null) {
                    BigDecimal reduceServiceAmount = currentServiceAmount;
                    BigDecimal serviceAmount = reduceServiceAmount.add(quoteAdjustmentPriceSum);
                    appointment.setAmount(serviceAmount);
                }
            }else{
                appointment.setQuoteAdjustmentPrice(quoteAdjustmentPriceSum);
                BigDecimal currentServiceAmount=appointment.getAmount();
                if(currentServiceAmount!=null) {
                    BigDecimal reduceServiceAmount = currentServiceAmount.subtract(currentQuoteAdjustmentPrice);
                    BigDecimal serviceAmount = reduceServiceAmount.add(quoteAdjustmentPriceSum);
                    appointment.setAmount(serviceAmount);
                }
            }
            if(serviceHistoryDto.getTotalCost()!=null){
                appointment.setAmount(serviceHistoryDto.getTotalCost());
                appointment.setSource("Bookit Appointment Price Edited");
            }
       /*    appointment.setQuoteAdjustmentPrice(quoteAdjustmentPriceSum);
           BigDecimal currentServiceAmount=appointment.getAmount();
           if(currentServiceAmount!=null) {
               if(currentQuoteAdjustmentPrice==null){
                   currentQuoteAdjustmentPrice=BigDecimal.ZERO;
               }
               BigDecimal reduceServiceAmount = currentServiceAmount.subtract(currentQuoteAdjustmentPrice);
               BigDecimal serviceAmount = reduceServiceAmount.add(quoteAdjustmentPriceSum);
               appointment.setAmount(serviceAmount);
           }*/

            //food feed checking
            if (ObjectUtils.isNotEmpty(serviceHistoryDto.getFoodFed())) {
                serviceHistory.setFoodFed(serviceHistoryDto.getFoodFed());
            }else{
                if (serviceData.getRequireFoodFedPostService().equals(true))
                    throw new EntityNotFoundException("Please add Food feeding information which is configured in service");
            }
            if (ObjectUtils.isNotEmpty(serviceHistoryDto.getPhotos())) {
                List<String> pictures = new ArrayList<>();
                for (MultipartFile multipartFile : serviceHistoryDto.getPhotos()) {
                    try {
                        String awsUrl = FileUploadUtil.uploadFile(multipartFile, dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getServicePhotosPrefix(), FileType.Photos);
                        pictures.add(awsUrl);
                    } catch (Exception e) {
                        throw new EtailBookItException("Exception occurred while file upload");
                    }
                }
                serviceHistory.setPhotos(pictures);
            }else if(serviceHistoryDto.getPhotos() == null){
                if(serviceData.getSendPictureToPetParentPostService().equals(true))
                    throw new EntityNotFoundException("Please add Photos information which is configured in service");
            }
            appointment.setServiceStatus(ServiceStatus.COMPLETE);
            if(appointment.getService().getPaymentAfterServiceCompleted()) {
                appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
            }
            serviceHistory.setAppointment(appointment);
            appointment.setQuoteAdjustments(quoteAdjustmentsSet);
            if(serviceHistoryDto.getUpdatedCustomerId()!=null && serviceHistoryDto.getUpdatedCustomerName()!=null){
                appointment.setUpdatedCustomerName(serviceHistoryDto.getUpdatedCustomerName());
                appointment.setUpdatedCustomerId(serviceHistoryDto.getUpdatedCustomerId());
            }
            appointment = appointmentRepository.save(appointment);
            serviceHistory.setIsFilledWaterBowl(serviceHistoryDto.isFilledWaterBowl() ? "Yes" : "No");
            serviceHistoryRepository.save(serviceHistory);
            
            // Update Google Calendar event status if attendant has it enabled
            try {
                googleCalendarService.updateCalendarEvent(appointment);
            } catch (Exception e) {
                logger.warn("Failed to update Google Calendar event: {}", e.getMessage());
                // Don't fail service end if calendar sync fails
            }

            // Calculating the remaining payable amount/ refund amount/ full amount at time of service end
            BigDecimal refundAmount = BigDecimal.ZERO;
            BigDecimal remainingBalance = BigDecimal.ZERO;

            if (serviceHistoryDto.getActualVariableScheduleDuration() != null &&
                    appointment.getVariableScheduleDuration() != null &&
                    serviceHistoryDto.getActualVariableScheduleDuration() > appointment.getVariableScheduleDuration()) {
                //  calculate the extra duration amount
                Integer extraDuration = serviceHistoryDto.getActualVariableScheduleDuration() - appointment.getVariableScheduleDuration();
                remainingBalance = remainingBalance.add(AmountCalculation.calculateServiceCharge(appointment.getService().getFixedScheduleUnit(),
                        appointment.getService().getServiceUnit(), extraDuration, appointment.getService().getAmountPerUnit()));
            }

            if (serviceHistoryDto.getActualOvernights() != null && appointment.getOvernights() != null
                    && serviceHistoryDto.getActualOvernights() > appointment.getOvernights()) {
                // calculate the extra duration amount
                Integer extraDays = serviceHistoryDto.getActualOvernights() - appointment.getOvernights();
                remainingBalance = remainingBalance.add(AmountCalculation.calculateServiceCharge(appointment.getService().getFixedScheduleUnit(),
                        appointment.getService().getServiceUnit(), extraDays, appointment.getService().getAmountPerUnit()));
            }

            if (serviceHistoryDto.getTimesFed() != null && serviceHistoryDto.getCupFedPerTime() != null &&
                    serviceHistoryDto.getTimesFed() > 0 && serviceHistoryDto.getCupFedPerTime() > 0) {
                // calculate the amount  for the food fed
                BigDecimal amountPerCup;
                FeedingInformation feedingInformation = feedingInformationRepository.findByRetailer(RetailerContext.getRetailer());
                amountPerCup = feedingInformation.getAmountPerExtraCup();
                if (amountPerCup.compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal totalFeedingAmount = amountPerCup.multiply(new BigDecimal(serviceHistoryDto.getTimesFed() * serviceHistoryDto.getCupFedPerTime()));
                    remainingBalance = remainingBalance.add(totalFeedingAmount);
                }
            }

            if (serviceHistoryDto.getPetShampooId() != null && appointment.getShamppo() != null &&
                    !serviceHistoryDto.getPetShampooId().equals(appointment.getShamppo().getId())) {
                // the shampoo is changed
                Optional<PetShampoo> serviceShampoo = petShampooRepository.findById(serviceHistoryDto.getPetShampooId());
                Optional<PetShampoo> appointmentShampoo = petShampooRepository.findById(appointment.getShamppo().getId());
                if (!appointmentShampoo.isPresent() && serviceShampoo.isPresent() && serviceShampoo.get().getExtraCharge() != null) {
                    remainingBalance = remainingBalance.add(BigDecimal.valueOf(serviceShampoo.get().getExtraCharge()));
                } else if (!serviceShampoo.isPresent() && appointmentShampoo.isPresent() && appointmentShampoo.get().getExtraCharge() != null) {
                    refundAmount = refundAmount.add(BigDecimal.valueOf(appointmentShampoo.get().getExtraCharge()));
                } else if (serviceShampoo.isPresent() && appointmentShampoo.isPresent()
                        && serviceShampoo.get().getExtraCharge() != null && appointmentShampoo.get().getExtraCharge() != null) {
                    if (serviceShampoo.get().getExtraCharge().compareTo(appointmentShampoo.get().getExtraCharge()) > 0) {
                        BigDecimal extraShampooAmount = BigDecimal.valueOf(serviceShampoo.get().getExtraCharge()
                                - appointmentShampoo.get().getExtraCharge());
                        remainingBalance = remainingBalance.add(extraShampooAmount);
                    } else if (serviceShampoo.get().getExtraCharge().compareTo(appointmentShampoo.get().getExtraCharge()) < 0) {
                        BigDecimal refundShampooBalance = BigDecimal.valueOf(appointmentShampoo.get().getExtraCharge()
                                - serviceShampoo.get().getExtraCharge());
                        refundAmount = refundAmount.add(refundShampooBalance);
                    }
                }
            }

            if (serviceHistoryDto.getPetCologneId() != null && appointment.getCologne() != null &&
                    serviceHistoryDto.getPetCologneId().equals(appointment.getCologne().getId())) {
                // the cologne is changed
                Optional<PetCologne> serviceCologne = petCologneRepository.findById(serviceHistoryDto.getPetCologneId());
                Optional<PetCologne> appointmentCologne = petCologneRepository.findById(serviceHistoryDto.getPetCologneId());
                if (!appointmentCologne.isPresent() && serviceCologne.isPresent() && serviceCologne.get().getExtraCharge() != null) {
                    remainingBalance = remainingBalance.add(BigDecimal.valueOf(serviceCologne.get().getExtraCharge()));
                } else if (!serviceCologne.isPresent() && appointmentCologne.isPresent() && appointmentCologne.get().getExtraCharge() != null) {
                    refundAmount = refundAmount.add(BigDecimal.valueOf(appointmentCologne.get().getExtraCharge()));
                } else if (serviceCologne.isPresent() && appointmentCologne.isPresent()
                        && serviceCologne.get().getExtraCharge() != null && appointmentCologne.get().getExtraCharge() != null) {
                    if (serviceCologne.get().getExtraCharge().compareTo(appointmentCologne.get().getExtraCharge()) > 0) {
                        BigDecimal extraCologneAmount = BigDecimal.valueOf(serviceCologne.get().getExtraCharge()
                                - appointmentCologne.get().getExtraCharge());
                        remainingBalance = remainingBalance.add(extraCologneAmount);
                    } else if (serviceCologne.get().getExtraCharge().compareTo(appointmentCologne.get().getExtraCharge()) < 0) {
                        BigDecimal refundCologneBalance = BigDecimal.valueOf(appointmentCologne.get().getExtraCharge()
                                - serviceCologne.get().getExtraCharge());
                        refundAmount = refundAmount.add(refundCologneBalance);
                    }
                }
            }

            if (appointment.getPaymentStatus().equalsIgnoreCase(OrderStatus.ORDERED.name())) {
                // pay remaining amount or refund the amount
                if (refundAmount.compareTo(remainingBalance) == 1) {
                    paymentDetails.setIsRefund(true);
                    paymentDetails.setPayableAmount(refundAmount.subtract(remainingBalance));
                    paymentDetails.setOrderId(appointment.getOrderReference());
                } else if (remainingBalance.compareTo(refundAmount) == 1) {
                    paymentDetails.setPayableAmount(remainingBalance.subtract(refundAmount));
                    paymentDetails.setOrderId(appointment.getOrderReference());
                }
                if(serviceHistoryDto.getTotalCost()!=null){
                    paymentDetails.setPayableAmount(serviceHistoryDto.getTotalCost());
                    paymentDetails.setServiceAmount(serviceHistoryDto.getTotalCost());
                }
            }
            if (appointment.getService().getPaymentAfterServiceCompleted()) {
                // the full amount is payable at end
                paymentDetails = AmountCalculation.getPaymentDetails(appointment);
                BigDecimal actualAmount = (paymentDetails.getPayableAmount().add(remainingBalance)).subtract(refundAmount);
              //  BigDecimal actualAmountWithQuoteAdjustment=actualAmount.subtract(currentQuoteAdjustmentPrice).add(quoteAdjustmentPriceSum);
                paymentDetails.setPayableAmount(actualAmount);
                if(serviceHistoryDto.getTotalCost()!=null){
                    paymentDetails.setPayableAmount(serviceHistoryDto.getTotalCost());
                    paymentDetails.setServiceAmount(serviceHistoryDto.getTotalCost());
                }
            }

            try {
                //customerEmailNotification.endServiceMailNotification(serviceHistoryDto, appointment, serviceHistory);
                customerSMSNotification.endServiceSMSNotification(serviceHistoryDto, appointment, serviceHistory);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            throw new EntityNotFoundException("Appointment not found with id :::" + appointmentId);
        }
        if(serviceHistoryDto.getTotalCost()!=null){
            paymentDetails.setPayableAmount(serviceHistoryDto.getTotalCost());
            paymentDetails.setServiceAmount(serviceHistoryDto.getTotalCost());
        }
        return paymentDetails;

    }

    @Override
    public Page<AppointmentHistoryListingProjection> appointmentHistory(Integer customer, Integer pageNo, Integer pageSize, String sortBy, Optional<String> search,
                                                                        String startDate, String endDate, Optional<Integer> petType, Optional<String> serviceStatus, Optional<String> service) throws EtailBookItException {

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        ServiceStatus status = serviceStatus.map(ServiceStatus::valueOf).orElse(null);
        String searchTerm = search.isPresent() && search.hashCode() != 0 ? search.get() : null;
        Integer pt = petType.orElse(null);
        String serviceName = service.orElse(null);
        Page<AppointmentHistoryListingProjection> appointments;
        if (startDate != null && endDate != null) {
            LocalDate start = LocalDate.parse(startDate, monthDateYearFormat);
            LocalDate end = LocalDate.parse(endDate, monthDateYearFormat);
            appointments = appointmentRepository.getAllAppointmentByCustomerSearch(
                    customer, pt, searchTerm, status, start, end, serviceName, RetailerContext.getRetailer(), paging
            );
        } else if (startDate != null) {
            LocalDate start = LocalDate.parse(startDate, monthDateYearFormat);
            appointments = appointmentRepository.getAllAppointmentByCustomerSearchAfter(
                    customer, pt, searchTerm, status, start, serviceName, RetailerContext.getRetailer(), paging
            );
        } else if (endDate != null) {
            LocalDate end = LocalDate.parse(endDate, monthDateYearFormat);
            appointments = appointmentRepository.getAllAppointmentByCustomerSearchBefore(
                    customer, pt, searchTerm, status, end, serviceName, RetailerContext.getRetailer(), paging
            );
        } else {

            appointments = appointmentRepository.getAllAppointmentByCustomerSearch(
                    customer, pt, searchTerm, status, serviceName, RetailerContext.getRetailer(), paging
            );
        }
        return appointments;
    }

    @Override
    public DetailHistoryDto getDetailedHistory(Integer appointmentId) throws EtailBookItException {

        DetailHistoryDto detailHistory = new DetailHistoryDto();
        if (Optional.ofNullable(appointmentId).orElse(0) != 0) {
            Optional<Appointment> appointmentValues = appointmentRepository.findById(appointmentId);
            if (appointmentValues.isPresent()) {
                Appointment appointment = appointmentValues.get();
                detailHistory = ServiceHistoryMapper.toDetailedHistoryDto(appointment);
                detailHistory.setStartDateAndTime(appointment.getStartTime());
                detailHistory.setEndDateAndTime(appointment.getEndTime());
                if (appointment.getPet() != null) {
                    detailHistory.setPetName(appointment.getPet().getName());
                    detailHistory.setPetType(appointment.getPet().getPetType().getName());
                    Pet pet = appointment.getPet();
                    if (pet.getHairLength() != null) {
                        detailHistory.setHairLength(pet.getHairLength().getName());
                    }
                    if (pet.getHairTexture() != null) {
                        detailHistory.setHairTexture(pet.getHairTexture().getName());
                    }
                    if (pet.getWeightRange() != null) {
                        WeightRangeDto weight = new WeightRangeDto();
                        weight.setId(pet.getWeightRange().getWeightRangeId());
                        weight.setMinValue(pet.getWeightRange().getMinValue());
                        weight.setMaxValue(pet.getWeightRange().getMaxValue());
                        weight.setWeightUnit(pet.getWeightRange().getWeightUnit());
                        detailHistory.setWeight(weight);
                    }
                    if (pet.getTemperament() != null) {
                        detailHistory.setTemperament(pet.getTemperament().getName());
                    }
                    //todo: change this data from appointment
                    if (pet.getPetVetInformation() != null) {
                        List<VetInformationDto> vetList = new ArrayList<>();
                        pet.getPetVetInformation().stream().forEach(vet -> {
                            VetInformationDto vetInfo = new VetInformationDto();
                            vetInfo.setName(vet.getVetInformation().getName());
                            vetInfo.setValue(vet.getValue());
                            vetList.add(vetInfo);
                        });
                        detailHistory.setVetInformation(vetList);
                    }
                    //todo: change this data from appointment
                    if (pet.getPetEmergencyContactInfo() != null) {
                        List<EmergencyContactInfoDto> emergencyList = new ArrayList<>();
                        pet.getPetEmergencyContactInfo().stream().forEach(contact -> {
                            EmergencyContactInfoDto contactInfoDto = new EmergencyContactInfoDto();
                            contactInfoDto.setName(contact.getEmergencyContactInfo().getName());
                            contactInfoDto.setValue(contact.getValue());
                            emergencyList.add(contactInfoDto);
                        });
                        detailHistory.setEmergencyContact(emergencyList);
                    }
                    if (pet.getPersonalityParameters() != null) {
                        List<PersonalityParameterDto> personalityList = new ArrayList<>();
                        pet.getPersonalityParameters().stream().forEach(personality -> {
                            PersonalityParameterDto personalityDto = new PersonalityParameterDto();
                            personalityDto.setName(personality.getName());
                            personalityList.add(personalityDto);
                        });
                        detailHistory.setPetPersonality(personalityList);
                    }
                    if (pet.getUnfriendlyBehaviourTriggers() != null) {
                        List<UnfriendlyBehaviourTriggerDto> unfriendlyList = new ArrayList<>();
                        pet.getUnfriendlyBehaviourTriggers().stream().forEach(unfriendly -> {
                            UnfriendlyBehaviourTriggerDto unfriendlyDto = new UnfriendlyBehaviourTriggerDto();
                            unfriendlyDto.setName(unfriendly.getName());
                            unfriendlyList.add(unfriendlyDto);
                        });
                        detailHistory.setUnfriendlyBehaviour(unfriendlyList);
                    }
                }

                List<AddonService> addOnservices = appointmentRepository.getAppointmentAddOnservices(appointment.getId());
                if (addOnservices.size() != 0) {
                    List<AddonServiceDto> addonServiceList = new ArrayList<>();
                    for (AddonService addOnservice : addOnservices) {
                        AddonServiceDto addonServiceDto = new AddonServiceDto();
                        addonServiceDto.setId(addOnservice.getAddonServiceId());
                        addonServiceDto.setName(addOnservice.getName());
                        addonServiceList.add(addonServiceDto);
                    }
                    if (addonServiceList.size() != 0) {
                        detailHistory.setAddOnServices(addonServiceList);
                    }
                }

                List<AppointmentDesiredHairLengths> desiredHairLengths = appointmentRepository.getAppointmentDesiredHairLength(appointment.getId());
                if (desiredHairLengths.size() != 0) {
                    List<DesiredHairLengthDto> desiredHairLengthDtos = new ArrayList<>();
                    for (AppointmentDesiredHairLengths desiredHairs : desiredHairLengths) {
                        DesiredHairLengthDto desiredHairLengthDto = new DesiredHairLengthDto();
                        desiredHairLengthDto.setId(desiredHairs.getId());
                        if (desiredHairs.getDesiredHairLength() != null) {
                            desiredHairLengthDto.setName(desiredHairs.getDesiredHairLength().getName());
                        }
                        desiredHairLengthDto.setValue(desiredHairs.getValue());
                        desiredHairLengthDtos.add(desiredHairLengthDto);
                    }
                    if (desiredHairLengthDtos.size() != 0) {
                        detailHistory.setDesiredHairLengths(desiredHairLengthDtos);
                    }
                }

                List<Allergies> allergies = appointmentRepository.getAppointmentAllergies(appointment.getId());
                if (allergies.size() != 0) {
                    List<AllergiesDto> allergiesList = new ArrayList<>();
                    for (Allergies allergy : allergies) {
                        AllergiesDto allergiesDto = new AllergiesDto();
                        allergiesDto.setId(allergy.getAllergyId());
                        allergiesDto.setName(allergy.getName());
                        allergiesList.add(allergiesDto);
                    }
                    if (allergiesList.size() != 0) {
                        detailHistory.setAllergies(allergiesList);
                    }
                }

                List<AppointmentVaccinationInformation> vaccinations = appointmentRepository.getAppointmentVaccination(appointment.getId());
                if (vaccinations.size() != 0) {
                    List<VaccinationRecordsDto> vaccinationRecordsDtoList = new ArrayList<>();
                    for (AppointmentVaccinationInformation vaccination : vaccinations) {
                        VaccinationRecordsDto vaccinationRecordsDto = new VaccinationRecordsDto();
                        vaccinationRecordsDto.setId(vaccination.getVaccinationRecords().getVaccinationRecordId());
                        vaccinationRecordsDto.setName(vaccination.getVaccinationRecords().getName());
                        vaccinationRecordsDtoList.add(vaccinationRecordsDto);
                    }
                    if (vaccinationRecordsDtoList.size() != 0) {
                        detailHistory.setVaccination(vaccinationRecordsDtoList);
                    }
                }

                List<AppointmentDocuments> documents = appointmentRepository.getAppointmentDocuments(appointment.getId());
                if (documents.size() != 0) {
                    List<DocumentOptionDto> documentOptionDtoList = new ArrayList<>();
                    for (AppointmentDocuments document : documents) {
                        DocumentOptionDto documentOptionDto = new DocumentOptionDto();
                        documentOptionDto.setId(document.getDocumentOption().getDocumentOptionId());
                        documentOptionDto.setName(document.getDocumentOption().getName());
                        documentOptionDtoList.add(documentOptionDto);
                    }
                    if (documentOptionDtoList.size() != 0) {
                        detailHistory.setDocuments(documentOptionDtoList);
                    }
                }

                if (appointment.getShamppo() != null) {
                    PetShampooDto petShampooDto = new PetShampooDto();
                    petShampooDto.setId(appointment.getShamppo().getId());
                    petShampooDto.setName(appointment.getShamppo().getName());
                    detailHistory.setShamppo(petShampooDto);
                }

                if (appointment.getCologne() != null) {
                    PetCologneDto petCologneDto = new PetCologneDto();
                    petCologneDto.setId(appointment.getCologne().getId());
                    petCologneDto.setName(appointment.getCologne().getName());
                    detailHistory.setCologne(petCologneDto);
                }

            } else
                throw new EntityNotFoundException("Appointment details not found with appointmentId:: " + appointmentId);
        }

        return detailHistory;
    }

    @Override
    public ServiceHistoryDto getDetailedNote(Integer appointmentId) throws EtailBookItException {

        ServiceHistoryDto noteHistory = new ServiceHistoryDto();
        if (Optional.ofNullable(appointmentId).orElse(0) != 0) {

            ServiceHistoryProjection projection = serviceHistoryRepository.getServiceNote(appointmentId);

            if (projection != null) {
                noteHistory = ServiceHistoryMapper.toServiceHistoryDto(projection);
                if (projection.getServiceHistoryNote() != null)
                    noteHistory.setNotes(projection.getServiceHistoryNote());

                noteHistory.setAppointmentNo(projection.getAppointmentNo());
                if (projection.getCombName() != null) {
                    CombsDto combsDto = new CombsDto();
                    combsDto.setName(projection.getCombName());
                    noteHistory.setComb(combsDto);
                }

                if (projection.getBladeName() != null) {
                    BladesDto bladesDto = new BladesDto();
                    bladesDto.setName(projection.getBladeName());
                    noteHistory.setBlade(bladesDto);
                }
                if (projection.getPetCologneName() != null) {
                    PetCologneDto petCologneDto = new PetCologneDto();
                    petCologneDto.setName(projection.getPetCologneName());
                    noteHistory.setPetCologne(petCologneDto);
                }
                if (projection.getPetShampooName() != null) {
                    PetShampooDto petShampooDto = new PetShampooDto();
                    petShampooDto.setName(projection.getPetShampooName());
                    noteHistory.setPetShampoo(petShampooDto);
                }
                if (projection.getPicturesSent() == null)
                    noteHistory.setPicturesSent(false);
                else
                    noteHistory.setPicturesSent(projection.getPicturesSent());
            }


        }
        return noteHistory;
    }

    @Override
    public Boolean cancelService(int appointmentId,String timeZone,String cancellationReason,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException {

        if (Optional.of(appointmentId).orElse(0) != 0) {
            Optional<Appointment> appointmentOptional = appointmentRepository.findById(appointmentId);
            if (appointmentOptional.isPresent()) {
                Appointment appointment = appointmentOptional.get();
                if (appointment.getServiceStatus().ordinal() != ServiceStatus.valueOf("CREATED").ordinal()) {
                    String stage = ServiceStatus.valueOf(Utils.serviceStatusValues.get(appointment.getServiceStatus().ordinal())).toString();
                    throw new EtailBookItException("The appointment can't cancel Because its in " + stage + " Stage");
                }
                appointment.setServiceStatus(ServiceStatus.CANCELLED);
                appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
                if(cancellationReason.equalsIgnoreCase("null")){
                    throw new EtailBookItException("Please select a reason for cancellation");
                }else {
                    appointment.setCancellationReason(cancellationReason);
                }
                if(updatedCustomerId!=null && updatedCustomerName!=null){
                    appointment.setUpdatedCustomerId(updatedCustomerId);
                    appointment.setUpdatedCustomerName(updatedCustomerName);
                }
                if(appointment.getTimeSlotId()!=null){
                    Optional<TimeSlots> timeSlots=timeSlotRepository.findById(appointment.getTimeSlotId());
                    if(timeSlots.isPresent()){
                        TimeSlots timeSlots1=timeSlots.get();
                        timeSlots1.setSlotBooked(Boolean.FALSE);
                        timeSlotRepository.save(timeSlots1);
                    }
                }
                appointmentRepository.save(appointment);
                
                // Delete from Google Calendar if attendant has it enabled
                try {
                    googleCalendarService.deleteCalendarEvent(appointment);
                } catch (Exception e) {
                    logger.warn("Failed to delete Google Calendar event: {}", e.getMessage());
                    // Don't fail cancellation if calendar sync fails
                }
                
                try {
                    //customerEmailNotification.cancelServiceMailNotification(appointment);
                    customerSMSNotification.cancelServiceSmsNotification(appointment,timeZone);
                    // Notify waitlist customers if slot was released
                    if(appointment.getTimeSlotId() != null) {
                        try {
                            waitListEntryService.notifyWaitlistCustomersForSlotAvailability(
                                appointment.getTimeSlotId(), 
                                RetailerContext.getRetailer(), 
                                timeZone
                            );
                        } catch (Exception e) {
                            // Log the error but don't fail the cancellation
                            logger.warn("Failed to notify waitlist customers for slot {}: {}", appointment.getTimeSlotId(), e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                return true;
            } else
                throw new EntityNotFoundException("There is no appointment on this ID::" + appointmentId);

        }
        return false;
    }

    @Override
    public void addNotes(Integer appointmentId, NoteDto noteDto) throws EtailBookItException {
        Appointment appointment = appointmentRepository.getOne(appointmentId);

        List<ServiceHistoryAddNotes> serviceHistoryAddNotes = appointment.getAddNotes();

        ServiceHistoryAddNotes newNote = new ServiceHistoryAddNotes();
        newNote.setValue(noteDto.getNote());
        newNote.setAppointment(appointment);
        newNote.setRetailer(RetailerContext.getRetailer());
        newNote.setUserName(noteDto.getUserName());
        serviceHistoryAddNotes.add(newNote);
        appointment.setAddNotes(serviceHistoryAddNotes);
        appointmentRepository.save(appointment);
    }

    @Override
    public ServiceHistoryNotesListingDto listNotes(Integer appointmentId) throws EtailBookItException {
        Appointment appointment = appointmentRepository.getOne(appointmentId);
        List<ServiceHistoryAddNotes> serviceHistoryAddNotes = serviceHistoryAddNotesRepository.findByAppointmentId(appointment.getId());

        List<ServiceHistoryNotes> serviceHistoryListNotesList = new ArrayList<>();
        for (ServiceHistoryAddNotes serviceHistoryAddNote : serviceHistoryAddNotes) {
            ServiceHistoryNotes serviceHistoryListNotes = new ServiceHistoryNotes();
            serviceHistoryListNotes.setValue(serviceHistoryAddNote.getValue());
            serviceHistoryListNotes.setId(serviceHistoryAddNote.getId());

            String offset = OffsetContext.getOffset();
            String date = serviceHistoryAddNote.getModifiedAt().toString();
            String[] parts = date.split("T");
            String firstPart = parts[0].toString() + "/" + parts[1].substring(0, 5);

            //ZoneOffset should be set to the timezone of the server
            OffsetDateTime odt = OffsetDateTime.of(LocalDateTime.parse(firstPart,
                            DateTimeFormatter.ofPattern("yyyy-MM-dd/HH:mm")),
                    ZoneOffset.UTC);

            ZonedDateTime utcZdt = odt.atZoneSameInstant(ZoneId.of(TimeZoneContext.getTimeZone()));
            String timeAndUser = utcZdt.format(DateTimeFormatter.ofPattern("E, MMM dd, yyyy hh:mm:ss a")) + " ," + serviceHistoryAddNote.getUserName() + "(User)";
            serviceHistoryListNotes.setCreatedTimeAndUser(timeAndUser);
            serviceHistoryListNotes.setAppointmentId(appointmentId);
            serviceHistoryListNotesList.add(serviceHistoryListNotes);
        }
        ServiceHistoryNotesListingDto returnValue = new ServiceHistoryNotesListingDto();
        ServiceHistory serviceHistory = serviceHistoryRepository.findByAppointmentId(appointmentId);
        if(ObjectUtils.isNotEmpty(serviceHistory) && ObjectUtils.isNotEmpty(serviceHistory.getNotes()))
            returnValue.setGeneralNotes(serviceHistory.getNotes());
        returnValue.setServiceHistoryListNotes(serviceHistoryListNotesList);
        return returnValue;
    }

    @Override
    public Page<ServiceHistoryNotesDto> listNotesByPetId(Integer petId, Integer pageNo, Integer pageSize) throws EtailBookItException {
        Pageable paging = PageRequest.of(pageNo, pageSize);
        Page<ServiceHistoryAddNotes> serviceHistoryAddNotes = serviceHistoryAddNotesRepository.findAllByAppointmentPetIdOrderByAppointmentIdAscModifiedAtAsc(petId, paging);

        List<ServiceHistoryNotesDto> serviceHistoryListNotesList = new ArrayList<>();
        for (ServiceHistoryAddNotes serviceHistoryAddNote : serviceHistoryAddNotes.getContent()) {
            ServiceHistoryNotesDto serviceHistoryListNotes = new ServiceHistoryNotesDto();
            serviceHistoryListNotes.setValue(serviceHistoryAddNote.getValue());
            serviceHistoryListNotes.setId(serviceHistoryAddNote.getId());
            OffsetDateTime odt = OffsetDateTime.of(serviceHistoryAddNote.getModifiedAt(), ZoneOffset.UTC);
            serviceHistoryListNotes.setCreatedAt(odt.toString());
            serviceHistoryListNotes.setCreatedBy(serviceHistoryAddNote.getUserName());
            serviceHistoryListNotes.setAppointmentId(serviceHistoryAddNote.getAppointment().getId());
            serviceHistoryListNotesList.add(serviceHistoryListNotes);
        }
        return new PageImpl<>(serviceHistoryListNotesList, serviceHistoryAddNotes.getPageable(), serviceHistoryAddNotes.getTotalElements());
    }

    @Override
    public void updateNotes(Integer id, NoteDto noteDto) throws EtailBookItException {
        Optional<ServiceHistoryAddNotes> serviceHistoryAddNotes = serviceHistoryAddNotesRepository.findById(id);
        ServiceHistoryAddNotes response;
        if (serviceHistoryAddNotes.isPresent()) {
            ServiceHistoryAddNotes serviceHistoryAddNotes1 = serviceHistoryAddNotes.get();
            serviceHistoryAddNotes1.setValue(noteDto.getNote());
            response = serviceHistoryAddNotesRepository.save(serviceHistoryAddNotes1);
        } else {
            throw new EntityNotFoundException("No Service History Notes found with the Id :: " + id);
        }
    }

    @Override
    public Boolean waiveService(Integer appointmentId,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException {
        Optional<Appointment> appointmentOptional=appointmentRepository.findById(appointmentId);
        Appointment appointment=appointmentOptional.get();
        if(appointment!=null) {
            appointment.setPaymentStatus(OrderStatus.WAIVED.name());
            appointment.setServiceStatus(ServiceStatus.COMPLETE);
            if(updatedCustomerId!=null && updatedCustomerName!=null){
                appointment.setUpdatedCustomerId(updatedCustomerId);
                appointment.setUpdatedCustomerName(updatedCustomerName);
            }
            appointment = appointmentRepository.save(appointment);
            
            // Update Google Calendar event status if attendant has it enabled
            try {
                googleCalendarService.updateCalendarEvent(appointment);
            } catch (Exception e) {
                logger.warn("Failed to update Google Calendar event: {}", e.getMessage());
                // Don't fail waive if calendar sync fails
            }
            
            return true;
        }else{
            throw new BadRequestException(" No appointment found with id ::"+appointmentId);
        }
    }

    @Override
    public Boolean changeServiceStatus(Integer appointmentId, String serviceStatus,String timeZone, String cancellationReason,String rejectionReason,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException{
        Optional<Appointment> appointmentOptional=appointmentRepository.findById(appointmentId);
        if(appointmentOptional.isPresent()) {
            Appointment appointment = appointmentOptional.get();
            /*if (appointment.getServiceStatus().ordinal() != ServiceStatus.valueOf("CREATED").ordinal()) {
                String stage = ServiceStatus.valueOf(Utils.serviceStatusValues.get(appointment.getServiceStatus().ordinal())).toString();
                throw new EtailBookItException("The appointment can't cancel Because its in " + stage + " Stage");
            }*/
            if(serviceStatus.equalsIgnoreCase("CANCEL")) {
                appointment.setServiceStatus(ServiceStatus.CANCELLED);
                appointment.setPaymentStatus(OrderStatus.READY_TO_PAY.name());
                if (cancellationReason.equalsIgnoreCase("null")) {
                    throw new EtailBookItException("Please select a reason for cancellation");
                } else {
                    appointment.setCancellationReason(cancellationReason);
                }
                if(updatedCustomerId!=null && updatedCustomerName!=null){
                    appointment.setUpdatedCustomerId(updatedCustomerId);
                    appointment.setUpdatedCustomerName(updatedCustomerName);
                }
                // Release the time slot when canceling the appointment
                if(appointment.getTimeSlotId()!=null){
                    Optional<TimeSlots> timeSlots=timeSlotRepository.findById(appointment.getTimeSlotId());
                    if(timeSlots.isPresent()){
                        TimeSlots timeSlots1=timeSlots.get();
                        timeSlots1.setSlotBooked(Boolean.FALSE);
                        timeSlotRepository.save(timeSlots1);
                    }
                }
                appointmentRepository.save(appointment);
                
                // Delete from Google Calendar if attendant has it enabled
                try {
                    googleCalendarService.deleteCalendarEvent(appointment);
                } catch (Exception e) {
                    logger.warn("Failed to delete Google Calendar event: {}", e.getMessage());
                    // Don't fail cancellation if calendar sync fails
                }
                
                try {
                    //customerEmailNotification.cancelServiceMailNotification(appointment);
                    customerSMSNotification.cancelServiceSmsNotification(appointment, timeZone);
                    // Send slot release notification
                    customerSMSNotification.slotReleaseNotification(appointment, timeZone);
                    
                    // Notify waitlist customers if slot was released
                    if(appointment.getTimeSlotId() != null) {
                        try {
                            waitListEntryService.notifyWaitlistCustomersForSlotAvailability(
                                appointment.getTimeSlotId(), 
                                RetailerContext.getRetailer(), 
                                timeZone
                            );
                        } catch (Exception e) {
                            // Log the error but don't fail the cancellation
                            logger.warn("Failed to notify waitlist customers for slot {}: {}", appointment.getTimeSlotId(), e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                return true;
            } else if (serviceStatus.equalsIgnoreCase("APPROVE")){
                appointment.setServiceStatus(ServiceStatus.CREATED);
                appointment = appointmentRepository.save(appointment);
                
                // Update Google Calendar event status if attendant has it enabled
                try {
                    googleCalendarService.updateCalendarEvent(appointment);
                } catch (Exception e) {
                    logger.warn("Failed to update Google Calendar event: {}", e.getMessage());
                    // Don't fail approval if calendar sync fails
                }
                
                try {
                   // customerSMSNotification.appointmentApprove(appointment);
                    customerSMSNotification.setAppointmentSmsNotiication(appointment, "BOOKIT_APPOINTMENT_CONFIRMATION", timeZone);
                }catch (Exception e){
                    e.printStackTrace();
                }
                return true;
            } else if (serviceStatus.equalsIgnoreCase("REJECT")) {
                appointment.setServiceStatus(ServiceStatus.REJECTED);
                appointment.setRejectionReason(rejectionReason);
                if(updatedCustomerId!=null && updatedCustomerName!=null){
                    appointment.setUpdatedCustomerId(updatedCustomerId);
                    appointment.setUpdatedCustomerName(updatedCustomerName);
                }
                appointmentRepository.save(appointment);
                
                // Delete from Google Calendar if attendant has it enabled
                try {
                    googleCalendarService.deleteCalendarEvent(appointment);
                } catch (Exception e) {
                    logger.warn("Failed to delete Google Calendar event: {}", e.getMessage());
                    // Don't fail rejection if calendar sync fails
                }
                
                try {
                    customerSMSNotification.appointmentReject(appointment);
                }catch (Exception e){
                    e.printStackTrace();
                }
                return true;
            }
        }else{
            throw new EntityNotFoundException("Appointment not found with id::"+appointmentId);
        }
        return true;
    }


}