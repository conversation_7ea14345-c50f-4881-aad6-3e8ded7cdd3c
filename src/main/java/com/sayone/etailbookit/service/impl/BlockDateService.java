package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.BlockDateInfoDto;
import com.sayone.etailbookit.dto.BlockDateTimeDto;
import com.sayone.etailbookit.dto.BlockTimeDto;
import com.sayone.etailbookit.dto.RecurringBlockResponseDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.model.BlockDateInfo;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.AttendantRepository;
import com.sayone.etailbookit.repository.BlockDatesRepository;
import com.sayone.etailbookit.service.IBlockDateService;
import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.RetailerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BlockDateService implements IBlockDateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BlockDateService.class);

    @Autowired
    BlockDatesRepository blockDatesRepository;

    @Autowired
    AppointmentServiceImpl appointmentServiceImpl;

    @Autowired
    AttendantRepository attendantRepository;
    
    @Autowired
    AppointmentRepository appointmentRepository;

    @Override
    public void saveBlockDates(OffsetDateTime startTime, OffsetDateTime endTime) throws EtailBookItException {
        LocalDate blockDateto = startTime.toLocalDate();
        if (blockDateto==null) {
            throw new EntityNotFoundException("Block Dates not found");
        }
        LocalDate today = LocalDate.now();
        if (today.isAfter(blockDateto)){
            throw new BadRequestException("Past dates are not allowed");
        }
        LocalDate date =startTime.toLocalDate();
        System.out.println("**********offset Context"+ZoneOffset.of(OffsetContext.getOffset()));
        //System.out.println("******Local date time"+LocalDateTime.of(date, LocalTime.parse(startTime)));
        //OffsetDateTime blockStartTime = OffsetDateTime.of(LocalDateTime.of(date, LocalTime.parse(startTime)), ZoneOffset.of(OffsetContext.getOffset()));
        OffsetDateTime blockStartTime =startTime;
        System.out.println("88888888 block start time "+blockStartTime);
        //OffsetDateTime blockEndTime = OffsetDateTime.of(LocalDateTime.of(date, LocalTime.parse(endTime)), ZoneOffset.of(OffsetContext.getOffset()));
        OffsetDateTime blockEndTime = endTime;
        List<BlockDateInfo> blockDateInfos = blockDatesRepository.findByBlockDateAndRetailer(date,RetailerContext.getRetailer());
        for (BlockDateInfo blockDateInfo: blockDateInfos) {
            if (blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null) {
                throw new BadRequestException("The date is already blocked");
            }
            if (blockDateInfo.getBlockStartTime().isBefore(blockStartTime) && blockDateInfo.getBlockEndTime().isAfter(blockStartTime)) {
                throw new BadRequestException("The start time is already blocked");
            }

            if (blockDateInfo.getBlockStartTime().isBefore(blockEndTime) && blockDateInfo.getBlockEndTime().isAfter(blockEndTime)) {
                throw new BadRequestException("The end time is already blocked");
            }
        }
        BlockDateInfo blockDateInfo = new BlockDateInfo();
        blockDateInfo.setBlockDate(date);
        blockDateInfo.setBlockStartTime(blockStartTime);
        blockDateInfo.setBlockEndTime(blockEndTime);
        blockDateInfo.setRetailer(RetailerContext.getRetailer());
        blockDatesRepository.save(blockDateInfo);
    }
    @Override
    public List<BlockDateInfoDto> saveBlockDayWithTime(BlockDateTimeDto blockDateTimeDto,String timeZone) throws EtailBookItException{
        OffsetDateTime offsetDateTimeBlocked=blockDateTimeDto.getDate();
        Integer offsetTimeDifference= appointmentServiceImpl.calculateOffset(timeZone,offsetDateTimeBlocked);

        List<BlockDateInfoDto> blockDateInfoList =new ArrayList<>() ;
        BlockDateInfo blockDateInformation;
        if (offsetDateTimeBlocked==null) {
            throw new EntityNotFoundException("Block Dates not found");
        }
        LocalDate today = LocalDate.now();
        if (today.isAfter(offsetDateTimeBlocked.plusMinutes(offsetTimeDifference).toLocalDate())){
            throw new BadRequestException("Past dates are not allowed");
        }
        LocalDate date = blockDateTimeDto.getDate().toLocalDate();

        // if full day block
        if(blockDateTimeDto.isBlockFullDay()){
            // Pre-checks before creating the full-day record to avoid partial saves
            BlockDateInfo.BlockedTimeType blockType = blockDateTimeDto.getBlockType();
            if (blockType == null) {
                throw new BadRequestException("Block type is required");
            }

            // Check if STORE is already blocked for this day - TIMEZONE-SAFE
            List<BlockDateInfo> storeBlocksForDay = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                date, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.STORE
            );

            // Check if any ATTENDANT blocks exist for this day - TIMEZONE-SAFE
            List<BlockDateInfo> attendantBlocksForDay = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                date, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.ATTENDANT
            );

            Attendant attendant = null;
            if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                // Validate attendantId is provided
                if (blockDateTimeDto.getAttendantId() == null) {
                    throw new BadRequestException("Attendant ID is required when block type is ATTENDANT");
                }

                // Look up attendant early (fail fast if not found)
                attendant = attendantRepository.findByAttendantId(blockDateTimeDto.getAttendantId());
                if (attendant == null) {
                    throw new EntityNotFoundException("Attendant not found with id: " + blockDateTimeDto.getAttendantId());
                }

                // If store is blocked, attendant can't be blocked
                if (!storeBlocksForDay.isEmpty()) {
                    throw new BadRequestException("Cannot block attendant - store is already blocked for this date");
                }

                // Do not allow creating a full-day attendant block if any blocks already exist for that attendant on the day - TIMEZONE-SAFE
                List<BlockDateInfo> specificAttendantBlocksForDay = blockDatesRepository.findByBlockDateAndRetailerAndAttendantAndBlockType(
                    date, RetailerContext.getRetailer(), blockDateTimeDto.getAttendantId(), BlockDateInfo.BlockedTimeType.ATTENDANT
                );
                if (!specificAttendantBlocksForDay.isEmpty()) {
                    throw new BadRequestException("Cannot block full day for attendant - attendant already has blocked times on this date");
                }

                // Check if appointments exist for this ATTENDANT full day block
                List<Appointment> appointmentsForAttendant = appointmentRepository.findByDateAttendantAndRetailerExcludingCancelled(
                    date, blockDateTimeDto.getAttendantId(), RetailerContext.getRetailer()
                );
                if (!appointmentsForAttendant.isEmpty()) {
                    throw new BadRequestException("Cannot block attendant - " + appointmentsForAttendant.size() + " appointment(s) exist for this attendant on this date");
                }
            } else {
                // If creating STORE block, check if STORE full day block already exists
                for (BlockDateInfo existingBlock : storeBlocksForDay) {
                    if (existingBlock.getBlockStartTime() == null && existingBlock.getBlockEndTime() == null) {
                        throw new BadRequestException("Full day block already exists for store");
                    }
                }

                // Check if STORE partial time blocks exist
                StringBuilder storePartialBlocks = new StringBuilder();
                for (BlockDateInfo existingBlock : storeBlocksForDay) {
                    if (existingBlock.getBlockStartTime() != null && existingBlock.getBlockEndTime() != null) {
                        storePartialBlocks.append(existingBlock.getBlockStartTime().toLocalTime())
                            .append(" to ")
                            .append(existingBlock.getBlockEndTime().toLocalTime())
                            .append("; ");
                    }
                }
                if (storePartialBlocks.length() > 0) {
                    throw new BadRequestException("Cannot block full day for store - store already has blocked times on this date: " + storePartialBlocks.toString());
                }

                // REMOVED: Check for attendant blocks - STORE blocks take precedence over ATTENDANT blocks
                // Store can be blocked even if attendants have individual blocks (store hierarchy > attendant hierarchy)

                // Check if appointments exist for this STORE full day block
                // Appointments are ALWAYS for attendants, so if ANY appointment exists, cannot create STORE block
                List<Appointment> appointmentsOnDate = appointmentRepository.findByDateAndRetailerExcludingCancelled(
                    date, RetailerContext.getRetailer()
                );
                if (!appointmentsOnDate.isEmpty()) {
                    throw new BadRequestException("Cannot block store - " + appointmentsOnDate.size() + " appointment(s) exist for this date");
                }
            }

            // Create the base full-day record
            blockDateInformation = saveBlockDates(offsetDateTimeBlocked,timeZone);

            // Set blockType/attendant on the saved entity (attendant already validated and looked up above)
            if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                blockDateInformation.setAttendant(attendant);
                blockDateInformation.setBlockType(BlockDateInfo.BlockedTimeType.ATTENDANT);
                LOGGER.info("Setting attendant-specific block for attendant ID: {}", blockDateTimeDto.getAttendantId());
            } else {
                blockDateInformation.setAttendant(null);
                blockDateInformation.setBlockType(BlockDateInfo.BlockedTimeType.STORE);
                LOGGER.info("Setting store-wide block for retailer: {}", RetailerContext.getRetailer());
            }

            // Update the saved entity with attendant and blockType
            blockDateInformation = blockDatesRepository.save(blockDateInformation);

            // create dto
            BlockDateInfoDto blockDateInfo=new BlockDateInfoDto();
            blockDateInfo.setBlockDate(blockDateInformation.getOffsetBlockDate().toString());
            blockDateInfo.setId(blockDateInformation.getId());
            blockDateInfo.setBlockFullDay(true);

            blockDateInfo.setBlockType(blockDateInformation.getBlockType());
            if(blockDateInformation.getAttendant() != null) {
                blockDateInfo.setAttendantId(blockDateInformation.getAttendant().getAttendantId());
            }
            blockDateInfoList.add(blockDateInfo);
            return blockDateInfoList;
        }
        else {
            // Validate blockTimes is not null or empty when blockFullDay is false
            if (blockDateTimeDto.getBlockTimes() == null || blockDateTimeDto.getBlockTimes().isEmpty()) {
                throw new BadRequestException("Block times are required when blockFullDay is false");
            }

            // Validate blockType is provided
            BlockDateInfo.BlockedTimeType blockType = blockDateTimeDto.getBlockType();
            if (blockType == null) {
                throw new BadRequestException("Block type is required");
            }

            // Validate and look up attendant for ATTENDANT blocks (do once before loop)
            Attendant attendant = null;
            if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                if (blockDateTimeDto.getAttendantId() == null) {
                    throw new BadRequestException("Attendant ID is required when block type is ATTENDANT");
                }
                attendant = attendantRepository.findByAttendantId(blockDateTimeDto.getAttendantId());
                if (attendant == null) {
                    throw new EntityNotFoundException("Attendant not found with id: " + blockDateTimeDto.getAttendantId());
                }
            }

            for (BlockTimeDto blockTimeDto : blockDateTimeDto.getBlockTimes()) {
                OffsetDateTime startTime = blockTimeDto.getBlockStartTime();
                OffsetDateTime endTime = blockTimeDto.getBlockEndTime();

                // Validate startTime and endTime are not null
                if (startTime == null || endTime == null) {
                    throw new BadRequestException("Block start time and end time are required");
                }

                // Validate startTime is before endTime
                if (!startTime.isBefore(endTime)) {
                    throw new BadRequestException("Block start time must be before end time");
                }

                // Validate time ranges are on the same day as the date field
                LocalDate blockStartDate = startTime.toLocalDate();
                LocalDate blockEndDate = endTime.toLocalDate();
                LocalDate requestDate = date;
                
                if (!blockStartDate.equals(requestDate) || !blockEndDate.equals(requestDate)) {
                    throw new BadRequestException("Block time ranges must be on the same day as the specified date");
                }

               // OffsetDateTime blockStartTime = OffsetDateTime.of(LocalDateTime.of(date, LocalTime.parse(startTime)),ZoneId.systemDefault().getRules().getOffset(Instant.now()));
                //OffsetDateTime blockEndTime = OffsetDateTime.of(LocalDateTime.of(date, LocalTime.parse(endTime)),ZoneId.systemDefault().getRules().getOffset(Instant.now()));
                OffsetDateTime blockStartTime=startTime;
                OffsetDateTime blockEndTime=endTime;
                
                // Check if STORE is already blocked (applies to both STORE and ATTENDANT blocks) - TIMEZONE-SAFE
                List<BlockDateInfo> storeBlocks = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                    date, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.STORE
                );
                
                // Check if any ATTENDANT blocks exist for this day - TIMEZONE-SAFE
                List<BlockDateInfo> attendantBlocks = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                    date, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.ATTENDANT
                );
                
                List<BlockDateInfo> blockDateInfos;

                if (blockDateTimeDto.getBlockType() == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                    // If store is blocked, attendant can't be blocked
                    if (!storeBlocks.isEmpty()) {
                        throw new BadRequestException("Cannot block attendant - store is already blocked for this date");
                    }
                    // Check if THIS attendant is already blocked - TIMEZONE-SAFE
                    blockDateInfos = blockDatesRepository.findByBlockDateAndRetailerAndAttendantAndBlockType(
                        date, RetailerContext.getRetailer(), blockDateTimeDto.getAttendantId(), BlockDateInfo.BlockedTimeType.ATTENDANT
                    );
                } else {
                    // Creating STORE block
                    // REMOVED: Check for attendant blocks - STORE blocks take precedence over ATTENDANT blocks
                    // Store can create partial time blocks even if attendants have blocks

                    // Check existing STORE blocks
                    blockDateInfos = storeBlocks;
                }
                
                for (BlockDateInfo blockDateInfo : blockDateInfos) {
                    if (blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null) {
                        throw new BadRequestException("The date is already blocked");
                    }
                    
                    // Truncate to seconds to avoid nanosecond precision issues
                    OffsetDateTime existingStartTime = blockDateInfo.getBlockStartTime().truncatedTo(ChronoUnit.SECONDS);
                    OffsetDateTime existingEndTime = blockDateInfo.getBlockEndTime().truncatedTo(ChronoUnit.SECONDS);
                    OffsetDateTime newStartTime = blockStartTime.truncatedTo(ChronoUnit.SECONDS);
                    OffsetDateTime newEndTime = blockEndTime.truncatedTo(ChronoUnit.SECONDS);
                    
                    // Check for exact time period overlap
                    if (existingStartTime.equals(newStartTime) && existingEndTime.equals(newEndTime)) {
                        LOGGER.error("EXACT OVERLAP DETECTED: Existing {} to {} matches new {} to {}", 
                            existingStartTime, existingEndTime, newStartTime, newEndTime);
                        throw new  BadRequestException("The time period is already blocked : block start time:-" + blockStartTime + " and block end time:-" + blockEndTime);
                    }
                    
                    // Check for overlapping time periods
                    if ((existingStartTime.isBefore(newStartTime) && existingEndTime.isAfter(newStartTime)) ||
                        (existingStartTime.isBefore(newEndTime) && existingEndTime.isAfter(newEndTime)) ||
                        (newStartTime.isBefore(existingStartTime) && newEndTime.isAfter(existingStartTime)) ||
                        (newStartTime.isBefore(existingEndTime) && newEndTime.isAfter(existingEndTime))) {
                        LOGGER.error("OVERLAP DETECTED: New {} to {} overlaps with existing {} to {}", 
                            newStartTime, newEndTime, existingStartTime, existingEndTime);
                        BadRequestException exception = new BadRequestException("The time period overlaps with existing blocked time: " +
                            existingStartTime + " to " + existingEndTime);
                        LOGGER.error("Throwing BadRequestException: {}", exception.getMessage());
                        throw exception;
                    }
                }

                // Check for appointment conflicts with the new block time range
                List<Appointment> appointments;
                if (blockDateTimeDto.getBlockType() == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                    // Check appointments for this specific attendant
                    appointments = appointmentRepository.findByDateAttendantAndRetailerExcludingCancelled(
                        date, blockDateTimeDto.getAttendantId(), RetailerContext.getRetailer()
                    );
                } else {
                    // Check all appointments for STORE blocks
                    appointments = appointmentRepository.findByDateAndRetailerExcludingCancelled(
                        date, RetailerContext.getRetailer()
                    );
                }

                // Check if any appointment overlaps with the new block time range
                for (Appointment appointment : appointments) {
                    OffsetDateTime appointmentStart = appointment.getOffsetStartTime();
                    OffsetDateTime appointmentEnd = appointment.getOffsetEndTime();

                    // Check for time overlap using the same logic as hasTimeOverlap
                    // Overlap exists if: start1 < end2 AND start2 < end1
                    if (blockStartTime.isBefore(appointmentEnd) && appointmentStart.isBefore(blockEndTime)) {
                        String blockTypeStr = blockDateTimeDto.getBlockType() == BlockDateInfo.BlockedTimeType.ATTENDANT ? "attendant" : "store";
                        throw new BadRequestException("Cannot create " + blockTypeStr + " block - appointment exists from " +
                            appointmentStart.toLocalTime() + " to " + appointmentEnd.toLocalTime());
                    }
                }

                BlockDateInfo blockDateInfo=new BlockDateInfo();
                blockDateInfo.setOffsetBlockDate(offsetDateTimeBlocked.toString());
                blockDateInfo.setBlockDate(offsetDateTimeBlocked.toLocalDate());
                blockDateInfo.setBlockStartTime(blockTimeDto.getBlockStartTime());
                blockDateInfo.setBlockEndTime(blockTimeDto.getBlockEndTime());
                blockDateInfo.setRetailer(RetailerContext.getRetailer());
                
                // Set block type and attendant (already validated and looked up earlier)
                if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                    blockDateInfo.setAttendant(attendant);
                    blockDateInfo.setBlockType(BlockDateInfo.BlockedTimeType.ATTENDANT);
                    LOGGER.info("Setting attendant-specific block for attendant ID: {}", blockDateTimeDto.getAttendantId());
                } else {
                    // Store-wide block - no attendant needed
                    blockDateInfo.setAttendant(null);
                    blockDateInfo.setBlockType(BlockDateInfo.BlockedTimeType.STORE);
                    LOGGER.info("Setting store-wide block for retailer: {}", RetailerContext.getRetailer());
                }
                
                blockDateInformation = blockDatesRepository.save(blockDateInfo);
                BlockDateInfoDto blockDateInfoDto=new BlockDateInfoDto();
                blockDateInfoDto.setId(blockDateInformation.getId());
                blockDateInfoDto.setBlockDate(blockDateInformation.getOffsetBlockDate());
                blockDateInfoDto.setBlockStartTime(blockDateInformation.getBlockStartTime().toString());
                blockDateInfoDto.setBlockEndTime(blockDateInformation.getBlockEndTime().toString());
                blockDateInfoDto.setRetailer(RetailerContext.getRetailer());
                blockDateInfoDto.setBlockType(blockDateInformation.getBlockType());
                if(blockDateInformation.getAttendant() != null) {
                    blockDateInfoDto.setAttendantId(blockDateInformation.getAttendant().getAttendantId());
                }
                blockDateInfoList.add(blockDateInfoDto);
               // saveBlockDates(date,startTime,endTime);

            }
        }
    return blockDateInfoList;

    }

    @Override
    public void addDataToOffsetBlockDate(String retailer) {
        List<BlockDateInfo> blockDateInfos=blockDatesRepository.findByRetailer(retailer);
        for(BlockDateInfo blockDateInfo:blockDateInfos) {
            switch (retailer) {
                case "sandyspetdepot":
                case "dunkndogwash2022":
                case "allaboutpuppies":
                case "sfoceanpaws":
                case "yuppypuppyspokane":
                case "petplacemarket":
                    blockDateInfo.setOffsetBlockDate(blockDateInfo.getBlockDate().toString()+"T08:00:00.000Z");
                blockDatesRepository.save(blockDateInfo);
                break;
                case "petsupplyport":
                case "steviesnelly":
                case "pawofthefamily":
                    blockDateInfo.setOffsetBlockDate(blockDateInfo.getBlockDate().toString()+"T06:00:00.000Z");
                    blockDatesRepository.save(blockDateInfo);
                    break;
                case "fancypawsllc1":
                case "woofsingle":
                case "hamnbone":
                case "barklifestpete":
                case "pawbypawdogtraining":
                case "auggiespetsupplies":
                    blockDateInfo.setOffsetBlockDate(blockDateInfo.getBlockDate().toString()+"T05:00:00.000Z");
                    blockDatesRepository.save(blockDateInfo);
                    break;
                case "pethaus":
                    blockDateInfo.setOffsetBlockDate(blockDateInfo.getBlockDate().toString()+"T07:00:00.000Z");
                    blockDatesRepository.save(blockDateInfo);
                    break;

            }
        }
    }

    @Override
    public List<BlockDateInfoDto> findBlockTimesOfSelectedDate(OffsetDateTime selectedDateTime, String timeZone) {
        OffsetDateTime startTime = selectedDateTime;
        OffsetDateTime endTime = selectedDateTime.plusDays(1);
        
        // Get time-specific blocks (blocks with start and end times)
        List<BlockDateInfo> timeSpecificBlocks = blockDatesRepository.findBlocksBetween(startTime, endTime, RetailerContext.getRetailer());
        
        // Get full-day blocks for the selected date
        LocalDate selectedDate = selectedDateTime.toLocalDate();
        List<BlockDateInfo> fullDayBlocks = blockDatesRepository.findByBlockDateAndRetailer(selectedDate, RetailerContext.getRetailer())
                .stream()
                .filter(block -> block.getBlockStartTime() == null && block.getBlockEndTime() == null)
                .collect(Collectors.toList());
        
        // Combine both lists
        List<BlockDateInfo> allBlocks = new ArrayList<>();
        allBlocks.addAll(timeSpecificBlocks);
        allBlocks.addAll(fullDayBlocks);
        
        return allBlocks.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public void unblockSelectedTimes(List<BlockDateInfoDto> blockTimeDtos, String timeZone) {
        List<BlockDateInfo> blockDateInfos=new ArrayList<>();
        for(BlockDateInfoDto blockTimeDto:blockTimeDtos){
            Integer id=blockTimeDto.getId();
            Optional<BlockDateInfo> blockDateInfo=blockDatesRepository.findById(id);
            blockDateInfo.ifPresent(blockDateInfos::add);
        }
        if(!blockDateInfos.isEmpty()){
            blockDatesRepository.deleteAll(blockDateInfos);
        }
    }

    public BlockDateInfoDto toDto(BlockDateInfo block) {
        BlockDateInfoDto dto = new BlockDateInfoDto();
        dto.setId(block.getId());
        dto.setBlockDate(block.getBlockDate().toString());
        if(block.getBlockStartTime() != null) {
            dto.setBlockStartTime(block.getBlockStartTime().toString());
        }
        if(block.getBlockEndTime() != null) {
            dto.setBlockEndTime(block.getBlockEndTime().toString());
        }
        if(block.getBlockStartTime() == null && block.getBlockEndTime() == null) {
            dto.setBlockFullDay(true);
        }
        dto.setRetailer(block.getRetailer());
        dto.setBlockType(block.getBlockType());
        if(block.getAttendant() != null) {
            dto.setAttendantId(block.getAttendant().getAttendantId());
        }
        return dto;
    }


    @Override
    public BlockDateInfo saveBlockDates(OffsetDateTime blockDate,String timeZone) throws EtailBookItException {
        if (blockDate==null) {
            throw new EntityNotFoundException("Block Dates not found");
        }
        Integer offsetDifference=appointmentServiceImpl.calculateOffset(timeZone,blockDate);
        OffsetDateTime offsetAddedDate=blockDate.plusMinutes(offsetDifference);
        LocalDate today = LocalDate.now();
        if (today.isAfter(offsetAddedDate.toLocalDate())){
            throw new BadRequestException("Past dates are not allowed");
        }
        LocalDate date = blockDate.toLocalDate();
        List<BlockDateInfo> blockDateInfos = blockDatesRepository.findByOffsetBlockDateAndRetailer(offsetAddedDate.toString(),RetailerContext.getRetailer());
        for (BlockDateInfo blockDateInfo: blockDateInfos) {
            if (blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null) {
                throw new BadRequestException("The date is already blocked");
            }
        }
        BlockDateInfo blockDateInfo = new BlockDateInfo();
        blockDateInfo.setBlockDate(date);
        blockDateInfo.setOffsetBlockDate(blockDate.toString());
        //blockDateInfo.setBlockStartTime(blockDate);
        blockDateInfo.setRetailer(RetailerContext.getRetailer());
        return blockDatesRepository.save(blockDateInfo);
    }

    @Override
    public List<BlockDateInfoDto> getBlockDatesByMonth(OffsetDateTime startDate,OffsetDateTime endDate,String timeZone) throws EtailBookItException {
        /*if (month.isEmpty()) {
            throw new EntityNotFoundException("Please select a month");
        }

        String[] parts = month.split("-");
        String year = parts[0];
        String monthNum = parts[1];
        int years = Integer.valueOf(year);
        int months = Integer.valueOf(monthNum);
        YearMonth yearMonthObject = YearMonth.of(years, months);
        int daysInMonth = yearMonthObject.lengthOfMonth(); //28
        LocalDate startDate = LocalDate.parse(month+"-01");
        LocalDate endDate = LocalDate.parse(month+"-"+daysInMonth);*/
        String retailer = RetailerContext.getRetailer();
      //  startDate=startDate.plusMinutes(offsetTimeDifference);
        //endDate=endDate.plusMinutes(offsetTimeDifference);
        List<BlockDateInfoDto> blockDateInfoDtos=new ArrayList<>();
       // List<BlockDateInfo> blockDatesByMonth = blockDatesRepository.findByRetailerAndBlockDateBetween(retailer,startDate.toLocalDate(),endDate.toLocalDate());
        List<BlockDateInfo> blockDatesByMonth=blockDatesRepository.findByRetailerAndOffsetBlockDateBetween(retailer,startDate.toString(),endDate.toString());
        for(BlockDateInfo blockDateInfo:blockDatesByMonth){
            BlockDateInfoDto blockDateInfoDto=new BlockDateInfoDto();
           // blockDateInfoDto.setBlockDate(blockDateInfo.getBlockDate().atStartOfDay(ZoneId.of(timeZone)).withZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime());
           blockDateInfoDto.setBlockDate(blockDateInfo.getOffsetBlockDate());
           blockDateInfoDto.setId(blockDateInfo.getId());
            if(blockDateInfo.getBlockStartTime()!=null) {
                blockDateInfoDto.setBlockStartTime(blockDateInfo.getBlockStartTime().toString().split("[+]")[0]);
            }if(blockDateInfo.getBlockEndTime()!=null) {
                blockDateInfoDto.setBlockEndTime(blockDateInfo.getBlockEndTime().toString().split("[+]")[0]);
            }if(blockDateInfo.getBlockStartTime()==null && blockDateInfo.getBlockEndTime()==null){
                blockDateInfoDto.setBlockFullDay(true);
            }
            blockDateInfoDto.setRecurringGroupId(blockDateInfo.getRecurringGroupId());
            blockDateInfoDtos.add(blockDateInfoDto);
        }
        return blockDateInfoDtos;
    }

    @Override
    public void deleteBlockDate(int blockDateId, String timeZone) throws EtailBookItException {
        Optional<BlockDateInfo> existingBlockDate = blockDatesRepository.findById(blockDateId);
        
        // If block date doesn't exist, log it and return successfully
        if (!existingBlockDate.isPresent()) {
            LOGGER.error("Block date with ID " + blockDateId + " not found. Proceeding with success response.");
            return; // Return successfully without throwing exception
        }
        
        // Block date exists, proceed with validation and deletion
        BlockDateInfo blockDateInfo = existingBlockDate.get();
        LocalDate today = LocalDate.now();
        String blockDate = blockDateInfo.getOffsetBlockDate();
        Integer offsetTimeDifference = appointmentServiceImpl.calculateOffset(timeZone, OffsetDateTime.parse(blockDate));
        LocalDate blockDateto = OffsetDateTime.parse(blockDate).plusMinutes(offsetTimeDifference).toLocalDate();
        
        if (today.isAfter(blockDateto)) {
            throw new BadRequestException("Past dates are not allowed");
        }
        
        blockDatesRepository.deleteById(blockDateId);
    }

    @Override
    @Transactional
    public RecurringBlockResponseDto saveRecurringBlockDayWithTime(BlockDateTimeDto blockDateTimeDto, String timeZone) throws EtailBookItException {
        // Validate recurring fields
        if (!blockDateTimeDto.isRecurringEnabled()) {
            throw new BadRequestException("This method is only for recurring blocks. Use saveBlockDayWithTime for non-recurring blocks.");
        }
        
        if (blockDateTimeDto.getRecurringPattern() == null || blockDateTimeDto.getRecurringPattern().isEmpty()) {
            throw new BadRequestException("Recurring pattern is required for recurring blocks");
        }
        
        if (blockDateTimeDto.getRecurringEndDate() == null) {
            throw new BadRequestException("Recurring end date is required for recurring blocks");
        }
        
        if (blockDateTimeDto.getRecurringInterval() == null || blockDateTimeDto.getRecurringInterval() <= 0) {
            throw new BadRequestException("Recurring interval must be greater than 0");
        }
        
        if (blockDateTimeDto.getDate() == null) {
            throw new BadRequestException("Start date is required");
        }
        
        // Validate that end date is after start date
        if (!blockDateTimeDto.getRecurringEndDate().isAfter(blockDateTimeDto.getDate())) {
            throw new BadRequestException("Recurring end date must be after start date");
        }
        
        // Validate block type
        BlockDateInfo.BlockedTimeType blockType = blockDateTimeDto.getBlockType();
        if (blockType == null) {
            throw new BadRequestException("Block type is required");
        }
        
        // Validate and look up attendant if needed
        Attendant attendant = null;
        if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
            if (blockDateTimeDto.getAttendantId() == null) {
                throw new BadRequestException("Attendant ID is required when block type is ATTENDANT");
            }
            attendant = attendantRepository.findByAttendantId(blockDateTimeDto.getAttendantId());
            if (attendant == null) {
                throw new EntityNotFoundException("Attendant not found with id: " + blockDateTimeDto.getAttendantId());
            }
        }
        
        // Generate recurring dates with DST awareness
        List<OffsetDateTime> recurringDates = generateRecurringDates(
            blockDateTimeDto.getDate(),
            blockDateTimeDto.getRecurringEndDate(),
            blockDateTimeDto.getRecurringPattern(),
            blockDateTimeDto.getRecurringInterval(),
            timeZone
        );
        
        ZoneId zoneId = ZoneId.of(timeZone);
        
        // PHASE 1: Validate all dates first (fail-fast)
        validateRecurringBlockDays(blockDateTimeDto, recurringDates, zoneId);
        
        // PHASE 2: All validations passed, create all blocks
        return createRecurringBlockDays(blockDateTimeDto, recurringDates, zoneId);
    }
    
    /**
     * PHASE 1: Validate all recurring block dates for conflicts
     * Throws BadRequestException if ANY conflict is found (fail-fast)
     */
    private void validateRecurringBlockDays(BlockDateTimeDto blockDateTimeDto, List<OffsetDateTime> recurringDates, ZoneId zoneId) throws EtailBookItException {
        List<String> conflictErrors = new ArrayList<>();
        BlockDateInfo.BlockedTimeType blockType = blockDateTimeDto.getBlockType();
        
        for (OffsetDateTime currentDate : recurringDates) {
            // Use LocalDate for timezone-safe comparison
            LocalDate blockDate = currentDate.toLocalDate();
            
            if (blockDateTimeDto.isBlockFullDay()) {
                // ===== FULL DAY BLOCK VALIDATION =====
                
                if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                    // Creating ATTENDANT full day block
                    
                    // Check if STORE block exists and conflicts with ATTENDANT full day block
                    List<BlockDateInfo> storeBlocks = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                        blockDate, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.STORE
                    );
                    if (!storeBlocks.isEmpty()) {
                        // Check for conflicts: STORE full day blocks always conflict with ATTENDANT full day
                        for (BlockDateInfo storeBlock : storeBlocks) {
                            if (storeBlock.getBlockStartTime() == null && storeBlock.getBlockEndTime() == null) {
                                // STORE full day block exists - ATTENDANT full day block not allowed
                                conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot create attendant full day block - STORE full day block exists");
                                break; // No need to check other store blocks
                            }
                            // STORE partial time blocks do NOT conflict with ATTENDANT full day blocks
                            // This allows attendant full day blocks when only partial store blocks exist
                        }
                        if (!conflictErrors.isEmpty() && conflictErrors.get(conflictErrors.size() - 1).contains(currentDate.toLocalDate().toString())) {
                            continue; // Skip to next date if conflict found
                        }
                    }
                    
                    // Check if ATTENDANT full day block already exists - TIMEZONE-SAFE
                    List<BlockDateInfo> attendantBlocks = blockDatesRepository.findByBlockDateAndRetailerAndAttendantAndBlockType(
                        blockDate, RetailerContext.getRetailer(), blockDateTimeDto.getAttendantId(), BlockDateInfo.BlockedTimeType.ATTENDANT
                    );
                    for (BlockDateInfo existingBlock : attendantBlocks) {
                        if (existingBlock.getBlockStartTime() == null && existingBlock.getBlockEndTime() == null) {
                            conflictErrors.add(currentDate.toLocalDate().toString() + ": Full day block already exists for this attendant");
                            break;
                        }
                    }
                    // Check if ATTENDANT specific time block exists
                    StringBuilder attendantPartialBlocks = new StringBuilder();
                    for (BlockDateInfo existingBlock : attendantBlocks) {
                        if (existingBlock.getBlockStartTime() != null && existingBlock.getBlockEndTime() != null) {
                            attendantPartialBlocks.append(existingBlock.getBlockStartTime().toLocalTime())
                                .append(" to ")
                                .append(existingBlock.getBlockEndTime().toLocalTime())
                                .append("; ");
                        }
                    }
                    if (attendantPartialBlocks.length() > 0) {
                        conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot create attendant full day block - attendant already has partial time blocks: " + attendantPartialBlocks.toString());
                    }

                    // Check if appointments exist for this ATTENDANT full day block
                    List<Appointment> attendantAppointments = appointmentRepository.findByDateAttendantAndRetailerExcludingCancelled(
                        blockDate, blockDateTimeDto.getAttendantId(), RetailerContext.getRetailer()
                    );
                    if (!attendantAppointments.isEmpty()) {
                        conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot block attendant full day - " + attendantAppointments.size() + " appointment(s) exist for this attendant");
                        continue;
                    }

                } else {
                    // Creating STORE full day block

                    // Check if STORE full day block already exists - TIMEZONE-SAFE
                    List<BlockDateInfo> storeBlocks = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                        blockDate, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.STORE
                    );
                    for (BlockDateInfo existingBlock : storeBlocks) {
                        if (existingBlock.getBlockStartTime() == null && existingBlock.getBlockEndTime() == null) {
                            conflictErrors.add(currentDate.toLocalDate().toString() + ": Full day block already exists for store");
                            break;
                        }
                    }

                    // Check if STORE partial time blocks exist
                    StringBuilder storePartialBlocks = new StringBuilder();
                    for (BlockDateInfo existingBlock : storeBlocks) {
                        if (existingBlock.getBlockStartTime() != null && existingBlock.getBlockEndTime() != null) {
                            storePartialBlocks.append(existingBlock.getBlockStartTime().toLocalTime())
                                .append(" to ")
                                .append(existingBlock.getBlockEndTime().toLocalTime())
                                .append("; ");
                        }
                    }
                    if (storePartialBlocks.length() > 0) {
                        conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot create store full day block - store already has partial time blocks: " + storePartialBlocks.toString());
                    }

                    // REMOVED: Check for attendant blocks - STORE blocks take precedence over ATTENDANT blocks
                    // Store can be blocked even if attendants have individual blocks (store hierarchy > attendant hierarchy)

                    // Check if appointments exist for this STORE full day block
                    List<Appointment> appointmentsOnDate = appointmentRepository.findByDateAndRetailerExcludingCancelled(
                        blockDate, RetailerContext.getRetailer()
                    );
                    if (!appointmentsOnDate.isEmpty()) {
                        conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot block store - " + appointmentsOnDate.size() + " appointment(s) exist for this date");
                        continue;
                    }
                }
                
            } else {
                // ===== SPECIFIC TIME BLOCK VALIDATION =====
                
                if (blockDateTimeDto.getBlockTimes() == null || blockDateTimeDto.getBlockTimes().isEmpty()) {
                    continue;
                }
                
                // Query by LocalDate for timezone-safe comparison
                List<BlockDateInfo> storeBlocks = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                    blockDate, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.STORE
                );
                List<BlockDateInfo> attendantBlocks = blockDatesRepository.findByBlockDateAndRetailerAndBlockType(
                    blockDate, RetailerContext.getRetailer(), BlockDateInfo.BlockedTimeType.ATTENDANT
                );
                
                if (blockType == BlockDateInfo.BlockedTimeType.ATTENDANT) {
                    // Creating ATTENDANT specific time block
                    
                    // Check if STORE full day block exists (fail immediately)
                    boolean storeFullDayExists = false;
                    for (BlockDateInfo storeBlock : storeBlocks) {
                        if (storeBlock.getBlockStartTime() == null && storeBlock.getBlockEndTime() == null) {
                            conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot create attendant partial time block - STORE has FULL DAY block");
                            storeFullDayExists = true;
                            break;
                        }
                    }
                    if (storeFullDayExists) {
                        continue;
                    }
                    
                    // Check for ATTENDANT blocks (full day OR specific time) - TIMEZONE-SAFE
                    List<BlockDateInfo> existingAttendantBlocks = blockDatesRepository.findByBlockDateAndRetailerAndAttendantAndBlockType(
                        blockDate, RetailerContext.getRetailer(), blockDateTimeDto.getAttendantId(), BlockDateInfo.BlockedTimeType.ATTENDANT
                    );
                    
                    // Check if ATTENDANT full day block already exists
                    for (BlockDateInfo existingBlock : existingAttendantBlocks) {
                        if (existingBlock.getBlockStartTime() == null && existingBlock.getBlockEndTime() == null) {
                            conflictErrors.add(currentDate.toLocalDate().toString() + ": Full day block already exists for this attendant");
                            break;
                        }
                    }
                    
                    // Check for appointment conflicts with this ATTENDANT's partial time blocks
                    List<Appointment> attendantAppointments = appointmentRepository.findByDateAttendantAndRetailerExcludingCancelled(
                        blockDate, blockDateTimeDto.getAttendantId(), RetailerContext.getRetailer()
                    );
                    
                    for (BlockTimeDto blockTimeDto : blockDateTimeDto.getBlockTimes()) {
                        LocalTime startLocalTime = blockTimeDto.getBlockStartTime().toLocalTime();
                        LocalTime endLocalTime = blockTimeDto.getBlockEndTime().toLocalTime();
                        
                        for (Appointment appointment : attendantAppointments) {
                            if (appointment.getOffsetStartTime() != null && appointment.getOffsetEndTime() != null) {
                                LocalTime apptStart = appointment.getOffsetStartTime().toLocalTime();
                                LocalTime apptEnd = appointment.getOffsetEndTime().toLocalTime();

                                // Check for overlap using simplified logic
                                if (hasTimeOverlap(startLocalTime, endLocalTime, apptStart, apptEnd)) {
                                    conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot block attendant - appointment exists at " + apptStart + " to " + apptEnd);
                                    break;
                                }
                            }
                        }
                        if (!conflictErrors.isEmpty() && conflictErrors.get(conflictErrors.size() - 1).contains(currentDate.toLocalDate().toString())) {
                            break; // Already added error for this date
                        }
                    }
                    
                    // Check for STORE partial time block overlaps (allow if no overlap)
                    for (BlockTimeDto blockTimeDto : blockDateTimeDto.getBlockTimes()) {
                        LocalTime startLocalTime = blockTimeDto.getBlockStartTime().toLocalTime();
                        LocalTime endLocalTime = blockTimeDto.getBlockEndTime().toLocalTime();
                        
                        for (BlockDateInfo storeBlock : storeBlocks) {
                            if (storeBlock.getBlockStartTime() != null && storeBlock.getBlockEndTime() != null) {
                                LocalTime storeStartLocalTime = storeBlock.getBlockStartTime().toLocalTime();
                                LocalTime storeEndLocalTime = storeBlock.getBlockEndTime().toLocalTime();

                                // Check for overlap with store partial time blocks
                                if (hasTimeOverlap(startLocalTime, endLocalTime, storeStartLocalTime, storeEndLocalTime)) {
                                    conflictErrors.add(currentDate.toLocalDate().toString() + ": Time range overlaps with store block (" + storeStartLocalTime + " to " + storeEndLocalTime + ")");
                                    break;
                                }
                            }
                        }
                        if (!conflictErrors.isEmpty() && conflictErrors.get(conflictErrors.size() - 1).contains(currentDate.toLocalDate().toString())) {
                            break; // Already added error for this date
                        }
                    }
                    
                    // Check for ATTENDANT time overlaps
                    for (BlockTimeDto blockTimeDto : blockDateTimeDto.getBlockTimes()) {
                        OffsetDateTime blockStartTime = blockTimeDto.getBlockStartTime();
                        OffsetDateTime blockEndTime = blockTimeDto.getBlockEndTime();
                        
                        LocalTime startLocalTime = blockStartTime.toLocalTime();
                        LocalTime endLocalTime = blockEndTime.toLocalTime();
                        
                        for (BlockDateInfo existingBlock : existingAttendantBlocks) {
                            if (existingBlock.getBlockStartTime() != null && existingBlock.getBlockEndTime() != null) {
                                // Extract local times for comparison (timezone-agnostic overlap check)
                                LocalTime existingStartLocalTime = existingBlock.getBlockStartTime().toLocalTime();
                                LocalTime existingEndLocalTime = existingBlock.getBlockEndTime().toLocalTime();

                                // Check for overlap using simplified logic
                                if (hasTimeOverlap(startLocalTime, endLocalTime, existingStartLocalTime, existingEndLocalTime)) {
                                    conflictErrors.add(currentDate.toLocalDate().toString() + ": Time range overlaps with existing attendant block (" + existingStartLocalTime + " to " + existingEndLocalTime + ")");
                                    break;
                                }
                            }
                        }
                    }
                    
                } else {
                    // Creating STORE specific time block
                    
                    // REMOVED: Check for attendant full day blocks - STORE blocks take precedence over ATTENDANT blocks
                    // Store can create partial time blocks even if attendants have full day blocks
                    
                    // Check if STORE full day block already exists
                    for (BlockDateInfo existingBlock : storeBlocks) {
                        if (existingBlock.getBlockStartTime() == null && existingBlock.getBlockEndTime() == null) {
                            conflictErrors.add(currentDate.toLocalDate().toString() + ": Full day block already exists for store");
                            break;
                        }
                    }
                    
                    // Check for appointment conflicts with STORE partial time blocks
                    List<Appointment> appointmentsOnDate = appointmentRepository.findByDateAndRetailerExcludingCancelled(
                        blockDate, RetailerContext.getRetailer()
                    );
                    
                    for (BlockTimeDto blockTimeDto : blockDateTimeDto.getBlockTimes()) {
                        LocalTime startLocalTime = blockTimeDto.getBlockStartTime().toLocalTime();
                        LocalTime endLocalTime = blockTimeDto.getBlockEndTime().toLocalTime();
                        
                        for (Appointment appointment : appointmentsOnDate) {
                            if (appointment.getOffsetStartTime() != null && appointment.getOffsetEndTime() != null) {
                                LocalTime apptStart = appointment.getOffsetStartTime().toLocalTime();
                                LocalTime apptEnd = appointment.getOffsetEndTime().toLocalTime();

                                // Check for overlap using simplified logic
                                if (hasTimeOverlap(startLocalTime, endLocalTime, apptStart, apptEnd)) {
                                    conflictErrors.add(currentDate.toLocalDate().toString() + ": Cannot block store - appointment exists at " + apptStart + " to " + apptEnd);
                                    break;
                                }
                            }
                        }
                        if (!conflictErrors.isEmpty() && conflictErrors.get(conflictErrors.size() - 1).contains(currentDate.toLocalDate().toString())) {
                            break; // Already added error for this date
                        }
                    }
                    
                    // REMOVED: Check for attendant partial time block overlaps - STORE blocks take precedence over ATTENDANT blocks
                    // Store can create partial time blocks even if they overlap with attendant partial blocks
                    
                    // Check for STORE time overlaps
                    for (BlockTimeDto blockTimeDto : blockDateTimeDto.getBlockTimes()) {
                        OffsetDateTime blockStartTime = blockTimeDto.getBlockStartTime();
                        OffsetDateTime blockEndTime = blockTimeDto.getBlockEndTime();
                        
                        LocalTime startLocalTime = blockStartTime.toLocalTime();
                        LocalTime endLocalTime = blockEndTime.toLocalTime();
                        
                        for (BlockDateInfo existingBlock : storeBlocks) {
                            if (existingBlock.getBlockStartTime() != null && existingBlock.getBlockEndTime() != null) {
                                // Extract local times for comparison (timezone-agnostic overlap check)
                                LocalTime existingStartLocalTime = existingBlock.getBlockStartTime().toLocalTime();
                                LocalTime existingEndLocalTime = existingBlock.getBlockEndTime().toLocalTime();

                                // Check for overlap using simplified logic
                                if (hasTimeOverlap(startLocalTime, endLocalTime, existingStartLocalTime, existingEndLocalTime)) {
                                    conflictErrors.add(currentDate.toLocalDate().toString() + ": Time range overlaps with existing store block (" + existingStartLocalTime + " to " + existingEndLocalTime + ")");
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // FAIL-FAST: If ANY conflicts found, throw error immediately - DON'T CREATE ANYTHING
        if (!conflictErrors.isEmpty()) {
            String errorMessage = "Cannot create recurring blocks - conflicts found:\n" + String.join("\n", conflictErrors);
            throw new BadRequestException(errorMessage);
        }
    }
    
    /**
     * Helper method to check if two time ranges overlap
     * Returns true if there is ANY overlap (excluding touching boundaries)
     * Based on user requirements:
     * - "store block 2-3pm but attendant block 2.30-3.30pm overlap and throw error" (REJECT overlaps)
     * - "store block 2-3pm, attendant block 3-4pm is OK" (ALLOW touching boundaries)
     */
    private boolean hasTimeOverlap(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
        // Two time ranges overlap if:
        // start1 < end2 AND start2 < end1
        // This covers all overlap scenarios:
        // - Partial overlaps (start of one range is within the other)
        // - Complete containment (one range completely contains the other)
        // - Exact matches
        // But EXCLUDES touching boundaries (e.g., 2:00-3:00 and 3:00-4:00 will NOT overlap)

        return start1.isBefore(end2) && start2.isBefore(end1);
    }

    /**
     * PHASE 2: Create all recurring block dates
     * This method is only called after all validations pass
     * Creates blocks directly without re-validating
     * Generates a unique recurringGroupId (UUID) for all blocks in this recurring series
     */
    private RecurringBlockResponseDto createRecurringBlockDays(BlockDateTimeDto blockDateTimeDto, List<OffsetDateTime> recurringDates, ZoneId zoneId) throws EtailBookItException {
        RecurringBlockResponseDto response = new RecurringBlockResponseDto();

        // Generate a unique UUID for this recurring group (set by backend only)
        String recurringGroupId = UUID.randomUUID().toString();
        LOGGER.info("Creating recurring block series with groupId: {}", recurringGroupId);

        for (OffsetDateTime currentDate : recurringDates) {
            ZonedDateTime dateZoned = ZonedDateTime.of(currentDate.toLocalDate(), LocalTime.MIDNIGHT, zoneId);
            OffsetDateTime normalizedDate = dateZoned.toOffsetDateTime();

            // Create block for this date (use normalized date for consistency)
            BlockDateTimeDto singleBlockDto = new BlockDateTimeDto();
            singleBlockDto.setDate(normalizedDate);
            singleBlockDto.setBlockFullDay(blockDateTimeDto.isBlockFullDay());
            singleBlockDto.setAttendantId(blockDateTimeDto.getAttendantId());
            singleBlockDto.setBlockType(blockDateTimeDto.getBlockType());
            singleBlockDto.setRecurringEnabled(false); // Mark as non-recurring to use existing logic

            // Adjust block times for this specific date if not full day
            // Follow the same pattern as saveBlockDayWithTime: preserve local time with timezone offset
            if (!blockDateTimeDto.isBlockFullDay() && blockDateTimeDto.getBlockTimes() != null) {
                List<BlockTimeDto> adjustedBlockTimes = new ArrayList<>();
                LocalDate targetDate = normalizedDate.toLocalDate();

                for (BlockTimeDto originalTime : blockDateTimeDto.getBlockTimes()) {
                    // Extract local time (preserve the local time, not the instant)
                    LocalTime startLocalTime = originalTime.getBlockStartTime().toLocalTime();
                    LocalTime endLocalTime = originalTime.getBlockEndTime().toLocalTime();

                    // Create OffsetDateTime for the target date with the same local time
                    // Use the timezone's offset for this date (DST-aware)
                    ZonedDateTime startZoned = ZonedDateTime.of(targetDate, startLocalTime, zoneId);
                    ZonedDateTime endZoned = ZonedDateTime.of(targetDate, endLocalTime, zoneId);

                    // Convert to OffsetDateTime preserving the timezone offset (will be stripped by converter, but preserves local time)
                    OffsetDateTime adjustedStart = startZoned.toOffsetDateTime();
                    OffsetDateTime adjustedEnd = endZoned.toOffsetDateTime();

                    BlockTimeDto adjustedTime = new BlockTimeDto();
                    adjustedTime.setBlockStartTime(adjustedStart);
                    adjustedTime.setBlockEndTime(adjustedEnd);
                    adjustedBlockTimes.add(adjustedTime);
                }
                singleBlockDto.setBlockTimes(adjustedBlockTimes);
            } else {
                // For full day blocks, no need to adjust times
                singleBlockDto.setBlockTimes(null);
            }

            // NOTE: We call saveBlockDayWithTime (which re-validates) instead of directly persisting to catch race conditions.
            // Between Phase 1 validation and Phase 2 creation, concurrent requests could create conflicting blocks.
            List<BlockDateInfoDto> created = saveBlockDayWithTime(singleBlockDto, zoneId.getId());

            // Set recurringGroupId on all created blocks (backend-generated UUID)
            for (BlockDateInfoDto createdDto : created) {
                // Fetch the entity, set recurringGroupId, and save
                BlockDateInfo blockEntity = blockDatesRepository.findById(createdDto.getId())
                    .orElseThrow(() -> new EntityNotFoundException("Block not found with id: " + createdDto.getId()));
                blockEntity.setRecurringGroupId(recurringGroupId);
                blockDatesRepository.save(blockEntity);

                // Update DTO to reflect the recurringGroupId
                createdDto.setRecurringGroupId(recurringGroupId);
            }

            response.getCreated().addAll(created);
            response.setTotalCreated(response.getTotalCreated() + created.size());
        }

        response.setRecurringGroupId(recurringGroupId);
        return response;
    }
    
    /**
     * Generate recurring dates with DST awareness
     * ZonedDateTime automatically maintains local time across DST transitions
     */
    private List<OffsetDateTime> generateRecurringDates(
            OffsetDateTime startDate,
            OffsetDateTime endDate,
            String pattern,
            Integer interval,
            String timeZone
    ) throws BadRequestException {
        List<OffsetDateTime> dates = new ArrayList<>();
        ZoneId zoneId = ZoneId.of(timeZone);
        
        // Convert start date to ZonedDateTime for DST-aware arithmetic
        ZonedDateTime startZoned = startDate.atZoneSameInstant(zoneId);
        ZonedDateTime endZoned = endDate.atZoneSameInstant(zoneId);
        
        ZonedDateTime current = startZoned;
        
        while (!current.isAfter(endZoned)) {
            // ZonedDateTime automatically handles DST transitions correctly
            // plusDays/plusWeeks/plusMonths maintain local time across DST boundaries
            dates.add(current.toOffsetDateTime());
            
            // Move to next occurrence based on pattern
            switch (pattern.toUpperCase()) {
                case "DAILY":
                    current = current.plusDays(interval);
                    break;
                case "WEEKLY":
                    current = current.plusWeeks(interval);
                    break;
                case "MONTHLY":
                    current = current.plusMonths(interval);
                    break;
                default:
                    throw new BadRequestException("Invalid recurring pattern: " + pattern + ". Supported patterns: DAILY, WEEKLY, MONTHLY");
            }
        }
        
        return dates;
    }


}
