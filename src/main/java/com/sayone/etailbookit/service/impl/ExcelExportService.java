package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.model.Appointment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

@Service
public class ExcelExportService {
    private static Logger LOGGER = LoggerFactory.getLogger(ExcelExportService.class);

    public byte[] exportAppointments(List<Appointment> appointments,String timeZone) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Integer offsetDifference;
        Sheet sheet = workbook.createSheet("Appointments");
        if(timeZone!=null){
            TimeZone tz = TimeZone.getTimeZone(timeZone);
             offsetDifference= tz.getOffset(new Date().getTime()) / 1000 / 60;
        }else{
            offsetDifference=0;
        }

        // Create Header
        Row headerRow = sheet.createRow(0);
        String[] headers = {"Retailer", "Start Time", "End Time", "Service Name","Pet Name","Customer Name","Attendant","Venue","Address"};
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }

        // Populate Data
        int rowNum = 1;
        for (Appointment appointment : appointments) {
            Row row = sheet.createRow(rowNum++);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM d yyyy hh:mm a", Locale.ENGLISH);
            String offsetStartTime=OffsetDateTime.parse(appointment.getAppointmentStartDateAndTime()).plusMinutes(offsetDifference).format(formatter);
            String offsetEndTime=OffsetDateTime.parse(appointment.getAppointmentEndDateAndTime()).plusMinutes(offsetDifference).format(formatter);
           // row.createCell(0).setCellValue(appointment.getId());
            row.createCell(0).setCellValue(appointment.getRetailer());
           // row.createCell(1).setCellValue(appointment.getDate().toString());
            row.createCell(1).setCellValue(offsetStartTime);
            row.createCell(2).setCellValue(offsetEndTime);
            row.createCell(3).setCellValue(appointment.getService().getName());
            row.createCell(4).setCellValue(appointment.getPet().getName());
            row.createCell(5).setCellValue(appointment.getCustomerName());
            row.createCell(6).setCellValue(appointment.getAttendant().getFirstName());
            row.createCell(7).setCellValue(appointment.getVenue().getInternalName());
            row.createCell(8).setCellValue(appointment.getAddress());
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            // Write to ByteArrayOutputStream
            workbook.write(outputStream);

        }catch (Exception e){
            LOGGER.error("Error writing to excel file ");
        }
        finally {
            workbook.close();
        }
        return outputStream.toByteArray();
    }
}
