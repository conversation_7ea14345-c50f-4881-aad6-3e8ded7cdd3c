package com.sayone.etailbookit.service.impl;

import com.itextpdf.kernel.pdf.StampingProperties;
import com.itextpdf.signatures.*;
import com.itextpdf.text.DocumentException;
import com.itextpdf.kernel.geom.Rectangle;
import com.sayone.etailbookit.component.CustomerSMSNotification;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.AppointmentWaiverOfLiabilityInformationRepository;
import com.sayone.etailbookit.repository.PetRepository;
import com.sayone.etailbookit.repository.PetWaiverOfLiabilityInfoRepository;
import com.sayone.etailbookit.service.IEsignSerive;
import com.sayone.etailbookit.service.IWaiverOfLiabilityService;
import com.sayone.etailbookit.util.DataFileProperties;
import com.sayone.etailbookit.util.RequestTypeContext;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.SignUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.itextpdf.kernel.pdf.PdfReader;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class EsignService implements IEsignSerive {

    @Autowired
    SignUtil signUtils;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    S3Integration s3Integration;

    @Autowired
    PetWaiverOfLiabilityInfoRepository petWaiverOfLiabilityInfoRepository;

    @Autowired
    AppointmentWaiverOfLiabilityInformationRepository appointmentWaiverOfLiabilityInformationRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    IWaiverOfLiabilityService iWaiverOfLiabilityService;

    @Autowired
    CustomerSMSNotification customerSMSNotification;

    private static org.slf4j.Logger LOGGER = LoggerFactory.getLogger(EsignService.class);
    @Override
    public String  sign(String src, String dest, Certificate[] chain, PrivateKey pk, String digestAlgorithm, String provider, PdfSigner.CryptoStandard signatureType, String initialName,Integer petId,String documentType) throws GeneralSecurityException, IOException, DocumentException, EtailBookItException {
        PDDocument pdfDoc;
        int pageCount = 0;
        String fileUrl;
        pdfDoc = PDDocument.load(new File(src));
        pageCount = pdfDoc.getNumberOfPages();
        LOGGER.info("Number of pages"+pageCount);
        PdfReader reader = new PdfReader(src);
        PdfSigner signer = new PdfSigner(reader, new FileOutputStream(dest), new StampingProperties());

        Rectangle rect = signUtils.findXandYCoordinatesOfPdf(src, dest);
        PdfSignatureAppearance appearance = signer.getSignatureAppearance();
        appearance.setLayer2Text(setAppearenceDetails(initialName)).setPageRect(rect)
                .setPageNumber(pageCount);
        LOGGER.info("Created the place holder for signature");
        IExternalSignature pks = new PrivateKeySignature(pk, digestAlgorithm, provider);
        IExternalDigest digest = new BouncyCastleDigest();

        signer.signDetached(digest, pks, chain, null, null, null, 0, signatureType);
        LOGGER.info("Signed the document");
        String fileName= FilenameUtils.getName(dest);
       // PDDocument  signedDocument = PDDocument.load(new File(dest));
        fileUrl= s3Integration.uploadFile(dest,fileName);
        if(documentType.equalsIgnoreCase("WAIVER OF LIABILITY")) {
            saveSignedFileurl(fileUrl, petId);
            changeWaiverStatus(petId);
        }
        /*if(RequestTypeContext.getRequestType().equals("Retailer")){
            customerSMSNotification.notifyRetailerOnWaiverSignature(petId);
        }*/
        return fileUrl;
    }

    @Transactional
    private void changeWaiverStatus(Integer petId) {
        List<Appointment> appointments = appointmentRepository.findByPetIdAndWaiverStatus(petId, Boolean.FALSE);
        if (!appointments.isEmpty()) {
            appointments.forEach(appointment -> appointment.setWaiverAcknowledged(Boolean.TRUE));
            appointmentRepository.saveAll(appointments);
        }
    }


    private String setAppearenceDetails(String name) {
        String text;
        StringBuilder buf = new StringBuilder();
        buf.append("Digitally signed by ");
        buf.append(name).append('\n');
        SimpleDateFormat sd = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss z");
        buf.append("Date: ").append(sd.format(new Date()));
       // buf.append('\n').append(" IP Address  :").append(location);
        text = buf.toString();
        return text;
    }

    private void saveSignedFileurl(String fileUrl,Integer petId) throws EtailBookItException {
        Pet pet= petRepository.findByPetId(petId);
        if(pet!=null) {
            PetWaiverOfLiabilityInfo  petWaiverOfLiabilityInfo=  petWaiverOfLiabilityInfoRepository.findByPet(petId, RetailerContext.getRetailer());
            if(petWaiverOfLiabilityInfo!=null){
                throw new BadRequestException("Waiver of Liability is already signed for the pet::"+pet.getName());
            }
            else {
                PetWaiverOfLiabilityInfo petWaiverOfLiability=new PetWaiverOfLiabilityInfo();
                petWaiverOfLiability.setSignedFile(fileUrl);
                petWaiverOfLiability.setPet(pet);
                petWaiverOfLiability.setRetailer(RetailerContext.getRetailer());
                WaiverOfLiability waiverOfLiability = iWaiverOfLiabilityService.getWaiverOfLiabilityInformation();
                if(waiverOfLiability!=null){
                    petWaiverOfLiability.setWaiverOfLiability(waiverOfLiability);
                }
                petWaiverOfLiabilityInfoRepository.save(petWaiverOfLiability);
            }
        }else{
            throw new BadRequestException("Pet is not found with id::"+petId);
        }
    }

}

