package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.VenueMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.VenueDetailProjection;
import com.sayone.etailbookit.projections.VenueDropdownProjection;
import com.sayone.etailbookit.projections.VenueListingProjection;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IVenueService;
import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import com.sayone.etailbookit.validator.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.*;

@Service
public class VenueService implements IVenueService {

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    ServiceTypeRepository serviceTypeRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    VenuePetTypesRepository venuePetTypesRepository;

    @Autowired
    VenueAvailabilityRepository venueAvailabilityRepository;

    @Autowired
    Validator validator;

    @Override
    public VenueDto addVenue(VenueDto venueDto) throws EtailBookItException {

        validator.validateVenue(venueDto, null);
        Set<VenuePetTypes> venuePetTypes = new HashSet();
        Set<ServiceType> serviceTypes = new HashSet<>();
        Venue venue = VenueMapper.toVenueEntity(venueDto);

        if(venueDto.getServiceTypeIds() != null) {
            for (Integer serviceTypeId : venueDto.getServiceTypeIds()) {
                ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
                if (serviceType != null) {
                    serviceTypes.add(serviceType);
                } else {
                    throw new EntityNotFoundException("Service Type not found");
                }
            }
            venue.setServiceTypes(serviceTypes);
        }

        if(venueDto.getPetTypes() !=null) {
            for (VenuePetTypeDto petTypeDto : venueDto.getPetTypes()) {
                PetType petType = petTypeRepository.findByPetTypeId(petTypeDto.getId());
                if (petType != null) {
                    VenuePetTypes venuePetType = new VenuePetTypes();
                    venuePetType.setPetType(petType);
                    Set<GeneralPetSize> generalPetSizes = new HashSet();
                    if(petTypeDto.getGeneralPetSizeIds() != null){
                        for (Integer petSizeId : petTypeDto.getGeneralPetSizeIds()) {
                            GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(petSizeId);
                            if (generalPetSize != null) {
                                generalPetSizes.add(generalPetSize);
                            } else {
                                throw new EntityNotFoundException("General Pet Size not found");
                            }
                        }
                    }

                    Set<Temperament> temperaments = new HashSet();
                    if(petTypeDto.getTemperamentIds() != null) {
                        for (Integer temperamentId : petTypeDto.getTemperamentIds()) {
                            Temperament temperament = temperamentRepository.findByTemperamentId(temperamentId);
                            if (temperament != null) {
                                temperaments.add(temperament);
                            } else {
                                throw new EntityNotFoundException("Temperament not found");
                            }
                        }
                    }

                    venuePetType.setTemperaments(temperaments);
                    venuePetType.setGeneralPetSizes(generalPetSizes);
                    venuePetType.setVenue(venue);
                    venuePetType.setRetailer(RetailerContext.getRetailer());
                    venuePetTypes.add(venuePetType);
                } else {
                    throw new EntityNotFoundException("Pet Type not found with ID :::" + petTypeDto.getId());
                }
            }
            venue.setVenuePetTypes(venuePetTypes);
        }

        if (venueDto.getAvailabilityDays() != null) {
            Set<VenueAvailability> venueAvailabilities = new HashSet();

            for(AvailabilityDto venueAvailability : venueDto.getAvailabilityDays()){
                VenueAvailability availability = new VenueAvailability();
                if(venueAvailability.getAvailabilityOpenTime() == null) {
                    throw new BadRequestException("Invalid opening time for ::: " + venueAvailability.getAvailableDay());
                }
                if(venueAvailability.getAvailabilityCloseTime() == null) {
                    throw new BadRequestException("Invalid closing time for ::: " + venueAvailability.getAvailableDay());
                }
                availability.setAvailabilityCloseTime(
                        OffsetTime.parse(venueAvailability.getAvailabilityCloseTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailabilityOpenTime(
                        OffsetTime.parse(venueAvailability.getAvailabilityOpenTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailableDay(venueAvailability.getAvailableDay());
                availability.setAvailableDay(venueAvailability.getAvailableDay().toUpperCase());
                availability.setVenue(venue);
                availability.setRetailer(RetailerContext.getRetailer());
                venueAvailabilities.add(availability);
            }
            venue.setAvailabilityDays(venueAvailabilities);
        }

        venue.setServiceTypes(serviceTypes);
        venue.setVenuePetTypes(venuePetTypes);
        venue.setTimezone(venueDto.getTimezone());
        venue = venueRepository.save(venue);
        return VenueMapper.toVenueDto(venue);
    }

    @Override
    @Transactional
    public void updateVenue(VenueDto venueDto, Integer venueId) throws EtailBookItException {
        if (venueId != null) {
            Optional<Venue> venue = venueRepository.findById(venueId);
            if (!venue.isPresent()) {
                throw new EntityNotFoundException("Venue not found");
            }
            if(venue.get().getInternalName().equalsIgnoreCase("Pet Emporium DEV")){
                throw new EtailBookItException("This is test data and can not be updated");
            }
        }
        validator.validateVenue(venueDto, venueId);
        Set<VenuePetTypes> venuePetTypes = new HashSet();
        Set<ServiceType> serviceTypes = new HashSet<>();

        Venue venue = VenueMapper.toVenueEntity(venueDto);
        venue.setVenueId(venueId);

        if(venueDto.getServiceTypeIds() != null) {
            for (Integer serviceTypeId : venueDto.getServiceTypeIds()) {
                ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
                if (serviceType != null) {
                    serviceTypes.add(serviceType);
                } else {
                    throw new EntityNotFoundException("Service Type not found with ID :::" + serviceTypeId);
                }
            }
            venue.setServiceTypes(serviceTypes);
        }

        if(venueDto.getPetTypes() !=null) {
            for (VenuePetTypeDto petTypeDto : venueDto.getPetTypes()) {
                PetType petType = petTypeRepository.findByPetTypeId(petTypeDto.getId());
                if (petType != null) {
                    VenuePetTypes venuePetType = new VenuePetTypes();
                    venuePetType.setPetType(petType);
                    Set<GeneralPetSize> generalPetSizes = new HashSet();
                    if(petTypeDto.getGeneralPetSizeIds() != null){
                        for (Integer petSizeId : petTypeDto.getGeneralPetSizeIds()) {
                            GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(petSizeId);
                            if (generalPetSize != null) {
                                generalPetSizes.add(generalPetSize);
                            } else {
                                throw new EntityNotFoundException("General Pet Size not found with ID :::" + petSizeId);
                            }
                        }
                    }
                  /*  if(petTypeDto.getTemperamentIds()==null){
                        throw new BadRequestException(" pet Temperaments not found for pet"+petType.getName());
                    }*/
                        Set<Temperament> temperaments = new HashSet();
                        if (petTypeDto.getTemperamentIds() != null) {
                            for (Integer temperamentId : petTypeDto.getTemperamentIds()) {
                                Temperament temperament = temperamentRepository.findByTemperamentId(temperamentId);
                                if (temperament != null) {
                                    temperaments.add(temperament);
                                } else {
                                    throw new EntityNotFoundException("Temperament not found with ID :::" + temperamentId);
                                }
                            }

                            venuePetType.setTemperaments(temperaments);
                        }

                    venuePetType.setGeneralPetSizes(generalPetSizes);
                    venuePetType.setVenue(venue);
                    venuePetType.setRetailer(RetailerContext.getRetailer());
                    venuePetTypes.add(venuePetType);
                } else {
                    throw new EntityNotFoundException("Pet Type not found with ID :::" + petTypeDto.getId());
                }
            }
            venue.setVenuePetTypes(venuePetTypes);
        }

        if (venue.getAvailabilityDays() != null) {
            if(venueAvailabilityRepository.findByVenueVenueId(venue.getVenueId()) != null) {
                List<VenueAvailability> availability = venueAvailabilityRepository.findByVenueVenueId(venue.getVenueId());
                venueAvailabilityRepository.deleteAll(availability);
            }
            Set<VenueAvailability> venueAvailabilities = new HashSet();

            for(AvailabilityDto venueAvailability : venueDto.getAvailabilityDays()){
                VenueAvailability availability = new VenueAvailability();
                if(venueAvailability.getAvailabilityOpenTime() == null) {
                    throw new BadRequestException("Invalid opening time for ::: " + venueAvailability.getAvailableDay());
                }
                if(venueAvailability.getAvailabilityCloseTime() == null) {
                    throw new BadRequestException("Invalid closing time for ::: " + venueAvailability.getAvailableDay());
                }
                availability.setAvailabilityCloseTime(
                        OffsetTime.parse(venueAvailability.getAvailabilityCloseTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailabilityOpenTime(
                        OffsetTime.parse(venueAvailability.getAvailabilityOpenTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailableDay(venueAvailability.getAvailableDay());
                availability.setVenue(venue);
                availability.setRetailer(RetailerContext.getRetailer());
                venueAvailabilities.add(availability);
            }
            venue.setAvailabilityDays(venueAvailabilities);
        }
        venuePetTypesRepository.deleteByVenue(venue);
        venueRepository.save(venue);
    }

    @Override
    @Transactional
    public void deleteVenue(int venueId) throws EtailBookItException {
        Venue venue = venueRepository.findByVenueId(venueId);
        if (venue == null) {
            throw new EntityNotFoundException("Venue Id not found");
        }
        if(venue.getInternalName().equalsIgnoreCase("Pet Emporium DEV")){
            throw new EtailBookItException("This is test data and can not be deleted");
        }
        if (Utils.isNotEmpty(venue.getServices())) {
            throw new BadRequestException("Venue is currently used by Services");
        } else {
            venuePetTypesRepository.deleteByVenue(venue);
            venueRepository.delete(venue);
        }
    }

    @Override
    public VenueDto getVenueById(int venueId) throws EtailBookItException {
        validator.validateVenue(null, venueId);
        VenueDetailProjection venue = venueRepository.getVenueDetails(venueId);
        return VenueMapper.toVenueDto(venue);
    }

    @Override
    public List<VenueDropdownProjection> getVenues() {
        return venueRepository.findByRetailerOrderByVenueIdDesc(RetailerContext.getRetailer());
    }

    @Override
    public Page<VenueListingProjection> venuesByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> searchKey) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        if (sortBy.equals("internalName")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<VenueListingProjection> venues ;
        venues = venueRepository.findByKeyword(
                searchKey.isPresent() && searchKey.hashCode() != 0 ? searchKey.get().toLowerCase() : null,
                RetailerContext.getRetailer(), false,paging
        );
        return venues;
    }

    @Override
    public List<VenueDto> getVenuesForService(SearchDto searchDto) throws EntityNotFoundException {
        List<Venue> venues = venueRepository.findByRetailer(RetailerContext.getRetailer());
        List<ServiceType> serviceTypes = new ArrayList<>();

        // ✅ Skip deleted ServiceTypes
        for (Integer serviceId : searchDto.getServiceTypeIds()) {
            if(serviceId==null){
                continue;
            }
            ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceId);
            if (serviceType != null) {
                serviceTypes.add(serviceType);
            }
        }

        Set<VenuePetTypes> venuePetTypeSet = new HashSet<>();
        HashMap<Integer, Set<GeneralPetSize>> gPT = new HashMap<>();

        // ✅ Skip deleted PetTypes
        for (SearchPetTypeDto venuePetType : searchDto.getPetTypes()) {
            if(venuePetType.getId()==null)continue;
            PetType petType = petTypeRepository.findByPetTypeId(venuePetType.getId());
            if (petType == null) {
                continue; // skip if PetType was deleted
            }

            Set<GeneralPetSize> generalPetSizes = new HashSet<>();
            for (Integer generalPetSizeId : venuePetType.getGeneralPetSize()) {
                GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeId);
                if (generalPetSize == null) {
                    throw new EntityNotFoundException("General pet size not found");
                }
                generalPetSizes.add(generalPetSize);
            }
            gPT.put(petType.getPetTypeId(), generalPetSizes);

            venuePetTypeSet.addAll(venuePetTypesRepository.findByPetType(petType.getPetTypeId()));
        }

        // find temperament of each petType
        HashMap<Integer, Set<Temperament>> petTypeTemperament = new HashMap<>();
        for (SearchPetTypeDto venuePetTypeTemperament : searchDto.getPetTypes()) {
            PetType petType = petTypeRepository.findByPetTypeId(venuePetTypeTemperament.getId());
            if (petType == null) {
                continue; // skip deleted PetTypes
            }

            Set<Temperament> generalPetTemperaments = new HashSet<>();
            for (Integer petTemperament : venuePetTypeTemperament.getTemperamentIds()) {
                Temperament temperament = temperamentRepository.findByTemperamentId(petTemperament);
                if (temperament == null) {
                    throw new EntityNotFoundException("Pet Temperament not found");
                }
                generalPetTemperaments.add(temperament);
            }
            petTypeTemperament.put(petType.getPetTypeId(), generalPetTemperaments);
        }

        // Group VenuePetTypes by Venue
        HashMap<Integer, Set<VenuePetTypes>> vPT = new HashMap<>();
        venuePetTypeSet.forEach(pt -> {
            vPT.computeIfAbsent(pt.getVenue().getVenueId(), k -> new HashSet<>()).add(pt);
        });

        // Only keep venues where all requested PetTypes are present
        HashMap<Integer, Set<VenuePetTypes>> vPT1 = new HashMap<>();
        vPT.forEach((k, v) -> {
            if (v.size() >= searchDto.getPetTypes().size()) {
                vPT1.put(k, v);
            }
        });

        // Final filtering
        List<Venue> filteredVenues = new ArrayList<>();
        venues.stream()
                .filter(v -> vPT1.containsKey(v.getVenueId()))
                .forEach(v -> {
                    Set<ServiceType> serviceTypes1 = v.getServiceTypes();
                    Set<VenuePetTypes> venuePetTypes1 = v.getVenuePetTypes();
                    Set<VenuePetTypes> venuePetTypes2 = vPT1.get(v.getVenueId());

                    if (
                            serviceTypes1.containsAll(serviceTypes) &&
                                    venuePetTypes1.containsAll(venuePetTypes2) &&
                                    venuePetTypes2.stream().allMatch(e -> gPT.containsKey(e.getPetType().getPetTypeId()) &&
                                            e.getGeneralPetSizes().containsAll(gPT.get(e.getPetType().getPetTypeId()))) &&
                                    venuePetTypes2.stream().allMatch(e -> petTypeTemperament.containsKey(e.getPetType().getPetTypeId()) &&
                                            e.getTemperaments().containsAll(petTypeTemperament.get(e.getPetType().getPetTypeId())))
                    ) {
                        filteredVenues.add(v);
                    }
                });

        return VenueMapper.toVenueDtoList(filteredVenues);
    }


}
