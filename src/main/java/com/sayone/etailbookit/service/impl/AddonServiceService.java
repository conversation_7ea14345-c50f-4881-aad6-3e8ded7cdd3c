package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.AddonServiceDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.AddonServiceMapper;
import com.sayone.etailbookit.model.AddonService;
import com.sayone.etailbookit.repository.AddonServiceRepository;
import com.sayone.etailbookit.service.IAddonServiceService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import com.sayone.etailbookit.validator.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class AddonServiceService implements IAddonServiceService {

    @Autowired
    AddonServiceRepository addonServiceRepository;

    @Override
    public AddonServiceDto addAddonService(AddonServiceDto addonServiceDto) throws EtailBookItException {
        Validator.validateAddonService(addonServiceDto, null, addonServiceRepository);
        AddonService addonService = AddonServiceMapper.toAddonServiceEntity(addonServiceDto);
        addonService.setRetailer(RetailerContext.getRetailer());
     /*   if (addonService.getIndexValue() == null) {
            Integer lastIndexId = addonServiceRepository.getByLastIndexValueByRetailerAndDeleted(addonService.getRetailer(), false);
            if (lastIndexId != null) {
                addonService.setIndexValue(lastIndexId + 1);
            } else {
                addonService.setIndexValue(1);
            }
        }*/
        addonService = addonServiceRepository.save(addonService);
        return AddonServiceMapper.toAddonServiceDto(addonService);
    }

    @Override
    public AddonServiceDto updateAddonService(AddonServiceDto addonServiceDto, int addonServiceId) throws EtailBookItException {
        Validator.validateAddonService(addonServiceDto, addonServiceId, addonServiceRepository);
        AddonService addonService = AddonServiceMapper.toAddonServiceEntity(addonServiceDto);
        addonService.setAddonServiceId(addonServiceId);
        addonService.setRetailer(RetailerContext.getRetailer());
        addonService = addonServiceRepository.save(addonService);
        return AddonServiceMapper.toAddonServiceDto(addonService);
    }

    @Override
    public void deleteAddonService(int addonServiceId) throws EtailBookItException {
        AddonService addonService = addonServiceRepository.findByAddonServiceId(addonServiceId);
        if (addonService == null) {
            throw new EntityNotFoundException("Add-on Service not found");
        }
        if (Utils.isNotEmpty(addonService.getServices())) {
            throw new BadRequestException("Add-on Service is currently used by Services");
        } else  if (Utils.isNotEmpty(addonService.getAttendants())) {
            throw new BadRequestException("Add-on Service is currently used by attendants");
        } else {
            addonServiceRepository.delete(addonService);
        }
    }

    @Override
    public AddonServiceDto getAddonServiceById(int addonServiceId) throws EtailBookItException {
        Validator.validateAddonService(null, addonServiceId, addonServiceRepository);
        AddonService addonService = addonServiceRepository.getOne(addonServiceId);
        return AddonServiceMapper.toAddonServiceDto(addonService);
    }

    @Override
    public void bulkUpdateAddonService(List<AddonServiceDto> addonServiceDtos) throws EtailBookItException {
        for(AddonServiceDto addonServiceDto:addonServiceDtos){
            Validator.validateAddonService(addonServiceDto,addonServiceDto.getId(),addonServiceRepository);
        }
        List<AddonService> addonServices = AddonServiceMapper.toAddonServiceEntityList(addonServiceDtos);
        addonServiceRepository.saveAll(addonServices);
    }

    @Override
    public Page<AddonServiceDto> getAddonServices(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search) {
        Pageable paging = PageRequest.of(pageNo, pageSize,Sort.by((Sort.Order.desc("active")),Sort.Order.desc(sortBy)));
        Page<AddonService> addonServices;
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.asc(sortBy)));
        }if (search.isPresent() && search.hashCode() != 0) {
             addonServices = addonServiceRepository.findByKeyword(search.get().toLowerCase(),RetailerContext.getRetailer(),false,paging);
        }else{
            addonServices = addonServiceRepository.findAByRetailerAndDeleted(RetailerContext.getRetailer(), false, paging);
        }
        return new PageImpl<>(AddonServiceMapper.toAddonServiceDtoList(addonServices.getContent()), paging, addonServices.getTotalElements());
    }

    @Override
    public Page<AddonServiceDto> getActiveService(Integer pageNo, Integer pageSize, String sortBy,String search) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<AddonService> addonServices;
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }if (!search.isEmpty() && search.hashCode() != 0) {
            addonServices=addonServiceRepository.findByKeywordActiveRetailerAndDeleted(search.toLowerCase(),true,RetailerContext.getRetailer(),false,paging);
        }
        else {
             addonServices = addonServiceRepository.findByActiveAndRetailerAndDeleted(true, RetailerContext.getRetailer(), false,paging);

        }return new PageImpl<>(AddonServiceMapper.toAddonServiceDtoList(addonServices.getContent()), paging, addonServices.getTotalElements());
    }
}
