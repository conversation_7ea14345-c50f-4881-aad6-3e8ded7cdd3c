package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.VacationDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.model.Vacation;
import com.sayone.etailbookit.projections.VacationProjection;
import com.sayone.etailbookit.repository.AttendantRepository;
import com.sayone.etailbookit.repository.VacationRepository;
import com.sayone.etailbookit.service.IVacationService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class VacationService implements IVacationService {

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    VacationRepository vacationRepository;

    @Override
    public void createVacation(VacationDto vacationDto) throws EtailBookItException {

        for (Integer attendantId : vacationDto.getAttendantIds()) {
            Vacation vacation = new Vacation();
            Attendant attendant = attendantRepository.findByAttendantId(attendantId);
            if (attendant != null) {
                vacation.setAttendant(attendant);
            } else {
                throw new EtailBookItException("No attendant found with id::" + attendantId);
            }
            OffsetDateTime vacationStartTime=OffsetDateTime.parse(vacationDto.getVacationStartTime());
            OffsetDateTime vacationEndTime=OffsetDateTime.parse(vacationDto.getVacationEndTime());
            vacation.setVacationStartDate(vacationStartTime.toLocalDate());
            vacation.setVacationEndDate(vacationEndTime.toLocalDate());
            vacation.setVacationStartTime(vacationStartTime);
            vacation.setVacationEndTime(vacationEndTime);
            vacation.setRetailer(RetailerContext.getRetailer());
            vacationRepository.save(vacation);
        }
    }

    @Override
    public BaseResponseDto getAllVacationList(OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws EtailBookItException {
        BaseResponseDto responseDto;
        ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(ZoneOffset.UTC.toString()));
        startDate = currentTime.toOffsetDateTime();
        List<VacationProjection> vacationList = vacationRepository.getAllVacations(startDate, endDate, RetailerContext.getRetailer());
        responseDto = new BaseResponseDto<>(Status.SUCCESS, vacationList);
        return responseDto;
    }

    @Override
    public BaseResponseDto updateVacation(Integer vacationId, VacationDto vacationDto) throws EtailBookItException {
        BaseResponseDto responseDto = new BaseResponseDto<>();
        Vacation vacation = new Vacation();
        for (Integer attendantId : vacationDto.getAttendantIds()) {
            Attendant attendant = attendantRepository.findByAttendantId(attendantId);
            if (attendant == null) {
                throw new EtailBookItException("No attendant found with id::" + attendantId);
            } else {
                vacation = vacationRepository.findByIdAttendantAndRetailer(vacationId, attendant, RetailerContext.getRetailer());
                if (vacation != null) {
                    OffsetDateTime vacationStartTime=OffsetDateTime.parse(vacationDto.getVacationStartTime());
                    OffsetDateTime vacationEndTime=OffsetDateTime.parse(vacationDto.getVacationEndTime());
                    vacation.setVacationStartDate(vacationStartTime.toLocalDate());
                    vacation.setVacationEndDate(vacationEndTime.toLocalDate());
                    vacation.setVacationStartTime(vacationStartTime);
                    vacation.setVacationEndTime(vacationStartTime);
                    vacation.setRetailer(RetailerContext.getRetailer());
                    vacation.setAttendant(attendant);
                    vacation = vacationRepository.save(vacation);
                }
            }
        }
        responseDto = new BaseResponseDto<>(Status.SUCCESS, vacation);
        return responseDto;
    }

    @Override
    public BaseResponseDto deleteVacation(Integer vacationId)throws EtailBookItException {
        Optional<Vacation> vacation=vacationRepository.findById(vacationId);
        if(vacation.isPresent()){
            vacationRepository.deleteById(vacationId);
        }
        else{
            throw new EntityNotFoundException("No vacation found with id::"+vacationId);
        }
        return null;
    }
}