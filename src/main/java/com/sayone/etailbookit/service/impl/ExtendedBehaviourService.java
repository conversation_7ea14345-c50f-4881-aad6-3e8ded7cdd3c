package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.ExtendedBehaviourDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IExtendedBehaviourService;
import com.sayone.etailbookit.util.RetailerContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
public class ExtendedBehaviourService implements IExtendedBehaviourService {

    @Autowired
    PersonalityParameterRepository personalityParameterRepository;

    @Autowired
    ThreatReactionRepository threatReactionRepository;

    @Autowired
    BittingHistoryRepository bittingHistoryRepository;

    @Autowired
    UnfriendlyBehaviourTriggerRepository unfriendlyBehaviourTriggerRepository;

    @Autowired
    ConfigurationRepository configurationRepository;

    private static final String EXTENDED_BEHAVIOUR = "extended_behaviour";


    @Override
    @Transactional
    public void updateExtendedBehaviours(ExtendedBehaviourDto extendedBehaviourDto) throws EtailBookItException {
        Configuration configuration = configurationRepository.findByNameAndRetailer(EXTENDED_BEHAVIOUR, RetailerContext.getRetailer());
        Boolean firstTimeConfig = false;
        if (configuration == null) {
            configuration=new Configuration();
            configuration.setName(EXTENDED_BEHAVIOUR);
            firstTimeConfig = true;
        }
        configuration.setRetailer(RetailerContext.getRetailer());
        configuration.setActive(extendedBehaviourDto.isActive());
        if(extendedBehaviourDto.isActive()) {
            List<PersonalityParameter> personalityParameters = extendedBehaviourDto.getPersonalityParameters();
            List<BittingHistory> bittingHistories = extendedBehaviourDto.getBittingHistory();
            List<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers = extendedBehaviourDto.getUnfriendlyBehaviourTriggers();
            List<ThreatReaction> threatReactions = extendedBehaviourDto.getThreatReactions();
            for (PersonalityParameter personalityParameter : personalityParameters) {
                personalityParameter.setRetailer(RetailerContext.getRetailer());
            }
            for (BittingHistory bittingHistory : bittingHistories) {
                bittingHistory.setRetailer(RetailerContext.getRetailer());
            }
            for (UnfriendlyBehaviourTrigger unfriendlyBehaviourTrigger : unfriendlyBehaviourTriggers) {
                unfriendlyBehaviourTrigger.setRetailer(RetailerContext.getRetailer());
            }
            for (ThreatReaction threatReaction : threatReactions) {
                threatReaction.setRetailer(RetailerContext.getRetailer());
            }
            personalityParameterRepository.saveAll(personalityParameters);
            bittingHistoryRepository.saveAll(bittingHistories);
            unfriendlyBehaviourTriggerRepository.saveAll(unfriendlyBehaviourTriggers);
            threatReactionRepository.saveAll(threatReactions);

        }
        configurationRepository.save(configuration);
    }

    @Override
    public ExtendedBehaviourDto getExtendedBehaviours() {
        ExtendedBehaviourDto extendedBehaviourDto = new ExtendedBehaviourDto() ;
        extendedBehaviourDto.setBittingHistory(bittingHistoryRepository.findByRetailer(RetailerContext.getRetailer()));
        extendedBehaviourDto.setPersonalityParameters(personalityParameterRepository.findByRetailer(RetailerContext.getRetailer()));
        extendedBehaviourDto.setThreatReactions(threatReactionRepository.findByRetailer(RetailerContext.getRetailer()));
        extendedBehaviourDto.setUnfriendlyBehaviourTriggers(unfriendlyBehaviourTriggerRepository.findByRetailer(RetailerContext.getRetailer()));
        Configuration configuration = configurationRepository.findByNameAndRetailer(EXTENDED_BEHAVIOUR, RetailerContext.getRetailer());
        if (configuration != null) {
            extendedBehaviourDto.setActive(configuration.isActive());
        }
        return extendedBehaviourDto;
    }

}
