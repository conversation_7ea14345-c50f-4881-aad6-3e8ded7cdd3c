package com.sayone.etailbookit.service.impl;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.AmazonS3URI;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.sayone.etailbookit.controller.WaiverOfLiabilityController;
import com.sayone.etailbookit.service.IS3Integration;
import com.sayone.etailbookit.util.DataFileProperties;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;

@Service
public class S3Integration implements IS3Integration {

 /*   @Value("${aws.access_key_id}")
    private String awsAccessKeyId;

    @Value("${aws.secret_access_key}")
    private String awsSecretAccessKey;*/

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    private DataFileProperties dataFileDetails;

    @Value("${esignservice.tempfolder}")
    private String tempfolder;

    @Value("${aws.s3.region}")
    private  String  awsS3Region;

    private static Logger LOGGER = LoggerFactory.getLogger(WaiverOfLiabilityController.class);
    @Override
    public String downloadFile(String SRC) throws IOException, URISyntaxException {
     URI fileToBeDownloaded = new URI(SRC);
     AmazonS3URI s3URI = new AmazonS3URI(fileToBeDownloaded);

     LOGGER.info ("S3 url bucket name"+s3URI.getBucket());
     LOGGER.info("S3 region"+s3URI.getRegion());

     AmazonS3 s3client= configureS3Client(s3URI.getRegion());
     URL url=new URL(SRC);
     String filename= FilenameUtils.getName(url.getPath());
     String folderPath=dataFileDetails.getPetDocumentPrefix();

     LOGGER.info("Folder Path ::"+folderPath);
     LOGGER.info("Filename::"+FilenameUtils.getName(url.getPath()));

     S3Object s3Object=s3client.getObject(s3URI.getBucket(),folderPath + "/" + filename);
     S3ObjectInputStream objectData = s3Object.getObjectContent();
     LOGGER.info("-----------Downloaded S3 document ------------");
     String temp_outPutFile=tempfolder+"/"+filename;

     FileOutputStream out = new FileOutputStream(temp_outPutFile);
     writeToFile(objectData, out);
     return temp_outPutFile;

 }
 public  String uploadFile(String signedDocumentPath, String fileName) throws FileNotFoundException {
     String fileUrl;
     String awsEndpoint=dataFileProperties.getAwsEndpoint();
     String folderPath=dataFileProperties.getPetDocumentPrefix();
     String bucketName=dataFileProperties.getBucketName();
     AmazonS3 s3client= configureS3Client(awsS3Region);
     //FileOutputStream out = new FileOutputStream(signedDocumentPath);
     File file = new File(signedDocumentPath);
     try {
         fileUrl = awsEndpoint + "/" + folderPath + "/" + fileName;
         s3client.putObject(bucketName, folderPath + "/" + fileName,file);
        // file.delete();
         LOGGER.info("File uploaded successfully");
         return fileUrl;
     }
     catch (Exception e) {
       //  file.delete();
         LOGGER.error("Exception occurred while file upload ::::" + e);
         throw e;
     }
 }
    private AmazonS3 configureS3Client(String awsRegion) {
      //  BasicAWSCredentials basicAWSCredentials = new BasicAWSCredentials(awsAccessKeyId, awsSecretAccessKey);
        AmazonS3 s3Client = AmazonS3ClientBuilder
                .standard()
                .withRegion(Regions.fromName(awsRegion))
             //   .withCredentials(new AWSStaticCredentialsProvider(basicAWSCredentials))
                .build();
        return s3Client;
    }
    private static void writeToFile(InputStream input, FileOutputStream out) throws IOException {
        LOGGER.info("Entered writeToFile");
        try (BufferedInputStream in = new BufferedInputStream(input);) {
            byte[] chunk = new byte[1024];
            while (in.read(chunk) > 0) {
                out.write(chunk);
            }
        } finally {
            input.close();
        }
    }
}
