package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.AttendantMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.AttendantDetailsProjection;
import com.sayone.etailbookit.projections.AttendantListingProjection;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IAttendantService;
import com.sayone.etailbookit.service.IGoogleCalendarService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import com.sayone.etailbookit.validator.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class AttendantService implements IAttendantService {

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    ServiceTypeRepository serviceTypeRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    AddonServiceRepository addonServiceRepository;

    @Autowired
    AttendantPetTypesRepository attendantPetTypesRepository;

    @Autowired
    AttendantAvailabilityRepository attendantAvailabilityRepository;

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    PetSizeConstraintRepository petSizeConstraintRepository;

    @Autowired
    PetSizeLimitRepository petSizeLimitRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    Validator attendantValidator;

    @Autowired
    IGoogleCalendarService googleCalendarService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AttendantService.class);

    @Override
    @Transactional
    public void addAttendant(AttendantDto attendantDto) throws EtailBookItException {
        attendantValidator.validateAttendant(attendantDto, null);
        
        // Validate email is present if Google Calendar sync is enabled
        if (Boolean.TRUE.equals(attendantDto.getGoogleCalendarSyncEnabled())) {
            if (attendantDto.getEmail() == null || attendantDto.getEmail().trim().isEmpty()) {
                throw new BadRequestException("Email is required when Google Calendar sync is enabled");
            }
        }
        
        Set<AttendantPetTypes> attendantPetTypes = new HashSet();
        Set<ServiceType>  serviceTypes = new HashSet<>();
        Set<AddonService> addonServices = new HashSet<>();

        for (Integer serviceTypeId : attendantDto.getServiceTypeIds()) {
            ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
            if (serviceType != null) {
                serviceTypes.add(serviceType);
            } else {
                throw new EntityNotFoundException("Service Type not found");
            }
        }

        if(attendantDto.getAddonServiceIds() != null) {
            for (Integer addonServiceId : attendantDto.getAddonServiceIds()) {
                AddonService addonService = addonServiceRepository.findByAddonServiceId(addonServiceId);
                if (addonService != null) {
                    addonServices.add(addonService);
                } else {
                    throw new EntityNotFoundException("Addon service not found");
                }
            }
        }

        Attendant attendant = AttendantMapper.toAttendantEntity(attendantDto);
        if(attendantDto.getPetTypes() != null) {
            for (PetTypeDto petTypeDto : attendantDto.getPetTypes()) {
                PetType petType = petTypeRepository.findByPetTypeId(petTypeDto.getId());
                if (petType != null) {
                    AttendantPetTypes attendantPetType = new AttendantPetTypes();
                    attendantPetType.setPetType(petType);
                    Set<GeneralPetSize> generalPetSizes = new HashSet();

                    if(petTypeDto.getGeneralPetSizeIds() != null) {
                        for (Integer petSizeId : petTypeDto.getGeneralPetSizeIds()) {
                            GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(petSizeId);
                            if (generalPetSize != null) {
                                generalPetSizes.add(generalPetSize);
                            } else {
                                throw new EntityNotFoundException("General Pet Size not found with ID :::" + petSizeId);
                            }
                        }
                    }

                    if(petTypeDto.getTemperamentIds() != null) {
                        Set<Temperament> temperaments = new HashSet();
                        for (Integer temperamentId : petTypeDto.getTemperamentIds()) {
                            Temperament temperament = temperamentRepository.findByTemperamentId(temperamentId);
                            if (temperament != null) {
                                temperaments.add(temperament);
                            } else {
                                throw new EntityNotFoundException("Temperament not found with ID :::" + temperamentId);
                            }
                        }
                        attendantPetType.setTemperaments(temperaments);
                    }

                    attendantPetType.setGeneralPetSizes(generalPetSizes);
                    attendantPetType.setAttendant(attendant);
                    attendantPetType.setRetailer(RetailerContext.getRetailer());
                    attendantPetTypes.add(attendantPetType);
                } else {
                    throw new EntityNotFoundException("Pet Type not found with ID :::" + petTypeDto.getId());
                }
            }
        }

        if (Utils.isNotEmpty(attendantDto.getPetSizeConstraints())) {
            Set<PetSizeConstraint> petSizeConstraints = new HashSet();
            for (PetSizeConstraintDto petSizeConstraintDto: attendantDto.getPetSizeConstraints()) {
                PetSizeConstraint petSizeConstraint = new PetSizeConstraint();
                petSizeConstraint.setAttendant(attendant);
                petSizeConstraint.setPetType(petTypeRepository.findByPetTypeId(petSizeConstraintDto.getPetTypeId()));
                petSizeConstraint.setCapacity(petSizeConstraintDto.getCapacity());
                petSizeConstraint.setGeneralPetSize(generalPetSizeRepository.findByGeneralPetSizeId(petSizeConstraintDto.getGeneralPetSizeId()));
                petSizeConstraint.setRetailer(RetailerContext.getRetailer());

                petSizeConstraints.add(petSizeConstraint);
            }
            attendant.setPetSizeConstraints(petSizeConstraints);
        }
        if (Utils.isNotEmpty(attendantDto.getPetSizeLimits())) {
            Set<PetSizeLimit> petSizeLimits = new HashSet();
            for (PetSizeLimitDto petSizeLimitDto: attendantDto.getPetSizeLimits()) {
                PetSizeLimit petSizeLimit = new PetSizeLimit();
                petSizeLimit.setAttendant(attendant);
                petSizeLimit.setPetType(petTypeRepository.findByPetTypeId(petSizeLimitDto.getPetTypeId()));
                petSizeLimit.setCapacity(petSizeLimitDto.getCapacity());
                petSizeLimit.setGeneralPetSize(generalPetSizeRepository.findByGeneralPetSizeId(petSizeLimitDto.getGeneralPetSizeId()));
                petSizeLimit.setRetailer(RetailerContext.getRetailer());

                petSizeLimits.add(petSizeLimit);
            }
            attendant.setPetSizeLimits(petSizeLimits);
        }

        Venue venue = venueRepository.findByVenueId(attendantDto.getVenue().getId());
        if(venue != null) {
            attendant.setVenue(venue);
        }
        else {
            throw new BadRequestException("Invalid venue selected for attendant");
        }

        attendant.setAddonServices(addonServices);
        attendant.setServiceTypes(serviceTypes);
        attendant.setAttendantPetTypes(attendantPetTypes);
        Attendant savedAttendant = attendantRepository.save(attendant);
        
        // Auto-send Google Calendar invitation if enabled
        if (Boolean.TRUE.equals(savedAttendant.getGoogleCalendarSyncEnabled())) {
            try {
                googleCalendarService.sendCalendarSyncInvitation(savedAttendant.getAttendantId());
            } catch (Exception e) {
                LOGGER.warn("Failed to send Google Calendar invitation for attendant {}: {}", 
                    savedAttendant.getAttendantId(), e.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public AttendantDto updateAttendant(AttendantDto attendantDto, Integer attendantId) throws EtailBookItException {
        String preservedGoogleRefreshToken = null;
        String preservedGoogleAccessToken = null;
        LocalDateTime preservedGoogleTokenExpiry = null;
        String preservedGoogleCalendarId = null;
        Boolean preservedCalendarSyncInProgress = null;
        Boolean preservedGoogleCalendarAuthorized = null;
        Boolean previousGoogleCalendarSyncEnabled = null;
        Boolean previousGoogleCalendarAuthorized = null;
        
        if (attendantId != null) {
            Attendant attendant = attendantRepository.findByAttendantId(attendantId);
            if (attendant == null) {
                throw new EntityNotFoundException("Attendant not found");
            }
            if (attendant.getFirstName().equalsIgnoreCase("Rachel Green DEV")){
                throw new EtailBookItException("This is test data and can not be updated");
            }
            // Preserve ALL Google Calendar related fields
            preservedGoogleRefreshToken = attendant.getGoogleRefreshToken();
            preservedGoogleAccessToken = attendant.getGoogleAccessToken();
            preservedGoogleTokenExpiry = attendant.getGoogleTokenExpiry();
            preservedGoogleCalendarId = attendant.getGoogleCalendarId();
            preservedCalendarSyncInProgress = attendant.getCalendarSyncInProgress();
            preservedGoogleCalendarAuthorized = attendant.getGoogleCalendarAuthorized();
            
            // Track previous values for change detection
            previousGoogleCalendarSyncEnabled = attendant.getGoogleCalendarSyncEnabled();
            previousGoogleCalendarAuthorized = attendant.getGoogleCalendarAuthorized();
        }
        
        attendantValidator.validateAttendant(attendantDto, attendantId);
        
        // Validate email is present if Google Calendar sync is enabled
        if (Boolean.TRUE.equals(attendantDto.getGoogleCalendarSyncEnabled())) {
            if (attendantDto.getEmail() == null || attendantDto.getEmail().trim().isEmpty()) {
                throw new BadRequestException("Email is required when Google Calendar sync is enabled");
            }
        }
        
        Set<AttendantPetTypes> attendantPetTypes = new HashSet();
        Set<ServiceType> serviceTypes = new HashSet<>();
        Set<AddonService> addonServices = new HashSet<>();
        for (Integer serviceTypeId : attendantDto.getServiceTypeIds()) {
            ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
            if (serviceType != null) {
                serviceTypes.add(serviceType);
            } else {
                throw new EntityNotFoundException("Service Type not found");
            }
        }

        if(attendantDto.getAddonServiceIds() != null) {
            for (Integer addonServiceId : attendantDto.getAddonServiceIds()) {
                AddonService addonService = addonServiceRepository.findByAddonServiceId(addonServiceId);
                if (addonService != null) {
                    addonServices.add(addonService);
                } else {
                    throw new EntityNotFoundException("Addon service not found");
                }
            }
        }

        Attendant attendant = AttendantMapper.toAttendantEntity(attendantDto);
        
        // Restore ALL Google Calendar related fields (OAuth flow/service is the only way to set these)
        attendant.setGoogleRefreshToken(preservedGoogleRefreshToken);
        attendant.setGoogleAccessToken(preservedGoogleAccessToken);
        attendant.setGoogleTokenExpiry(preservedGoogleTokenExpiry);
        attendant.setGoogleCalendarId(preservedGoogleCalendarId);
        attendant.setCalendarSyncInProgress(preservedCalendarSyncInProgress);
        attendant.setGoogleCalendarAuthorized(preservedGoogleCalendarAuthorized);
        for (PetTypeDto petTypeDto : attendantDto.getPetTypes()) {
            PetType petType = petTypeRepository.findByPetTypeId(petTypeDto.getId());
            if (petType != null) {
                AttendantPetTypes attendantPetType = new AttendantPetTypes();
                attendantPetType.setPetType(petType);
                Set<GeneralPetSize> generalPetSizes = new HashSet();
                Set<Temperament> temperaments = new HashSet();

                if(petTypeDto.getGeneralPetSizeIds() != null) {
                    for (Integer petSizeId : petTypeDto.getGeneralPetSizeIds()) {
                        GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(petSizeId);
                        if (generalPetSize != null) {
                            generalPetSizes.add(generalPetSize);
                        } else {
                            throw new EntityNotFoundException("General Pet Size not found with ID ::: " + petSizeId);
                        }
                    }
                }
                if(petTypeDto.getTemperamentIds()!=null) {
                    //throw new BadRequestException("No pet temperament selected for pet type:::"+petType.getName());

                    for (Integer temperamentId : petTypeDto.getTemperamentIds()) {
                        Temperament temperament = temperamentRepository.findByTemperamentId(temperamentId);
                        if (temperament != null) {
                            temperaments.add(temperament);
                        } else {
                            throw new EntityNotFoundException("Temperament not found with ID ::: " + temperamentId);
                        }
                    }
                    attendantPetType.setTemperaments(temperaments);
                }
                attendantPetType.setGeneralPetSizes(generalPetSizes);

                attendantPetType.setAttendant(attendant);
                attendantPetType.setRetailer(RetailerContext.getRetailer());
                attendantPetTypes.add(attendantPetType);
            } else {
                throw new EntityNotFoundException("Pet Type not found with ID ::: " + petTypeDto.getId());
            }
        }

        Venue venue = venueRepository.findByVenueId(attendantDto.getVenue().getId());
        if(venue != null) {
            attendant.setVenue(venue);
        }
        else {
            throw new BadRequestException("Invalid venue selected for attendant");
        }


        attendant.setServiceTypes(serviceTypes);
        attendant.setAttendantPetTypes(attendantPetTypes);
        attendant.setAddonServices(addonServices);
        attendant.setAttendantId(attendantId);
        if (Utils.isNotEmpty(attendantDto.getPetSizeConstraints())) {
            Set<PetSizeConstraint> petSizeConstraints = new HashSet();
            for (PetSizeConstraintDto petSizeConstraintDto: attendantDto.getPetSizeConstraints()) {
                PetSizeConstraint petSizeConstraint = new PetSizeConstraint();
                if (petSizeConstraintDto.getId() != null) petSizeConstraint.setId(petSizeConstraintDto.getId());
                petSizeConstraint.setAttendant(attendant);
                petSizeConstraint.setPetType(petTypeRepository.findByPetTypeId(petSizeConstraintDto.getPetTypeId()));
                petSizeConstraint.setCapacity(petSizeConstraintDto.getCapacity());
                petSizeConstraint.setGeneralPetSize(generalPetSizeRepository.findByGeneralPetSizeId(petSizeConstraintDto.getGeneralPetSizeId()));
                petSizeConstraint.setRetailer(RetailerContext.getRetailer());

                petSizeConstraints.add(petSizeConstraint);
            }
            attendant.setPetSizeConstraints(petSizeConstraints);
        }
        if (attendant.getPetSizeConstraints() != null) {
            if (attendant.getAttendantId() != null) {
                List<Integer> validIds = new ArrayList<>();
                attendant.getPetSizeConstraints().forEach(value -> {
                    if (value.getId() != null) {
                        validIds.add(value.getId());
                    }
                });
                if (validIds.size() == 0) {
                    petSizeConstraintRepository.deleteAllByAttendantAttendantId(attendant.getAttendantId());
                } else {
                    petSizeConstraintRepository.deleteAllByIdNotInAndAttendantAttendantId(validIds, attendant.getAttendantId());
                }
            }
        } else if (attendant.getAttendantId() != null) {
            petSizeConstraintRepository.deleteAllByAttendantAttendantId(attendant.getAttendantId());
        }
        if (Utils.isNotEmpty(attendantDto.getPetSizeLimits())) {
            Set<PetSizeLimit> petSizeLimits = new HashSet();
            for (PetSizeLimitDto petSizeLimitDto: attendantDto.getPetSizeLimits()) {
                PetSizeLimit petSizeLimit = new PetSizeLimit();
                if (petSizeLimitDto.getId() != null) petSizeLimit.setId(petSizeLimitDto.getId());
                petSizeLimit.setAttendant(attendant);
                petSizeLimit.setPetType(petTypeRepository.findByPetTypeId(petSizeLimitDto.getPetTypeId()));
                petSizeLimit.setCapacity(petSizeLimitDto.getCapacity());
                petSizeLimit.setGeneralPetSize(generalPetSizeRepository.findByGeneralPetSizeId(petSizeLimitDto.getGeneralPetSizeId()));
                petSizeLimit.setRetailer(RetailerContext.getRetailer());

                petSizeLimits.add(petSizeLimit);
            }
            attendant.setPetSizeLimits(petSizeLimits);
        }
        if (attendant.getPetSizeLimits() != null) {
            if (attendant.getAttendantId() != null) {
                List<Integer> validIds = new ArrayList<>();
                attendant.getPetSizeLimits().forEach(value -> {
                    if (value.getId() != null) {
                        validIds.add(value.getId());
                    }
                });
                if (validIds.size() == 0) {
                    petSizeLimitRepository.deleteAllByAttendantAttendantId(attendant.getAttendantId());
                } else {
                    petSizeLimitRepository.deleteAllByIdNotInAndAttendantAttendantId(validIds, attendant.getAttendantId());
                }
            }
        } else if (attendant.getAttendantId() != null) {
            petSizeLimitRepository.deleteAllByAttendantAttendantId(attendant.getAttendantId());
        }
        if(Utils.isNotEmpty(attendantDto.getAvailabilityDays()))
            attendantAvailabilityRepository.deleteByAttendant(attendant);
        attendantPetTypesRepository.deleteByAttendant(attendant);
        Attendant savedAttendant = attendantRepository.save(attendant);
        
        // Handle Google Calendar sync flag changes
        Boolean newGoogleCalendarSyncEnabled = savedAttendant.getGoogleCalendarSyncEnabled();
        
        // Case 1: Enabled changed from false to true → Send invitation
        if (!Boolean.TRUE.equals(previousGoogleCalendarSyncEnabled) && Boolean.TRUE.equals(newGoogleCalendarSyncEnabled)) {
            try {
                googleCalendarService.sendCalendarSyncInvitation(savedAttendant.getAttendantId());
            } catch (Exception e) {
                LOGGER.warn("Failed to send Google Calendar invitation for attendant {}: {}", savedAttendant.getAttendantId(), e.getMessage());
            }
        }
        // Case 2: Enabled stays true but NOT authorized → Re-send invitation
        else if (Boolean.TRUE.equals(previousGoogleCalendarSyncEnabled) && Boolean.TRUE.equals(newGoogleCalendarSyncEnabled) &&
                 !Boolean.TRUE.equals(previousGoogleCalendarAuthorized) && preservedGoogleRefreshToken == null) {
            try {
                googleCalendarService.sendCalendarSyncInvitation(savedAttendant.getAttendantId());
            } catch (Exception e) {
                LOGGER.warn("Failed to send Google Calendar invitation for attendant {}: {}", 
                    savedAttendant.getAttendantId(), e.getMessage());
            }
        }
        // Case 3: Enabled changed from true to false AND has authorization → Clear credentials
        else if (Boolean.TRUE.equals(previousGoogleCalendarSyncEnabled) && !Boolean.TRUE.equals(newGoogleCalendarSyncEnabled) &&
                 (Boolean.TRUE.equals(previousGoogleCalendarAuthorized) || preservedGoogleRefreshToken != null)) {
            LOGGER.info("Google Calendar sync disabled for attendant {}, clearing credentials", savedAttendant.getAttendantId());
            
            try {
                // Call invalidateGoogleCalendarSync which deletes calendar, revokes token, and clears DB
                // It returns the updated attendant to avoid lazy loading issues
                savedAttendant = googleCalendarService.invalidateGoogleCalendarSync(savedAttendant.getAttendantId());
            } catch (Exception e) {
                LOGGER.warn("Failed to invalidate Google Calendar sync for attendant {}: {}", 
                    savedAttendant.getAttendantId(), e.getMessage());
            }
        }
        return AttendantMapper.toAttendantDto(savedAttendant);
    }

    @Override
    @Transactional
    public void deleteAttendant(int attendantId) throws EtailBookItException {
        Attendant attendant = attendantRepository.findByAttendantId(attendantId);
        if (attendant == null) {
            throw new EntityNotFoundException("Attendant Id not found");
        }
        if(attendant.getFirstName().equalsIgnoreCase("Rachel Green DEV")){
            throw new EtailBookItException("This is test data and can not be deleted");
        }
        if (Utils.isNotEmpty(attendant.getServices())) {
            throw new BadRequestException("Attendant is currently used by Services");
        } /*else {
            attendantPetTypesRepository.deleteByAttendant(attendant);
            attendantRepository.delete(attendant);
        }*/
        attendant.setDeleted(Boolean.TRUE);
        attendantRepository.save(attendant);
    }

    @Override
    public AttendantDto getAttendantById(int attendantId) throws EtailBookItException {
        attendantValidator.validateAttendant(null, attendantId);
        AttendantDetailsProjection attendant = attendantRepository.getAttendantDetails(attendantId);
        return AttendantMapper.toAttendantDto(attendant);
    }

    @Override
    public List<AttendantListingProjection> getAttendants() {
        return attendantRepository.findByRetailerOrderByAttendantIdDesc(RetailerContext.getRetailer());
    }

    @Override
    public Page<AttendantListingProjection> getAttendantsByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> keyword) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        if (sortBy.equals("firstName")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<AttendantListingProjection> attendants ;
        if(keyword.isPresent() && keyword.hashCode() != 0){
            attendants = attendantRepository.findByKeyword(keyword.get().toLowerCase(),RetailerContext.getRetailer(),false,paging);
        } else{
            attendants = attendantRepository.findByRetailerAndDeleted(RetailerContext.getRetailer(),false,paging);
        }
        return attendants;
    }

    @Override
    public List<AttendantDto> getAttendantsForService(SearchDto searchDto) throws EntityNotFoundException {
        List<Attendant> attendants = attendantRepository.findByRetailer(RetailerContext.getRetailer());
        List<ServiceType> serviceTypes = new ArrayList<>();

        // ✅ Skip deleted ServiceTypes
        for (Integer serviceId : searchDto.getServiceTypeIds()) {
            if(serviceId==null)continue;
            ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceId);
            if (serviceType != null) {
                serviceTypes.add(serviceType);
            }
        }

        Set<AttendantPetTypes> attendantPetTypesSet = new HashSet<>();
        HashMap<Integer, Set<GeneralPetSize>> gPT = new HashMap<>();
        HashMap<Integer, Set<Temperament>> petTemperamentMap = new HashMap<>();

        // ✅ Skip deleted PetTypes
        for (SearchPetTypeDto attendantPetType : searchDto.getPetTypes()) {
            if(attendantPetType.getId()==null)continue;
            PetType petType = petTypeRepository.findByPetTypeId(attendantPetType.getId());
            if (petType == null) {
                continue; // skip deleted PetType
            }

            // General Pet Sizes
            Set<GeneralPetSize> generalPetSizes = new HashSet<>();
            for (Integer generalPetSizeId : attendantPetType.getGeneralPetSize()) {
                GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeId);
                if (generalPetSize == null) {
                    throw new EntityNotFoundException("General pet size not found");
                }
                generalPetSizes.add(generalPetSize);
            }
            gPT.put(petType.getPetTypeId(), generalPetSizes);

            // Temperaments
            Set<Temperament> petTemperament = new HashSet<>();
            for (Integer petTemperamentId : attendantPetType.getTemperamentIds()) {
                Temperament temperament = temperamentRepository.findByTemperamentId(petTemperamentId);
                if (temperament == null) {
                    throw new EntityNotFoundException("Temperament not found");
                }
                petTemperament.add(temperament);
            }
            petTemperamentMap.put(petType.getPetTypeId(), petTemperament);

            // Link attendantPetTypes for valid PetType
            attendantPetTypesSet.addAll(attendantPetTypesRepository.findByPetType(petType.getPetTypeId()));
        }

        // Group AttendantPetTypes by Attendant
        HashMap<Integer, Set<AttendantPetTypes>> aPT = new HashMap<>();
        attendantPetTypesSet.forEach(pt -> {
            aPT.computeIfAbsent(pt.getAttendant().getAttendantId(), k -> new HashSet<>()).add(pt);
        });

        // Only keep attendants where all requested PetTypes are present
        HashMap<Integer, Set<AttendantPetTypes>> aPTFiltered = new HashMap<>();
        aPT.forEach((k, v) -> {
            if (v.size() >= searchDto.getPetTypes().size()) {
                aPTFiltered.put(k, v);
            }
        });

        // Final filtering
        List<Attendant> filteredAttendants = new ArrayList<>();
        attendants.forEach(a -> {
            if (aPTFiltered.containsKey(a.getAttendantId())) {
                Set<ServiceType> serviceTypes1 = a.getServiceTypes();
                Set<AttendantPetTypes> attendantPetTypes1 = a.getAttendantPetTypes();
                Set<AttendantPetTypes> attendantPetTypes2 = aPTFiltered.get(a.getAttendantId());

                if (
                        serviceTypes1.containsAll(serviceTypes) &&
                                attendantPetTypes1.containsAll(attendantPetTypes2) &&
                                attendantPetTypes2.stream().allMatch(e ->
                                        gPT.containsKey(e.getPetType().getPetTypeId()) &&
                                                e.getGeneralPetSizes().containsAll(gPT.get(e.getPetType().getPetTypeId()))
                                ) &&
                                attendantPetTypes2.stream().allMatch(e ->
                                        petTemperamentMap.containsKey(e.getPetType().getPetTypeId()) &&
                                                e.getTemperaments().containsAll(petTemperamentMap.get(e.getPetType().getPetTypeId()))
                                )
                ) {
                    filteredAttendants.add(a);
                }
            }
        });

        return AttendantMapper.toAttendantDtoList(filteredAttendants);
    }


    @Override
    public List<AttendantListingProjection> getActiveAttendants() throws EntityNotFoundException {
        List<AttendantListingProjection> attendants = attendantRepository.findByActiveAndRetailer(true, RetailerContext.getRetailer());
        if(attendants.size()>0)
            return attendants;
        throw new EntityNotFoundException("No Active Attendants" );


    }

    @Override
    public List<AttendantDto> getAttendantsByVenue(Integer venueId,Integer serviceId) throws EtailBookItException {
        List<Attendant> attendantList = new ArrayList<>();
        List<Attendant> attendants = attendantRepository.findByVenueId(venueId, RetailerContext.getRetailer(), false);
        if (attendants != null) {
            com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
            if (service != null) {
                for (Attendant attendant : attendants) {
                    if (attendant.getServices().contains(service)) {
                        attendantList.add(attendant);
                    }
                }
            }
            if (attendantList != null) {
                return AttendantMapper.toAttendantDtoList(attendantList);
            } else {
                throw new EntityNotFoundException(" No attendants configured for this venue for this service");
            }

        }else{
            throw new EntityNotFoundException(" No attendants configured for this venue");
        }
    }

    @Override
    public List<AttendantInfo> fetchAttendantIdAndName(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search, String retailer) throws EtailBookItException{
        List<AttendantInfo> attendantInfoList=new ArrayList<>();
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        if (sortBy.equals("firstName")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<AttendantListingProjection> attendants ;
        if(search.isPresent() && search.hashCode() != 0){
            attendants = attendantRepository.findByKeyword(search.get().toLowerCase(),RetailerContext.getRetailer(),false,paging);
        } else{
            attendants = attendantRepository.findByRetailerAndDeleted(RetailerContext.getRetailer(),false,paging);
        }
        List<AttendantListingProjection> attendantListingProjections=attendants.getContent();
        for(AttendantListingProjection attendantListingProjection:attendantListingProjections){
            AttendantInfo attendantInfo=new AttendantInfo();
            String firstName=attendantListingProjection.getFirstName();
            String lastName=attendantListingProjection.getLastName();
            attendantInfo.setAttendantName(firstName+""+lastName);
            attendantInfo.setAttendantId(attendantListingProjection.getAttendantId());
            attendantInfoList.add(attendantInfo);
        }
        return attendantInfoList;
    }

    @Override
    public List<AttendantDto> filterAttendantsByVenue(Integer venueId, String retailer) throws EntityNotFoundException {
        List<Attendant> attendantList = new ArrayList<>();
        List<Attendant> attendants = attendantRepository.findByVenueId(venueId, RetailerContext.getRetailer(), false);
            if (attendants != null) {
                return AttendantMapper.toAttendantDtoList(attendantList);
            } else {
                throw new EntityNotFoundException(" No attendants configured for this venue");
            }

        }


}
