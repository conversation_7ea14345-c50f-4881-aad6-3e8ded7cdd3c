package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.PetTypeMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IPetTypeService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sayone.etailbookit.mapper.PetTypeMapper.toPetTypeConfigurationEntity;

@Service
public class PetTypeService implements IPetTypeService {

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    HairLengthRepository hairLengthRepository;

    @Autowired
    BreedRepository breedRepository;

    @Autowired
    HairTextureRepository hairTextureRepository;

    @Autowired
    CombsRepository combsRepository;

    @Autowired
    BladesRepository bladesRepository;

    @Autowired
    VetInformationRepository vetInformationRepository;

    @Autowired
    AllergiesRepository allergiesRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    DesiredHairLengthRepository desiredHairLengthRepository;

    @Autowired
    VaccinationRecordsRepository vaccinationRecordsRepository;

    @Autowired
    PetTypeConfigurationRepository petTypeConfigurationRepository;

    @Autowired
    EmergencyContactInfoRepository emergencyContactInfoRepository;

    @Autowired
    DocumentOptionRepository documentOptionRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    PetVaccinationRecordRepository petVaccinationRecordRepository;

    @Autowired
    AppointmentVaccinationInformationRepository appointmentVaccinationInformationRepository;

    @Autowired
    AppointmentDesiredHairlengthRepository appointmentDesiredHairlengthRepository;

    @Autowired
    AppointmentEmergencyContactInfoRpository appointmentEmergencyContactInfoRpository;

    @Autowired
    AppointmentDocumentRepository appointmentDocumentRepository;

    @Autowired
    AppointmentVetInfoRepository appointmentVetInfoRepository;

    @Autowired
    PetEmergencyContactRepository petEmergencyContactRepository;

    @Autowired
    PetDocumentRepository petDocumentRepository;

    @Autowired
    PetVetInformationRepository petVetInformationRepository;

    @Autowired
    VenuePetTypesRepository venuePetTypesRepository;

    @Autowired
    AttendantPetTypesRepository attendantPetTypesRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    PetBreedsInformationRepository petBreedsInformationRepository;

    @Autowired
    private DataFileProperties dataFileDetails;

    @Autowired
    private AmazonS3 s3client;

    @Autowired
    PetSizeConstraintRepository petSizeConstraintRepository;

    @Autowired
    PetSizeLimitRepository petSizeLimitRepository;

    @Override
    public void addPetType(PetTypeDto petTypeDto, MultipartFile multipartFile) throws EtailBookItException, IOException,Exception {
        Validator.validatePetType(petTypeDto, null, petTypeRepository);
        //commented out this code to temporarily to get rid of the file not found exception. Need to fix this and uncomment the code
        /*if(multipartFile != null)
            petTypeDto.setFileUrl( FileUploadUtil.uploadFile(multipartFile,dataFileDetails.getBucketName(),s3client,dataFileDetails.getAwsEndpoint(),dataFileDetails.getPetTypePrefix(),FileType.Photos));
        else
            throw new EntityNotFoundException("Pet_Type Image not found ");*/
        PetType petType = PetTypeMapper.toPetTypeEntity(petTypeDto,hairLengthRepository,hairTextureRepository,desiredHairLengthRepository,allergiesRepository,combsRepository,bladesRepository,documentOptionRepository,temperamentRepository,vaccinationRecordsRepository,vetInformationRepository,emergencyContactInfoRepository,breedRepository);
        petTypeRepository.save(petType);
    }

    @Override
    @Transactional
    public void updatePetType(PetTypeDto petTypeDto, int petTypeId,Optional<MultipartFile> multipartFile) throws Exception {
        Validator.validatePetType(petTypeDto, petTypeId, petTypeRepository);
        PetType existingValue = petTypeRepository.findByPetTypeIdAndDeleted(petTypeId,false);
        if (existingValue == null) {
            throw new BadRequestException("Pet Type with id ::" + petTypeId + " not found");
        }
        if(existingValue.getName().equalsIgnoreCase("Dog DEV")){
            throw new EtailBookItException("This is test data and can not be updated");
        }
        PetType petType = new PetType();
        petType.setPetTypeId(petTypeId);
        if (petTypeDto != null) {
            Set<PetTypeConfiguration> petTypeConfigurations = new HashSet<>();
            if (petTypeDto.getBreeds() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.BREEDS, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getBreeds().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                else if (petTypeDto.getBreeds() != null) {
                    petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.BREEDS, petTypeDto.getBreeds().getDisplayType(), petType));
                }
                List<BreedDto> breedDtos = petTypeDto.getBreeds().getOptions();
                if (Utils.isNotEmpty(breedDtos)) {
                    breedRepository.saveAll(PetTypeMapper.toBreedEntityList(breedDtos, petType,breedRepository));
                }
                if(Utils.isNotEmpty(petTypeDto.getBreeds().getRemovedIds())){
                    List<Breed> removeBreeds = new ArrayList<>();
                    for (Integer breedId:petTypeDto.getBreeds().getRemovedIds()) {
                        Optional<Breed> optionalBreed = breedRepository.findById(breedId);
                        if(optionalBreed.isPresent()) {
                            removeBreeds.add(optionalBreed.get());
                        }
                       /* petBreedsInformationRepository.updatePetIdByBreed(breedId);
                        //breedRepository.updatePetTypeIdByBreedId(breedId);
                        petBreedsInformationRepository.updateBreedId(breedId);
                        petBreedsInformationRepository.deleteByBreedId(breedId);
                        breedRepository.updateDeletedByBreedId(breedId,true);*/
                    }
                    breedRepository.deleteAll(removeBreeds);
                }
                if(petTypeDto.getBreeds().getOptions().isEmpty()){
                    if(petTypeDto.getBreeds().getRemovedIds() != null && petTypeDto.getBreeds().getDisplayType() == DisplayType.OPTION && existingValue.getActive() == petTypeDto.getActive()){
                        throw new BadRequestException("Required at least one breed.");
                    }
                }

            }
            if (!StringUtils.isEmpty(petTypeDto.getName())) {
                petType.setName(petTypeDto.getName());
                petType.setActive(petTypeDto.getActive());
                petType.setRetailer(RetailerContext.getRetailer());
                if (multipartFile.isPresent() && multipartFile.get().getSize() != 0) {
                    petType.setFileUrl( FileUploadUtil.uploadFile(multipartFile.get(),dataFileDetails.getBucketName(),s3client,dataFileDetails.getAwsEndpoint(),dataFileDetails.getPetTypePrefix(),FileType.Photos));
                    FileUploadUtil.deleteFile(existingValue.getFileUrl(),s3client,dataFileDetails.getBucketName(),dataFileDetails.getPetTypePrefix());
                }
            else {
                    petType.setFileUrl(existingValue.getFileUrl());
                }
                petTypeRepository.save(petType);
            }
            if (petTypeDto.getHairLengths() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.HAIR_LENGTH, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getHairLengths().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<HairLengthDto> hairLengths = petTypeDto.getHairLengths().getOptions();
                if (Utils.isNotEmpty(hairLengths)) {
                    hairLengthRepository.saveAll(PetTypeMapper.toHairLengthEntityList(hairLengths, petType, hairLengthRepository));
                }

                if(Utils.isNotEmpty(petTypeDto.getHairLengths().getRemovedIds())){
                    List<HairLength> removeHairLengths=new ArrayList<>();
                    for (Integer hairLengthId:petTypeDto.getHairLengths().getRemovedIds()) {
                        Optional<HairLength> hairLength = hairLengthRepository.findById(hairLengthId);
                        if(hairLength.isPresent())
                            removeHairLengths.add(hairLength.get());
                        petRepository.updateByHairLength(hairLengthId);
                        appointmentRepository.updateByHairLength(hairLengthId);
                    }
                    hairLengthRepository.deleteAll(removeHairLengths);
                }
            }

            if (petTypeDto.getHairTextures() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.HAIR_TEXTURE, petType, RetailerContext.getRetailer());
               if(petTypeConfiguration!=null) {
                   petTypeConfiguration.setDisplayType(petTypeDto.getHairTextures().getDisplayType());
                   petTypeConfigurations.add(petTypeConfiguration);
               }
                List<HairTextureDto> hairTextures = petTypeDto.getHairTextures().getOptions();
                if (Utils.isNotEmpty(hairTextures)) {
                    hairTextureRepository.saveAll(PetTypeMapper.toHairTextureEntityList(hairTextures, petType, hairTextureRepository));
                }

                if(Utils.isNotEmpty(petTypeDto.getHairTextures().getRemovedIds())){
                    List<HairTexture> removeHairTextures=new ArrayList<>();
                    for (Integer hairTextureId:petTypeDto.getHairTextures().getRemovedIds()) {
                        Optional<HairTexture> hairTexture = hairTextureRepository.findById(hairTextureId);
                        if(hairTexture.isPresent())
                            removeHairTextures.add(hairTexture.get());
                        petRepository.updateByHairTexture(hairTextureId);
                        appointmentRepository.updateByHairTexture(hairTextureId);
                    }
                    hairTextureRepository.deleteAll(removeHairTextures);
                }
            }

            if (petTypeDto.getCombsBlades() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.COMBS_BLADES, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getCombsBlades().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<CombsDto> combs = petTypeDto.getCombsBlades().getCombs();
                List<BladesDto> blades = petTypeDto.getCombsBlades().getBlades();

                if (Utils.isNotEmpty(combs)) {
                    combsRepository.saveAll(PetTypeMapper.toCombsEntityList(combs, petType,combsRepository));
                }

                if(Utils.isNotEmpty(petTypeDto.getCombsBlades().getRemoveCombIds())){
                    List<Combs> removeCombs=new ArrayList<>();
                    for (Integer combId:petTypeDto.getCombsBlades().getRemoveCombIds()) {
                        Optional<Combs> comb = combsRepository.findById(combId);
                        if(comb.isPresent())
                            removeCombs.add(comb.get());
                    }
                    combsRepository.deleteAll(removeCombs);
                }

                if (Utils.isNotEmpty(blades)) {
                    bladesRepository.saveAll(PetTypeMapper.toBladesEntityList(blades, petType, bladesRepository));
                }

                if(Utils.isNotEmpty(petTypeDto.getCombsBlades().getRemoveBladeIds())){
                    List<Blades> removeBlades=new ArrayList<>();
                    for (Integer bladeId:petTypeDto.getCombsBlades().getRemoveBladeIds()) {
                        Optional<Blades> blade = bladesRepository.findById(bladeId);
                        if(blade.isPresent())
                            removeBlades.add(blade.get());
                    }
                    bladesRepository.deleteAll(removeBlades);
                }
            }

            if (petTypeDto.getVetInformation() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.VET_INFO, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getVetInformation().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<VetInformationDto> vetInformation = petTypeDto.getVetInformation().getOptions();
                if (Utils.isNotEmpty(vetInformation)) {
                    vetInformationRepository.saveAll(PetTypeMapper.toVetInformationEntityList(vetInformation, petType, vetInformationRepository));
                }

                if(Utils.isNotEmpty(petTypeDto.getVetInformation().getRemovedIds())){
                    List<VetInformation> removeVetInformations=new ArrayList<>();
                    for (Integer vetInformationId:petTypeDto.getVetInformation().getRemovedIds()) {
                        Optional<VetInformation> vetInfo = vetInformationRepository.findById(vetInformationId);
                        if(vetInfo.isPresent()){
                            removeVetInformations.add(vetInfo.get());
                        }
                        appointmentVetInfoRepository.deleteByVetInformation(vetInformationId);
                        petVetInformationRepository.deleteByVetInformation(vetInformationId);
                    }
                    vetInformationRepository.deleteAll(removeVetInformations);
                }
            }

            if (petTypeDto.getAllergies() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.ALLERGIES, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getAllergies().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<AllergiesDto> allergies = petTypeDto.getAllergies().getOptions();
                if (Utils.isNotEmpty(allergies)) {
                    allergiesRepository.saveAll(PetTypeMapper.toAllergiesEntityList(allergies, petType,allergiesRepository));
                }
                if(Utils.isNotEmpty(petTypeDto.getAllergies().getRemovedIds())){
                    List<Allergies> removeAllergies=new ArrayList<>();
                    for (Integer allergyId:petTypeDto.getAllergies().getRemovedIds()) {
                        Allergies allergy = new Allergies();
                        allergy.setAllergyId(allergyId);
                        removeAllergies.add(allergy);
                    }
                    allergiesRepository.deleteAll(removeAllergies);
                }
            }

            if (petTypeDto.getTemperaments() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.TEMPERAMENT, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getTemperaments().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<TemperamentDto> temperaments = petTypeDto.getTemperaments().getOptions();
                if (Utils.isNotEmpty(temperaments)) {
                    temperamentRepository.saveAll(PetTypeMapper.toTemperamentEntityList(temperaments, petType,temperamentRepository));
                }
                if(Utils.isNotEmpty(petTypeDto.getTemperaments().getRemovedIds())){
                    List<Temperament> removeTemperaments=new ArrayList<>();
                    for (Integer temperamentId:petTypeDto.getTemperaments().getRemovedIds()) {
                        Temperament temperament = temperamentRepository.findByTemperamentId(temperamentId);
                        if(temperament != null){
                            removeTemperaments.add(temperament);
                        }
                        venuePetTypesRepository.deleteByTemparament(temperamentId);
                        attendantPetTypesRepository.deleteByTemparament(temperamentId);
                        appointmentRepository.updateTemparament(temperamentId);
                        petRepository.updateTempermant(temperamentId);
                    }
                    temperamentRepository.deleteAll(removeTemperaments);
                }
            }

            if (petTypeDto.getDesiredHairLengths() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.DESIRED_HAIR_LENGTH, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getDesiredHairLengths().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<DesiredHairLengthDto> allergies = petTypeDto.getDesiredHairLengths().getOptions();
                if (Utils.isNotEmpty(allergies)) {
                    desiredHairLengthRepository.saveAll(PetTypeMapper.toDesiredHairLengthEntityList(allergies, petType,desiredHairLengthRepository));
                }
                if(Utils.isNotEmpty(petTypeDto.getDesiredHairLengths().getRemovedIds())){
                    List<DesiredHairLength> removeDesiredHairLengts=new ArrayList<>();
                    for (Integer desiredHairLengthId:petTypeDto.getDesiredHairLengths().getRemovedIds()) {
                        DesiredHairLength desiredHairLength = desiredHairLengthRepository.findByDesiredHairLengthId(desiredHairLengthId);
                        if(desiredHairLength != null) {
                            removeDesiredHairLengts.add(desiredHairLength);
                        }
                        appointmentDesiredHairlengthRepository.deleteByDesiredHairLength(desiredHairLengthId);
                    }
                    desiredHairLengthRepository.deleteAll(removeDesiredHairLengts);
                }
            }

            if (petTypeDto.getVaccinationRecords() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.VACCINATION_RECORDS, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getVaccinationRecords().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<VaccinationRecordsDto> vaccinationRecords = petTypeDto.getVaccinationRecords().getOptions();
                if (Utils.isNotEmpty(vaccinationRecords)) {
                    vaccinationRecordsRepository.saveAll(PetTypeMapper.toVaccinationRecordsEntityList(vaccinationRecords, petType,vaccinationRecordsRepository));
                }

                if(Utils.isNotEmpty(petTypeDto.getVaccinationRecords().getRemovedIds())){
                    List<VaccinationRecords> removeVaccinationRecords=new ArrayList<>();
                    for (Integer vaccinationRecordsId:petTypeDto.getVaccinationRecords().getRemovedIds()) {
                        VaccinationRecords byVaccinationRecordId = vaccinationRecordsRepository.findByVaccinationRecordId(vaccinationRecordsId);
                        if(byVaccinationRecordId != null){
                            removeVaccinationRecords.add(byVaccinationRecordId);
                        }
                        petVaccinationRecordRepository.deleteByVaccinationInfo(vaccinationRecordsId);
                        serviceRepository.deleteByVaccinationInfo(vaccinationRecordsId);
                        List<AppointmentVaccinationInformation> vaccinationInfoList = appointmentVaccinationInformationRepository.findByVaccinationRecordsVaccinationRecordId(vaccinationRecordsId);
                        if(!vaccinationInfoList.isEmpty()) {
                            for (AppointmentVaccinationInformation vaccinationInfo : vaccinationInfoList) {
                                Integer AppointmentvaccinationRecordsId = vaccinationInfo.getId();
                                appointmentRepository.deleteByVaccinationInfo(AppointmentvaccinationRecordsId);
                            }
                        }
                        appointmentVaccinationInformationRepository.deleteByVaccinationInfo(vaccinationRecordsId);
                    }
                    vaccinationRecordsRepository.deleteAll(removeVaccinationRecords);
                }
            }

            if (petTypeDto.getEmergencyContactInfo() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.EMERGENCY_CONTACT_INFO, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getEmergencyContactInfo().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<EmergencyContactInfoDto> emergencyContactInfos = petTypeDto.getEmergencyContactInfo().getOptions();
                if (Utils.isNotEmpty(emergencyContactInfos)) {
                    emergencyContactInfoRepository.saveAll(PetTypeMapper.toEmergencyContactInfoEntityList(emergencyContactInfos, petType,emergencyContactInfoRepository));
                }
                if(Utils.isNotEmpty(petTypeDto.getEmergencyContactInfo().getRemovedIds())){
                    List<EmergencyContactInfo> removeEmergencyContactInfos=new ArrayList<>();
                    for (Integer emergencyContactInfosId:petTypeDto.getEmergencyContactInfo().getRemovedIds()) {
                        EmergencyContactInfo emergencyContactInfo = emergencyContactInfoRepository.findByEmergencyContactInfoId(emergencyContactInfosId);
                        if(emergencyContactInfo != null){
                            removeEmergencyContactInfos.add(emergencyContactInfo);
                        }
                        petEmergencyContactRepository.deleteByEmergencyContactInfo(emergencyContactInfosId);
                        appointmentEmergencyContactInfoRpository.deleteByEmergencyContactInfo(emergencyContactInfosId);

                    }
                    emergencyContactInfoRepository.deleteAll(removeEmergencyContactInfos);
                }
            }

            if (petTypeDto.getDocumentOptions() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.DOCUMENT_OPTIONS, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getDocumentOptions().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<DocumentOptionDto> documentOptionDtos = petTypeDto.getDocumentOptions().getOptions();
                if (Utils.isNotEmpty(documentOptionDtos)) {
                    Set<DocumentOption> documentOptions = PetTypeMapper.toDocumentOptionList(documentOptionDtos, petType,documentOptionRepository);
                    petType.setDocumentOptions(documentOptions);
                    petTypeRepository.save(petType);
                    documentOptionRepository.saveAll(PetTypeMapper.toDocumentOptionList(documentOptionDtos, petType,documentOptionRepository));
                }
                if(Utils.isNotEmpty(petTypeDto.getDocumentOptions().getRemovedIds())){
                    for (Integer documentOptionId:petTypeDto.getDocumentOptions().getRemovedIds()) {
                        petTypeRepository.deleteByPetTypeIdAndDocumentOptionId(petTypeId, documentOptionId);
                    }
                }
            }

            if (petTypeDto.getWeightRanges() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.WEIGHT_RANGE, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getWeightRanges().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<WeightRangeDto> weightRanges = petTypeDto.getWeightRanges().getOptions();
                if (Utils.isNotEmpty(weightRanges)) {
                    weightRangeRepository.saveAll(PetTypeMapper.toWeightRangeList(weightRanges, petType));
                }

                if(Utils.isNotEmpty(petTypeDto.getWeightRanges().getRemovedIds())){
                    List<WeightRange> removeWeightRanges=new ArrayList<>();
                    for (Integer weightRangeId:petTypeDto.getWeightRanges().getRemovedIds()) {
                        WeightRange weightRange=weightRangeRepository.findByWeightRangeId(weightRangeId);
                        weightRange.setDeleted(true);
                        weightRangeRepository.save(weightRange);
                      //  removeWeightRanges.add(weightRange);
                       // petRepository.deleteByWeightRangeId(weightRangeId,RetailerContext.getRetailer());
                        //appointmentRepository.deleteByWeightRangeId(weightRangeId,RetailerContext.getRetailer());
                    }
                   // weightRangeRepository.deleteAll(removeWeightRanges);
                }

            }

            if (petTypeDto.getGeneralPetSizes() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.GENERAL_PET_SIZE, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getGeneralPetSizes().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<GeneralPetSizeDto> generalPetSizeDtos = petTypeDto.getGeneralPetSizes().getOptions();
                if (Utils.isNotEmpty(generalPetSizeDtos)) {
                    Set<String> sizeSet = new HashSet<>();
                    for (GeneralPetSizeDto dto : generalPetSizeDtos) {
                        String normalizedSize = dto.getSize().trim().toLowerCase();
                        if (!sizeSet.add(normalizedSize)) {
                            throw new BadRequestException("Duplicate pet size found in request: " + dto.getSize());
                        }
                    }
                    Set<GeneralPetSize> newGeneralPetSizes = PetTypeMapper.toGeneralPetSizeList(generalPetSizeDtos, petType);
                    if (newGeneralPetSizes.size() == 3) {
                        int xLargeUnitValue = -1;
                        for (GeneralPetSize generalPetSize: newGeneralPetSizes) {
                            if (generalPetSize.getSize().equalsIgnoreCase("large")) {
                                xLargeUnitValue = generalPetSize.getWeightValue() + 1;
                            }
                        }
                        if (petTypeConfiguration != null && petTypeConfiguration.getDisplayType().equals(DisplayType.OPTION) && xLargeUnitValue != -1) {
                            GeneralPetSize newGeneralPetSize = new GeneralPetSize();
                            newGeneralPetSize.setSize("X-Large");
                            newGeneralPetSize.setWeightValue(xLargeUnitValue);
                            newGeneralPetSize.setWeightUnit(generalPetSizeDtos.get(0).getWeightUnit());
                            newGeneralPetSize.setPetType(petType);
                            newGeneralPetSize.setRetailer(RetailerContext.getRetailer());
                            List<GeneralPetSize> existingGeneralPetSizes = generalPetSizeRepository.findAllByPetTypePetTypeIdAndRetailer(petTypeId, RetailerContext.getRetailer());
                            for (GeneralPetSize existingGeneralPetSize: existingGeneralPetSizes) {
                                if (existingGeneralPetSize.getSize().equalsIgnoreCase("X-Large")) {
                                    newGeneralPetSize.setGeneralPetSizeId(existingGeneralPetSize.getGeneralPetSizeId());
                                }
                            }
                            newGeneralPetSizes.add(newGeneralPetSize);
                        } else {
                            generalPetSizeRepository.deleteAllBySizeAndPetTypePetTypeIdAndRetailer("X-Large", petTypeId, RetailerContext.getRetailer());
                        }
                    }
                    else if(newGeneralPetSizes.size()==4){
                        List<GeneralPetSize> existingSizes = generalPetSizeRepository.findAllByPetTypePetTypeIdAndRetailer(petTypeId, RetailerContext.getRetailer());
                        boolean existsAlready = existingSizes.stream()
                                .anyMatch(size -> size.getSize().equalsIgnoreCase("X-Large") || size.getSize().equalsIgnoreCase("Extra Large"));

                        boolean addingAgain = newGeneralPetSizes.stream()
                                .anyMatch(size -> size.getSize().equalsIgnoreCase("X-Large") || size.getSize().equalsIgnoreCase("Extra Large"));

                        // Only throw error if trying to add a NEW X-Large entry when one already exists
                        // Check if the X-Large in payload has a different ID than existing ones
                        if (existsAlready && addingAgain) {
                            boolean isNewXLargeEntry = newGeneralPetSizes.stream()
                                    .anyMatch(size -> (size.getSize().equalsIgnoreCase("X-Large") || size.getSize().equalsIgnoreCase("Extra Large")) 
                                            && size.getGeneralPetSizeId() == null);
                            
                            if (isNewXLargeEntry) {
                                throw new IllegalArgumentException("'X-Large' or 'Extra Large' entry already exists for this pet type.");
                            }
                        }
                    }
                    generalPetSizeRepository.saveAll(newGeneralPetSizes);
                }

                if(Utils.isNotEmpty(petTypeDto.getGeneralPetSizes().getRemovedIds())){
                    List<GeneralPetSize> removeGeneralPetSizes=new ArrayList<>();
                    for (Integer generalPetSizeId:petTypeDto.getGeneralPetSizes().getRemovedIds()) {
                        GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeId);
                        removeGeneralPetSizes.add(generalPetSize);
                        generalPetSizeRepository.deleteGeneralPetSizeFromPet(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromAttendant(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromVenue(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromService(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromPetSizeConstraint(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromPetSizeLimit(generalPetSizeId);
                    }
                    generalPetSizeRepository.deleteAll(removeGeneralPetSizes);
                }

            }

            if (petTypeDto.getDeceaseDate() != null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.DECEASE_DATE, petType, RetailerContext.getRetailer());
                if(petTypeConfiguration!=null) {
                    petTypeConfiguration.setDisplayType(petTypeDto.getDeceaseDate().getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
            }

            petTypeConfigurationRepository.saveAll(petTypeConfigurations);
        }
    }

    @Override
    @Transactional
    public void deletePetType(int petTypeId) throws EtailBookItException, IOException {
        PetType petType = petTypeRepository.findByPetTypeIdAndDeleted(petTypeId, false);
        if (petType == null) {
            throw new EntityNotFoundException("Pet Type not found with id " + petTypeId);
        }
        if (petType.getName().equalsIgnoreCase("Dog DEV")) {
            throw new EtailBookItException("This test data cannot be deleted");
        }
        if (petType.getFileUrl() != null) {
            if(!petType.getFileUrl().isEmpty()){
                FileUploadUtil.deleteFile(petType.getFileUrl(), s3client,
                        dataFileDetails.getBucketName(), dataFileDetails.getPetTypePrefix());
            }

        }

        // unlink pets
        if (!petType.getPets().isEmpty()) {
            for (Pet pet : petType.getPets()) {
                pet.setPetType(null);
            }
        }

        // unlink services
        if (!petType.getServices().isEmpty()) {
            for (com.sayone.etailbookit.model.Service service : petType.getServices()) {
                service.setPetType(null);
            }
        }

        // unlink attendants
        if (!petType.getAttendants().isEmpty()) {
            for (AttendantPetTypes att : petType.getAttendants()) {
                att.setPetType(null);
            }
        }

        // unlink venues
        if (!petType.getVenues().isEmpty()) {
            for (VenuePetTypes venue : petType.getVenues()) {
                venue.setPetType(null);
            }
        }

        // unlink hairLengths
        if (!petType.getHairLengths().isEmpty()) {
            for (HairLength hl : petType.getHairLengths()) {
                hl.setPetType(null);
            }
        }

        // unlink hairTextures
        if (!petType.getHairTextures().isEmpty()) {
            for (HairTexture ht : petType.getHairTextures()) {
                ht.setPetType(null);
            }
        }

        // unlink combs
        if (!petType.getCombs().isEmpty()) {
            for (Combs comb : petType.getCombs()) {
                comb.setPetType(null);
            }
        }

        // unlink blades
        if (!petType.getBlades().isEmpty()) {
            for (Blades blade : petType.getBlades()) {
                blade.setPetType(null);
            }
        }

        // unlink allergies
        if (!petType.getAllergies().isEmpty()) {
            for (Allergies allergy : petType.getAllergies()) {
                allergy.setPetType(null);
            }
        }

        // unlink vet information
        if (!petType.getVetInformation().isEmpty()) {
            for (VetInformation vi : petType.getVetInformation()) {
                vi.setPetType(null);
            }
        }

        // unlink desired hair lengths
        if (!petType.getDesiredHairLengths().isEmpty()) {
            for (DesiredHairLength dhl : petType.getDesiredHairLengths()) {
                dhl.setPetType(null);
            }
        }

        // unlink breeds
        if (!petType.getBreeds().isEmpty()) {
            for (Breed breed : petType.getBreeds()) {
                breed.setPetType(null);
            }
        }

        // unlink temperaments
        if (!petType.getTemperaments().isEmpty()) {
            for (Temperament temperament : petType.getTemperaments()) {
                temperament.setPetType(null);
            }
        }

        // unlink vaccination records
        if (!petType.getVaccinationRecords().isEmpty()) {
            for (VaccinationRecords vr : petType.getVaccinationRecords()) {
                vr.setPetType(null);
            }
        }

        // unlink emergency contacts
        if (!petType.getEmergencyContactInfo().isEmpty()) {
            for (EmergencyContactInfo eci : petType.getEmergencyContactInfo()) {
                eci.setPetType(null);
            }
        }

        // unlink weight ranges
        if (!petType.getWeightRanges().isEmpty()) {
            for (WeightRange wr : petType.getWeightRanges()) {
                wr.setPetType(null);
            }
        }

        // unlink general pet sizes
        if (!petType.getGeneralPetSizes().isEmpty()) {
            for (GeneralPetSize gps : petType.getGeneralPetSizes()) {
                gps.setPetType(null);
            }
        }

        // unlink pet type configurations
        if (!petType.getPetTypeConfigurations().isEmpty()) {
            for (PetTypeConfiguration ptc : petType.getPetTypeConfigurations()) {
                ptc.setPetType(null);
            }
        }

        // unlink document options
        if (!petType.getDocumentOptions().isEmpty()) {
            for (DocumentOption doc : petType.getDocumentOptions()) {
              //  petType.getDocumentOptions().remove(doc);
                petType.setDocumentOptions(null); // unlink from join table
            }
        }

        // unlink pet size constraints
        List<PetSizeConstraint> sizeConstraints = petSizeConstraintRepository.findAllByPetType(petType);
        for (PetSizeConstraint psc : sizeConstraints) {
            psc.setPetType(null);
        }
        List<PetSizeLimit> sizeLimits =petSizeLimitRepository.findAllByPetType(petType);
        for(PetSizeLimit sizeLimit:sizeLimits){
            sizeLimit.setPetType(null);
        }
        // finally delete the petType
        petTypeRepository.delete(petType);
    }


    @Override
    public PetTypeDto getPetTypeById(int petTypeId) throws EtailBookItException {
        Validator.validatePetType(null, petTypeId, petTypeRepository);
        PetType petType = petTypeRepository.getOne(petTypeId);
        PetTypeDto petTypeDto = PetTypeMapper.toCompletePetTypeDto(petType);
        PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.WEIGHT_RANGE, petType, RetailerContext.getRetailer());
        if(petTypeConfiguration == null)
            throw new EtailBookItException("Pet type configuration for weight range not found.");
        if(petTypeDto.getGeneralPetSizes() != null && !petTypeConfiguration.getDisplayType().equals(DisplayType.NONE)) {
            petTypeDto.getGeneralPetSizes().setDisplayType(petTypeConfiguration.getDisplayType());
        } else if (petTypeDto.getGeneralPetSizes() != null) {
            petTypeDto.getGeneralPetSizes().setDisplayType(DisplayType.NONE);
            petTypeDto.getGeneralPetSizes().setOptions(new ArrayList<>());
        }
        if(petTypeDto.getWeightRanges()!=null){
            List<WeightRangeDto> weightRangeDtos=petTypeDto.getWeightRanges().getOptions();
            weightRangeDtos.removeIf(weightRangeDto -> weightRangeDto.getDeleted() == Boolean.TRUE);
        }if(petTypeDto.getBreeds()!=null){
            List<BreedDto> breedsDtos=petTypeDto.getBreeds().getOptions();
            breedsDtos.removeIf(breedDto -> breedDto.getDeleted() == Boolean.TRUE);
        }
        return petTypeDto;
    }

    @Override
    public Page<PetTypeDto> getPetTypes(Integer pageNo, Integer pageSize, String sortBy, String search) {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.desc(sortBy)));
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(Sort.Order.desc("active"),Sort.Order.asc(sortBy)));
        }
        petTypeActiveNullCheck();
        Page<PetType> petTypes = petTypeRepository.getPetTypes(RetailerContext.getRetailer(), search.toLowerCase(), false, paging);
        return new PageImpl<>(PetTypeMapper.toPetTypeDtoList(petTypes.getContent()), paging, petTypes.getTotalElements());
    }

    @Override
    public PetTypeDto getActiveDetails(int petTypeId) throws EtailBookItException {

        PetType petType = petTypeRepository.findByPetTypeId(petTypeId);
        if (petType == null) {
            throw new EntityNotFoundException("PetType not found with id " + petTypeId);
        }
        return PetTypeMapper.toActiveValues(petType);
    }

    @Override
    public List<TemperamentDto> getTemperaments(int petTypeId) throws EntityNotFoundException {
        Optional<PetType> petType = petTypeRepository.findById(petTypeId);
        List<Temperament> temperaments;
        if (petType.isPresent() && petType.get().getTemperaments() != null) {
            temperaments = petType.get().getTemperaments().stream()
                    .filter(Temperament::getActive)
                    .collect(Collectors.toList());
        } else {
            throw new EntityNotFoundException("Pet Type not found");
        }
        return PetTypeMapper.getConfigDtos(Temperament.class, TemperamentDto.class, temperaments);
    }

    @Override
    public List<VaccinationRecordsDto> getVaccinationRecords(int petTypeId) throws EntityNotFoundException {
        Optional<PetType> petType = petTypeRepository.findById(petTypeId);
        List<VaccinationRecords> vaccinationRecords;
        if (petType.isPresent() && petType.get().getVaccinationRecords() != null) {
            vaccinationRecords = petType.get().getVaccinationRecords().stream()
                    .filter(VaccinationRecords::getActive)
                    .collect(Collectors.toList());
        } else {
            throw new EntityNotFoundException("Pet Type not found");
        }
        return PetTypeMapper.getConfigDtos(VaccinationRecords.class, VaccinationRecordsDto.class, vaccinationRecords);
    }

    @Override
    public List<GeneralPetSizeDto> getSizes(int petTypeId) throws EntityNotFoundException {
        Optional<PetType> petType = petTypeRepository.findById(petTypeId);
        List<GeneralPetSize> generalPetSizes = new ArrayList<>();
        if(petType.isPresent()) {
            PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.WEIGHT_RANGE, petType.get(), RetailerContext.getRetailer());
            if(petType.get().getGeneralPetSizes() != null && !petTypeConfiguration.getDisplayType().equals(DisplayType.NONE)) {
                generalPetSizes = new ArrayList<>(petType.get().getGeneralPetSizes());
                generalPetSizes=generalPetSizes.stream().sorted((o1, o2) -> o1.getSize().compareTo(o2.getSize())).collect(Collectors.toList());
            }
        }
        else {
            throw new EntityNotFoundException("Pet Type not found");
        }
        return PetTypeMapper.getConfigDtos(GeneralPetSize.class, GeneralPetSizeDto.class, generalPetSizes);
    }

    @Override
    public void updateGeneralPetSize(UpdateGeneralPetSizeDto updateGeneralPetSizeDto, int petTypeId) {

            PetType petType = petTypeRepository.findByPetTypeId(petTypeId);
        Set<PetTypeConfiguration> petTypeConfigurations = new HashSet<>();
            if (petType!=null) {
                PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.GENERAL_PET_SIZE, petType, RetailerContext.getRetailer());
                if (petTypeConfiguration != null) {
                    petTypeConfiguration.setDisplayType(updateGeneralPetSizeDto.getDisplayType());
                    petTypeConfigurations.add(petTypeConfiguration);
                }
                List<GeneralPetSizeDto> generalPetSizeDtos = updateGeneralPetSizeDto.getOptions();
                if (Utils.isNotEmpty(generalPetSizeDtos)) {
                    for(GeneralPetSizeDto generalPetSizeDto:generalPetSizeDtos) {
                        GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeDto.getId());
                        if(generalPetSize!=null){
                            generalPetSize.setSize(generalPetSizeDto.getSize());
                            generalPetSize.setGeneralPetSizeId(generalPetSize.getGeneralPetSizeId());
                            generalPetSize.setPetType(petType);
                            generalPetSize.setWeightUnit(generalPetSizeDto.getWeightUnit());
                            generalPetSize.setWeightValue(generalPetSizeDto.getWeightValue());
                            generalPetSizeRepository.save(generalPetSize);
                        }else{
                            GeneralPetSize generalPetSize1=new GeneralPetSize();
                            generalPetSize1.setSize(generalPetSizeDto.getSize());
                            generalPetSize1.setPetType(petType);
                            generalPetSize1.setWeightUnit(generalPetSizeDto.getWeightUnit());
                            generalPetSize1.setWeightValue(generalPetSizeDto.getWeightValue());
                            generalPetSizeRepository.save(generalPetSize1);

                        }
                    }
                }
                if(Utils.isNotEmpty(updateGeneralPetSizeDto.getRemovedIds())){
                    List<GeneralPetSize> removeGeneralPetSizes=new ArrayList<>();
                    for (Integer generalPetSizeId:updateGeneralPetSizeDto.getRemovedIds()) {
                        GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeId);
                        removeGeneralPetSizes.add(generalPetSize);
                        generalPetSizeRepository.deleteGeneralPetSizeFromPet(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromAttendant(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromVenue(generalPetSizeId);
                        generalPetSizeRepository.deleteGeneralPetSizeFromService(generalPetSizeId);
                    }
                    generalPetSizeRepository.deleteAll(removeGeneralPetSizes);
                }

                petTypeConfigurationRepository.saveAll(petTypeConfigurations);
            }

    }

    @Override
    public void insertPetConfiguration(Integer petTypeId) throws EtailBookItException {
        PetType petType=petTypeRepository.findByPetTypeId(petTypeId);
        Set<PetTypeConfiguration> petTypeConfigurations=new HashSet<>();
        PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.HAIR_LENGTH,petType,RetailerContext.getRetailer());
        if(petTypeConfiguration==null){
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.HAIR_LENGTH, DisplayType.NONE, petType));
        }
        petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.HAIR_TEXTURE,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.HAIR_TEXTURE, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.COMBS_BLADES,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.COMBS_BLADES, DisplayType.NONE, petType));
        }
        petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.VET_INFO,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.VET_INFO, DisplayType.NONE, petType));

        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.ALLERGIES,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.ALLERGIES, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.DESIRED_HAIR_LENGTH,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.DESIRED_HAIR_LENGTH, DisplayType.NONE, petType));
        }
        petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.BREEDS,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.BREEDS, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.TEMPERAMENT,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.TEMPERAMENT, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.VACCINATION_RECORDS,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.VACCINATION_RECORDS, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.DOCUMENT_OPTIONS,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.DOCUMENT_OPTIONS, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.EMERGENCY_CONTACT_INFO,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.EMERGENCY_CONTACT_INFO, DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.WEIGHT_RANGE,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.WEIGHT_RANGE,DisplayType.NONE, petType));
        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.GENERAL_PET_SIZE,petType,RetailerContext.getRetailer());
        if(petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.GENERAL_PET_SIZE, DisplayType.NONE, petType));

        }
         petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.DECEASE_DATE,petType,RetailerContext.getRetailer());
        if (petTypeConfiguration==null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.DECEASE_DATE,DisplayType.NONE, petType));
        }
        petTypeConfigurations.forEach( config -> config.setRetailer(RetailerContext.getRetailer()) );

        petType.setPetTypeConfigurations(petTypeConfigurations);
        petType.setRetailer(RetailerContext.getRetailer());
        petTypeRepository.save(petType);
    }

    @Override
    public void changeValuesOfGeneralPetSize() throws EtailBookItException{
        List<PetType> petTypeList=petTypeRepository.findByRetailer(RetailerContext.getRetailer());
        List<Integer> removeIds=new ArrayList<>();
        List<Integer> retainIds=new ArrayList<>();
        Integer count =0;
        if(!petTypeList.isEmpty()) {
            for(PetType petType:petTypeList) {
                PetType petType1 = petTypeRepository.findByPetTypeId(petType.getPetTypeId());
                Set<PetTypeConfiguration> petTypeConfigurations = new HashSet<>();
                if (petType1 != null) {
                    PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.GENERAL_PET_SIZE, petType1, RetailerContext.getRetailer());
                    if (petTypeConfiguration != null) {
                       List<GeneralPetSize> generalPetSizeList=generalPetSizeRepository.findAllByPetTypeAndRetailer(petType1,RetailerContext.getRetailer());
                       if(!generalPetSizeList.isEmpty() && generalPetSizeList.size()>4){
                            for(GeneralPetSize generalPetSize:generalPetSizeList){
                                if(generalPetSize.getSize().equalsIgnoreCase("extra large") || generalPetSize.getSize().equalsIgnoreCase("X-Large")){
                                    count=count+1;
                                    removeIds.add(generalPetSize.getGeneralPetSizeId());
                                }
                            }
                            if(count>1){
                                retainIds.add(removeIds.get(0));
                            }
                       }
                    }
                    removeIds.removeAll(retainIds);
                    if (Utils.isNotEmpty(removeIds)) {
                        List<GeneralPetSize> removeGeneralPetSizes = new ArrayList<>();
                        for (Integer generalPetSizeId : removeIds) {
                            GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeId);
                            removeGeneralPetSizes.add(generalPetSize);
                            generalPetSizeRepository.deleteGeneralPetSizeFromPet(generalPetSizeId);
                            generalPetSizeRepository.deleteGeneralPetSizeFromAttendant(generalPetSizeId);
                            generalPetSizeRepository.deleteGeneralPetSizeFromVenue(generalPetSizeId);
                            generalPetSizeRepository.deleteGeneralPetSizeFromService(generalPetSizeId);
                        }
                        removeGeneralPetSizes.removeIf(Objects::isNull);
                        generalPetSizeRepository.deleteAll(removeGeneralPetSizes);
                    }

                    petTypeConfigurationRepository.saveAll(petTypeConfigurations);
                }
            }
        }
    }

    @Override
    public List<PetAndPetTypeMappingDto> getPetList(Integer serviceId,Integer customerId) throws EtailBookItException{

        List<PetAndPetTypeMappingDto> petAndPetTypeMappingDtos=new ArrayList<>();
        List<PetType> petTypes=serviceRepository.findPetType(serviceId,RetailerContext.getRetailer());
        List<Pet> petList= petRepository.findPetsByPetTypesAndCustomerId(petTypes, customerId);
        for (Pet pet:petList){
            PetAndPetTypeMappingDto petAndPetTypeMappingDto=new PetAndPetTypeMappingDto();
            petAndPetTypeMappingDto.setId(pet.getId());
            petAndPetTypeMappingDto.setCustomerId(pet.getCustomerId());
            petAndPetTypeMappingDto.setName(pet.getName());
            petAndPetTypeMappingDto.setPetTypeId(pet.getPetType().getPetTypeId());
            petAndPetTypeMappingDtos.add(petAndPetTypeMappingDto);
        }
        return petAndPetTypeMappingDtos;
    }

    private void petTypeActiveNullCheck(){
        List<PetType> petTypeList = petTypeRepository.findByRetailerAndActiveAndDeleted(RetailerContext.getRetailer(), null, false);
        for(PetType petType : petTypeList){
            petType.setActive(true);
            petTypeRepository.save(petType);
        }
    }
}
