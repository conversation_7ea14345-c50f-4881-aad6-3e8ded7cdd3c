package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.Batch.CancelledAppointmentReaderConfiguration;
import com.sayone.etailbookit.Batch.CancelledAppointmentWritereConfiguration;
import com.sayone.etailbookit.Batch.ReaderConfiguration;
import com.sayone.etailbookit.Batch.WriterConfiguration;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.BatchAppointmentDto;
import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import com.sayone.etailbookit.dto.PaginatedResponse;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.projections.AppointmentsListing;
import com.sayone.etailbookit.projections.CancelledAppointmentProjections;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.service.IReportService;
import com.sayone.etailbookit.util.CancelledAppointmentExcelGenerator;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Service
public class ReportService implements IReportService {

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    JobLauncher jobLauncher;

    @Autowired
    Job cancelledAppointmentJob;

    @Autowired
    CancelledAppointmentWritereConfiguration writerConfiguration;

    @Autowired
    CancelledAppointmentReaderConfiguration readerConfiguration;

    @Autowired
    CancelledAppointmentExcelGenerator cancelledAppointmentExcelGenerator;


    @Override
    public ByteArrayOutputStream getAllCancelledAppointments(Integer pageNo, Integer pageSize, String sortBy,boolean selectAll) throws EtailBookItException, IOException {
       if(selectAll) {
           readerConfiguration.setRetailer(RetailerContext.getRetailer());
           try {
               JobParameters jobParameters = new JobParametersBuilder()
                       .addString("timestamp", String.valueOf(System.currentTimeMillis()))
                       .toJobParameters();
               JobExecution jobExecution = jobLauncher.run(cancelledAppointmentJob, jobParameters);
           } catch (Exception e) {
               throw new EtailBookItException(e.getMessage());
           }
           return writerConfiguration.getGeneratedContent();
       }else {
           Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
           Page<CancelledAppointmentProjections> appointmentList=appointmentRepository.findCancelledAppointments(RetailerContext.getRetailer(),paging);
           XSSFWorkbook workbook = new XSSFWorkbook();
           Sheet sheet = workbook.createSheet("Cancelled_Appointments");
           sheet.setDefaultColumnWidth(15);
           cancelledAppointmentExcelGenerator.createHeaderRow(sheet);
           for (CancelledAppointmentProjections appointment : appointmentList) {
               CancelledAppointmentsDto batchAppointmentDto = new CancelledAppointmentsDto();
               batchAppointmentDto.setCustomerName(appointment.getCustomerName());
               batchAppointmentDto.setService(appointment.getService().getName());
               batchAppointmentDto.setPetName(appointment.getPet().getName());
               batchAppointmentDto.setAppointmentCancellationRate(appointment.getService().getcancelationAmountValue());
               batchAppointmentDto.setAppointmentDateTime(appointment.getAppointmentStartDateAndTime());
               cancelledAppointmentExcelGenerator.createDataRow(sheet, batchAppointmentDto);
           }
           ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
           workbook.write(outputStream);
           outputStream.close();
           return outputStream;
       }
    }


    @Override
    public BaseResponseDto fetchAllAppointments(Integer pageNo, Integer pageSize, String sortBy)throws EtailBookItException {

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<CancelledAppointmentProjections> appointmentList=appointmentRepository.findCancelledAppointments(RetailerContext.getRetailer(),paging);
        BaseResponseDto responseDto=new BaseResponseDto(Status.SUCCESS,appointmentList);
        return responseDto;
    }
}
