package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Configuration;
import com.sayone.etailbookit.model.WaiverOfLiability;
import com.sayone.etailbookit.repository.ConfigurationRepository;
import com.sayone.etailbookit.service.IEnableSlotService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EnableSlotService implements IEnableSlotService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EnableSlotService.class);

    @Autowired
    ConfigurationRepository configurationRepository;

    private static final String ENABLE_SLOT = "enable_slots";
    
    @Override
    public void createOrUpdateEnableSlot(boolean isEnableSlot) throws EtailBookItException {
        try {
            LOGGER.debug("Creating or updating enable slot configuration for retailer: {}, value: {}", 
                RetailerContext.getRetailer(), isEnableSlot);
            
            Configuration configuration = configurationRepository.findByNameAndRetailer(ENABLE_SLOT, RetailerContext.getRetailer());
            if (configuration == null) {
                configuration = new Configuration();
                configuration.setName(ENABLE_SLOT);
                LOGGER.debug("Creating new enable slot configuration for retailer: {}", RetailerContext.getRetailer());
            } else {
                LOGGER.debug("Updating existing enable slot configuration for retailer: {}", RetailerContext.getRetailer());
            }
            
            configuration.setRetailer(RetailerContext.getRetailer());
            configuration.setActive(isEnableSlot);
            configurationRepository.save(configuration);
            
            LOGGER.debug("Successfully saved enable slot configuration for retailer: {}", RetailerContext.getRetailer());
            
        } catch (Exception e) {
            LOGGER.error("Error creating/updating enable slot configuration for retailer {}: {}", 
                RetailerContext.getRetailer(), e.getMessage(), e);
            throw new EtailBookItException("Failed to create or update enable slot configuration: " + e.getMessage());
        }
    }

    @Override
    public BaseResponseDto getEnableSlotValue(String retailer) throws EtailBookItException {
        try {
            LOGGER.debug("Getting enable slot value for retailer: {}", retailer);
            
            if (retailer == null || retailer.trim().isEmpty()) {
                LOGGER.warn("Retailer is null or empty, using context retailer: {}", RetailerContext.getRetailer());
                retailer = RetailerContext.getRetailer();
            }
            
            Configuration configuration = configurationRepository.findByNameAndRetailer(ENABLE_SLOT, retailer);
            
            if (configuration == null) {
                LOGGER.warn("No enable slot configuration found for retailer: {}. Creating default configuration.", retailer);
                
                // Create default configuration instead of throwing exception
                configuration = new Configuration();
                configuration.setName(ENABLE_SLOT);
                configuration.setRetailer(retailer);
                configuration.setActive(false); // Default to disabled
                configurationRepository.save(configuration);
                
                LOGGER.info("Created default enable slot configuration (disabled) for retailer: {}", retailer);
            }
            
            BaseResponseDto responseDto = new BaseResponseDto<>(Status.SUCCESS, configuration);
            LOGGER.debug("Successfully retrieved enable slot configuration for retailer: {}", retailer);
            
            return responseDto;
            
        } catch (Exception e) {
            LOGGER.error("Error getting enable slot value for retailer {}: {}", retailer, e.getMessage(), e);
            throw new EtailBookItException("Failed to retrieve enable slot configuration: " + e.getMessage());
        }
    }
}
