package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.dto.PetShampooDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.PetShampooMapper;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Configuration;
import com.sayone.etailbookit.model.PetShampoo;
import com.sayone.etailbookit.projections.ServiceListingProjection;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.ConfigurationRepository;
import com.sayone.etailbookit.repository.PetShampooRepository;
import com.sayone.etailbookit.service.IPetShampooService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class PetShampooService implements IPetShampooService {

    @Autowired
    PetShampooRepository petShampooRepository;

    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    private static final String PET_SHAMPOO="pet_shampoo";

    @Override
    public PetShampooDto addPetShampoo(PetShampooDto petShampooDto) throws EtailBookItException {
        Validator.validatePetShampoo(petShampooDto, null, petShampooRepository);
        PetShampoo petShampoo = PetShampooMapper.toPetShampooEntity(petShampooDto);
        //commented out this code as per the requirement of the ticket BKI-1213
       /* if (petShampoo.getIndexValue() == null) {
            Integer lastIndexId = petShampooRepository.getByLastIndexValueByRetailerAndDeleted(petShampoo.getRetailer(),false);
            if(lastIndexId != null){
                petShampoo.setIndexValue(lastIndexId+1);
            }else{
                petShampoo.setIndexValue(1);
            }
        }*/
        petShampoo = petShampooRepository.save(petShampoo);
        return PetShampooMapper.toPetShampooDto(petShampoo);
    }

    @Override
    public PetShampooDto updatePetShampoo(PetShampooDto petShampooDto, int petShampooId) throws EtailBookItException {
        Validator.validatePetShampoo(petShampooDto, petShampooId, petShampooRepository);
        PetShampoo petShampoo = PetShampooMapper.toPetShampooEntity(petShampooDto);
        petShampoo.setId(petShampooId);
        petShampoo = petShampooRepository.save(petShampoo);
        return PetShampooMapper.toPetShampooDto(petShampoo);
    }

    @Override
    public void deletePetShampoo(int petShampooId) throws EtailBookItException {
        Optional<PetShampoo> petShampoo = petShampooRepository.findById(petShampooId);
        if (!petShampoo.isPresent()) {
            throw new EntityNotFoundException("Pet Shampoo not found");
        }
        else {
            List<Appointment> existingAppointment = appointmentRepository.findByShamppo(petShampoo.get());
            if(!existingAppointment.isEmpty()) {
                throw new BadRequestException("Shampoo is used in an appointment");
            }
            else {
                petShampooRepository.deleteById(petShampooId);
            }
        }
    }

    @Override
    public PetShampooDto getPetShampooById(int petShampooId) throws EtailBookItException {
        Validator.validatePetShampoo(null, petShampooId, petShampooRepository);
        PetShampoo petShampoo = petShampooRepository.getOne(petShampooId);
        return PetShampooMapper.toPetShampooDto(petShampoo);
    }

    @Override
    public ConfigurationDto<PetShampooDto> getPetShampoos(Integer pageNo, Integer pageSize, String sortBy, String search) {
        Pageable paging = PageRequest.of(pageNo, pageSize,Sort.by((Sort.Order.desc("active")),Sort.Order.desc(sortBy)));
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by((Sort.Order.desc("active")),Sort.Order.asc(sortBy)));
        }
        Page<PetShampoo> petShampoos = petShampooRepository.getPetShampoos(RetailerContext.getRetailer(), false, search.toLowerCase(), paging);
        ConfigurationDto configurationDto=new ConfigurationDto();
        Configuration configuration = configurationRepository.findByNameAndRetailer(PET_SHAMPOO, RetailerContext.getRetailer());
        if(configuration!=null) {
            configurationDto.setActive(configuration.isActive());
        }
        if(Utils.isNotEmpty(petShampoos.getContent()))
            configurationDto.setOptionsPage(new PageImpl<>(PetShampooMapper.toPetShampooDtoList(petShampoos.getContent()), paging, petShampoos.getTotalElements()));
        return configurationDto;
    }

    @Override
    @Transactional
    public void bulkUpdateShampoo(ConfigurationDto<PetShampooDto> petShampooDtos) throws EtailBookItException {
        List<String> shampooNames = new ArrayList<>();
        if(petShampooDtos.getOptions() != null) {
            for(PetShampooDto petShampooDto : petShampooDtos.getOptions()) {
                Validator.validatePetShampoo(petShampooDto, petShampooDto.getId(), petShampooRepository);
                if(shampooNames.contains(petShampooDto.getName().toLowerCase())) {
                    throw new BadRequestException("Shampoo with name " + petShampooDto.getName() + " already exists");
                }
                shampooNames.add(petShampooDto.getName().toLowerCase());
            }
        }
        else if(petShampooDtos.isActive()) {
            throw new BadRequestException("No shampoo options created");
        }
        Configuration configuration = configurationRepository.findByNameAndRetailer(PET_SHAMPOO, RetailerContext.getRetailer());
        if (configuration == null) {
            configuration=new Configuration();
            configuration.setName(PET_SHAMPOO);
        }
        configuration.setRetailer(RetailerContext.getRetailer());
        configuration.setActive(petShampooDtos.isActive());
        configurationRepository.save(configuration);
        if(petShampooDtos.getOptions()!=null) {
            for (PetShampooDto petShampooDto : petShampooDtos.getOptions()) {
                Validator.validatePetShampoo(petShampooDto, petShampooDto.getId(), petShampooRepository);
            }
            List<PetShampoo> petShampoos = PetShampooMapper.toPetShampooList(petShampooDtos.getOptions());
            petShampooRepository.saveAll(petShampoos);
        }
    }

    @Override
    public Page<PetShampooDto> getActiveShampoo(Integer pageNo, Integer pageSize, String sortBy, String search) throws EtailBookItException {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<PetShampoo> petShampooList;
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        if(!search.isEmpty() && search.hashCode()!=0){
            petShampooList=petShampooRepository.findByKeywordActiveRetailerAndDeleted(search.toLowerCase(),true,RetailerContext.getRetailer(),false,paging);
        }
        else {
            petShampooList = petShampooRepository.findByActiveAndRetailerAndDeleted(true, RetailerContext.getRetailer(), false, paging);
        }
         return new PageImpl<>(PetShampooMapper.toPetShampooDtoList(petShampooList.getContent()), paging, petShampooList.getTotalElements());
    }

    @Override
    public Page<PetShampooDto> getShampooByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search) throws EtailBookItException{
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        if (sortBy.equals("name")) {
            paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        }
        Page<PetShampoo> shampooList ;
        if(search.isPresent() && search.hashCode() != 0){
            shampooList= petShampooRepository.findByKeyword(search.get().toLowerCase(), RetailerContext.getRetailer(),false,paging);
        } else{
            shampooList= petShampooRepository.getAll(RetailerContext.getRetailer(), false,paging);
        }
        return new PageImpl<>(PetShampooMapper.toPetShampooDtoList(shampooList.getContent()), paging, shampooList.getTotalElements());
    }
}
