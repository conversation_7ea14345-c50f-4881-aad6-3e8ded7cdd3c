package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.AppointmentWaiverOfLiabilityInformation;
import com.sayone.etailbookit.model.Configuration;
import com.sayone.etailbookit.model.PetWaiverOfLiabilityInfo;
import com.sayone.etailbookit.model.WaiverOfLiability;
import com.sayone.etailbookit.repository.AppointmentWaiverOfLiabilityInformationRepository;
import com.sayone.etailbookit.repository.ConfigurationRepository;
import com.sayone.etailbookit.repository.PetWaiverOfLiabilityInfoRepository;
import com.sayone.etailbookit.repository.WaiverOfLiabilityRepository;
import com.sayone.etailbookit.service.IWaiverOfLiabilityService;
import com.sayone.etailbookit.util.DataFileProperties;
import com.sayone.etailbookit.util.FileType;
import com.sayone.etailbookit.util.FileUploadUtil;
import com.sayone.etailbookit.util.RetailerContext;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class WaiverOfLiabilityService implements IWaiverOfLiabilityService {
    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    WaiverOfLiabilityRepository waiverOfLiabilityRepository;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    PetWaiverOfLiabilityInfoRepository petWaiverOfLiabilityInfoRepository;

    @Autowired
    AppointmentWaiverOfLiabilityInformationRepository appointmentWaiverOfLiabilityInformationRepository;

    private static final String WAIVER_OF_LIABILITY = "waiver_of_liability";

    @Override
    @Transactional
    public void updateWaiverOfLiabilityInformation(WaiverOfLiability waiverOfLiability) throws EtailBookItException {
        Configuration configuration = configurationRepository.findByNameAndRetailer(WAIVER_OF_LIABILITY, RetailerContext.getRetailer());
        WaiverOfLiability existingWaiverOfLiability = waiverOfLiabilityRepository.findByRetailer(RetailerContext.getRetailer());
        if (existingWaiverOfLiability != null)
            waiverOfLiability.setWaiverOfLiabilityId(existingWaiverOfLiability.getWaiverOfLiabilityId());
        Boolean firstTimeConfig = false;
        if (configuration == null) {
            configuration=new Configuration();
            configuration.setName(WAIVER_OF_LIABILITY);
            firstTimeConfig = true;
        }
        configuration.setRetailer(RetailerContext.getRetailer());
        configuration.setActive(waiverOfLiability.getActive());
        waiverOfLiability.setRetailer(configuration.getRetailer());
        if(waiverOfLiability.getActive()) {
            try {
                if (!ObjectUtils.isEmpty(waiverOfLiability.getFile())) {
                    String awsUrl = FileUploadUtil.uploadFile(waiverOfLiability.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.Documents);
                    waiverOfLiability.setFileURL(awsUrl);
                } else if (!ObjectUtils.isEmpty(waiverOfLiability.getFileURL())) {
                    waiverOfLiability.setFileURL(waiverOfLiability.getFileURL());
                }
            } catch (Exception e) {
                throw new EtailBookItException("Exception occurred while file upload");
            }
            waiverOfLiabilityRepository.save(waiverOfLiability);
        }
        configurationRepository.save(configuration);
    }

    @Override
    public WaiverOfLiability getWaiverOfLiabilityInformation() {
        WaiverOfLiability waiverOfLiability = waiverOfLiabilityRepository.findByRetailer(RetailerContext.getRetailer());
        Configuration configuration = configurationRepository.findByNameAndRetailer(WAIVER_OF_LIABILITY, RetailerContext.getRetailer());
        if (configuration != null) {
            waiverOfLiability.setActive(configuration.isActive());
        }
        return waiverOfLiability;
    }

    @Override
    public String getWaiverOfLiabilityInformationByPetId(Integer id) {
        String fileUrl=null;
        PetWaiverOfLiabilityInfo petWaiverOfLiabilityInfo= petWaiverOfLiabilityInfoRepository.findByPet(id,RetailerContext.getRetailer());
        if(petWaiverOfLiabilityInfo!=null){
            fileUrl=petWaiverOfLiabilityInfo.getSignedFile();
        }
        return fileUrl;
    }

    @Override
    public String getWaiverOfLiabilityInformationByAppointmentId(Integer appointmentId) {
        String fileUrl=null;
        AppointmentWaiverOfLiabilityInformation appointmentWaiverOfLiabilityInformation=appointmentWaiverOfLiabilityInformationRepository.findByAppointmentId(appointmentId,RetailerContext.getRetailer());
        if(appointmentWaiverOfLiabilityInformation!=null){
            fileUrl=appointmentWaiverOfLiabilityInformation.getFileUrl();
        }
        return fileUrl;
    }
}
