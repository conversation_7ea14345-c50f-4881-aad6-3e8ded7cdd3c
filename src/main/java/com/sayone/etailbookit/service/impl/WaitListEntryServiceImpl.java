package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.component.AuthenticationEcom;
import com.sayone.etailbookit.component.CustomerSMSNotification;
import com.sayone.etailbookit.client.POSApiClient;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.dto.POSCustomerResponseDto;
import com.sayone.etailbookit.dto.TimeSlotWaitListDto;
import com.sayone.etailbookit.dto.ServiceInfoDto;
import com.sayone.etailbookit.mapper.WaitlistEntryMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.AppointmentRepository;
import com.sayone.etailbookit.repository.CustomerRepository;
import com.sayone.etailbookit.repository.TimeSlotRepository;
import com.sayone.etailbookit.repository.WaitListEntryRepository;
import com.sayone.etailbookit.service.WaitListEntryService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import lombok.SneakyThrows;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;


import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Service
public class WaitListEntryServiceImpl implements WaitListEntryService {

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    TimeSlotRepository timeSlotRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    WaitListEntryRepository waitListEntryRepository;
    
    @Autowired
    WaitlistEntryMapper waitlistEntryMapper;
    
    @Autowired
    CustomerSMSNotification customerSMSNotification;

    @Autowired
    AuthenticationEcom authenticationEcom;
    
    @Autowired
    POSApiClient posApiClient;

    @Value("${ecom.auth.uri}")
    String auth_Uri;

    @Override
    public BaseResponseDto addToWaitList(WaitlistFromAppointmentRequest waitlistFromAppointmentRequest, String retailer) throws EtailBookItException {
        Integer appointmentId = waitlistFromAppointmentRequest.getAppointmentId();
        List<Integer> customerIds = waitlistFromAppointmentRequest.getCustomerIds();
        
        if (customerIds == null || customerIds.isEmpty()) {
            throw new EtailBookItException("At least one customer ID is required");
        }

        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);

        if (!appointmentOpt.isPresent()) {
          throw new EtailBookItException("Appointment not found with id ::"+appointmentId);
        }

        Appointment appointment = appointmentOpt.get();

        // Validate timeslot
        Integer timeSlotId = appointment.getTimeSlotId();
        if (timeSlotId == null) {
            throw  new EtailBookItException("Appointment has no associated TimeSlot");
        }

        Optional<TimeSlots> slotOpt = timeSlotRepository.findById(timeSlotId);
        if (!slotOpt.isPresent()) {
           throw new EtailBookItException("TimeSlot not found");
        }

        // Find all customers
        Set<Customer> customers = new HashSet<>();
        for (Integer ecomId : customerIds) {
            Customer customer = customerRepository.findByEcomIdandRetailer(ecomId, retailer);
            if (customer == null) {
                // Customer not found in Bookit DB, try to fetch from POS API and create
                try {
                    System.out.println("Customer not found in Bookit DB, fetching from POS API for ecom_id: " + ecomId);
                    
                    // Get customer details from POS API
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    if (attributes == null) {
                        throw new EtailBookItException("Could not access request context");
                    }
                    
                    HttpServletRequest request = attributes.getRequest();
                    String authorizationHeader = request.getHeader("Authorization");
                    if (authorizationHeader == null) {
                        throw new EtailBookItException("Authorization header not found in request");
                    }
                    
                    // Call POS API to get customer details
                    POSCustomerResponseDto posResponse = posApiClient.getCustomerListByIds(
                            authorizationHeader,
                            retailer,
                            List.of(ecomId)
                    );
                    
                    if (posResponse != null && posResponse.getResponseData() != null && 
                        posResponse.getResponseData().getData() != null && 
                        !posResponse.getResponseData().getData().isEmpty()) {
                        
                        POSCustomerResponseDto.POSCustomerData posCustomerData = posResponse.getResponseData().getData().get(0);
                        
                        // Create new Customer entity with POS data
                        Customer newCustomer = new Customer();
                        newCustomer.setEcom_id(ecomId);
                        newCustomer.setRetailer(retailer);
                        
                        // Parse customer name from POS data
                        String firstName = posCustomerData.getFirstName();
                        String lastName = posCustomerData.getLastName();
                        
                        if (firstName != null && !firstName.trim().isEmpty()) {
                            newCustomer.setFirstName(firstName.trim());
                        } else {
                            newCustomer.setFirstName("Unknown");
                        }
                        
                        if (lastName != null && !lastName.trim().isEmpty()) {
                            newCustomer.setLastName(lastName.trim());
                        } else {
                            newCustomer.setLastName("Customer");
                        }
                        
                        // Set phone number from POS data
                        newCustomer.setPhoneNumber(posCustomerData.getPhone());
                        // Note: is_active defaults to true in the model, so no need to set it explicitly
                        
                        // Save the new customer to Bookit DB
                        Customer savedCustomer = customerRepository.save(newCustomer);
                        System.out.println("Successfully created and saved new customer: " + savedCustomer.getFirstName() + " " + savedCustomer.getLastName());
                        
                        customers.add(savedCustomer);
                    } else {
                        throw new EtailBookItException("Customer not found in POS API with ecom_id: " + ecomId);
                    }
                    
                } catch (Exception e) {
                    System.err.println("Failed to fetch customer from POS API: " + e.getMessage());
                    throw new EtailBookItException("Failed to fetch customer details from POS API for ecom_id: " + ecomId + ". Error: " + e.getMessage());
                }
            } else {
                customers.add(customer);
            }
        }
        
        // Check if any customer is already in the waitlist for this time slot
        for (Customer customer : customers) {
            List<WaitlistEntry> existingEntries = waitListEntryRepository.findByTimeSlotAndCustomer(timeSlotId, customer.getEcom_id(), retailer);
            if (!existingEntries.isEmpty()) {
                throw new BadRequestException("Customer " + customer.getFirstName() + " " + customer.getLastName() +
                    " (ID: " + customer.getEcom_id() + ") is already in the waitlist for this time slot");
            }
        }

        // Create waitlist entry with multiple customers
        WaitlistEntry entry = new WaitlistEntry();
        entry.setTimeSlot(slotOpt.get());
        entry.setAppointment(appointment);
        entry.setCustomers(customers);
        entry.setNotified(false);
        entry.setBooked(false);

        waitListEntryRepository.save(entry);

        return new BaseResponseDto(Status.SUCCESS, 
            "Successfully added " + customers.size() + " customer(s) to waitlist");
    }
    
    @Override
    public List<WaitlistEntryDto> getWaitlistEntriesByTimeSlot(Integer timeSlotId, String retailer) throws EtailBookItException {
        List<WaitlistEntry> entries = waitListEntryRepository.findByTimeSlotId(timeSlotId);
        
        // Process entries with customer details from POS API
        List<WaitlistTableResponseDto> processedEntries = processWaitlistEntriesWithCustomerDetails(entries);
        
        // Create a map of customer ID to processed customer details for quick lookup
        Map<Integer, WaitlistTableResponseDto> customerDetailsMap = processedEntries.stream()
                .collect(Collectors.toMap(
                    WaitlistTableResponseDto::getCustomerId,
                    entry -> entry
                ));
        
        // Convert to WaitlistEntryDto using the mapper, but enhance with processed customer details
        return entries.stream()
                .map(entry -> {
                    WaitlistEntryDto dto = waitlistEntryMapper.toDto(entry);
                    
                    // Enhance the DTO with processed customer details if available
                    if (entry.getCustomers() != null && !entry.getCustomers().isEmpty()) {
                        Customer firstCustomer = entry.getCustomers().iterator().next();
                        WaitlistTableResponseDto processedDetails = customerDetailsMap.get(firstCustomer.getEcom_id());
                        
                        if (processedDetails != null) {
                            // Update the customer details in the DTO with the processed data from POS API
                            dto.getCustomers().forEach(customerDto -> {
                                if (customerDto.getId().equals(firstCustomer.getEcom_id())) {
                                    // Update with enhanced customer details from POS API
                                    customerDto.setFirstName(processedDetails.getCustomerName().split(" ")[0]);
                                    if (processedDetails.getCustomerName().split(" ").length > 1) {
                                        customerDto.setLastName(processedDetails.getCustomerName().split(" ")[1]);
                                    }
                                    customerDto.setPhoneNumber(processedDetails.getPhone());
                                }
                            });
                        }
                    }
                    
                    return dto;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public BaseResponseDto removeCustomerFromWaitlist(Integer waitlistEntryId, Integer customerEcomId, String retailer) throws EtailBookItException {
        Optional<WaitlistEntry> entryOpt = waitListEntryRepository.findById(waitlistEntryId);
        
        if (!entryOpt.isPresent()) {
            throw new EtailBookItException("Waitlist entry not found with id: " + waitlistEntryId);
        }
        
        WaitlistEntry entry = entryOpt.get();
        Set<Customer> customers = entry.getCustomers();
        
        // Find and remove the specific customer
        Customer customerToRemove = customers.stream()
                .filter(c -> c.getEcom_id().equals(customerEcomId))
                .findFirst()
                .orElse(null);
                
        if (customerToRemove == null) {
            throw new EtailBookItException("Customer not found in this waitlist entry");
        }
        
        customers.remove(customerToRemove);
        entry.setCustomers(customers);
        
        // If no customers left, delete the entire entry
        if (customers.isEmpty()) {
            waitListEntryRepository.delete(entry);
            return new BaseResponseDto(Status.SUCCESS, "Customer removed and waitlist entry deleted (no customers remaining)");
        } else {
            waitListEntryRepository.save(entry);
            return new BaseResponseDto(Status.SUCCESS, "Customer removed from waitlist");
        }
    }
    
    @Override
    public BaseResponseDto markWaitlistAsNotified(Integer waitlistEntryId, String retailer) throws EtailBookItException {
        Optional<WaitlistEntry> entryOpt = waitListEntryRepository.findById(waitlistEntryId);
        
        if (!entryOpt.isPresent()) {
            throw new EtailBookItException("Waitlist entry not found with id: " + waitlistEntryId);
        }
        
        WaitlistEntry entry = entryOpt.get();
        entry.setNotified(true);
        waitListEntryRepository.save(entry);
        
        return new BaseResponseDto(Status.SUCCESS, "Waitlist marked as notified");
    }
    
    @Override
    public BaseResponseDto markWaitlistAsBooked(Integer waitlistEntryId, String retailer) throws EtailBookItException {
        Optional<WaitlistEntry> entryOpt = waitListEntryRepository.findById(waitlistEntryId);
        
        if (!entryOpt.isPresent()) {
            throw new EtailBookItException("Waitlist entry not found with id: " + waitlistEntryId);
        }
        
        WaitlistEntry entry = entryOpt.get();
        entry.setBooked(true);
        waitListEntryRepository.save(entry);
        
        return new BaseResponseDto(Status.SUCCESS, "Waitlist marked as booked");
    }
    
    @Override
    public BaseResponseDto bulkUpdateWaitlist(WaitlistBulkUpdateRequest request, String retailer) throws EtailBookItException {
        Integer timeSlotId = request.getTimeSlotId();
        Integer appointmentId = request.getAppointmentId();
        List<Integer> customerIdsToRemove = request.getCustomerIdsToRemove();
        List<Integer> customerIdsToAdd = request.getCustomerIdsToAdd();

        // Validate timeSlot
        Optional<TimeSlots> slotOpt = timeSlotRepository.findById(timeSlotId);
        if (!slotOpt.isPresent()) {
            throw new EtailBookItException("TimeSlot not found with id: " + timeSlotId);
        }

        // Validate appointment if provided
        Appointment appointment = null;
        if (appointmentId != null) {
            Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
            if (!appointmentOpt.isPresent()) {
                throw new EtailBookItException("Appointment not found with id: " + appointmentId);
            }
            appointment = appointmentOpt.get();
        }

        // Remove customers from waitlist
        if (customerIdsToRemove != null && !customerIdsToRemove.isEmpty()) {
            for (Integer customerEcomId : customerIdsToRemove) {
                // Find waitlist entries for this customer and timeSlot
                List<WaitlistEntry> entries = waitListEntryRepository.findByTimeSlotAndCustomer(timeSlotId, customerEcomId, retailer);

                for (WaitlistEntry entry : entries) {
                    Set<Customer> customers = entry.getCustomers();
                    Customer customerToRemove = customers.stream()
                            .filter(c -> c.getEcom_id().equals(customerEcomId))
                            .findFirst()
                            .orElse(null);

                    if (customerToRemove != null) {
                        customers.remove(customerToRemove);

                        // If no customers left, delete the entire entry
                        if (customers.isEmpty()) {
                            waitListEntryRepository.delete(entry);
                        } else {
                            entry.setCustomers(customers);
                            waitListEntryRepository.save(entry);
                        }
                    }
                }
            }
        }

        // Add customers to waitlist
        if (customerIdsToAdd != null && !customerIdsToAdd.isEmpty()) {
            // Find all customers to add
            Set<Customer> customersToAdd = new HashSet<>();
            for (Integer ecomId : customerIdsToAdd) {
                Customer customer = customerRepository.findByEcomIdandRetailer(ecomId, retailer);
                if (customer == null) {
                    throw new EtailBookItException("Customer not found with ecom_id: " + ecomId);
                }
                customersToAdd.add(customer);
            }

            // Check if any customer is already in the waitlist for this time slot
            for (Customer customer : customersToAdd) {
                List<WaitlistEntry> existingEntries = waitListEntryRepository.findByTimeSlotAndCustomer(timeSlotId, customer.getEcom_id(), retailer);
                if (!existingEntries.isEmpty()) {
                    throw new EtailBookItException("Customer " + customer.getFirstName() + " " + customer.getLastName() +
                            " (ID: " + customer.getEcom_id() + ") is already in the waitlist for this time slot");
                }
            }

            // Create new waitlist entry
            WaitlistEntry newEntry = new WaitlistEntry();
            newEntry.setTimeSlot(slotOpt.get());
            newEntry.setAppointment(appointment);
            newEntry.setCustomers(customersToAdd);
            newEntry.setNotified(false);
            newEntry.setBooked(false);

            waitListEntryRepository.save(newEntry);
        }
        String message = String.format("Bulk update completed");

        return new BaseResponseDto(Status.SUCCESS, message);
    }


    @Override
    public Page<WaitlistTableResponseDto> findAllWaitListedEntries(String retailer, int pageNo, int pageSize, String sortBy){
        // Default sorting by createdAt if no sortBy is provided or if it's invalid
        Sort sort = Sort.by(sortBy).descending();
        if (sortBy != null && !sortBy.trim().isEmpty()) {
            try {
                sort = Sort.by(sortBy).descending();
            } catch (Exception e) {
                // If invalid sort field, use default sorting
                sort = Sort.by("createdAt").descending();
            }
        }
        
        Pageable paging = PageRequest.of(pageNo, pageSize, sort);
        Page<WaitlistEntry> waitlistEntriesPage = waitListEntryRepository.findAllByRetailer(retailer, paging);
        
        // Process all entries and fetch customer details efficiently
        List<WaitlistTableResponseDto> processedEntries = processWaitlistEntriesWithCustomerDetails(waitlistEntriesPage.getContent());
        
        // Create a new page with processed data
        return new PageImpl<>(processedEntries, paging, waitlistEntriesPage.getTotalElements());
    }

    private List<WaitlistTableResponseDto> processWaitlistEntriesWithCustomerDetails(List<WaitlistEntry> entries) {
        if (entries == null || entries.isEmpty()) {
            return List.of();
        }

        // Collect all unique customer IDs from all entries
        Set<Integer> allCustomerIds = entries.stream()
                .filter(entry -> entry.getCustomers() != null)
                .flatMap(entry -> entry.getCustomers().stream())
                .map(Customer::getEcom_id)
                .collect(Collectors.toSet());

        System.out.println("Processing " + entries.size() + " waitlist entries with " + allCustomerIds.size() + " unique customers");

        // Fetch all customer details from POS API in one call
        Map<Integer, POSCustomerResponseDto.POSCustomerData> customerDetailsMap = new HashMap<>();
        if (!allCustomerIds.isEmpty()) {
            try {
                System.out.println("Fetching customer details from POS API for customer IDs: " + allCustomerIds);
                ResponseEntity<POSCustomerResponseDto> customerDetailsResponse = 
                    getCustomerDetailsFromPOS(new ArrayList<>(allCustomerIds));
                
                if (customerDetailsResponse.getBody() != null && 
                    customerDetailsResponse.getBody().getResponseData() != null &&
                    customerDetailsResponse.getBody().getResponseData().getData() != null) {
                    
                    customerDetailsResponse.getBody().getResponseData().getData().forEach(customerData -> 
                        customerDetailsMap.put(customerData.getCustomerId(), customerData));
                    
                    System.out.println("Successfully fetched " + customerDetailsMap.size() + " customer details from POS API");
                }
            } catch (Exception e) {
                System.err.println("Failed to fetch customer details from POS API: " + e.getMessage());
                System.err.println("Continuing with local customer data for waitlist entries");
                // Continue with local data if POS API fails - this allows other parts of the API to work
            }
        }

        // Process each entry using the fetched customer details
        return entries.stream()
                .map(entry -> mapToDtoWithCustomerDetails(entry, customerDetailsMap))
                .collect(Collectors.toList());
    }

    private WaitlistTableResponseDto mapToDtoWithCustomerDetails(WaitlistEntry entry, 
                                                               Map<Integer, POSCustomerResponseDto.POSCustomerData> customerDetailsMap) {
        TimeSlots timeSlot = entry.getTimeSlot();
        TimeSlotWaitListDto timeSlotWaitListDto = null;
        List<ServiceInfoDto> serviceInfoDtos=new ArrayList<>();
        if (timeSlot != null) {
            timeSlotWaitListDto = new TimeSlotWaitListDto();
            String slotName = timeSlot.getSlotName();
            boolean isSlotBooked = timeSlot.isSlotBooked();
            Integer attendantId = timeSlot.getAttendant().getAttendantId();
            String attendantName = timeSlot.getAttendant().getFirstName();
            String venueName = timeSlot.getVenue().getPublicName();
            Integer venueId = timeSlot.getVenue().getVenueId();
            Set<com.sayone.etailbookit.model.Service> services =timeSlot.getServices();
            for (com.sayone.etailbookit.model.Service service : services) {
               ServiceInfoDto serviceInfoDto=new ServiceInfoDto();
               serviceInfoDto.setServiceId(service.getServiceId());
               serviceInfoDto.setServiceName(service.getName());
               serviceInfoDtos.add(serviceInfoDto);
            }
            timeSlotWaitListDto.setSlotBooked(isSlotBooked);
            timeSlotWaitListDto.setServiceInfoDtos(serviceInfoDtos);
            timeSlotWaitListDto.setVenueId(venueId);
            timeSlotWaitListDto.setVenueName(venueName);
            timeSlotWaitListDto.setAttendantId(attendantId);
            timeSlotWaitListDto.setAttendantFirstName(attendantName);
            timeSlotWaitListDto.setSlotName(slotName);
            timeSlotWaitListDto.setSlotStartTime(timeSlot.getSlotStartTime());
            timeSlotWaitListDto.setSlotEndTime(timeSlot.getSlotEndTime());
            timeSlotWaitListDto.setTimeSlotId(timeSlot.getId());
        }

        // Get the first customer from the set (or handle multiple customers as needed)
        Customer customer = entry.getCustomers() != null && !entry.getCustomers().isEmpty()
                ? entry.getCustomers().iterator().next()
                : null;

        String customerName = "";
        String phone = "";
        String email = "";
        Integer customerId = 0;
        
        if (customer != null) {
            customerId = customer.getEcom_id();
            
            // Try to get customer details from the map (POS API data)
            POSCustomerResponseDto.POSCustomerData customerDetails = customerDetailsMap.get(customerId);
            
            if (customerDetails != null) {
                // Use POS API data
                customerName = customerDetails.getFirstName() + " " + customerDetails.getLastName();
                phone = customerDetails.getPhone();
                email = customerDetails.getEmail();
            } else {
                // Fallback to local data
                customerName = customer.getFirstName() + " " + customer.getLastName();
                phone = customer.getPhoneNumber();
                email = "";
            }
        }

        return WaitlistTableResponseDto.builder()
                .customerName(customerName)
                .phone(phone)
                .email(email)
                .customerId(customerId)
                .timeSlotWaitListDto(timeSlotWaitListDto)
                .build();
    }
    private WaitlistTableResponseDto mapToDto(WaitlistEntry entry) throws EtailBookItException {
        // Use the new efficient method with empty map for single entry processing
        return mapToDtoWithCustomerDetails(entry, new HashMap<>());
    }

  /*  private ResponseEntity<EcommCustomerDetails> getCustomerDetails(Integer customerId) throws EtailBookItException {
        try {
            String accessToken = getAccessTokenFromRequest();

        return authenticationEcom.getCustomerDeails(
                customerId,
                    accessToken,
                RetailerContext.getRetailer()
        );
        } catch (Exception e) {
            System.err.println("Failed to get customer details due to missing token: " + e.getMessage());
            throw new EtailBookItException("Failed to get customer details: " + e.getMessage());
        }
    }*/

   /* private ResponseEntity<POSCustomerResponseDto> getCustomerDetailsFromPOS(Integer customerId) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String accessToken = authToken.getBody().getAccess_token();

        System.out.println("Fetching customer details from POS API for customer ID: " + customerId);
        return authenticationEcom.getCustomerListByIds(
                List.of(customerId),
                accessToken,
                RetailerContext.getRetailer()
        );
    }*/

    private ResponseEntity<POSCustomerResponseDto> getCustomerDetailsFromPOS(List<Integer> customerIds) throws EtailBookItException {
        System.out.println("Fetching customer details from POS API for customer IDs: " + customerIds);
        
        try {
            String retailer = RetailerContext.getRetailer();
            
            // Get the Authorization header directly from the current request
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new EtailBookItException("Could not access request context");
            }
            
            HttpServletRequest request = attributes.getRequest();
            String authorizationHeader = request.getHeader("Authorization");
            if (authorizationHeader == null) {
                throw new EtailBookItException("Authorization header not found in request");
            }
            
            // Use FeignClient to make the API call with the authorization header
            POSCustomerResponseDto response = posApiClient.getCustomerListByIds(
                    authorizationHeader,
                    retailer,
                    customerIds
            );
            
            // Wrap in ResponseEntity to maintain compatibility
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("Failed to fetch customer details from POS API: " + e.getMessage());
            throw new EtailBookItException("Failed to fetch customer details from POS API: " + e.getMessage());
        }
    }





    @Override
    public BaseResponseDto notifyWaitlistCustomersForSlotAvailability(Integer timeSlotId, String retailer, String timeZone) throws EtailBookItException {
        // Validate time slot exists
        Optional<TimeSlots> timeSlotOpt = timeSlotRepository.findById(timeSlotId);
        if (!timeSlotOpt.isPresent()) {
            throw new EtailBookItException("Time slot not found with id: " + timeSlotId);
        }
        
        TimeSlots timeSlot = timeSlotOpt.get();
        
        // Find all waitlist entries for this time slot
        List<WaitlistEntry> waitlistEntries = waitListEntryRepository.findByTimeSlotId(timeSlotId);
        
        if (waitlistEntries.isEmpty()) {
            return new BaseResponseDto(Status.SUCCESS, "No waitlist entries found for time slot: " + timeSlotId);
        }
        
        int notificationCount = 0;
        int failedNotifications = 0;
        
        System.out.println("Found " + waitlistEntries.size() + " waitlist entries for time slot " + timeSlotId);
        
        for (WaitlistEntry entry : waitlistEntries) {

            System.out.println("Processing waitlist entry " + entry.getId() + " with " + entry.getCustomers().size() + " customers");
            
            // Notify each customer in the waitlist entry
            for (Customer customer : entry.getCustomers()) {
                try {
                    System.out.println("Sending notification to customer " + customer.getEcom_id() + " (" + customer.getFirstName() + " " + customer.getLastName() + ")");
                    
                    // Create a temporary appointment object for notification
                    Appointment tempAppointment = new Appointment();
                    tempAppointment.setCustomerId(customer.getEcom_id());
                    tempAppointment.setTimeSlotId(timeSlotId);
                    tempAppointment.setAppointmentStartDateAndTime(timeSlot.getSlotStartTime().toString());
                    tempAppointment.setTime(timeSlot.getSlotStartTime().toOffsetTime());
                    
                    // Send waitlist availability notification
                    customerSMSNotification.sendWaitlistAvailabilityNotification(tempAppointment, timeZone, timeSlot);
                    
                    notificationCount++;
                    System.out.println("Successfully sent notification to customer " + customer.getEcom_id());
                } catch (Exception e) {
                    failedNotifications++;
                    // Log the error but continue with other notifications
                    System.err.println("Failed to send notification to customer " + customer.getEcom_id() + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }
            waitListEntryRepository.save(entry);
            System.out.println("Marked waitlist entry " + entry.getId() + " as notified");
        }
        
        String message = String.format("Successfully notified %d customers, %d notifications failed", 
                                     notificationCount, failedNotifications);
        
        System.out.println("Waitlist notification completed: " + message);
        return new BaseResponseDto(Status.SUCCESS, message);
    }



}
