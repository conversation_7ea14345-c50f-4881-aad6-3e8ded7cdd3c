package com.sayone.etailbookit.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.event.SlotUpdateEvent;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.kinesis.KinesisClient;
import software.amazon.awssdk.services.kinesis.model.PutRecordRequest;
import software.amazon.awssdk.services.kinesis.model.PutRecordResponse;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class SlotUpdatePublisher {

    private final KinesisClient kinesisClient;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Value("${kinesis.stream-name}")
    private String streamName;

    @Value("${kinesis.shard.id.slots}")
    private String slotGenerationShardId; // Same shard as slot creation

    private final AtomicInteger recordCount = new AtomicInteger(0);

    public SlotUpdatePublisher(ObjectMapper objectMapper, ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
        this.kinesisClient = KinesisClient.create();
        this.objectMapper = objectMapper;
    }

    public void publishSlotUpdateEvent(ServiceSlotsDto serviceSlotsDto, Integer attendantId, Integer timeSlotClusterId, String retailer) throws EtailBookItException {
        try {
            // Prepare the JSON payload
            String message = objectMapper.writeValueAsString(Map.of(
                    "serviceSlotsDto", serviceSlotsDto,
                    "attendantId", attendantId,
                    "timeSlotClusterId", timeSlotClusterId,
                    "retailer", retailer,
                    "eventType", "SLOT_UPDATE"
            ));

            String formattedShardId = "shardId-" + String.format("%012d", Integer.parseInt(slotGenerationShardId));

            PutRecordRequest request = PutRecordRequest.builder()
                    .streamName(streamName)
                    .partitionKey(formattedShardId) // Ensure it routes to the correct shard
                    .data(SdkBytes.fromUtf8String(message))
                    .build();

            PutRecordResponse response = kinesisClient.putRecord(request);

            if (response.sdkHttpResponse().isSuccessful()) {
                int count = recordCount.incrementAndGet();
                System.out.println("Slot Update Published. Sequence Number: " + response.sequenceNumber());
                System.out.println("Total Updates Sent: " + count);
                eventPublisher.publishEvent(new SlotUpdateEvent(this, serviceSlotsDto, attendantId, timeSlotClusterId, retailer));
            } else {
                throw new EtailBookItException("Failed to publish slot update to Kinesis. HTTP Status: " + response.sdkHttpResponse().statusCode());
            }
        } catch (Exception e) {
            throw new EtailBookItException("Failed to publish slot update event: " + e.getMessage());
        }
    }
}

