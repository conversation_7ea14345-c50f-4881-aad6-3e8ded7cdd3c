package com.sayone.etailbookit.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.itextpdf.signatures.DigestAlgorithms;
import com.itextpdf.signatures.PdfSigner;
import com.itextpdf.text.DocumentException;
import com.sayone.etailbookit.Batch.ReaderConfiguration;
import com.sayone.etailbookit.Batch.WriterConfiguration;
import com.sayone.etailbookit.component.AuthenticationEcom;
import com.sayone.etailbookit.component.CustomerSMSNotification;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.PetMapper;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.PetDetailsProjection;
import com.sayone.etailbookit.projections.PetDropdownProjection;
import com.sayone.etailbookit.projections.PetListingProjection;
import com.sayone.etailbookit.projections.PetProjection;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.IPetService;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.validator.Validator;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PetService implements IPetService {

    @Autowired
    PetRepository petRepository;

    @Autowired
    HairLengthRepository hairLengthRepository;

    @Autowired
    HairTextureRepository hairTextureRepository;

    @Autowired
    VetInformationRepository vetInformationRepository;

    @Autowired
    AllergiesRepository allergiesRepository;

    @Autowired
    TemperamentRepository temperamentRepository;

    @Autowired
    ThreatReactionRepository threatReactionRepository;

    @Autowired
    PersonalityParameterRepository personalityParameterRepository;

    @Autowired
    DesiredHairLengthRepository desiredHairLengthRepository;

    @Autowired
    VaccinationRecordsRepository vaccinationRecordsRepository;

    @Autowired
    EmergencyContactInfoRepository emergencyContactInfoRepository;

    @Autowired
    PetVaccinationRecordRepository petVaccinationRecordRepository;

    @Autowired
    PetVetInformationRepository petVetInformationRepository;

    @Autowired
    PetEmergencyContactRepository petEmergencyContactRepository;

    @Autowired
    DocumentOptionRepository documentOptionRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    BittingHistoryRepository bittingHistoryRepository;

    @Autowired
    UnfriendlyBehaviourTriggerRepository unfriendlyBehaviourTriggerRepository;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    PetDocumentRepository petDocumentRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    WaiverOfLiabilityRepository waiverOfLiabilityRepository;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    PetTypeConfigurationRepository petTypeConfigurationRepository;

    @Autowired
    PetBreedsInformationRepository petBreedsInformationRepository;

    @Autowired
    BreedRepository breedRepository;

    private static final String EXTENDED_BEHAVIOUR = "extended_behaviour";

    @Autowired
    S3Integration s3Integration;

    @Autowired
    EsignService esignService;

    @Autowired
    JobLauncher jobLauncher;

    @Autowired
    Job petJob;

    @Autowired
    WriterConfiguration writerConfiguration;

    @Autowired
    ReaderConfiguration readerConfiguration;


    @Autowired
    AuthenticationEcom authenticationEcom;

    @Autowired
    UserTimeZoneConversion userTimeZoneConversion;

    @Autowired
    CustomerSMSNotification customerSMSNotification;

    @Value("${ecom.auth.uri}")
    String auth_Uri;

    RestTemplate restTemplate = new RestTemplate();


    public static final String RESULT_FILE = new String( "_petDocument_signed.pdf");


    @Value("${esignservice.keystore}")
    private String KEYSTORE;

    @Value("${esignservice.keystore.password}")
    private String pass;
    private static Logger LOGGER = LoggerFactory.getLogger(PetService.class);
    private static DateTimeFormatter monthDateYearFormat = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public void addPet(PetDto petDto) throws EtailBookItException {
        createOrUpdatePet(petDto, null);
    }
    @Override
    @Transactional
    public void updatePetWithCustomer(PetCustomerDto petCustomerDto, Integer petId, Integer customerId) throws EtailBookItException {
        if (petCustomerDto.getName() == null) {
            throw new BadRequestException("Pet name cannot be empty");
        }
        Pet pet= petRepository.findByIdAndCustomer(petId,customerId);
        if(pet!=null){
            if(petCustomerDto.getDob()!=null) {
                try {
                    pet.setDob(LocalDate.parse(petCustomerDto.getDob()));
                } catch (Exception e) {
                    throw new BadRequestException("Invalid date format");
                }
            }
            if (petCustomerDto.getPetTypeId() != null) {
                PetProjection petTypeName = petTypeRepository.getPetTypeName(petCustomerDto.getPetTypeId());
                if (petTypeName == null)
                    throw new EntityNotFoundException("Pet type not found");
                PetType petType = new PetType();
                petType.setPetTypeId(petCustomerDto.getPetTypeId());
                petType.setName(petTypeName.getPetTypeName());
                pet.setPetType(petType);
            }
            pet.setRetailer(RetailerContext.getRetailer());
            pet.setName(petCustomerDto.getName());
          /*  if(petCustomerDto.getBreedId()!=null){
                Optional<Breed> breed = breedRepository.findById(petCustomerDto.getBreedId());
               Set<PetBreedsInformation> petBreedsInformations=pet.getPetBreedsInformations();
               if(!petBreedsInformations.isEmpty()) {
                   for (PetBreedsInformation petBreedsInformation : petBreedsInformations) {
                       petBreedsInformation.setBreed(breed.get());
                   }
               }else{
                   PetBreedsInformation petBreedsInformation=new PetBreedsInformation();
                   petBreedsInformation.setPet(pet);
                   petBreedsInformation.setBreed(breed.get());
                   petBreedsInformation.setRetailer(RetailerContext.getRetailer());
                   petBreedsInformations.add(petBreedsInformation);
               }
                pet.setPetBreedsInformations(petBreedsInformations);
            }
            if(petCustomerDto.getExactWeight()!=null){
                if(petCustomerDto.getExactWeight().intValue()!=0){
                    pet.setExactWeight(petCustomerDto.getExactWeight());
                    pet.setWeightUnit(petCustomerDto.getWeightUnit());
                }
            }
            if(petCustomerDto.getWeightRangeId()!=null){
                WeightRange weightRange=weightRangeRepository.findByWeightRangeId(petCustomerDto.getWeightRangeId());
                weightRange.setWeightUnit(petCustomerDto.getWeightUnit());
                pet.setWeightRange(weightRange);
                pet.setWeightUnit(petCustomerDto.getWeightUnit());
            }*/
            petRepository.save(pet);
        }
        else
            throw new EntityNotFoundException("Pet not found");
    }

    @Override
    @Transactional
    public void updatePet(PetDto petDto, Integer petId) throws EtailBookItException {
        Optional<Pet> pet = petRepository.findById(petId);

        if (pet.isPresent()) {
            if(pet.get().getName().equalsIgnoreCase("Browny DEV")){
                throw new EtailBookItException("This is test data and can not be updated");
            }
            List<String> documentURLs = new ArrayList<>();
            List<String> petPhotoURLs = new ArrayList<>();
            Set<PetDocuments> petDocuments = pet.get().getPetDocuments();

            if (Utils.isNotEmpty(petDto.getDocuments())) {
                for (PetDocuments petDocument : petDocuments) {
                    if(!StringUtils.isEmpty(petDocument.getFile()))
                        documentURLs.add(petDocument.getFile());
                }
            }

            if (Utils.isNotEmpty(petDto.getPhotos()))
                petPhotoURLs = pet.get().getPhotos();

            createOrUpdatePet(petDto, petId);

            try {
                if (Utils.isNotEmpty(petPhotoURLs))
                    FileUploadUtil.deleteMultipleFiles(petPhotoURLs, amazonS3, dataFileProperties.getBucketName(), dataFileProperties.getPetPhotosPrefix());
                if (Utils.isNotEmpty(documentURLs))
                    FileUploadUtil.deleteMultipleFiles(documentURLs, amazonS3, dataFileProperties.getBucketName(), dataFileProperties.getPetDocumentPrefix());
            } catch (Exception e) {
                LOGGER.error("Exception occurred while deleting pet files :::::: " + e);
            }

        }
    }

    public void createOrUpdatePet(PetDto petDto, Integer petId) throws EtailBookItException {
        Validator.validatePet(petDto, petId, petRepository);
        Pet pet = PetMapper.toPetEntity(petDto);

        if (petDto.getPetTypeId() != null) {
            PetProjection petTypeName = petTypeRepository.getPetTypeName(petDto.getPetTypeId());
            if (petTypeName == null)
                throw new EntityNotFoundException("Pet type not found");
            PetType petType = new PetType();
            petType.setPetTypeId(petDto.getPetTypeId());
            petType.setName(petTypeName.getPetTypeName());
            pet.setPetType(petType);
        }

        if (petDto.getAllergyIds() != null) {
            Set<Allergies> allergies = new HashSet();

            for (Integer allergyId : petDto.getAllergyIds()) {
                Allergies allergy = allergiesRepository.findByAllergyId(allergyId);
                if (allergy != null) {
                    allergies.add(allergy);
                } else {
                    throw new EntityNotFoundException("Allergy not found with ID :::" + allergyId);
                }
            }
            pet.setAllergiesText(null);
            pet.setAllergies(allergies);
        }

       // Configuration configuration = configurationRepository.findByNameAndRetailer(EXTENDED_BEHAVIOUR, RetailerContext.getRetailer());

        /*if (configuration != null && configuration.isActive()) {
            if (petDto.getThreatReactionIds() == null) {
                throw new BadRequestException("Please choose at least one threat reaction");
            }
            if (petDto.getBitingHistoryIds() == null) {
                throw new BadRequestException("Please choose at least one biting history entry");
            }
            if (petDto.getUnfriendlyBehaviourTriggerIds() == null) {
                throw new BadRequestException("Please choose at least one unfriendly behavior trigger");
            }
            if (petDto.getPersonalityParameterIds() == null) {
                throw new BadRequestException("Please choose at least one personality parameter");
            }
        }*/

        if (petDto.getBitingHistoryIds() != null) {
            Set<BittingHistory> bitingHistories = new HashSet();
            for (Integer bitingHistoryId : petDto.getBitingHistoryIds()) {
                Optional<BittingHistory> bitingHistory = bittingHistoryRepository.findById(bitingHistoryId);
                if (bitingHistory.isPresent()) {
                    bitingHistories.add(bitingHistory.get());
                } else {
                    throw new EntityNotFoundException("BitingHistory not found with ID :::" + bitingHistoryId);
                }
            }
            pet.setBitingHistories(bitingHistories);
        }

        if (petDto.getThreatReactionIds() != null) {
            Set<ThreatReaction> threatReactions = new HashSet();
            for (Integer threatReactionId : petDto.getThreatReactionIds()) {
                Optional<ThreatReaction> threatReaction = threatReactionRepository.findById(threatReactionId);
                if (threatReaction.isPresent()) {
                    threatReactions.add(threatReaction.get());
                } else {
                    throw new EntityNotFoundException("ThreatReaction not found with ID :::" + threatReactionId);
                }
            }
            pet.setThreatReactions(threatReactions);
        }

        PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.EMERGENCY_CONTACT_INFO, pet.getPetType(), RetailerContext.getRetailer());
        if(petTypeConfiguration!=null) {
            if (petTypeConfiguration.getDisplayType().equals(DisplayType.OPTION) && ObjectUtils.isEmpty(petDto.getEmergencyContactInfo())) {
                throw new BadRequestException("Please enter Emergency contact information");
            }
        }

        if (petDto.getEmergencyContactInfo() != null) {
            Set<PetEmergencyContactInfo> emergencyContactInfos = new HashSet();
            for (EmergencyContactInfoDto emergencyContactInfo : petDto.getEmergencyContactInfo()) {
                if (emergencyContactInfo.getValue().equals(""))
                    throw new BadRequestException("Please enter Emergency contact information");
                EmergencyContactInfo contactInfo = emergencyContactInfoRepository.findByEmergencyContactInfoId(emergencyContactInfo.getId());
                PetEmergencyContactInfo petEmergencyContactInfo = new PetEmergencyContactInfo();
                if (contactInfo != null) {
                    petEmergencyContactInfo.setEmergencyContactInfo(contactInfo);
                } else {
                    throw new EntityNotFoundException("EmergencyContactInfo not found with ID :::" + contactInfo.getEmergencyContactInfoId());
                }
                Optional<PetEmergencyContactInfo> existingPetEmergencyInfo = petEmergencyContactRepository.findByPetIdAndEmergencyContactInfoEmergencyContactInfoId(petId, contactInfo.getEmergencyContactInfoId());
                if (existingPetEmergencyInfo.isPresent()) {
                    petEmergencyContactInfo = existingPetEmergencyInfo.get();
                    petEmergencyContactInfo.setValue(emergencyContactInfo.getValue());
                } else {
                    petEmergencyContactInfo.setValue(emergencyContactInfo.getValue());
                }
                petEmergencyContactInfo.setPet(pet);
                petEmergencyContactInfo.setRetailer(RetailerContext.getRetailer());
                emergencyContactInfos.add(petEmergencyContactInfo);
            }
            pet.setPetEmergencyContactInfo(emergencyContactInfos);
        }

        petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.VET_INFO, pet.getPetType(), RetailerContext.getRetailer());
        if(petTypeConfiguration!=null) {
            if (petTypeConfiguration.getDisplayType().equals(DisplayType.OPTION) && ObjectUtils.isEmpty(petDto.getVetInformation())) {
                throw new BadRequestException("Please enter Vet information");
            }
        }

        if (petDto.getVetInformation() != null) {
            Set<PetVetInformation> petVetInformations = new HashSet();
            for (VetInformationDto vetInformationDto : petDto.getVetInformation()) {
                Optional<VetInformation> vetInformation = vetInformationRepository.findById(vetInformationDto.getId());
                PetVetInformation petVetInformation = new PetVetInformation();
                if (vetInformation.isPresent()) {
                    petVetInformation.setVetInformation(vetInformation.get());
                } else {
                    throw new EntityNotFoundException("VetInformation not found with ID :::" + vetInformationDto.getId());
                }
                Optional<PetVetInformation> existingPetVetInfo = petVetInformationRepository.findByPetIdAndVetInformationId(petId, vetInformation.get().getId());

                if (existingPetVetInfo.isPresent()) {
                    petVetInformation = existingPetVetInfo.get();
                    petVetInformation.setValue(vetInformationDto.getValue());
                } else {
                    petVetInformation.setValue(vetInformationDto.getValue());
                }
                petVetInformation.setPet(pet);
                petVetInformation.setRetailer(RetailerContext.getRetailer());
                petVetInformations.add(petVetInformation);
            }
            pet.setPetVetInformation(petVetInformations);
        }

        if (petDto.getSignedWaiverOfLiabilityInfo() != null) {
            PetWaiverOfLiabilityInfo petWaiverOfLiabilityInfo = new PetWaiverOfLiabilityInfo();
            WaiverOfLiability waiverOfLiability = waiverOfLiabilityRepository.findByRetailer(RetailerContext.getRetailer());
            petWaiverOfLiabilityInfo.setPet(pet);
            if (!ObjectUtils.isEmpty(waiverOfLiability)) {
                petWaiverOfLiabilityInfo.setWaiverOfLiability(waiverOfLiability);
            } else {
                throw new EntityNotFoundException("Waiver of liability not found");
            }
            try {
                if (petDto.getSignedWaiverOfLiabilityInfo().getFile() != null) {
                    String awsUrl = FileUploadUtil.uploadFile(petDto.getSignedWaiverOfLiabilityInfo().getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.Documents);
                    petWaiverOfLiabilityInfo.setSignedFile(awsUrl);
                } else if (petDto.getSignedWaiverOfLiabilityInfo().getFileURL() != null) {
                    petWaiverOfLiabilityInfo.setSignedFile(petDto.getSignedWaiverOfLiabilityInfo().getFileURL());
                }
            } catch (Exception e) {
                throw new EtailBookItException("Exception occurred while file upload");
            }
            pet.setPetWaiverOfLiabilityInfo(petWaiverOfLiabilityInfo);
        }

        if (petDto.getDocuments() != null) {
            Set<PetDocuments> documentOptions = new HashSet();
            List<Integer> validIds = new ArrayList<>();
            for (DocumentOptionDto documentOptionDto : petDto.getDocuments()) {
                Integer existingDocumentId = null;
                if (petId != null) {
                    List<PetDocuments> existingPetDocuments = petDocumentRepository.findAllByDocumentOptionDocumentOptionIdAndPetId(documentOptionDto.getId(), petId);
                    if (existingPetDocuments.size() > 0) {
                        existingDocumentId = existingPetDocuments.get(0).getId();
                    }
                }
                PetDocuments otherDocuments = new PetDocuments();
                if (existingDocumentId != null) {
                    otherDocuments.setId(existingDocumentId);
                    validIds.add(existingDocumentId);
                }
                Optional<DocumentOption> documentOption = documentOptionRepository.findById(documentOptionDto.getId());
                otherDocuments.setPet(pet);
                if (documentOption.isPresent()) {
                    otherDocuments.setDocumentOption(documentOption.get());
                } else {
                    throw new EntityNotFoundException("Document not found with ID :::" + documentOptionDto.getId());
                }
                try {
                    if (documentOptionDto.getFile() != null) {
                        String awsUrl = FileUploadUtil.uploadFile(documentOptionDto.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.Documents);
                        otherDocuments.setFile(awsUrl);
                   //     documentOption.get().setSigned(Boolean.FALSE);
                     //   documentOption.get().setSignedFileURL(null);
                    } else if (documentOptionDto.getFileURL() != null) {
                        otherDocuments.setFile(documentOptionDto.getFileURL());
                    }
                } catch (Exception e) {
                    throw new EtailBookItException("Exception occurred while file upload");
                }

                otherDocuments.setDescription(documentOptionDto.getDescription());

                documentOptions.add(otherDocuments);
            }
            if (petId != null) {
                if (validIds.size() == 0) {
                    petDocumentRepository.deleteByPetId(petId, RetailerContext.getRetailer());
                } else {
                    petDocumentRepository.deleteAllByIdNotInAndPetId(validIds, petId);
                }
            }
            pet.setPetDocuments(documentOptions);
        } else if ((petDto.getDocuments() == null || petDto.getDocuments().size() == 0) && petId != null) {
            petDocumentRepository.deleteByPetId(petId);
        }
        List<String> pictures = new ArrayList<>();
        if (petDto.getPictures() != null) {
            MultipartFile multipartFile = petDto.getPictures().get(0);
            {
                try {
                    String awsUrl = FileUploadUtil.uploadFile(multipartFile, dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetPhotosPrefix(), FileType.Photos);
                    pictures.add(awsUrl);
                } catch (BadRequestException e) {
                    LOGGER.error("Invalid file type ::::" + e);
                    throw e;
                } catch (Exception e) {
                    throw new EtailBookItException("Exception occurred while file upload");
                }
            }
            pet.setPhotos(pictures);
        } else if (petId != null) {
            Optional<Pet> petData = petRepository.findById(petId);
            if (petData.isPresent()) {
                pictures = petData.get().getPhotos();
            }
            pet.setPhotos(pictures);
        }

        if (petDto.getVaccinationRecords() != null) {
            Set<PetVaccinationRecords> petVaccinationRecords = new HashSet();
            List<Integer> validIds = new ArrayList<>();
            for (VaccinationRecordsDto vaccinationRecordsDto : petDto.getVaccinationRecords()) {
                Optional<VaccinationRecords> vaccinationRecords = vaccinationRecordsRepository.findById(vaccinationRecordsDto.getId());
                Integer existingDocumentId = null;
                if (petId != null) {
                    List<PetVaccinationRecords> existingPetVaccinationDocuments = petVaccinationRecordRepository.findAllByVaccinationRecordsVaccinationRecordIdAndPetId(vaccinationRecordsDto.getId(), petId);
                    if (existingPetVaccinationDocuments.size() > 0) {
                        existingDocumentId = existingPetVaccinationDocuments.get(0).getId();
                        validIds.add(existingDocumentId);
                    }
                }
                PetVaccinationRecords petVaccinationRecord = new PetVaccinationRecords();
                if (existingDocumentId != null) {
                    petVaccinationRecord.setId(existingDocumentId);
                }
                petVaccinationRecord.setPet(pet);
                if (vaccinationRecords.isPresent()) {
                    petVaccinationRecord.setVaccinationRecords(vaccinationRecords.get());
                } else {
                    throw new EntityNotFoundException("VaccinationRecord not found with ID :::" + vaccinationRecordsDto.getId());
                }
                if (vaccinationRecords.get().getRequireDateExpires() && ObjectUtils.isEmpty(vaccinationRecordsDto.getDateExpires()))
                    throw new BadRequestException("Vaccination Expires Date is required");
                if (vaccinationRecords.get().getRequireDateAdministrated() && ObjectUtils.isEmpty(vaccinationRecordsDto.getDateAdministrated()))
                    throw new BadRequestException("Vaccination Administrated Date is required");
                if (vaccinationRecords.get().getRequireVaccinationDocument() && ObjectUtils.isEmpty(vaccinationRecordsDto.getFile()) && ObjectUtils.isEmpty(vaccinationRecordsDto.getFileURL()))
                    throw new BadRequestException("Vaccination Document is required");

//                Optional<PetVaccinationRecords> existingPetVaccinationInfo = petVaccinationRecordRepository.findByPetIdAndVaccinationRecordsVaccinationRecordId(petId,vaccinationRecords.get().getVaccinationRecordId());
//                if (existingPetVaccinationInfo.isPresent()) {
//                    petVaccinationRecord = existingPetVaccinationInfo.get();
//                    petVaccinationRecord.setVaccinationRecords(vaccinationRecords.get());
//                }else{
//                    petVaccinationRecord.setVaccinationRecords(vaccinationRecords.get());
//                }
                if (vaccinationRecords.get().getRequireDateAdministrated()) {
                    try {
                        petVaccinationRecord.setDateAdministrated(LocalDate.parse(vaccinationRecordsDto.getDateAdministrated()));
                    } catch (Exception e) {
                        throw new BadRequestException("Invalid date format");
                    }
                }
                if (vaccinationRecords.get().getRequireDateExpires()) {
                    try {
                        petVaccinationRecord.setDateExpires(LocalDate.parse(vaccinationRecordsDto.getDateExpires()));
                    } catch (Exception e) {
                        throw new BadRequestException("Invalid date format");
                    }
                }
                if (vaccinationRecords.get().getRequireVaccinationDocument()) {
                    try {
                        if (vaccinationRecordsDto.getFile() != null) {
                            String awsUrl = FileUploadUtil.uploadFile(vaccinationRecordsDto.getFile(), dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getPetDocumentPrefix(), FileType.Documents);
                            petVaccinationRecord.setFile(awsUrl);
                        } else if (vaccinationRecordsDto.getFileURL() != null) {
                            petVaccinationRecord.setFile(vaccinationRecordsDto.getFileURL());
                        }
                    } catch (Exception e) {
                        throw new EtailBookItException("Exception occurred while file upload");
                    }
                }
                petVaccinationRecords.add(petVaccinationRecord);
            }
            if (petId != null) {
                if(petDto.getDocuments()!=null) {
                    petDto.getDocuments().forEach(value -> {
                        if (value.getId() != null) validIds.add(value.getId());
                    });
                }
                if (validIds.size() == 0) {
                    petVaccinationRecordRepository.deleteByPetId(petId, RetailerContext.getRetailer());
                } else {
                    petVaccinationRecordRepository.deleteAllByIdNotInAndPetId(validIds, petId);
                }
            }
            pet.setPetVaccinationRecords(petVaccinationRecords);
        } else if ((petDto.getVaccinationRecords() == null || petDto.getVaccinationRecords().size() == 0) && petId != null) {
            petVaccinationRecordRepository.deleteByPetId(petId);
        }


        if (petDto.getUnfriendlyBehaviourTriggerIds() != null) {
            Set<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers = new HashSet();
            for (Integer unfriendlyBehaviourTriggerId : petDto.getUnfriendlyBehaviourTriggerIds()) {
                Optional<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTrigger = unfriendlyBehaviourTriggerRepository.findById(unfriendlyBehaviourTriggerId);
                if (unfriendlyBehaviourTrigger.isPresent()) {
                    unfriendlyBehaviourTriggers.add(unfriendlyBehaviourTrigger.get());
                } else {
                    throw new EntityNotFoundException("UnfriendlyBehaviourTrigger not found with ID :::" + unfriendlyBehaviourTriggerId);
                }
            }
            pet.setUnfriendlyBehaviourTriggers(unfriendlyBehaviourTriggers);
        }

        if (petDto.getPersonalityParameterIds() != null) {
            Set<PersonalityParameter> personalityParameters = new HashSet();
            for (Integer personalityParameterId : petDto.getPersonalityParameterIds()) {
                Optional<PersonalityParameter> personalityParameter = personalityParameterRepository.findById(personalityParameterId);
                if (personalityParameter.isPresent()) {
                    personalityParameters.add(personalityParameter.get());
                } else {
                    throw new EntityNotFoundException("PersonalityParameter not found with ID :::" + personalityParameterId);
                }
            }
            pet.setPersonalityParameters(personalityParameters);
        }

        if (petDto.getTemperamentId() != null) {
            Temperament temperament = temperamentRepository.findByTemperamentId(petDto.getTemperamentId());
            if (temperament == null) {
                throw new EntityNotFoundException("Temperament not found with ID :::" + petDto.getTemperamentId());
            }
            pet.setTemperament(temperament);
        }


        if (petDto.getHairLengthId() != null) {
            Optional<HairLength> hairLength = hairLengthRepository.findById(petDto.getHairLengthId());
            if (!hairLength.isPresent()) {
                throw new EntityNotFoundException("Hair Length not found with ID :::" + petDto.getHairLengthId());
            }
            pet.setHairLength(hairLength.get());
        }

        Set<GeneralPetSize> generalPetSizes = generalPetSizeRepository.findByPetType(pet.getPetType());
        if (!ObjectUtils.isEmpty(generalPetSizes)) {
            List<GeneralPetSize> generalPetSizesSorted = generalPetSizes.stream().sorted(Comparator.comparing(GeneralPetSize::getWeightValue, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());

            if (petDto.getWeightRangeId() != null) {
                Optional<WeightRange> weightRange = weightRangeRepository.findById(petDto.getWeightRangeId());
                if (!weightRange.isPresent()) {
                    throw new EntityNotFoundException("Weight Range not found with ID :::" + petDto.getWeightRangeId());
                }
                pet.setWeightRange(weightRange.get());

                //determining general pet size
                for (GeneralPetSize generalPetSize : generalPetSizesSorted) {
                    int min = weightRange.get().getMinValue().intValue();
                    int max = weightRange.get().getMaxValue().intValue();
                    int size = 0;
                    if (!ObjectUtils.isEmpty(generalPetSize.getWeightValue())) {
                        size = generalPetSize.getWeightValue();
                    }
                    if (min <= size && size <= max) {
                        pet.setSize(generalPetSize);
                        break;
                    }
                }
                if (pet.getSize() == null) {
                    pet.setSize(generalPetSizesSorted.get(generalPetSizesSorted.size() - 1));
                }
                pet.setExactWeight(BigDecimal.ZERO);
                pet.setWeightUnit(weightRange.get().getWeightUnit());
            } else if (petDto.getExactWeight() != null) {
                BigDecimal exactWeight = pet.getExactWeight();
                int lastIndex = generalPetSizesSorted.size() - 1;
                GeneralPetSize lastElement = generalPetSizesSorted.get(lastIndex);
                if(exactWeight.intValue() > lastElement.getWeightValue()){
                    pet.setSize(lastElement);
                }
                else {
                    for (GeneralPetSize generalPetSize : generalPetSizesSorted) {
                        int size = generalPetSize.getWeightValue();
                        if (petDto.getExactWeight().intValue() <= size) {
                            pet.setSize(generalPetSize);
                            break;
                        }
                    }
                }
                pet.setExactWeight(petDto.getExactWeight());
                pet.setWeightUnit(petDto.getWeightUnit());
            }
        }

        //throw exception if weight is configured in pet type configuration and not added in add pet
        List<PetTypeConfiguration> petTypeConfigurationWeight = petTypeConfigurationRepository.findByPetTypePetTypeId(petDto.getPetTypeId());
        if(petDto.getExactWeight() == null && petDto.getWeightRangeId() == null) {
            for (PetTypeConfiguration petTypeConfigurationList : petTypeConfigurationWeight) {
                if (petTypeConfigurationList.getName().name() == "WEIGHT_RANGE") {
                    if (petTypeConfigurationList.getDisplayType().ordinal() != 0){
                        throw new EntityNotFoundException("Please enter pet weight");
                    }
                }
            }

        }

        if (petDto.getHairTextureId() != null) {
            Optional<HairTexture> hairTexture = hairTextureRepository.findById(petDto.getHairTextureId());
            if (!hairTexture.isPresent()) {
                throw new EntityNotFoundException("Hair Texture not found with ID :::" + petDto.getHairLengthId());
            }
            pet.setHairTexture(hairTexture.get());
        }

        if(petDto.getDeceaseDate() != null){
            pet.setDeceaseDate(petDto.getDeceaseDate());
        }

        if (petId != null) {
            pet.setId(petId);
            petEmergencyContactRepository.deleteByPet(pet);
            petVaccinationRecordRepository.deleteByPet(pet);
            petVetInformationRepository.deleteByPet(pet);
            petDocumentRepository.deleteByPet(pet);
        }

        Set<PetBreedsInformation> petBreedsInformations = new HashSet<>();
        if (petDto.getPetBreedsInfos() != null) {
            if (petId != null) {
                List<Integer> validIds = new ArrayList<>();
                petDto.getPetBreedsInfos().forEach(value -> {
                    if (value.getId() != null) validIds.add(value.getId());
                });
                if (validIds.size() == 0) {
                    petBreedsInformationRepository.deleteAllByPetId(petId);
                } else {
                    petBreedsInformationRepository.deleteAllByIdNotInAndPetId(validIds, petId);
                }
            }
            for (PetBreedsInfoDto petBreedsInfoDto : petDto.getPetBreedsInfos()) {
                Optional<Breed> optionalBreed = breedRepository.findById(petBreedsInfoDto.getBreedId());
                if (!optionalBreed.isPresent()) {
                    throw new EntityNotFoundException("Breed information not found");
                }
                PetBreedsInformation newPetBreedsInformation = new PetBreedsInformation();
                if (petBreedsInfoDto.getId() != null)
                    newPetBreedsInformation.setId(petBreedsInfoDto.getId());
                newPetBreedsInformation.setBreed(optionalBreed.get());
                newPetBreedsInformation.setRetailer(RetailerContext.getRetailer());
                newPetBreedsInformation.setPet(pet);
                petBreedsInformations.add(newPetBreedsInformation);
            }
        } else if (petId != null) {
            petBreedsInformationRepository.deleteAllByPetId(petId);
        }
        pet.setPetBreedsInformations(petBreedsInformations);

        petRepository.save(pet);
    }


    @Override
    @Transactional
    public void deletePet(int petId) throws EtailBookItException {
        Optional<Pet> pet = petRepository.findById(petId);
        if (!pet.isPresent()) {
            throw new EntityNotFoundException("Pet Id not found");
        }
        if(pet.get().getName().equalsIgnoreCase("Browny DEV")){
            throw new EtailBookItException("This is test data and can not be deleted");
        }
        else {
            List<Appointment> existingAppointment = appointmentRepository.findByPet(pet.get());
            if(!existingAppointment.isEmpty()) {
                throw new BadRequestException("Pet is used by an appointment");
            }
            else {
                petRepository.deleteById(petId);
            }
        }
    }

    //added below code  based on the requirement from POS
    @Override
    @Transactional
    public void deletePetIdAndCustomerId(int petId ,int customerId) throws EtailBookItException {
        Optional<Pet> pet = petRepository.findByPetIdAndCustomerId(petId,customerId);
        if (!pet.isPresent()) {
            throw new EntityNotFoundException("Pet Id not found");
        }
        else {
            //todo:Once soft deletion gets implemented, we should allow the pet to be deleted, even if it's currently used in an appointment. So that the pet would still be visible in the appointment details even after getting soft-deleted.
            List<Appointment> existingAppointment = appointmentRepository.findByPet(pet.get());
            if(!existingAppointment.isEmpty()) {
                throw new BadRequestException("Pet is used by an appointment");
            }
            else {
               // petRepository.deleteById(petId);
                petRepository.deletePet(Boolean.TRUE,petId);
            }
        }
    }

    @Override
    public void createPetWithCustomer(PetCustomerDto petCustomerDto, int customerId) throws EtailBookItException {
        Pet pet = new Pet();
        if (petCustomerDto.getName() == null) {
            throw new BadRequestException("Pet name cannot be empty");
        }
        if (petCustomerDto.getPetTypeId() == null) {
            throw new BadRequestException(" Pet Type can not be empty");
        }
        if (petCustomerDto.getDob() == null) {
            throw new BadRequestException("Pet dob can not be empty");
        } else {
            try {
                pet.setDob(LocalDate.parse(petCustomerDto.getDob()));
            } catch (Exception e) {
                throw new BadRequestException("Invalid date format");
            }
        }/*if(petCustomerDto.getExactWeight()==null && petCustomerDto.getWeightRangeId()==null){
            throw new BadRequestException("Pet weight is mandatory");
        }*/
        if (petCustomerDto.getPetTypeId() != null) {
            PetProjection petTypeName = petTypeRepository.getPetTypeName(petCustomerDto.getPetTypeId());
            if (petTypeName == null)
                throw new EntityNotFoundException("Pet type not found with id::" + petCustomerDto.getPetTypeId());
            PetType petType = new PetType();
            petType.setPetTypeId(petCustomerDto.getPetTypeId());
            petType.setName(petTypeName.getPetTypeName());
            pet.setPetType(petType);
        }
       /* if(petCustomerDto.getExactWeight()!=null){
            if(petCustomerDto.getExactWeight().intValue()!=0){
                pet.setExactWeight(petCustomerDto.getExactWeight());
                pet.setWeightUnit(petCustomerDto.getWeightUnit());
            }
        }
        if(petCustomerDto.getWeightRangeId()!=null){
            WeightRange weightRange=weightRangeRepository.findByWeightRangeId(petCustomerDto.getWeightRangeId());
            weightRange.setWeightUnit(petCustomerDto.getWeightUnit());
            pet.setWeightRange(weightRange);
            pet.setWeightUnit(petCustomerDto.getWeightUnit());
        }*/
        pet.setRetailer(RetailerContext.getRetailer());
        pet.setName(petCustomerDto.getName());
        pet.setCustomerId(customerId);
        petRepository.save(pet);
    }


    public void mergePetProfile(List<PetLegacyRetailerListDto> petLegacyRetailerListDtoList) throws EtailBookItException {
        String petTypeName="Dog";
        List<Pet> petList=new ArrayList<>();
        for(PetLegacyRetailerListDto petLegacyRetailerListDto:petLegacyRetailerListDtoList){
            String retailer=petLegacyRetailerListDto.getRetailer();
            PetType petType=petTypeRepository.findByPetTypenameAndRetailer(petTypeName,retailer);
           if(petType==null) {
               petType = new PetType();
               petType.setName(petTypeName);
               petType.setRetailer(retailer);
               petTypeRepository.save(petType);
           }
            if(!retailer.equalsIgnoreCase("ATB")) {
                for (PetLegacyDto petLegacyDto : petLegacyRetailerListDto.getPetLegacyDtoList()) {
                    Pet pet=new Pet();
                    pet.setRetailer(retailer);
                  //  petType.setRetailer(retailer);
                    pet.setPetType(petType);
                    pet.setDob(LocalDate.parse(petLegacyDto.getBirthday()));
                    pet.setName(petLegacyDto.getName());
                    pet.setCustomerId(petLegacyDto.getCustomer_id());
                    petList.add(pet);
                }
            }
        }
        petRepository.saveAll(petList);
    }

    @Override
    public void mergeArizonawagnwash(List<ArizonawagnwashPetProfileMergeDto> arizonawagnwashPetProfileMergeDtoList) throws EtailBookItException{
        List<Pet> petList=new ArrayList<>();
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        for (ArizonawagnwashPetProfileMergeDto arizonawagnwashPetProfileMergeDto:arizonawagnwashPetProfileMergeDtoList){
            String retailer="arizonawagnwash";
            String petTypeName;
            if(arizonawagnwashPetProfileMergeDto.getPetType()==null){
                petTypeName="Dog";
            }else{
                petTypeName=arizonawagnwashPetProfileMergeDto.getPetType();
            }
            PetType petType=petTypeRepository.findByPetTypenameAndRetailer(petTypeName,retailer);
            Pet pet=new Pet();
            if(petType==null) {
                petType = new PetType();
                petType.setName(arizonawagnwashPetProfileMergeDto.getPetType());
                petType.setRetailer(retailer);
                if (arizonawagnwashPetProfileMergeDto.getPetBreed() != null) {
                    Breed breed=new Breed();
                    breed.setPetType(petType);
                    breed.setName(arizonawagnwashPetProfileMergeDto.getPetBreed());
                    breed.setRetailer(retailer);
                    petType.getBreeds().add(breed);
                }

                petType=petTypeRepository.save(petType);
                pet.setPetType(petType);
            }else{
                pet.setPetType(petType);
                if(arizonawagnwashPetProfileMergeDto.getPetBreed()!=null){
                    Breed breed=new Breed();
                    breed.setPetType(petType);
                    breed.setName(arizonawagnwashPetProfileMergeDto.getPetBreed());
                    breed.setRetailer(retailer);
                    petType.getBreeds().add(breed);
                }

            }

            Set<PetBreedsInformation> petBreedsInformations = new HashSet<>();
            if (arizonawagnwashPetProfileMergeDto.getPetBreed() != null) {
                    PetBreedsInformation newPetBreedsInformation = new PetBreedsInformation();
                   List<Breed> breed= breedRepository.findByNamePetTypeRetailerAndDeleted(arizonawagnwashPetProfileMergeDto.getPetBreed(),petType,retailer,false);
                   if(!breed.isEmpty()){
                       newPetBreedsInformation.setBreed(breed.get(0));
                   }
                    newPetBreedsInformation.setRetailer(RetailerContext.getRetailer());
                    newPetBreedsInformation.setPet(pet);
                    petBreedsInformations.add(newPetBreedsInformation);

            }
            pet.setPetBreedsInformations(petBreedsInformations);


          //  DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if(arizonawagnwashPetProfileMergeDto.getPetBirthday()==null){
                ZonedDateTime currentTimeUTC = ZonedDateTime.now(ZoneId.of(ZoneOffset.UTC.toString()));
             //   String petBirthday=currentTimeUTC.toString();
                LocalDate date = currentTimeUTC.toLocalDate();
                pet.setDob(date);
            }else{

                LocalDate petBirthday = LocalDate.parse(arizonawagnwashPetProfileMergeDto.getPetBirthday(),inputFormatter);
                String  formattedDate = petBirthday.format(outputFormatter);
                pet.setDob(LocalDate.parse(formattedDate));
            }
            pet.setPetType(petType);
            pet.setRetailer(retailer);
            pet.setName(arizonawagnwashPetProfileMergeDto.getPetName());
            pet.setCustomerId(arizonawagnwashPetProfileMergeDto.getCustomerId());
            if(arizonawagnwashPetProfileMergeDto.getPetWeight()!=null){
                pet.setExactWeight(arizonawagnwashPetProfileMergeDto.getPetWeight());
            }
            else{
                pet.setExactWeight(BigDecimal.ZERO);
            }
            pet.setWeightUnit("lbs");
            if(arizonawagnwashPetProfileMergeDto.getGender()!=null){
                String gender=arizonawagnwashPetProfileMergeDto.getGender();
                if(gender.equalsIgnoreCase("male")){
                    pet.setSex("m");
                }
                else{
                    pet.setSex("f");
                }
            }

            Set<PetVetInformation> petVetInformations = new HashSet();
            PetVetInformation petVetInformation = new PetVetInformation();
            VetInformation vetInformation=new VetInformation();
            if (arizonawagnwashPetProfileMergeDto.getVetName() != null || arizonawagnwashPetProfileMergeDto.getVetPhone()!=null) {
                    vetInformation.setPetType(petType);
                    vetInformation.setRetailer(retailer);
                    if(arizonawagnwashPetProfileMergeDto.getVetName()!=null){
                        vetInformation.setName(arizonawagnwashPetProfileMergeDto.getVetName());
                        petVetInformation.setValue(arizonawagnwashPetProfileMergeDto.getVetName());
                    }
                    if(arizonawagnwashPetProfileMergeDto.getVetPhone()!=null){
                        vetInformation.setName(arizonawagnwashPetProfileMergeDto.getVetPhone());
                        petVetInformation.setValue(arizonawagnwashPetProfileMergeDto.getVetPhone());
                    }
                    petVetInformation.setPet(pet);
                    petVetInformation.setVetInformation(vetInformation);
                    petVetInformation.setRetailer(RetailerContext.getRetailer());
                    petVetInformations.add(petVetInformation);
                    pet.setPetVetInformation(petVetInformations);
            }

            PetVaccinationRecords petVaccinationRecord = new PetVaccinationRecords();
            Set<PetVaccinationRecords> petVaccinationRecords = new HashSet();
            VaccinationRecords vaccinationRecords=new VaccinationRecords();
            if(arizonawagnwashPetProfileMergeDto.getVaccineDateAdministrated()!=null || arizonawagnwashPetProfileMergeDto.getDateExpires()!=null) {
                vaccinationRecords.setName("PetVaccine");
                vaccinationRecords.setPetType(petType);
                vaccinationRecords.setRetailer(retailer);
                //vaccinationRecords=vaccinationRecordsRepository.save(vaccinationRecords);
                petVaccinationRecord.setVaccinationRecords(vaccinationRecords);

                if (arizonawagnwashPetProfileMergeDto.getVaccineDateAdministrated() != null) {
                    petVaccinationRecord.setPet(pet);
                    LocalDate vaccinationDateAdministrated = LocalDate.parse(arizonawagnwashPetProfileMergeDto.getVaccineDateAdministrated(),inputFormatter);
                    String  formattedDate = vaccinationDateAdministrated.format(outputFormatter);
                    LocalDate dateAdministrated=LocalDate.parse(formattedDate);
                    petVaccinationRecord.setDateAdministrated(dateAdministrated);
                }
                if (arizonawagnwashPetProfileMergeDto.getDateExpires() != null) {
                    petVaccinationRecord.setPet(pet);
                    LocalDate vaccinationDateExpired = LocalDate.parse(arizonawagnwashPetProfileMergeDto.getDateExpires(),inputFormatter);
                    String  formattedDate = vaccinationDateExpired.format(outputFormatter);
                    LocalDate dateAdministrated=LocalDate.parse(formattedDate);
                    petVaccinationRecord.setDateExpires(dateAdministrated);
                }
                petVaccinationRecords.add(petVaccinationRecord);
                pet.setPetVaccinationRecords(petVaccinationRecords);
            }
            if(arizonawagnwashPetProfileMergeDto.getBreeding_status()!=null){
                if(!arizonawagnwashPetProfileMergeDto.getBreeding_status().equalsIgnoreCase("Spayed/Neutered")){
                    pet.setSpayed(false);
                }else{
                    pet.setSpayed(true);
                }
            }
            petList.add(pet);
        }
        petRepository.saveAll(petList);
    }

    @Override
    public void mergePetPRofile(List<MergePetProfileDto> petProfileMergeDtoList) throws EtailBookItException {
        List<Pet> petList=new ArrayList<>();

        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("M/d/yyyy");

        for (MergePetProfileDto mergePetProfileDto:petProfileMergeDtoList){
            String retailer="hearthandhound";
            String petTypeName;
            if(mergePetProfileDto.getPetType()==null){
                petTypeName="Dog";
            }else{
                petTypeName=mergePetProfileDto.getPetType();
            }
            PetType petType=petTypeRepository.findByPetTypenameAndRetailer(petTypeName,retailer);
            Pet pet=new Pet();
            if(petType==null) {
                petType = new PetType();
                petType.setName(mergePetProfileDto.getPetType());
                petType.setRetailer(retailer);
                if (mergePetProfileDto.getPetBreed() != null) {
                    Breed breed=new Breed();
                    breed.setPetType(petType);
                    breed.setName(mergePetProfileDto.getPetBreed());
                    breed.setRetailer(retailer);
                    petType.getBreeds().add(breed);
                }

                petType=petTypeRepository.save(petType);
                pet.setPetType(petType);
            }else{
                pet.setPetType(petType);
                if(mergePetProfileDto.getPetBreed()!=null){
                    Breed breed=new Breed();
                    breed.setPetType(petType);
                    breed.setName(mergePetProfileDto.getPetBreed());
                    breed.setRetailer(retailer);
                    petType.getBreeds().add(breed);
                }

            }

            Set<PetBreedsInformation> petBreedsInformations = new HashSet<>();
            if (mergePetProfileDto.getPetBreed() != null) {
                PetBreedsInformation newPetBreedsInformation = new PetBreedsInformation();
                List<Breed> breed= breedRepository.findByNamePetTypeRetailerAndDeleted(mergePetProfileDto.getPetBreed(),petType,retailer,false);
                if(!breed.isEmpty()){
                    newPetBreedsInformation.setBreed(breed.get(0));
                }
                newPetBreedsInformation.setRetailer(RetailerContext.getRetailer());
                newPetBreedsInformation.setPet(pet);
                petBreedsInformations.add(newPetBreedsInformation);

            }
            pet.setPetBreedsInformations(petBreedsInformations);

            //  DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if(mergePetProfileDto.getPetBirthday()==null){
                ZonedDateTime currentTimeUTC = ZonedDateTime.now(ZoneId.of(ZoneOffset.UTC.toString()));
                //   String petBirthday=currentTimeUTC.toString();
                LocalDate date = currentTimeUTC.toLocalDate();
                pet.setDob(date);
            }else{

                LocalDate petBirthday = LocalDate.parse(mergePetProfileDto.getPetBirthday(),inputFormatter);
                String  formattedDate = petBirthday.format(outputFormatter);
                pet.setDob(LocalDate.parse(formattedDate));
            }
            pet.setPetType(petType);
            pet.setRetailer(retailer);
            String petFirstName = Optional.ofNullable(mergePetProfileDto.getPetFirstName()).orElse("").trim();
            String petLastName = Optional.ofNullable(mergePetProfileDto.getPetLastName()).orElse("").trim();

            String fullName = (petFirstName + " " + petLastName).trim();

            if (fullName.isEmpty()) {
                fullName = "Unnamed Pet";
            }

            pet.setName(fullName);
            pet.setCustomerId(mergePetProfileDto.getCustomerId());
            if(mergePetProfileDto.getPetWeight()!=null){
                pet.setExactWeight(mergePetProfileDto.getPetWeight());
            }
            else{
                pet.setExactWeight(BigDecimal.ZERO);
            }
            pet.setWeightUnit("lbs");
            if(mergePetProfileDto.getGender()!=null){
                String gender=mergePetProfileDto.getGender();
                if(gender.equalsIgnoreCase("male")){
                    pet.setSex("m");
                }
                else{
                    pet.setSex("f");
                }
            }

            PetVaccinationRecords petVaccinationRecord = new PetVaccinationRecords();
            Set<PetVaccinationRecords> petVaccinationRecords = new HashSet();
            VaccinationRecords vaccinationRecords=new VaccinationRecords();
            if(mergePetProfileDto.getVaccineDateExpires()!=null) {
                vaccinationRecords.setName("PetVaccine");
                vaccinationRecords.setPetType(petType);
                vaccinationRecords.setRetailer(retailer);
                //vaccinationRecords=vaccinationRecordsRepository.save(vaccinationRecords);
                petVaccinationRecord.setVaccinationRecords(vaccinationRecords);
                if (mergePetProfileDto.getVaccineDateExpires() != null) {
                    String dateOnly = mergePetProfileDto.getVaccineDateExpires().split(" ")[0];
                    LocalDate vaccinationDateExpired = LocalDate.parse(dateOnly, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    petVaccinationRecord.setPet(pet);
                    petVaccinationRecord.setDateExpires(vaccinationDateExpired);
                }
                petVaccinationRecords.add(petVaccinationRecord);
                pet.setPetVaccinationRecords(petVaccinationRecords);
            }
            petList.add(pet);
        }
        petRepository.saveAll(petList);
    }

    @Override
    @Transactional
    public void deletePetsFromBadImport(List<MergePetProfileDto> importedDtos) {
        String retailer = "hearthandhound";
        List<Pet> toDelete = new ArrayList<>();

        for (MergePetProfileDto dto : importedDtos) {
            String petFirstName = dto.getPetFirstName();
            String petLastName = dto.getPetLastName();

            // Replicate the buggy concatenation that caused issues
            String fullName = (petFirstName == null ? "null" : petFirstName)
                    + (petLastName == null ? "null" : petLastName);
            fullName = fullName.trim();

            Integer customerId = dto.getCustomerId();

            List<Pet> matchingPets = petRepository.findByNameAndCustomerIdAndRetailer(fullName, customerId, retailer);

            if (!matchingPets.isEmpty()) {
                LOGGER.info("Deleting pet '{}' for customer {}", fullName, customerId);
                toDelete.addAll(matchingPets);
            } else {
                LOGGER.warn("No pet found to delete for generated name='{}' and customerId={}", fullName, customerId);
            }
        }

        petRepository.deleteAll(toDelete);
        LOGGER.info("Deleted {} pets from incorrect import.", toDelete.size());
    }


    @Override
    public void mergeAtbPetProfile(List<AtbpetProfileMergeDto> atBpetProfileMergeDtoList) throws EtailBookItException, ParseException {
            List<Pet> petList=new ArrayList<>();
            for (AtbpetProfileMergeDto atBpetProfileMergeDto:atBpetProfileMergeDtoList){
                String retailer="allthebest";
                PetType petType=petTypeRepository.findByPetTypenameAndRetailer(atBpetProfileMergeDto.getPetType(),retailer);
                if(petType==null) {
                    petType = new PetType();
                    petType.setName(atBpetProfileMergeDto.getPetType());
                    petType.setRetailer(retailer);
                    petTypeRepository.save(petType);
                }
                String petBirthday = atBpetProfileMergeDto.getPetBirthday();
                Pet pet=new Pet();
                pet.setPetType(petType);
                pet.setRetailer(retailer);
                pet.setName(atBpetProfileMergeDto.getPetName());
                pet.setCustomerId(atBpetProfileMergeDto.getPlusPosId());
                DateTimeFormatter formatters = DateTimeFormatter.ofPattern("MM-dd-yyyy");
                LocalDate date = LocalDate.parse(petBirthday, formatters);
                pet.setDob(LocalDate.parse(myToString(date)));
                petList.add(pet);
            }
            petRepository.saveAll(petList);
    }

    @Override
    public String signPetDocument(String fileUrl, Integer petId, Integer documentId,String name) throws IOException, URISyntaxException, GeneralSecurityException, DocumentException, EtailBookItException {
        LOGGER.info("----------Entered SignDocument------------");

        String temp_outPutFile=s3Integration.downloadFile(fileUrl);
       // String temp_outPutFile=fileUrl;
        String documentType="PET DOCUMENT";
        BouncyCastleProvider provider = new BouncyCastleProvider();
        Security.addProvider(provider);
        KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
        ks.load(new FileInputStream(KEYSTORE), pass.toCharArray());
        String alias = ks.aliases().nextElement();
        PrivateKey pk = (PrivateKey) ks.getKey(alias, pass.toCharArray());
        Certificate[] chain = ks.getCertificateChain(alias);
        String dest = temp_outPutFile.split("\\.pdf")[0] + "_" + petId + "_" + new Date().getTime() + RESULT_FILE;

        LOGGER.info("Destination::" + dest);
        LOGGER.info("________before entering esign-----------");
        fileUrl = esignService.sign(temp_outPutFile, dest, chain, pk, DigestAlgorithms.SHA256, provider.getName(), PdfSigner.CryptoStandard.CMS, name, petId,documentType);
        File sourceTempFile = new File(temp_outPutFile);
        sourceTempFile.delete();
        File destTempFile = new File(dest);
        destTempFile.delete();
        LOGGER.info("------------Exited SignDocument------------");
        //saveSignedPetDocument(fileUrl,petId,documentId);
        return fileUrl;
    }

    @Override
    public PaginatedResponse<PetBirthdayDto> getPetBirthdayReport(Integer pageNo, Integer pageSize, String sortBy, Optional<String> petName, Integer petDob,List<Integer> petIds,List<Integer> cusIds) throws  EtailBookItException {

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
        Page<PetBirthdayDto> pets = null;
        Optional<Pet> pet;
        List<Pet> petsList = new ArrayList<>();
        List<Pet> petCustomerList = new ArrayList<>();
        List<Pet> petIdAndCustomerIdList;
        long totalCount = 0L;
        long totalPetIdsCount = 0L;
        long totalCustomerCount = 0L;
        long totalCountByName = 0L;
        long totalCountByDob = 0L;
        long totalCountByNameAndDob = 0L;
        long totalCountOfAllPets = 0L;
        long totalCountByPetIdAndCusId = 0L;
        List<PetBirthdayDto> petBirthdayDtoList = new ArrayList<>();
        if (petName.isPresent() && petName.hashCode() != 0 && petDob != null) {
            totalCountByNameAndDob = petRepository.countByNameAndDob(petName.get().toLowerCase(), petDob, RetailerContext.getRetailer(), false);
            petsList = petRepository.findByNameAndDob(petName.get().toLowerCase(), petDob, RetailerContext.getRetailer(), false, paging);
        } else if (petName.isPresent() && petName.hashCode() != 0) {
            LOGGER.info("Pet Name " + petName.get().toLowerCase());
            totalCountByName = petRepository.countOfName(petName.get(), RetailerContext.getRetailer(), false);
            petsList = petRepository.findByName(petName.get(), RetailerContext.getRetailer(), false, paging);
        } else if (petDob != null) {
            LOGGER.info("Pet Birth Month " + petDob);
            totalCountByDob = petRepository.countOfDob(petDob, RetailerContext.getRetailer(), false);
            petsList = petRepository.findByDob(petDob, RetailerContext.getRetailer(), false, paging);
        }
        if(petIds != null && !petIds.isEmpty() && cusIds!=null && !cusIds.isEmpty()){
                petIdAndCustomerIdList = petRepository.findByIdInOrCustomerIdIn(petIds, cusIds);
                petsList.addAll(petIdAndCustomerIdList);
                totalCountByPetIdAndCusId = totalCountByPetIdAndCusId + petIdAndCustomerIdList.size();
        }
        else if (petIds != null && !petIds.isEmpty()){
                long petIdsCount = 0L;
                for (Integer petId : petIds) {
                    petIdsCount = petRepository.countByPetIds(petId, RetailerContext.getRetailer(), false);
                    pet = petRepository.findByPetIdAndRetailer(petId, RetailerContext.getRetailer(), false);
                    if (pet.isPresent()) {
                        petsList.add(pet.get());
                        totalPetIdsCount = totalPetIdsCount + petIdsCount;
                }
            }
        }
        else if(cusIds!=null && !cusIds.isEmpty()) {
                long customerCount = 0L;
                for (Integer customerId : cusIds) {
                    customerCount = petRepository.countByCustomerCount(customerId, RetailerContext.getRetailer());
                    petCustomerList = petRepository.findByCustomerId(customerId, RetailerContext.getRetailer(), false, paging);
                    petsList.addAll(petCustomerList);
                    totalCustomerCount = totalCustomerCount + customerCount;
            }
        }
           else if(petDob==null && petName.hashCode()==0 && cusIds==null &&petIds==null){
                   totalCountOfAllPets = petRepository.countOfAllPets(RetailerContext.getRetailer(), false);
                   petsList = petRepository.findAllByRetailerAndDeleted(RetailerContext.getRetailer(), false, paging);
        }

           totalCount=totalCustomerCount+totalPetIdsCount+totalCountOfAllPets+totalCountByDob+totalCountByName+totalCountByNameAndDob+totalCountByPetIdAndCusId;
        petBirthdayDtoList=  convertToPetDtoList(petsList);
        pets= new PageImpl<>(petBirthdayDtoList, paging, petBirthdayDtoList.size());
        PaginatedResponse response=new PaginatedResponse();
        response.setData(pets);
        response.setTotal(totalCount);
        return response;


    }
    private List<PetBirthdayDto> convertToPetDtoList(List<Pet> petList){
        List<PetBirthdayDto> petBirthdayDtoList=new ArrayList<>();
        for(Pet pet1:petList){
            PetBirthdayDto petBirthdayDto=new PetBirthdayDto();
            petBirthdayDto.setCustomer_id(pet1.getCustomerId());
            petBirthdayDto.setId(pet1.getId());
            petBirthdayDto.setName(pet1.getName());
            petBirthdayDto.setDob(pet1.getDob() == null ? null : pet1.getDob().toString());
            petBirthdayDtoList.add(petBirthdayDto);
        }
        return petBirthdayDtoList;
    }

    private void saveSignedPetDocument(String fileUrl, Integer petId, Integer documentId) throws EtailBookItException {
        Optional<DocumentOption> documentOption=documentOptionRepository.findById(documentId);
        if(documentOption.isPresent()){
           DocumentOption document= documentOption.get();
           document.setSigned(Boolean.TRUE);
           document.setSignedFileURL(fileUrl);
           Pet pet =petRepository.findByPetId(petId);
           PetDocuments petDocuments=petDocumentRepository.findByDocumentOptionIdAndPetId(documentId,petId);
           if(petDocuments!=null) {
               petDocuments.setPet(pet);
               petDocuments.setDocumentOption(document);
              // petDocuments.setFile(document.getFileURL());
               petDocumentRepository.save(petDocuments);
               LOGGER.info("Saved Signed document to petDocument entity");
           }
        }else{
            throw new EntityNotFoundException("Document not found with id::"+documentId);
        }
    }

    public static String myToString(LocalDate localDate){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(formatter);

    }

    @Override
    public PetDetailsProjection getPetById(int petId) throws EtailBookItException {
        Validator.validatePet(null, petId, petRepository);
        PetDetailsProjection pet =petRepository.getPetDetails(petId);
        return pet;
    }

    @Override
    public List<PetDropdownProjection> getPets() {
        List<PetDropdownProjection> pets = petRepository.findAByRetailerOrderByIdDesc(RetailerContext.getRetailer());
        return pets;
    }

    @Override
    public Page<PetListingProjection> getPetsByPagination(Integer customer,Integer pageNo, Integer pageSize, String sortBy, Optional<String> search) throws EntityNotFoundException {

        if (Optional.ofNullable(customer).orElse(0) != 0) {
            Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).ascending());
            Page<PetListingProjection> pets;
            if (search.isPresent() && search.hashCode() != 0) {
                LOGGER.info(search.get().toLowerCase());
                if (customer!=null){
                    LOGGER.info(String.valueOf(customer));
                }else {
                    LOGGER.info("Customer not specified");
                }
                pets = petRepository.findByKeyword(search.get().toLowerCase(), customer,RetailerContext.getRetailer(),false, paging);
            } else {
                pets = petRepository.findAllByCustomerIdAndRetailerAndDeleted(customer,RetailerContext.getRetailer(),false,paging);
            }
            return pets;
        }
        throw new EntityNotFoundException("No pets found for customer");
    }

    @Override
    public List<PetDropdownProjection> getPetsByCustomer(
            Integer customer, Optional<Boolean> withoutDeceasedDate, Optional<String> search
    ) throws EtailBookItException {
        List<PetDropdownProjection> petList;
        if(withoutDeceasedDate.isPresent()) {
            if (withoutDeceasedDate.get().booleanValue() == Boolean.TRUE) {
                LOGGER.info("***********Entered getPetByCustomer API petService class with deceased date ::"+withoutDeceasedDate);
                petList = petRepository.findByCustomerWithoutDeceasedDate(
                        customer, RetailerContext.getRetailer(), Boolean.FALSE, search.orElse(""));
            }
            else {
                LOGGER.info("***********Entered getPetByCustomer API petService class with deceased date ::"+withoutDeceasedDate);
                petList = petRepository.findByCustomer(
                        customer, RetailerContext.getRetailer(), Boolean.FALSE, search.orElse("")
                );
            }
        }
        else {
            LOGGER.info("***********Entered getPetByCustomer API petService class and deceasedDate boolean was not provided");
            petList = petRepository.findByCustomer(
                    customer,RetailerContext.getRetailer(),Boolean.FALSE,search.orElse("")
            );
        }
        return petList;
    }

    @Override
    public Boolean checkEligibility(Integer petId, Integer attendantId, Integer venueId) throws EtailBookItException {
        Pet pet = petRepository.getOne(petId);
        if(ObjectUtils.isEmpty(pet)) {
            throw new EntityNotFoundException("Pet not found");
        }
        if(pet.getSize() == null) {
            return true;
        }
        Attendant attendant = attendantRepository.getOne(attendantId);
        if(ObjectUtils.isEmpty(pet)) {
            throw new EntityNotFoundException("Attendant not found");
        }
        Venue venue = venueRepository.getOne(venueId);
        if(ObjectUtils.isEmpty(pet)) {
            throw new EntityNotFoundException("Venue not found");
        }
        boolean attendantEligible = false;
        for(AttendantPetTypes attendantPetType : attendant.getAttendantPetTypes()) {
            if(attendantPetType.getGeneralPetSizes().contains(pet.getSize())&&attendantPetType.getTemperaments().contains(pet.getTemperament())) {
                attendantEligible = true;
                break;
            }
        }
        boolean venueEligible = false;
        for(VenuePetTypes venuePetTypes : venue.getVenuePetTypes()) {
            if(venuePetTypes.getGeneralPetSizes().contains(pet.getSize())&&venuePetTypes.getTemperaments().contains(pet.getTemperament())) {
                venueEligible = true;
                break;
            }
        }

        return attendantEligible && venueEligible;
    }

    @Override
    public List<ObjectNode> importExcelAndSave(MultipartFile file) throws EtailBookItException, IOException, ParseException {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        List<ObjectNode> rows = processSheet(sheet);
        return rows;
    }
    private List<ObjectNode> processSheet(Sheet sheet) throws ParseException {
        List<ObjectNode> rows = new ArrayList<>();
        boolean isFirstElement = true;
        for (Row row : sheet) {
        if (isFirstElement) {
            isFirstElement = false;
            continue; // Skip the first element
        }
        ObjectNode jsonRow = createJsonRow(sheet, row);
        if(!jsonRow.isEmpty())
            rows.add(jsonRow);
        }
        return rows;
    }

    private ObjectNode createJsonRow(Sheet sheet, Row row) throws ParseException {
        ObjectNode jsonRow = new ObjectNode(JsonNodeFactory.instance);
        for (Cell cell : row) {
            if(row.getLastCellNum() == 4) {
                if(cell.getColumnIndex() == 0){
                    String columnName = sheet.getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
                    Integer id = (int)cell.getNumericCellValue();
                    jsonRow.put(columnName, id);
                }
                else if(cell.getColumnIndex() == 3){
                    String inputDateString = cell.getStringCellValue();
                    SimpleDateFormat inputDateFormat = new SimpleDateFormat("EEEE, MMMM d, yyyy");
                    SimpleDateFormat outputDateFormat = new SimpleDateFormat("MM-dd-yyyy");
                    Date date = inputDateFormat.parse(inputDateString);
                    String formattedDate = outputDateFormat.format(date);
                    String columnName = sheet.getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
                    jsonRow.put(columnName, formattedDate);
                }
                else {
                    String columnName = sheet.getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
                    jsonRow.put(columnName, getCellValue(cell));
                    if(row.getCell(2) == null){
                        jsonRow.put("petName", "**");
                    }
                }
            }
        }
        return jsonRow;
    }

    private String getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return Double.toString(cell.getNumericCellValue());
            default:
                return "";
        }
    }

    @Override
    public List<Integer> bulkUpdateGeneralSizes(List<String> retailers) {
        List<Integer> modifiedPetList = new ArrayList<>();
        for (String retailer : retailers){
            List<Pet> petList = petRepository.petList(retailer);
        for (Pet pet : petList) {
            Set<GeneralPetSize> generalPetSizes = generalPetSizeRepository.findByPetType(pet.getPetType());
            if (!ObjectUtils.isEmpty(generalPetSizes)) {
                List<GeneralPetSize> generalPetSizesSorted = generalPetSizes.stream().sorted(Comparator.comparing(GeneralPetSize::getWeightValue, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
                BigDecimal exactWeight = pet.getExactWeight();
                int lastIndex = generalPetSizesSorted.size() - 1;
                GeneralPetSize lastElement = generalPetSizesSorted.get(lastIndex);

                if (exactWeight.intValue() > lastElement.getWeightValue()) {
                    pet.setSize(lastElement);
                } else {
                    for (GeneralPetSize generalPetSize : generalPetSizesSorted) {
                        int size = generalPetSize.getWeightValue();
                        if (exactWeight.intValue() <= size) {
                            pet.setSize(generalPetSize);
                            break;
                        }
                    }
                }
            }
            petRepository.save(pet);
            modifiedPetList.add(pet.getId());
        }
    }
        return modifiedPetList;
    }

    @Override
    public List<PetBirthdayDto> getPetReportForEcomm(Integer pageNo, Integer pageSize, String sortBy, Optional<String> petName, Integer petDob, List<Integer> petIds, List<Integer> cusIds) throws  EtailBookItException {
            readerConfiguration.setPetName(petName);
            readerConfiguration.setPetDob(petDob);
            readerConfiguration.setPetIds(petIds);
            readerConfiguration.setCusIds(cusIds);
            readerConfiguration.setRetailer(RetailerContext.getRetailer());
            readerConfiguration.setSortBy(sortBy);
            try {
                JobParameters jobParameters = new JobParametersBuilder()
                        .addString("timestamp", String.valueOf(System.currentTimeMillis()))
                        .toJobParameters();
                JobExecution jobExecution = jobLauncher.run(petJob, jobParameters);
            } catch (Exception e) {
                throw new EtailBookItException(e.getMessage());
            }
            return writerConfiguration.getItems();
    }

    @Override
    public BaseResponseDto sendEmailToSignWaiver(Integer customerId, Integer petId) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        //String url="https://posweb.dev.etailpet.com/sandyspetdepot/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto= new SMSNotificationDto();
        smsNotificationDto.setCustomer_id(customerId);
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_SIGN_WAIVER_OF_LIABILITY");
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setPet_id(petId);
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        return null;
    }



}
