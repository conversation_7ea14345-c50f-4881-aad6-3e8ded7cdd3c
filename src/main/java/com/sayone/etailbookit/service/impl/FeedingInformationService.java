package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.FeedingInformationDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.mapper.FeedingInformationMapper;
import com.sayone.etailbookit.model.Configuration;
import com.sayone.etailbookit.model.FeedingInformation;
import com.sayone.etailbookit.repository.ConfigurationRepository;
import com.sayone.etailbookit.repository.FeedingInformationRepository;
import com.sayone.etailbookit.service.IFeedingInformationService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class FeedingInformationService implements IFeedingInformationService {

    @Autowired
    FeedingInformationRepository feedingInformationRepository;

    @Autowired
    ConfigurationRepository configurationRepository;

    private static final String FEEDING_INFORMATION = "feeding_information";


    @Override
    @Transactional
    public FeedingInformationDto updateFeedingInformation(FeedingInformationDto feedingInformationDto) throws EtailBookItException {
        Configuration configuration = configurationRepository.findByNameAndRetailer(FEEDING_INFORMATION, RetailerContext.getRetailer());
        Boolean firstTimeConfig = false;
        if (configuration == null) {
            configuration = new Configuration();
            configuration.setName(FEEDING_INFORMATION);
            firstTimeConfig = true;

        }
        configuration.setRetailer(RetailerContext.getRetailer());
        configuration.setActive(feedingInformationDto.isActive());
        configurationRepository.save(configuration);
        if (feedingInformationDto.isActive()) {
            FeedingInformation feedingInformation;
            List<FeedingInformation> feedingInformations = feedingInformationRepository.findAll();
            Integer feedingInformationId = null;
            if (Utils.isNotEmpty(feedingInformations)) {
                feedingInformationId = feedingInformationRepository.findAll().get(0).getFeedingInformationId();
            }
            feedingInformation = FeedingInformationMapper.toFeedingInformationEntity(feedingInformationDto);
            feedingInformation.setFeedingInformationId(feedingInformationId);
            feedingInformation.setRetailer(RetailerContext.getRetailer());
            feedingInformation.setDisplay(configuration.isActive());
            feedingInformationRepository.save(feedingInformation);
        }
        return feedingInformationDto;
    }

    @Override
    public FeedingInformationDto getFeedingInformation() {
        Configuration configuration = configurationRepository.findByNameAndRetailer(FEEDING_INFORMATION, RetailerContext.getRetailer());
        FeedingInformation feedingInformation = feedingInformationRepository.findByRetailer(RetailerContext.getRetailer());
        FeedingInformationDto feedingInformationDto = FeedingInformationMapper.toFeedingInformationDto(feedingInformation);
        if (configuration != null) {
            feedingInformationDto.setActive(configuration.isActive());
            feedingInformationDto.setDisplay(configuration.isActive());
        }
        return feedingInformationDto;
    }

}
