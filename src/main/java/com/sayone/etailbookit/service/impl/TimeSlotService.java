package com.sayone.etailbookit.service.impl;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.TimeSlotMinimalProjection;
import com.sayone.etailbookit.projections.TimeSlotProjection;
import com.sayone.etailbookit.projections.TimeSlotProjectionOfAmonth;
import com.sayone.etailbookit.projections.TimeSlotWeekViewProjection;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.ITimeslotService;
import com.sayone.etailbookit.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TimeSlotService implements ITimeslotService {

    @Autowired
    VenueRepository venueRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    TimeSlotRepository timeSlotRepository;

    @Autowired
    TimeSlotClusterRepository timeSlotClusterRepository;

    @Autowired
    BlockDatesRepository blockDatesRepository;

    @Autowired
    SlotCreationPublisher slotCreationPublisher;

    @Autowired
    SlotUpdatePublisher slotUpdatePublisher;


    @Autowired
    AppointmentServiceImpl appointmentServiceImpl;
    private static Logger LOGGER = LoggerFactory.getLogger(TimeSlotService.class);

    @Override
    public BaseResponseDto generateServiceAvailability(ServiceSlotsDto serviceSlotsDto,String tz) throws EtailBookItException {
        List<OffsetDateTime> blockStartTimes =new ArrayList<>();
       // List<TimeSlotsDto> timeSlotsDtos=new ArrayList<>();
        try {

            TimeSlotCluster timeSlotCluster = createSlotCluster(serviceSlotsDto);
            for (Integer attendantId : serviceSlotsDto.getAttendants()) {
                blockStartTimes= generateSlots(attendantId, serviceSlotsDto, timeSlotCluster,RetailerContext.getRetailer());
                // timeSlotsDtos.addAll(newTimeSlots);
            }
        }catch (EtailBookItException e){
            throw new EtailBookItException("Exception thrown while procesing the reqest"+e);
        }
       // baseResponseDto.setData(timeSlotsDtos);

        return  new BaseResponseDto(Status.SUCCESS,blockStartTimes);
    }

    @Override
    public BaseResponseDto generateServiceAvailabilityBackgroundProcessing(ServiceSlotsDto serviceSlotsDto) throws EtailBookItException {
        try {
            // Publish each attendant's task to Kinesis
            for (Integer attendantId : serviceSlotsDto.getAttendants()) {
                TimeSlotCluster timeSlotCluster = createSlotCluster(serviceSlotsDto);
                slotCreationPublisher.publishSlotCreationEvent(serviceSlotsDto, attendantId, timeSlotCluster.getClusterId(),RetailerContext.getRetailer());
            }

            return new BaseResponseDto(Status.SUCCESS, "Slot creation initiated.");
        } catch (EtailBookItException e) {
            throw new EtailBookItException("Exception thrown while processing the request: " + e);
        }
    }

    @Override
    public BaseResponseDto fetchSlotsByAttendantOfaMonthNew(Integer serviceId, Integer venueId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException {
        List<AvailableSlotsCounterDto> availableSlotsCounterList = new ArrayList<>();
        Venue venue = venueRepository.findByVenueId(venueId);
        if (venue == null) throw new BadRequestException("Selected venue is invalid");

        com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
        if (service == null) throw new BadRequestException("Selected service is invalid");

        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByRetailerAndOffsetBlockDateBetween(
                RetailerContext.getRetailer(), startDate.toString(), endDate.toString());

        // Preprocess: Map block dates by local date
        Map<LocalDate, List<BlockDateInfo>> blockDateMap = blockDateInfoList.stream()
                .collect(Collectors.groupingBy(info -> OffsetDateTime.parse(info.getOffsetBlockDate()).toLocalDate()));

        OffsetDateTime currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            OffsetDateTime dayStartUtc = UserTimeZoneConversion.getStartOfDayInUTC(timeZone, currentDate);
            OffsetDateTime dayEndUtc = UserTimeZoneConversion.getEndOfDayInUTC(timeZone, currentDate);

            ZonedDateTime zonedDateTime = currentDate.atZoneSameInstant(ZoneId.of(timeZone));
            int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
            int offsetTimeDifference = offsetInSeconds / 60;

            AvailableSlotsCounterDto dto = new AvailableSlotsCounterDto();
            dto.setDate(dayStartUtc);

            LocalDate localDateWithOffset = dayStartUtc.plusMinutes(offsetTimeDifference).toLocalDate();
            List<BlockDateInfo> todaysBlocks = blockDateMap.getOrDefault(localDateWithOffset, Collections.emptyList());

            boolean isBlocked = todaysBlocks.stream()
                    .anyMatch(info -> info.getBlockStartTime() == null && info.getBlockEndTime() == null);

            if (isBlocked) {
                dto.setBlockFullDay(true);
                dto.setAvailableSlots(0);
            } else {
                int available = getAvailableSlotsCounterByDate1New(service, venue, dayStartUtc, dayEndUtc, blockDateInfoList, timeZone);
                dto.setAvailableSlots(available > 0 ? 1 : 0);
                dto.setBlockTimes(0);

                for (BlockDateInfo info : todaysBlocks) {
                    if (info.getBlockStartTime() != null && info.getBlockEndTime() != null) {
                        LocalDate blockStart = info.getBlockStartTime().plusMinutes(offsetTimeDifference).toLocalDate();
                        LocalDate blockEnd = info.getBlockEndTime().plusMinutes(offsetTimeDifference).toLocalDate();

                        if (!blockStart.isBefore(localDateWithOffset) && !blockEnd.isAfter(localDateWithOffset)) {
                            dto.setBlockTimes(1);
                            break;
                        }
                    }
                }
            }

            availableSlotsCounterList.add(dto);
            currentDate = dayEndUtc.plusMinutes(1);
        }

        return new BaseResponseDto<>(Status.SUCCESS, availableSlotsCounterList);
    }

    @Override
    public BaseResponseDto getSlotsByAttendantOfaMonth(Integer serviceId, Integer venueId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException {
        Venue venue = venueRepository.findByVenueId(venueId);
        if (venue == null) throw new BadRequestException("Selected venue is invalid");

        com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
        if (service == null) throw new BadRequestException("Selected service is invalid");

        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByRetailerAndOffsetBlockDateBetween(
                RetailerContext.getRetailer(), startDate.toString(), endDate.toString());

        Map<LocalDate, List<BlockDateInfo>> blockDateMap = blockDateInfoList.stream()
                .collect(Collectors.groupingBy(info -> OffsetDateTime.parse(info.getOffsetBlockDate()).toLocalDate()));

        List<TimeSlotMinimalProjection> allSlots = timeSlotRepository.findAllMinimalSlotsInRange(
                startDate, endDate, service, venue, RetailerContext.getRetailer());

        Set<Integer> clusterIds = allSlots.stream().map(TimeSlotMinimalProjection::getClusterId).collect(Collectors.toSet());
        Map<Integer, TimeSlotMinimalDTO> clusterSlotMap =
                timeSlotClusterRepository.findFirstSlotsByClusterIds(clusterIds).stream()
                        .collect(Collectors.toMap(
                                TimeSlotMinimalDTO::getClusterId,
                                Function.identity(),
                                (s1, s2) -> s1.getSlotStartTime().isBefore(s2.getSlotStartTime()) ? s1 : s2
                        ));

        ZoneId zoneId = ZoneId.of(timeZone);
        OffsetDateTime nowWithOffset = ZonedDateTime.now(zoneId).toOffsetDateTime();
        LOGGER.info("nowWithOffset: {}", nowWithOffset);

        // Map<LocalDate, List<TimeSlotMinimalProjection>> after adjusting for DST
        Map<LocalDate, List<Map.Entry<TimeSlotMinimalProjection, OffsetDateTime>>> slotsByDate = allSlots.stream()
                .map(slot -> {
                    TimeSlotMinimalDTO parentSlot = clusterSlotMap.get(slot.getClusterId());
                    if (parentSlot == null) return null;

                    OffsetDateTime parentStartUTC = parentSlot.getSlotStartTime();
                    OffsetDateTime slotStartUTC = slot.getSlotStartTime();

                    int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentStartUTC);
                    int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, slotStartUTC);
                    int dstDiff = parentDST - currentDST;

                    OffsetDateTime adjustedStart = slotStartUTC;
                    if (dstDiff != 0) {
                        adjustedStart = adjustedStart.plusMinutes(dstDiff);
                    }

                    LocalDate adjustedLocalDate = adjustedStart.atZoneSameInstant(zoneId).toLocalDate();

                    // Return a flat entry: LocalDate -> (slot, adjustedStart)
                    return Map.entry(adjustedLocalDate, Map.entry(slot, adjustedStart));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));


        List<AvailableSlotsCounterDto> availableSlotsCounterList = slotsByDate.entrySet().parallelStream()
                .map(entry -> {
                    LocalDate date = entry.getKey();
                    List<Map.Entry<TimeSlotMinimalProjection, OffsetDateTime>> adjustedSlots = entry.getValue();
                    List<BlockDateInfo> todaysBlocks = blockDateMap.getOrDefault(date, Collections.emptyList());

                    AvailableSlotsCounterDto dto = new AvailableSlotsCounterDto();
                    dto.setDate(date.atStartOfDay(zoneId).toOffsetDateTime());

                    boolean isBlocked = todaysBlocks.stream()
                            .anyMatch(info -> info.getBlockStartTime() == null && info.getBlockEndTime() == null);

                    if (isBlocked) {
                        dto.setBlockFullDay(true);
                        dto.setAvailableSlots(0);
                        return dto;
                    }

                    for (Map.Entry<TimeSlotMinimalProjection, OffsetDateTime> entrySlot : adjustedSlots) {
                        TimeSlotMinimalProjection slot = entrySlot.getKey();
                        OffsetDateTime adjustedStart = entrySlot.getValue();

                        boolean blocked = todaysBlocks.stream().anyMatch(b ->
                                b.getBlockStartTime() != null && b.getBlockEndTime() != null &&
                                        adjustedStart.isBefore(b.getBlockEndTime()) &&
                                        adjustedStart.plusMinutes(1).isAfter(b.getBlockStartTime()));

                        if (!slot.isAttendantDeleted() && !blocked && adjustedStart.isAfter(nowWithOffset)) {
                            dto.setAvailableSlots(1);
                            break;
                        }
                    }

                    dto.setBlockTimes((int) todaysBlocks.stream()
                            .filter(b -> b.getBlockStartTime() != null && b.getBlockEndTime() != null)
                            .count());

                    return dto;
                })
                .sorted(Comparator.comparing(AvailableSlotsCounterDto::getDate))
                .collect(Collectors.toList());

        return new BaseResponseDto<>(Status.SUCCESS, availableSlotsCounterList);
    }




    @Override
    public BaseResponseDto fetchSlotsByAttendantNew(Integer serviceId, Integer venueId, OffsetDateTime slotStartTime, String timeZone) throws EtailBookItException {
        // Convert to the local date in the given timezone
        LocalDate requestDate = slotStartTime.atZoneSameInstant(ZoneId.of(timeZone)).toLocalDate();
        OffsetDateTime wideStartTimeUTC = slotStartTime.minusHours(2); // or minusDays(1)
        OffsetDateTime wideEndTimeUTC = slotStartTime.plusDays(1).plusHours(2);


        Venue venue = venueRepository.findByVenueId(venueId);
        if (venue == null) throw new EtailBookItException("No venue found with the id ::" + venueId);

        com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
        if (service == null) throw new EtailBookItException("No service found with the id ::" + serviceId);

        // Check full-day block
        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByBlockDateAndRetailer(requestDate, RetailerContext.getRetailer());
        for (BlockDateInfo b : blockDateInfoList) {
            if (b.getBlockStartTime() == null && b.getBlockEndTime() == null) {
                throw new BadRequestException("This day is blocked");
            }
        }

        // Fetch matching slots
        List<TimeSlotProjection> slotProjections = timeSlotRepository.findBySlotSlotStartTimeServiceVenueAndRetailer(
                        wideStartTimeUTC, wideEndTimeUTC, service, venue, RetailerContext.getRetailer(), false
                ).stream()
                .filter(t -> !t.getAttendant().getDeleted())
                .collect(Collectors.toList());

        ZoneId zoneId = ZoneId.of(timeZone);

        List<TimeSlotsDto> slotsDtoList = new ArrayList<>();

        for (TimeSlotProjection proj : slotProjections) {
            TimeSlotsDto dto = mapToTimeSlotsDto(proj);
            List<TimeSlots> clusterSlots = timeSlotClusterRepository.findSlotByClusterId(dto.getTimeSlotClusterId());

            if (clusterSlots.isEmpty()) continue;

            OffsetDateTime parentSlotStartUTC = clusterSlots.get(0).getSlotStartTime();
            OffsetDateTime requestedSlotStartUTC = dto.getSlotStartTime();

            int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentSlotStartUTC);
            int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, requestedSlotStartUTC);
            int dstDiff = parentDST - currentDST;

            OffsetDateTime adjustedStart = requestedSlotStartUTC;
            OffsetDateTime adjustedEnd = dto.getSlotEndTime();

            if (dstDiff != 0) {
                adjustedStart = adjustedStart.plusMinutes(dstDiff);
                adjustedEnd = adjustedEnd.plusMinutes(dstDiff);
            }

            ZonedDateTime adjustedZonedStart = adjustedStart.atZoneSameInstant(zoneId);
            if (!adjustedZonedStart.toLocalDate().equals(requestDate)) continue;

            dto.setSlotStartTime(adjustedStart);
            dto.setSlotEndTime(adjustedEnd);

            // Check block times
            boolean blocked = blockDateInfoList.stream().anyMatch(b ->
                    b.getBlockStartTime() != null && b.getBlockEndTime() != null &&
                            dto.getSlotStartTime().isBefore(b.getBlockEndTime()) &&
                            dto.getSlotEndTime().isAfter(b.getBlockStartTime()));

            if (!blocked) {
                slotsDtoList.add(dto);
            }
        }


        // Group by attendant name
        Map<String, List<TimeSlotsDto>> attendantSlotMap = slotsDtoList.stream().collect(Collectors.toMap(
                TimeSlotsDto::getAttendantName,
                Collections::singletonList,
                (list1, list2) -> {
                    List<TimeSlotsDto> merged = new ArrayList<>(list1);
                    merged.addAll(list2);
                    merged.sort(Comparator.comparing(TimeSlotsDto::getSlotStartTime));
                    return merged;
                }
        ));

        return new BaseResponseDto<>(Status.SUCCESS, attendantSlotMap);
    }

    @Override
    public BaseResponseDto getAllTimeSlots1(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDateOffset, OffsetDateTime endDateOffset, String timeZone) throws EtailBookItException {
        long startTime = System.currentTimeMillis();
        
        try {
            LOGGER.debug("getAllTimeSlots1 called with date range: {} to {}", startDateOffset, endDateOffset);
            
            if (startDateOffset == null || endDateOffset == null) {
                LOGGER.warn("Start date or end date is null, returning empty result");
                return new BaseResponseDto(Status.SUCCESS, new ArrayList<>());
            }
            
            // OPTIMIZATION 1: Calculate timezone offset once
            ZoneId zoneId = ZoneId.of(timeZone);
            int offsetInSeconds = zoneId.getRules().getOffset(Instant.now()).getTotalSeconds();
            int offsetDifferenceInMinutes = offsetInSeconds / 60;
            
            // OPTIMIZATION 2: Single database query for entire date range
            OffsetDateTime wideStartUTC = UserTimeZoneConversion.getStartOfDayInUTC(timeZone, startDateOffset).minusHours(2);
            OffsetDateTime wideEndUTC = UserTimeZoneConversion.getEndOfDayInUTC(timeZone, endDateOffset).plusHours(2);
            
            LOGGER.debug("Fetching timeslots for range: {} to {}", wideStartUTC, wideEndUTC);
            
            // OPTIMIZATION 3: Single query to get all timeslots for the entire range
            List<TimeSlotWeekViewProjection> allSlots = timeSlotRepository.getAllTimeSlots(
                wideStartUTC, wideEndUTC, RetailerContext.getRetailer(), offsetDifferenceInMinutes, false
            );
            
            if (allSlots.isEmpty()) {
                LOGGER.debug("No timeslots found for the specified range");
                return new BaseResponseDto(Status.SUCCESS, new ArrayList<>());
            }
            
            // OPTIMIZATION 4: Single query to get all cluster data
            Set<Integer> clusterIds = allSlots.stream()
                .map(TimeSlotWeekViewProjection::getClusterId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
            
            List<TimeSlots> clusterSlots = timeSlotClusterRepository.findAllByClusterIdIn(clusterIds);
            
            // OPTIMIZATION 5: Create cluster map once
            Map<Integer, OffsetDateTime> clusterParentStartMap = clusterSlots.stream()
                .collect(Collectors.toMap(
                    t -> t.getTimeSlotCluster().getClusterId(),
                    TimeSlots::getSlotStartTime,
                    (s1, s2) -> s1.isBefore(s2) ? s1 : s2
                ));
            
            // OPTIMIZATION 6: Process all slots in single pass with efficient filtering
            List<TimeSlotsDto> adjustedSlots = allSlots.stream()
                .filter(slot -> {
                    try {
                        OffsetDateTime slotStart = OffsetDateTime.parse(slot.getSlotStartTime());
                        Integer clusterId = slot.getClusterId();
                        OffsetDateTime parentStart = clusterParentStartMap.get(clusterId);
                        
                        if (parentStart == null) return false;
                        
                        // Apply DST adjustment
                        int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentStart);
                        int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, slotStart);
                        int dstDiff = parentDST - currentDST;
                        
                        OffsetDateTime adjustedStart = dstDiff != 0 ? 
                            slotStart.plusMinutes(dstDiff) : slotStart;
                        
                        // Check if slot falls within requested date range
                        ZonedDateTime localAdjustedStart = adjustedStart.atZoneSameInstant(zoneId);
                        LocalDate slotDate = localAdjustedStart.toLocalDate();
                        
                        return !slotDate.isBefore(startDateOffset.atZoneSameInstant(zoneId).toLocalDate()) &&
                               !slotDate.isAfter(endDateOffset.atZoneSameInstant(zoneId).toLocalDate());
                        
                    } catch (Exception e) {
                        LOGGER.warn("Error processing slot: {}", e.getMessage());
                        return false;
                    }
                })
                .map(slot -> {
                    try {
                        OffsetDateTime slotStart = OffsetDateTime.parse(slot.getSlotStartTime());
                        OffsetDateTime slotEnd = OffsetDateTime.parse(slot.getSlotEndTime());
                        Integer clusterId = slot.getClusterId();
                        OffsetDateTime parentStart = clusterParentStartMap.get(clusterId);
                        
                        // Apply DST adjustment
                        int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentStart);
                        int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, slotStart);
                        int dstDiff = parentDST - currentDST;
                        
                        OffsetDateTime adjustedStart = slotStart;
                        OffsetDateTime adjustedEnd = slotEnd;
                        
                        if (dstDiff != 0) {
                            adjustedStart = slotStart.plusMinutes(dstDiff);
                            adjustedEnd = slotEnd.plusMinutes(dstDiff);
                        }
                        
                        TimeSlotsDto dto = mapWeekViewProjToDto(slot);
                        dto.setSlotStartTime(adjustedStart);
                        dto.setSlotEndTime(adjustedEnd);
                        
                        return dto;
                        
                    } catch (Exception e) {
                        LOGGER.warn("Error mapping slot: {}", e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            
            // OPTIMIZATION 7: Return all results without pagination
            long executionTime = System.currentTimeMillis() - startTime;
            LOGGER.debug("getAllTimeSlots1 completed in {}ms, processed {} slots, returned {} slots", 
                executionTime, allSlots.size(), adjustedSlots.size());
            
            return new BaseResponseDto(Status.SUCCESS, adjustedSlots);
            
        } catch (Exception e) {
            LOGGER.error("Error in getAllTimeSlots1: {}", e.getMessage(), e);
            throw new EtailBookItException("Failed to retrieve timeslots: " + e.getMessage());
        }
    }

    @Override
    public BaseResponseDto getSlotsByDateNew(OffsetDateTime slotDateTime, String timeZone, Integer pageNo, Integer pageSize, String sortBy) {
        OffsetDateTime wideStartUTC = slotDateTime.minusHours(2);
        OffsetDateTime wideEndUTC = UserTimeZoneConversion.getEndOfDayInUTC(timeZone, slotDateTime).plusHours(2);
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate requestDate = slotDateTime.atZoneSameInstant(zoneId).toLocalDate();

        List<TimeSlotsDto> adjustedSlots = new ArrayList<>(pageSize);
        int dbPageSize = 30; // safe batch size
        int dbPageIndex = 0;
        long totalDBElements = 0;
        int skippedValidSlots = pageNo * pageSize; // skip earlier page results

        while (adjustedSlots.size() < pageSize) {
            Pageable paging = PageRequest.of(dbPageIndex++, dbPageSize, Sort.by(sortBy).descending());
            Page<TimeSlotProjection> page = timeSlotRepository.getSlotsByDateTimeRange(
                    wideStartUTC, wideEndUTC, RetailerContext.getRetailer(), false, paging);

            if (page.isEmpty()) break;
            totalDBElements = page.getTotalElements();

            // Collect cluster IDs
            Set<Integer> clusterIds = page.getContent().stream()
                    .map(p -> p.getTimeSlotCluster() != null ? p.getTimeSlotCluster().getClusterId() : null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (clusterIds.isEmpty()) continue;

            // Fetch cluster parents once
            List<TimeSlots> allClusterSlots = timeSlotClusterRepository.findAllByClusterIdIn(clusterIds);
            Map<Integer, OffsetDateTime> clusterParentStartMap = allClusterSlots.stream()
                    .collect(Collectors.toMap(
                            ts -> ts.getTimeSlotCluster().getClusterId(),
                            TimeSlots::getSlotStartTime,
                            (slot1, slot2) -> slot1.isBefore(slot2) ? slot1 : slot2
                    ));

            for (TimeSlotProjection proj : page.getContent()) {
                OffsetDateTime currentStartUTC = proj.getSlotStartTime();
                OffsetDateTime currentEndUTC = proj.getSlotEndTime();
                Integer clusterId = proj.getTimeSlotCluster().getClusterId();

                OffsetDateTime parentStartUTC = clusterParentStartMap.get(clusterId);
                if (parentStartUTC == null) continue;

                int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentStartUTC);
                int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, currentStartUTC);
                int dstDiff = parentDST - currentDST;

                OffsetDateTime adjustedStart = currentStartUTC.plusMinutes(dstDiff);
                OffsetDateTime adjustedEnd = currentEndUTC.plusMinutes(dstDiff);

                if (!adjustedStart.atZoneSameInstant(zoneId).toLocalDate().equals(requestDate)) continue;

                // Skip valid slots from earlier pages
                if (skippedValidSlots > 0) {
                    skippedValidSlots--;
                    continue;
                }

                TimeSlotsDto dto = mapToTimeSlotsDtoAll(proj);
                dto.setSlotStartTime(adjustedStart);
                dto.setSlotEndTime(adjustedEnd);
                adjustedSlots.add(dto);

                if (adjustedSlots.size() == pageSize) break;
            }

            // Stop if we already hit the end of DB results
            if ((dbPageIndex * dbPageSize) >= totalDBElements) break;
        }

        // Build final pageable result
        Pageable frontendPaging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
       // Page<TimeSlotsDto> paginated = new PageImpl<>(adjustedSlots, frontendPaging, totalDBElements);
        long filteredTotalCount = calculateTotalValidSlots(wideStartUTC, wideEndUTC, requestDate, timeZone);
        Page<TimeSlotsDto> paginated = new PageImpl<>(adjustedSlots, frontendPaging, filteredTotalCount);


        return new BaseResponseDto<>(Status.SUCCESS, paginated);
    }

    private long calculateTotalValidSlots(OffsetDateTime wideStartUTC, OffsetDateTime wideEndUTC, LocalDate requestDate, String timeZone) {
        ZoneId zoneId = ZoneId.of(timeZone);
        long validCount = 0;
        int dbPageIndex = 0;
        int dbPageSize = 100; // safe large enough to reduce calls

        while (true) {
            Pageable paging = PageRequest.of(dbPageIndex++, dbPageSize);
            Page<TimeSlotProjection> page = timeSlotRepository.getSlotsByDateTimeRange(
                    wideStartUTC, wideEndUTC, RetailerContext.getRetailer(), false, paging);
            if (page.isEmpty()) break;

            Set<Integer> clusterIds = page.getContent().stream()
                    .map(p -> p.getTimeSlotCluster() != null ? p.getTimeSlotCluster().getClusterId() : null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            if (clusterIds.isEmpty()) continue;

            Map<Integer, OffsetDateTime> clusterParentStartMap = timeSlotClusterRepository.findAllByClusterIdIn(clusterIds)
                    .stream()
                    .collect(Collectors.toMap(
                            ts -> ts.getTimeSlotCluster().getClusterId(),
                            TimeSlots::getSlotStartTime,
                            (slot1, slot2) -> slot1.isBefore(slot2) ? slot1 : slot2
                    ));

            for (TimeSlotProjection proj : page.getContent()) {
                OffsetDateTime startUTC = proj.getSlotStartTime();
                OffsetDateTime parentStartUTC = clusterParentStartMap.get(proj.getTimeSlotCluster().getClusterId());
                if (parentStartUTC == null) continue;

                int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentStartUTC);
                int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, startUTC);
                int dstDiff = parentDST - currentDST;
                OffsetDateTime adjustedStart = startUTC.plusMinutes(dstDiff);

                if (adjustedStart.atZoneSameInstant(zoneId).toLocalDate().equals(requestDate)) {
                    validCount++;
                }
            }

            if (!page.hasNext()) break;
        }

        return validCount;
    }

    private int getAvailableSlotsCounterByDate1New(com.sayone.etailbookit.model.Service service, Venue venue, OffsetDateTime slotStartTime,
                                                   OffsetDateTime slotEndTime, List<BlockDateInfo> blockDateInfoList, String timeZone) throws BadRequestException {

        List<TimeSlotMinimalProjection> timeSlotsList = timeSlotRepository
                .findMinimalBySlotStartTimeAndServiceVenueRetailerBooked(slotStartTime, slotEndTime, service, venue, RetailerContext.getRetailer(), false);

        int offsetTimeDifference = calculateOffset(timeZone);
        OffsetDateTime nowWithOffset = OffsetDateTime.now(ZoneOffset.ofTotalSeconds(offsetTimeDifference * 60));

        if (timeSlotsList.isEmpty()) return 0;

        List<TimeSlotMinimalProjection> filteredSlots = getUnblockedTimeSlotsForCalendar(blockDateInfoList, timeSlotsList).stream()
                .filter(t -> !t.isAttendantDeleted())
                .collect(Collectors.toList());

        Set<Integer> clusterIds = filteredSlots.stream()
                .map(TimeSlotMinimalProjection::getClusterId)
                .collect(Collectors.toSet());
        Map<Integer, TimeSlotMinimalDTO> clusterSlotMap =
                timeSlotClusterRepository.findFirstSlotsByClusterIds(clusterIds).stream()
                        .collect(Collectors.toMap(
                                TimeSlotMinimalDTO::getClusterId,
                                Function.identity(),
                                (s1, s2) -> s1.getSlotStartTime().isBefore(s2.getSlotStartTime()) ? s1 : s2
                        ));



        for (TimeSlotMinimalProjection slot : filteredSlots) {
            TimeSlotMinimalDTO parentSlot = clusterSlotMap.get(slot.getClusterId());
            if (parentSlot == null) continue;

            OffsetDateTime clusterStart = parentSlot.getSlotStartTime();
            OffsetDateTime slotStart = slot.getSlotStartTime();
            int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, clusterStart);
            int slotDST = appointmentServiceImpl.checkDayLightSavings(timeZone, slotStart);

            if (parentDST > 0 && slotDST == 0 &&
                    slotStart.plusMinutes(parentDST + offsetTimeDifference).toLocalDate().isEqual(slotStart.plusDays(1).toLocalDate())) {
                continue;
            } else if (parentDST == 0 && slotDST > 0 &&
                    slotStart.minusMinutes(slotDST - offsetTimeDifference).toLocalDate().isEqual(slotStart.minusDays(1).toLocalDate())) {
                continue;
            }

            if (slot.getSlotStartTime().isAfter(nowWithOffset)) {
                return 1; // early exit if one valid slot found
            }
        }

        return 0;
    }


    private OffsetDateTime getSlotEndTime(OffsetDateTime slotStartTime, ServiceSlotsDto serviceSlotsDto) {
        Integer duration = serviceSlotsDto.getDuration();
        String durationUnit = serviceSlotsDto.getDurationUnit();
        OffsetDateTime slotEndTime = null;
        if (durationUnit.toLowerCase().equalsIgnoreCase("Minutes")) {
            slotEndTime = slotStartTime.plusMinutes(duration);
        } else if (durationUnit.toLowerCase().equalsIgnoreCase("Hours")) {
            slotEndTime = slotStartTime.plusHours(duration);
        } else if (durationUnit.toLowerCase().equalsIgnoreCase("Days")) {
            slotEndTime = slotStartTime.plusDays(duration);
        }
        return slotEndTime;
    }

    @Override
    public BaseResponseDto getSlotById(Integer id,String timeZone) throws EtailBookItException{
        BaseResponseDto baseResponseDto = new BaseResponseDto();
        TimeSlotProjection timeSlot = timeSlotRepository.getTimeSlotsById(id);
        if(timeSlot == null)
            throw new EntityNotFoundException("Time slot not found with id: "+ id);
        TimeSlotsDto timeSlotsDto = createTimeSlotsDto(timeSlot,timeZone);
        baseResponseDto.setData(timeSlotsDto);
        return baseResponseDto;
    }

    @Override
    public BaseResponseDto deleteSlotsById(Integer id,boolean changeEntireRecurringSlots) throws EtailBookItException{
        BaseResponseDto baseResponseDto = new BaseResponseDto();
        TimeSlots timeSlots = timeSlotRepository.findById(id).orElse(null);
        if(timeSlots != null) {
            if(!changeEntireRecurringSlots){
                timeSlotRepository.deleteById(id);
            }else{
              //  List<TimeSlots> timeSlotsList = timeSlotRepository.getTimeSlotsByTimeSlotClusterAndAttendant(timeSlots.getTimeSlotCluster(),timeSlots.getAttendant());
               // timeSlotRepository.deleteInBatch(timeSlotsList);
               //made this change as per ticket BKI-1549
                timeSlotRepository.deleteSlotsFromStartTime(
                        timeSlots.getTimeSlotCluster(),
                        timeSlots.getAttendant().getAttendantId(),
                        timeSlots.getSlotStartTime()
                );

            }

        }
        else
            throw new EntityNotFoundException("Time slot with id: "+ id);
        return baseResponseDto;
    }

    @Override
    public BaseResponseDto updateSlots(int id, ServiceSlotsDto serviceSlotsDto,boolean changeEntireRecurringSlots,String timeZone) throws EtailBookItException{
        List<TimeSlotsDto> timeSlotsDtos = new ArrayList<>();
        TimeSlots timeSlots=timeSlotRepository.findById(id).orElse(null);
        List<TimeSlotsDto> newTimeSlots;
        if(timeSlots == null)
            throw new EntityNotFoundException("Time slot not found with id::"+id);
        if(!changeEntireRecurringSlots){
            if(serviceSlotsDto.getSlotStartTime().toLocalDate().isBefore(LocalDate.now())){
                throw new EtailBookItException("Please select a valid date and time");
            }
            timeSlots.setSlotName(serviceSlotsDto.getSlotName());
            timeSlots.setAvailableDay(serviceSlotsDto.getSlotStartTime().getDayOfWeek().toString());
            Venue venue = venueRepository.findByVenueId(serviceSlotsDto.getVenue());
            if (venue != null) {
                timeSlots.setVenue(venue);
            } else {
                throw new EntityNotFoundException("Venue not found with id::" + serviceSlotsDto.getVenue());
            }
            if(!serviceSlotsDto.getServiceId().isEmpty()) {
                Set<com.sayone.etailbookit.model.Service> services=new HashSet<>();
                for(Integer serviceId:serviceSlotsDto.getServiceId()) {
                    com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
                    if (service != null) {
                        services.add(service);
                    } else {
                        throw new EntityNotFoundException("Service not found with id ::" + serviceSlotsDto.getServiceId());
                    }
                }
                timeSlots.setServices(services);
            }
            if(serviceSlotsDto.getAttendants().size()>1){
                throw new BadRequestException("If you are editing for a single slot please add only one attendant");
            }
            Attendant attendant=attendantRepository.findByAttendantId(serviceSlotsDto.getAttendants().get(0));
            if(attendant!=null){
                timeSlots.setAttendant(attendant);
            }else {
                throw new EntityNotFoundException("Attendant not found with this id");
            }
            /*if(serviceSlotsDto.getAvailableDays().size()>1){
                throw new BadRequestException("If you are editing for a single slot please add only a single value to available days");
            }
            else{
                timeSlots.setAvailableDay(serviceSlotsDto.getAvailableDays().get(0));
            }*/
            timeSlots.setSlotStartTime(serviceSlotsDto.getSlotStartTime());
            timeSlots.setSlotDate(serviceSlotsDto.getSlotStartTime().toLocalDate());
            timeSlots.setRetailer(RetailerContext.getRetailer());
            timeSlots.setSlotEndTime(getSlotEndTime(timeSlots.getSlotStartTime(), serviceSlotsDto));
            timeSlots.setAvailabilityInterval(serviceSlotsDto.getAvailabilityInterval());
            timeSlots.setAvailabilityIntervalUnit(serviceSlotsDto.getAvailabilityIntervalUnit());
            timeSlots.setColor(serviceSlotsDto.getColor());
            TimeSlots freeSlots= timeSlotRepository.save(timeSlots);
/*
            TimeSlotsDto timeSlotsDto=new TimeSlotsDto();
            List<Integer> serviceIds=new ArrayList<>();
            timeSlotsDto.setId(freeSlots.getId());
            for(com.sayone.etailbookit.model.Service service:freeSlots.getServices()){
                serviceIds.add(service.getServiceId());
            }
            timeSlotsDto.setServiceId(serviceIds);
            timeSlotsDto.setAttendantId(freeSlots.getAttendant().getAttendantId());
            timeSlotsDto.setVenueId(freeSlots.getVenue().getVenueId());
            timeSlotsDto.setSlotStartTime(freeSlots.getSlotStartTime());
            timeSlotsDto.setSlotEndTime(freeSlots.getSlotEndTime());
            timeSlotsDto.setSlotName(freeSlots.getSlotName());
            timeSlotsDto.setAvailableDay(freeSlots.getAvailableDay());
            timeSlotsDto.setAvailabilityIntervalUnit(freeSlots.getAvailabilityIntervalUnit());
            timeSlotsDto.setAvailabilityInterval(freeSlots.getAvailabilityInterval());
            timeSlotsDto.setColor(freeSlots.getColor());
            String duration=calculateDuration(freeSlots.getSlotStartTime(),freeSlots.getSlotEndTime());
            String[] words=duration.split("\\s");
            timeSlotsDto.setDuration(Integer.parseInt(words[0]));
            timeSlotsDto.setDurationUnit(words[1]);
           timeSlotsDtos.add(timeSlotsDto);*/
        }else{
           // List<TimeSlots> timeSlotsList = timeSlotRepository.getTimeSlotsByTimeSlotClusterAndAttendant(timeSlots.getTimeSlotCluster(), timeSlots.getAttendant());
            TimeSlotCluster timeSlotCluster = createSlotCluster(serviceSlotsDto);
            OffsetDateTime fromDateTime;
            Optional<TimeSlots> existingSlot = timeSlotRepository.findById(id);
            if (existingSlot.isPresent()) {
                fromDateTime = existingSlot.get().getSlotStartTime();
            } else {
                throw new EntityNotFoundException("Time slot not found with id ::" + id);
            }
            for(Integer attendantId:serviceSlotsDto.getAttendants()) {

                // newTimeSlots =
                // generateSlots(attendantId, serviceSlotsDto, timeSlotsList.get(0).getTimeSlotCluster(),RetailerContext.getRetailer());
                // timeSlotsDtos.addAll(newTimeSlots);
                timeSlotRepository.deleteSlotsFromStartTime(timeSlots.getTimeSlotCluster(), attendantId, fromDateTime);
                try {
                    // Publish slot update event to Kinesis
                    slotUpdatePublisher.publishSlotUpdateEvent(serviceSlotsDto, attendantId, timeSlotCluster.getClusterId(), RetailerContext.getRetailer());
                } catch (EtailBookItException e) {
                    LOGGER.error("Failed to publish slot update event: {}", e.getMessage());
                }
            }
           // timeSlotRepository.deleteInBatch(timeSlotsList);
        }

        //List<TimeSlotsDto> timeSlotsDtos = new ArrayList<>(newTimeSlots);
      /*  BaseResponseDto baseResponseDto=new BaseResponseDto<>();
        baseResponseDto.setData(timeSlotsDtos);*/
        return new BaseResponseDto(Status.SUCCESS);
    }

    private TimeSlotCluster createSlotCluster(ServiceSlotsDto serviceSlotsDto){
        TimeSlotCluster timeSlotCluster = new TimeSlotCluster();
        timeSlotCluster.setAvailableDays(serviceSlotsDto.getAvailableDays());
        return timeSlotClusterRepository.save(timeSlotCluster);
    }

    private int getRecurringDaysCount(Integer availabilityInterval, String availabilityIntervalUnit) throws EtailBookItException {
        int factor;
        if(availabilityIntervalUnit.isEmpty() || availabilityInterval == null)
            availabilityInterval = 0;
        switch (availabilityIntervalUnit.toLowerCase()) {
            case "days":
                factor = 1;
                break;
            case "weeks":
                factor = 7;
                break;
            case "months":
                factor = 30;
                break;
            default:
                factor = 0;
        }
        int recurringDaysCount=availabilityInterval*factor;
       /* LOGGER.info("RECURRING SLOT COUNT :: "+recurringDaysCount);
        if(recurringDaysCount>90){
           recurringDaysCount=90;
        }*/
        return recurringDaysCount;
    }

    public  List<OffsetDateTime> generateSlots(Integer attendantId,ServiceSlotsDto serviceSlotsDto,TimeSlotCluster timeSlotCluster,String retailer)throws EtailBookItException{

        LOGGER.info("Called generateSlots for attendantId={} with clusterId={}", attendantId, timeSlotCluster.getClusterId());
        List<OffsetDateTime> blockedSlotTimes =new ArrayList<>();
        Set<com.sayone.etailbookit.model.Service> services=new HashSet<>();
        List<TimeSlots> timeSlotsList = new ArrayList<>();
        OffsetDateTime slotStartTime = serviceSlotsDto.getSlotStartTime();
        int recurringDaysCount = getRecurringDaysCount(serviceSlotsDto.getAvailabilityInterval(), serviceSlotsDto.getAvailabilityIntervalUnit());
        List<OffsetDateTime> startTimeList = getSlotDays(slotStartTime, recurringDaysCount, serviceSlotsDto);
        Venue venue = venueRepository.findByVenueId(serviceSlotsDto.getVenue());
        if (venue == null) {
            throw new EntityNotFoundException("Venue not found with id::" + serviceSlotsDto.getVenue());
        }
        if(!serviceSlotsDto.getServiceId().isEmpty()) {

            for(Integer serviceId:serviceSlotsDto.getServiceId()) {
                com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
                if (service != null) {
                    services.add(service);
                } else {
                    throw new EntityNotFoundException("Service not found with id ::" + serviceSlotsDto.getServiceId());
                }
            }
        }
        Attendant attendant=attendantRepository.findByAttendantId(attendantId);
        if(attendant==null){
            throw new EntityNotFoundException("Attendant not found with id::"+attendantId);
        }
        for(OffsetDateTime startTime : startTimeList){
                List<BlockDateInfo> blockDateInfo = blockDatesRepository.findByRetailerAndOffsetBlockDate(startTime.toString(),retailer);
                if (checkBlockDate(blockDateInfo, startTime, serviceSlotsDto,retailer)) {
                    blockedSlotTimes.add(startTime);
                    continue;
                }
          /*  // Defensive Check to avoid duplicate slot creation
            boolean slotExists = timeSlotRepository.existsSlotByStartTime(attendantId, startTime, timeSlotCluster.getClusterId(),retailer);
            if (slotExists) {
                LOGGER.warn("Slot already exists for attendantId={} startTime={} clusterId={}", attendantId, startTime, timeSlotCluster.getClusterId());
                continue; // Skip creating the slot
            }*/

            if(serviceSlotsDto.getSlotStartTime().toLocalDate().isBefore(LocalDate.now())){
                throw new EtailBookItException("Please select a valid date and time");
            }
            TimeSlots timeSlots = new TimeSlots();
            timeSlots.setAttendant(attendant);
            timeSlots.setServices(services);
            services.forEach(service -> service.getTimeSlots().add(timeSlots));
            timeSlots.setVenue(venue);
            timeSlots.setSlotName(serviceSlotsDto.getSlotName());
            timeSlots.setAvailableDay(startTime.getDayOfWeek().toString());
            timeSlots.setTimeSlotCluster(timeSlotCluster);
            timeSlots.setSlotStartTime(startTime);
            timeSlots.setSlotDate(startTime.toLocalDate());
            timeSlots.setRetailer(retailer);
            timeSlots.setSlotEndTime(getSlotEndTime(timeSlots.getSlotStartTime(), serviceSlotsDto));
            timeSlots.setAvailabilityInterval(serviceSlotsDto.getAvailabilityInterval());
            timeSlots.setAvailabilityIntervalUnit(serviceSlotsDto.getAvailabilityIntervalUnit());
            timeSlots.setColor(serviceSlotsDto.getColor());
            timeSlotsList.add(timeSlots);
            //  newTimeSlots.add(timeSlotsDto);
        }
        timeSlotRepository.saveAll(timeSlotsList);
        return blockedSlotTimes;
    }

    private List<OffsetDateTime> getSlotDays(OffsetDateTime startTime, int days, ServiceSlotsDto serviceSlotsDto){
        List<OffsetDateTime> startTimeList = new ArrayList<>();
        for(String availableDay : serviceSlotsDto.getAvailableDays()) {
            for (int i = 0; i <= days; i++) {
                OffsetDateTime startDay = serviceSlotsDto.getSlotStartTime().plusDays(i);
                if (!startDay.isBefore(OffsetDateTime.now()) && startDay.getDayOfWeek().toString().equalsIgnoreCase(availableDay)) {
                    startTimeList.add(startDay);
                }
            }
        }
        boolean dayCheck = !serviceSlotsDto.getAvailableDays().stream().map(String::toLowerCase).anyMatch(day -> day.equals(startTime.getDayOfWeek().toString().toLowerCase()));
        if(dayCheck)
            startTimeList.add(startTime);
        return startTimeList;
    }

    private void createTimeSlots(TimeSlots timeSlots, ServiceSlotsDto serviceSlotsDto, Integer attendantId, TimeSlotCluster timeSlotCluster, OffsetDateTime startTime,String retailer) throws EtailBookItException {
        if(serviceSlotsDto.getSlotStartTime().toLocalDate().isBefore(LocalDate.now())){
            throw new EtailBookItException("Please select a valid date and time");
        }
        timeSlots.setSlotName(serviceSlotsDto.getSlotName());
        timeSlots.setAvailableDay(startTime.getDayOfWeek().toString());
        Venue venue = venueRepository.findByVenueId(serviceSlotsDto.getVenue());
        if (venue != null) {
            timeSlots.setVenue(venue);
        } else {
            throw new EntityNotFoundException("Venue not found with id::" + serviceSlotsDto.getVenue());
        }
        if(!serviceSlotsDto.getServiceId().isEmpty()) {
            Set<com.sayone.etailbookit.model.Service> services=new HashSet<>();
            for(Integer serviceId:serviceSlotsDto.getServiceId()) {
                com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
                if (service != null) {
                    services.add(service);
                } else {
                    throw new EntityNotFoundException("Service not found with id ::" + serviceSlotsDto.getServiceId());
                }
            }
            timeSlots.setServices(services);
            services.forEach(service -> service.getTimeSlots().add(timeSlots));
        }
        Attendant attendant=attendantRepository.findByAttendantId(attendantId);
        if(attendant!=null){
            timeSlots.setAttendant(attendant);
        }else {
            throw new EntityNotFoundException("Attendant not found with id::"+attendantId);
        }
        timeSlots.setTimeSlotCluster(timeSlotCluster);
        timeSlots.setSlotStartTime(startTime);
        timeSlots.setSlotDate(startTime.toLocalDate());
        timeSlots.setRetailer(retailer);
        timeSlots.setSlotEndTime(getSlotEndTime(timeSlots.getSlotStartTime(), serviceSlotsDto));
        timeSlots.setAvailabilityInterval(serviceSlotsDto.getAvailabilityInterval());
        timeSlots.setAvailabilityIntervalUnit(serviceSlotsDto.getAvailabilityIntervalUnit());
        timeSlots.setColor(serviceSlotsDto.getColor());
        TimeSlots freeSlots=timeSlotRepository.save(timeSlots);
       // return convertToTimeSlotDto(freeSlots);
    }
  private  TimeSlotsDto convertToTimeSlotDto(TimeSlots freeSlots){
      TimeSlotsDto timeSlotsDto = new TimeSlotsDto();
      List<Integer> serviceIds=new ArrayList<>();
      timeSlotsDto.setId(freeSlots.getId());
      for(com.sayone.etailbookit.model.Service service:freeSlots.getServices()){
          serviceIds.add(service.getServiceId());
      }
      timeSlotsDto.setServiceId(serviceIds);
      timeSlotsDto.setAttendantId(freeSlots.getAttendant().getAttendantId());
      timeSlotsDto.setVenueId(freeSlots.getVenue().getVenueId());
      timeSlotsDto.setSlotStartTime(freeSlots.getSlotStartTime());
      timeSlotsDto.setSlotEndTime(freeSlots.getSlotEndTime());
      timeSlotsDto.setSlotName(freeSlots.getSlotName());
      timeSlotsDto.setAvailableDays(freeSlots.getTimeSlotCluster().getAvailableDays());
      timeSlotsDto.setAvailabilityIntervalUnit(freeSlots.getAvailabilityIntervalUnit());
      timeSlotsDto.setAvailabilityInterval(freeSlots.getAvailabilityInterval());
      timeSlotsDto.setColor(freeSlots.getColor());
      String duration=calculateDuration(freeSlots.getSlotStartTime(),freeSlots.getSlotEndTime());
      String[] words=duration.split("\\s");
      timeSlotsDto.setDuration(Integer.parseInt(words[0]));
      timeSlotsDto.setDurationUnit(words[1]);
      return timeSlotsDto;
    }
    private TimeSlotsDto createTimeSlotsDto (TimeSlotProjection timeSlots,String timeZone){
        List<Integer> serviceIds=new ArrayList<>();
        List<String> serviceNames=new ArrayList<>();
        TimeSlotsDto timeSlotsDto = new TimeSlotsDto();
        timeSlotsDto.setId(timeSlots.getId());
        for(TimeSlotProjection.Service service:timeSlots.getService()){
            serviceIds.add(service.getServiceId());
            serviceNames.add(service.getServiceName());
        }
        timeSlotsDto.setServiceId(serviceIds);
        timeSlotsDto.setServiceName(serviceNames);
        timeSlotsDto.setAttendantId(timeSlots.getAttendant().getAttendantId());
        timeSlotsDto.setAttendantName(timeSlots.getAttendant().getAttendantName());
        timeSlotsDto.setVenueId(timeSlots.getVenue().getVenueId());
        timeSlotsDto.setVenueName(timeSlots.getVenue().getVenueName());
        List<TimeSlots> clusterSlots = timeSlotClusterRepository.findSlotByClusterId(timeSlots.getTimeSlotCluster().getClusterId());

        OffsetDateTime parentSlotStartUTC = clusterSlots.get(0).getSlotStartTime();
        OffsetDateTime requestedSlotStartUTC = timeSlots.getSlotStartTime();

        int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentSlotStartUTC);
        int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, requestedSlotStartUTC);
        int dstDiff = parentDST - currentDST;

        OffsetDateTime adjustedStart = requestedSlotStartUTC;
        OffsetDateTime adjustedEnd = timeSlots.getSlotEndTime();

        if (dstDiff != 0) {
            adjustedStart = adjustedStart.plusMinutes(dstDiff);
            adjustedEnd = adjustedEnd.plusMinutes(dstDiff);
            timeSlotsDto.setSlotStartTime(adjustedStart);
            timeSlotsDto.setSlotEndTime(adjustedEnd);
        }else{
            timeSlotsDto.setSlotStartTime(timeSlots.getSlotStartTime());
            timeSlotsDto.setSlotEndTime(timeSlots.getSlotEndTime());
        }

        timeSlotsDto.setSlotName(timeSlots.getSlotName());
        timeSlotsDto.setAvailableDays(timeSlots.getTimeSlotCluster().getAvailableDays());
        timeSlotsDto.setAvailabilityInterval(timeSlots.getAvailabilityInterval());
        timeSlotsDto.setAvailabilityIntervalUnit(timeSlots.getAvailabilityIntervalUnit());
        String duration=calculateDuration(timeSlots.getSlotStartTime(),timeSlots.getSlotEndTime());
        String[] words=duration.split("\\s");
        timeSlotsDto.setDuration(Integer.parseInt(words[0]));
        timeSlotsDto.setDurationUnit(words[1]);
        timeSlotsDto.setColor(timeSlots.getColor());
        return timeSlotsDto;
    }

    private String calculateDuration(OffsetDateTime slotStartTime, OffsetDateTime slotEndTime){
        String duration = new String();
        if (slotStartTime != null && slotEndTime != null) {
            Duration difference = Duration.ZERO;
            difference=Duration.between(slotStartTime,slotEndTime);
            if (difference.toDays() >= 1) {
                duration = difference.toDays() + " " + "Days";
            }
            else if (difference.toHours() >= 1) {
//                long seconds=   difference.getSeconds();
//                long HH = seconds / 3600;
//                long MM = (seconds % 3600) / 60;
//                long SS = seconds % 60;
//                if(difference.toHoursPart()>=1){
                    duration=difference.toHoursPart()+ " " + "Hours";
//                }
            } else {
                duration = difference.toMinutes() + " " + "Minutes";
            }
        }
        return duration;
    }

    @Override
    public BaseResponseDto getSlotsByDate(OffsetDateTime slotDateTime,String timeZone) throws EtailBookItException {
        OffsetDateTime tempDate=slotDateTime;
        Integer offsetDifference=calculateOffset(timeZone);
        slotDateTime=slotDateTime.plusMinutes(offsetDifference);
        OffsetDateTime offsetAddedDate=slotDateTime;
        List<TimeSlotProjection> entireTimeSlotList=new ArrayList<>();
        List<TimeSlotProjection> timeSlotsOffset=timeSlotRepository.findBySlotDateAndRetailer(offsetAddedDate.toLocalDate(), RetailerContext.getRetailer());
        entireTimeSlotList.addAll(timeSlotsOffset);
        List<TimeSlotProjection> filteredSlots= filterSlots(entireTimeSlotList, offsetAddedDate, offsetDifference, tempDate);

        if (offsetDifference > 0) {
            if(offsetAddedDate.toLocalDate()!=tempDate.toLocalDate()) {
                List<TimeSlotProjection> slotPreviousDate = timeSlotRepository.findBySlotDateAndRetailer(tempDate.toLocalDate(), RetailerContext.getRetailer());
                entireTimeSlotList.addAll(slotPreviousDate);
                filterSlots(entireTimeSlotList,offsetAddedDate,offsetDifference,tempDate);
            }
        }else{
            if(offsetAddedDate.toLocalDate()!=tempDate.toLocalDate()) {
                List<TimeSlotProjection> slotAfterDate = timeSlotRepository.findBySlotDateAndRetailer(tempDate.toLocalDate(), RetailerContext.getRetailer());
                entireTimeSlotList.addAll(slotAfterDate);
                filterSlots(entireTimeSlotList, offsetAddedDate, offsetDifference, tempDate);
            }
        }
        BaseResponseDto<Object> responseDto=new BaseResponseDto<>();
        responseDto.setData(filteredSlots);
        return responseDto;
    }

    public BaseResponseDto getSlotsByDate1(OffsetDateTime slotDateTime,String timeZone,Integer pageNo, Integer pageSize, String sortBy){
      /* // String startDateTime = slotDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm'Z'"));
       // String endDateTime = slotDateTime.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm'Z'"));
        OffsetDateTime endDateTime =UserTimeZoneConversion.getEndOfDayInUTC(timeZone,slotDateTime);
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        Page<TimeSlotProjection> timeSlotProjections;
        timeSlotProjections = timeSlotRepository.getSlotsByDateTimeRange(slotDateTime, endDateTime,RetailerContext.getRetailer(),false,paging);
        BaseResponseDto responseDto=new BaseResponseDto<>(Status.SUCCESS,timeSlotProjections);
        return responseDto;*/

        OffsetDateTime wideStartUTC = slotDateTime.minusHours(2);
        OffsetDateTime wideEndUTC = UserTimeZoneConversion.getEndOfDayInUTC(timeZone, slotDateTime).plusHours(2);
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());
        // Fetch slots within the wide window
        Page<TimeSlotProjection> timeSlotProjections = timeSlotRepository.getSlotsByDateTimeRange(
                wideStartUTC, wideEndUTC, RetailerContext.getRetailer(), false, paging);

        // Collect all unique cluster IDs from the fetched slots
        Set<Integer> clusterIds = timeSlotProjections.stream()
                .map(p -> p.getTimeSlotCluster() != null ? p.getTimeSlotCluster().getClusterId() : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Fetch all cluster slots in one DB call
        List<TimeSlots> allClusterSlots = timeSlotClusterRepository.findAllByClusterIdIn(clusterIds);

        // Map clusterId -> first slot (parent)
        Map<Integer, OffsetDateTime> clusterParentStartMap = allClusterSlots.stream()
                .collect(Collectors.toMap(
                        ts -> ts.getTimeSlotCluster().getClusterId(),
                        TimeSlots::getSlotStartTime,
                        (slot1, slot2) -> slot1.isBefore(slot2) ? slot1 : slot2 // pick earliest
                ));

        List<TimeSlotsDto> adjustedSlots = new ArrayList<>();
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate requestDate = slotDateTime.atZoneSameInstant(zoneId).toLocalDate();

        for (TimeSlotProjection proj : timeSlotProjections) {
            OffsetDateTime currentStartUTC = proj.getSlotStartTime();
            OffsetDateTime currentEndUTC = proj.getSlotEndTime();
            Integer clusterId = proj.getTimeSlotCluster().getClusterId();

            OffsetDateTime parentStartUTC = clusterParentStartMap.get(clusterId);
            if (parentStartUTC == null) continue;

            int parentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, parentStartUTC);
            int currentDST = appointmentServiceImpl.checkDayLightSavings(timeZone, currentStartUTC);
            int dstDiff = parentDST - currentDST;

            OffsetDateTime adjustedStart = currentStartUTC;
            OffsetDateTime adjustedEnd = currentEndUTC;

            if (dstDiff != 0) {
                adjustedStart = adjustedStart.plusMinutes(dstDiff);
                adjustedEnd = adjustedEnd.plusMinutes(dstDiff);
            }

            // Ensure the slot still belongs to the requested day
            ZonedDateTime localStart = adjustedStart.atZoneSameInstant(zoneId);
            if (!localStart.toLocalDate().equals(requestDate)) continue;

            TimeSlotsDto dto = mapToTimeSlotsDtoAll(proj);
            dto.setSlotStartTime(adjustedStart);
            dto.setSlotEndTime(adjustedEnd);

            adjustedSlots.add(dto);
        }
        System.out.println("adjustedSlots ::"+adjustedSlots.size());
        // Return filtered slots as a Page-like response (manual pagination)
        // No subList — take all adjusted slots
        // already filtered and adjusted

// Wrap with original pagination metadata from DB
        Page<TimeSlotsDto> paginated = new PageImpl<>(
                adjustedSlots,
                paging,
                timeSlotProjections.getTotalElements()  // Use DB page metadata
        );

        return new BaseResponseDto<>(Status.SUCCESS, paginated);
    }

    @Override
    public BaseResponseDto fetchSlotsByAttendant(Integer serviceId, Integer venueId, OffsetDateTime slotStartTime, String timeZone) throws EtailBookItException {
        List<TimeSlotsDto> timeSlotsDtoList = new ArrayList<>();
        List<TimeSlotsDto> timeSlotRemoveList = new ArrayList<>();
        Map<String, List<TimeSlotsDto>> attendantSlotsMap;

        OffsetDateTime slotEndTime;
        LocalDate currentDate = OffsetDateTime.now(ZoneOffset.UTC).toLocalDate(); // ✅ Always use UTC
        LocalDate slotDate = slotStartTime.toLocalDate();

        ZonedDateTime zonedDateTime = slotStartTime.atZoneSameInstant(ZoneId.of(timeZone));
        int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
        int offsetTimeDifference = offsetInSeconds / 60;

        if (slotDate.equals(currentDate)) {
            slotEndTime = UserTimeZoneConversion.getEndOfDayInUTC(timeZone, slotStartTime); // ✅ accurate end-of-day
        } else {
            slotEndTime = slotStartTime.plusDays(1);
        }

        Venue venue = venueRepository.findByVenueId(venueId);
        if (venue == null) {
            throw new EtailBookItException("No venue found with the id ::" + venueId);
        }

        com.sayone.etailbookit.model.Service service = serviceRepository.findByServiceId(serviceId);
        if (service == null) {
            throw new EtailBookItException("No service found with the id ::" + serviceId);
        }

        OffsetDateTime offsetDateTime = slotStartTime;
        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByBlockDateAndRetailer(offsetDateTime.toLocalDate(), RetailerContext.getRetailer());

        if (!blockDateInfoList.isEmpty()) {
            for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                if (blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null) {
                    throw new BadRequestException("This day is blocked");
                }
            }
        }

        List<TimeSlotProjection> timeSlotsProjectionList = timeSlotRepository.findBySlotSlotStartTimeServiceVenueAndRetailer(
                slotStartTime, slotEndTime, service, venue, RetailerContext.getRetailer(), false
        );

        List<TimeSlotProjection> timeSlotsProjectionListFiltered = timeSlotsProjectionList.stream()
                .filter(t -> !t.getAttendant().getDeleted())
                .collect(Collectors.toList());

        if (!blockDateInfoList.isEmpty()) {
            timeSlotsDtoList = timeSlotsProjectionListFiltered.stream()
                    .filter(slot -> blockDateInfoList.stream().noneMatch(bt ->
                            bt.getBlockStartTime() != null &&
                                    bt.getBlockEndTime() != null &&
                                    slot.getSlotStartTime().isBefore(bt.getBlockEndTime()) &&
                                    slot.getSlotEndTime().isAfter(bt.getBlockStartTime())
                    ))
                    .map(this::mapToTimeSlotsDto)
                    .collect(Collectors.toList());
        } else {
            timeSlotsDtoList = timeSlotsProjectionListFiltered.stream()
                    .map(this::mapToTimeSlotsDto)
                    .collect(Collectors.toList());
        }

        for (TimeSlotsDto timeSlotDto : timeSlotsDtoList) {
            List<TimeSlots> timeSlots = timeSlotClusterRepository.findSlotByClusterId(timeSlotDto.getTimeSlotClusterId());
            TimeSlots timeSlot = timeSlots.get(0);
            OffsetDateTime clusterSlotStartTime = timeSlot.getSlotStartTime();

            // Daylight saving check
            Integer parentSlotDST = appointmentServiceImpl.checkDayLightSavings(timeZone, clusterSlotStartTime);
            Integer requestDST = appointmentServiceImpl.checkDayLightSavings(timeZone, slotStartTime);

            if (parentSlotDST > 0 && requestDST == 0) {
                OffsetDateTime updatedStart = timeSlotDto.getSlotStartTime().plusMinutes(parentSlotDST);
                OffsetDateTime updatedEnd = timeSlotDto.getSlotEndTime().plusMinutes(parentSlotDST);

                if (updatedStart.plusMinutes(offsetTimeDifference).toLocalDate().isEqual(slotStartTime.plusDays(1).toLocalDate())) {
                    timeSlotRemoveList.add(timeSlotDto);
                    continue;
                }

                timeSlotDto.setSlotStartTime(updatedStart);
                timeSlotDto.setSlotEndTime(updatedEnd);

            } else if (parentSlotDST == 0 && requestDST > 0) {
                OffsetDateTime updatedStart = timeSlotDto.getSlotStartTime().minusMinutes(requestDST);
                OffsetDateTime updatedEnd = timeSlotDto.getSlotEndTime().minusMinutes(requestDST);

                if (updatedStart.plusMinutes(offsetTimeDifference).toLocalDate().isEqual(slotStartTime.minusDays(1).toLocalDate())) {
                    timeSlotRemoveList.add(timeSlotDto);
                    continue;
                }

                timeSlotDto.setSlotStartTime(updatedStart);
                timeSlotDto.setSlotEndTime(updatedEnd);
            }
        }

        timeSlotsDtoList.removeAll(timeSlotRemoveList);

        attendantSlotsMap = timeSlotsDtoList.stream().collect(Collectors.toMap(
                TimeSlotsDto::getAttendantName,
                Collections::singletonList,
                (existingList, replacementList) -> {
                    List<TimeSlotsDto> merged = new ArrayList<>(existingList);
                    merged.addAll(replacementList);
                    merged.sort(Comparator.comparing(TimeSlotsDto::getSlotStartTime));
                    return merged;
                }
        ));

        return new BaseResponseDto<>(Status.SUCCESS, attendantSlotsMap);
    }

//this method is commented out as part of the story 443851
  /*  @Override
    public BaseResponseDto fetchSlotsByAttendantOfaMonth(Integer serviceId, Integer venueId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException {
        List<AvailableSlotsCounterDto> availableSlotsCounterList = new ArrayList<>();

        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByRetailerAndOffsetBlockDateBetween(RetailerContext.getRetailer(), startDate.toString(), endDate.toString());
        OffsetDateTime currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            OffsetDateTime dayStartUtc=  UserTimeZoneConversion.getStartOfDayInUTC(timeZone,currentDate);
            OffsetDateTime dayEndUtc =UserTimeZoneConversion.getEndOfDayInUTC(timeZone,currentDate);
            ZonedDateTime zonedDateTime = currentDate.atZoneSameInstant(ZoneId.of(timeZone));
            int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
            int offsetTimeDifference= offsetInSeconds / 60;
            AvailableSlotsCounterDto availableSlotsCounterDto = new AvailableSlotsCounterDto();
            availableSlotsCounterDto.setDate(dayStartUtc);
            availableSlotsCounterDto.setAvailableSlots(0);
            boolean blocked = false;
            if (!blockDateInfoList.isEmpty()) {
                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (blockDateInfo.getBlockStartTime() == null
                            && blockDateInfo.getBlockEndTime() == null
                            && dayStartUtc.plusMinutes(offsetTimeDifference).toLocalDate().isEqual(OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).toLocalDate())
                        //  && !OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).isBefore(currentDate)
                        // &&!OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).isAfter(currentDate)
                    ) {
                        blocked = true;
                        availableSlotsCounterDto.setBlockFullDay(true);
                        break;
                    }
                }
            }
            if (!blocked) {
                availableSlotsCounterDto.setAvailableSlots(getAvailableSlotsCounterByDate1(
                        serviceId,
                        venueId,
                        //  currentDate.atTime(OffsetTime.of(0, 0, 0, 0, ZoneOffset.of(OffsetContext.getOffset()))),
                        //todo :remove adding offset time difference once availability revamp is completed;
                        dayStartUtc,
                        dayEndUtc,
                        blockDateInfoList, timeZone));
                availableSlotsCounterDto.setBlockTimes(0);

                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (blockDateInfo.getBlockStartTime() != null && blockDateInfo.getBlockEndTime() != null) {
                        LocalDate localBlockStartDate = LocalDate.of(blockDateInfo.getBlockStartTime().plusMinutes(offsetTimeDifference).getYear(), blockDateInfo.getBlockStartTime().plusMinutes(offsetTimeDifference).getMonth(), blockDateInfo.getBlockStartTime().plusMinutes(offsetTimeDifference).getDayOfMonth());
                        LocalDate localBlockEndDate = LocalDate.of(blockDateInfo.getBlockEndTime().plusMinutes(offsetTimeDifference).getYear(), blockDateInfo.getBlockEndTime().plusMinutes(offsetTimeDifference).getMonth(), blockDateInfo.getBlockEndTime().plusMinutes(offsetTimeDifference).getDayOfMonth());
                        if (blockDateInfo.getBlockStartTime() != null
                                && blockDateInfo.getBlockEndTime() != null
                                && !localBlockStartDate.isBefore(dayStartUtc.plusMinutes(offsetTimeDifference).toLocalDate())
                                && !localBlockEndDate.isAfter(dayStartUtc.plusMinutes(offsetTimeDifference).toLocalDate()))
                        // && !OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).isBefore(currentDate.plusMinutes(offsetTimeDifference))
                        //&&!OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).isAfter(currentDate.plusMinutes(offsetTimeDifference)))
                        {
                            availableSlotsCounterDto.setBlockTimes(availableSlotsCounterDto.getBlockTimes() + 1);
                        }
                    }
                }
            }

            availableSlotsCounterList.add(availableSlotsCounterDto);
            currentDate = dayEndUtc.plusMinutes(1);
        }
        BaseResponseDto responseDto=new BaseResponseDto<>(Status.SUCCESS,availableSlotsCounterList);
        return responseDto;
    }*/

    @Override
    public BaseResponseDto getAllTimeSlotsofAMonth(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) {

        BaseResponseDto responseDto;
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());

        List<TimeSlotProjectionOfAmonth> dataList = new ArrayList<>();

        if (startDate != null && endDate != null) {
            // Adjust endDate to be the last moment of the day (23:59:59)
            //endDate = endDate.withHour(23).withMinute(59).withSecond(59);
            endDate=UserTimeZoneConversion.getEndOfDayInUTC(timeZone,endDate);
            ZoneId zoneId = ZoneId.of(timeZone);
            Map<LocalDate, Integer> dailyOffsets = new HashMap<>();
            OffsetDateTime current = startDate;

            while (!current.isAfter(endDate)) {
                ZonedDateTime zonedDateTime = current.atZoneSameInstant(ZoneId.of(timeZone));
                int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
                int offsetDifferenceInMinutes= offsetInSeconds / 60;  // Offset in minutes
                /*ZoneOffset offset = zoneId.getRules().getOffset(current.toInstant());
               int offsetDifferenceInMinutes = offset.getTotalSeconds() / 60;*/
                OffsetDateTime dayStartUtc=  UserTimeZoneConversion.getStartOfDayInUTC(timeZone,current);
                OffsetDateTime dayEndUtc =UserTimeZoneConversion.getEndOfDayInUTC(timeZone,current);
                LocalDate convertedDate=dayStartUtc.plusMinutes(offsetDifferenceInMinutes).toLocalDate();
                dataList.addAll(timeSlotRepository.getAllTimeSlotsOfAMonth(dayStartUtc, dayEndUtc, RetailerContext.getRetailer(),offsetDifferenceInMinutes,convertedDate,false));
                current = dayEndUtc.plusMinutes(1);
            }

        }

        responseDto = new BaseResponseDto(Status.SUCCESS, dataList);
        return responseDto;
    }




    @Override
    public BaseResponseDto getAllTimeSlotsofAWeek(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) {
    return new BaseResponseDto();
    }

    @Override
    public BaseResponseDto deleteEntriesOfARetailer(String retailer) {

        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), 11, 1);
        List<Integer> attendantIds = new ArrayList<>(Arrays.asList(326, 356, 258, 245, 248));

        //  List<Integer> clusterIds = timeSlotRepository.findClusterIdsForTimeSlots(startDate, retailer);
        for(Integer attendantId:attendantIds){
            timeSlotRepository.deleteByAttendantRetailerAndDate(attendantId,startDate,retailer);
        }

        // timeSlotRepository.deleteAllFromNovemberOnwards(startDate, retailer);


       /* if (!clusterIds.isEmpty()) {
            timeSlotClusterRepository.deleteClustersByIds(clusterIds);
        }*/
        return new BaseResponseDto<>(Status.SUCCESS);
    }
 //This method is commented out as part of the ticket 443851

   /* private Integer getAvailableSlotsCounterByDate1(Integer serviceId, Integer venueId,OffsetDateTime slotStartTime, OffsetDateTime slotEndTime,List<BlockDateInfo> blockDateInfoList, String timeZone) throws BadRequestException {
      //  ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(TimeZoneContext.getTimeZone()));
      List<TimeSlotProjection> timeSlotRemoveList = new ArrayList<>();
        Venue currentVenue = venueRepository.findByVenueId(venueId);
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(serviceId);
        if(requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }
        List<TimeSlotProjection> timeSlotsList=timeSlotRepository.findBySlotSlotStartTimeServiceVenueAndRetailer(slotStartTime,slotEndTime,requiredService,currentVenue,RetailerContext.getRetailer(),false);
        Integer offsetTimeDifference = calculateOffset(timeZone);
        OffsetDateTime nowWithOffset = OffsetDateTime.now(ZoneOffset.ofTotalSeconds(offsetTimeDifference * 60));
        if (!timeSlotsList.isEmpty()) {
            List<TimeSlotProjection> newFreeSlots = getUnblockedTimeSlotsForCalendar(blockDateInfoList,timeSlotsList);
            *//*return (int) newFreeSlots.stream().filter(t ->
                    t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).count();*//*
            List<TimeSlotProjection> filterAttendant=newFreeSlots.stream().filter(t->!t.getAttendant().getDeleted()).collect(Collectors.toList());
            for (TimeSlotProjection timeSlotDto : filterAttendant) {
                List<TimeSlots> timeSlots = timeSlotClusterRepository.findSlotByClusterId(timeSlotDto.getTimeSlotCluster().getClusterId());
                TimeSlots timeSlot = timeSlots.get(0);
                OffsetDateTime clusterSlotStartTime = timeSlot.getSlotStartTime();

                // Daylight saving check
                Integer parentSlotDST = appointmentServiceImpl.checkDayLightSavings(timeZone, clusterSlotStartTime);
                Integer requestDST = appointmentServiceImpl.checkDayLightSavings(timeZone, timeSlotDto.getSlotStartTime());

                if (parentSlotDST > 0 && requestDST == 0) {
                    OffsetDateTime updatedStart = timeSlotDto.getSlotStartTime().plusMinutes(parentSlotDST);
                    OffsetDateTime updatedEnd = timeSlotDto.getSlotEndTime().plusMinutes(parentSlotDST);

                    if (updatedStart.plusMinutes(offsetTimeDifference).toLocalDate().isEqual(timeSlotDto.getSlotStartTime().plusDays(1).toLocalDate())) {
                        timeSlotRemoveList.add(timeSlotDto);
                    }

                } else if (parentSlotDST == 0 && requestDST > 0) {
                    OffsetDateTime updatedStart = timeSlotDto.getSlotStartTime().minusMinutes(requestDST);
                    OffsetDateTime updatedEnd = timeSlotDto.getSlotEndTime().minusMinutes(requestDST);

                    if (updatedStart.plusMinutes(offsetTimeDifference).toLocalDate().isEqual(timeSlotDto.getSlotStartTime().minusDays(1).toLocalDate())) {
                        timeSlotRemoveList.add(timeSlotDto);
                    }

                }
            }

            filterAttendant.removeAll(timeSlotRemoveList);
           return (int) filterAttendant.stream().filter(slot->slot.getSlotStartTime().isAfter(nowWithOffset)).count();
           // return (int) filterAttendant.stream().count();
        }
        else
            return 0;
    }*/
    private AttendantInfo getAttendantInfo(TimeSlotsDto slotProjection){
        AttendantInfo attendantInfo=new AttendantInfo();
        attendantInfo.setAttendantId(slotProjection.getAttendantId());
        attendantInfo.setAttendantName(slotProjection.getAttendantName());
        return attendantInfo;
    }
    private TimeSlotsDto mapToTimeSlotsDto(TimeSlotProjection slots) {
       List<Integer> serviceIds=new ArrayList<>();
       List<String> serviceNames=new ArrayList<>();
        TimeSlotsDto timeSlotsDto=new TimeSlotsDto();
        timeSlotsDto.setId(slots.getId());
        timeSlotsDto.setSlotName(slots.getSlotName());
        for(TimeSlotProjection.Service service:slots.getService()){
            serviceIds.add(service.getServiceId());
            serviceNames.add(service.getServiceName());
        }
        timeSlotsDto.setServiceId(serviceIds);
        timeSlotsDto.setServiceName(serviceNames);
        timeSlotsDto.setVenueId(slots.getVenue().getVenueId());
        timeSlotsDto.setVenueName(slots.getVenue().getVenueName());
        timeSlotsDto.setAttendantId(slots.getAttendant().getAttendantId());
        timeSlotsDto.setAttendantName(slots.getAttendant().getAttendantName());
        timeSlotsDto.setSlotStartTime(slots.getSlotStartTime());
        timeSlotsDto.setSlotEndTime(slots.getSlotEndTime());
        timeSlotsDto.setTimeSlotClusterId(slots.getTimeSlotCluster().getClusterId());
        return timeSlotsDto;
    }

    private TimeSlotsDto mapToTimeSlotsDtoAll(TimeSlotProjection slots) {
        List<Integer> serviceIds=new ArrayList<>();
        List<String> serviceNames=new ArrayList<>();
        TimeSlotsDto timeSlotsDto=new TimeSlotsDto();
        timeSlotsDto.setId(slots.getId());
        timeSlotsDto.setSlotName(slots.getSlotName());
        for(TimeSlotProjection.Service service:slots.getService()){
            serviceIds.add(service.getServiceId());
            serviceNames.add(service.getServiceName());
        }
        timeSlotsDto.setServiceId(serviceIds);
        timeSlotsDto.setServiceName(serviceNames);
        timeSlotsDto.setVenueId(slots.getVenue().getVenueId());
        timeSlotsDto.setVenueName(slots.getVenue().getVenueName());
        timeSlotsDto.setAttendantId(slots.getAttendant().getAttendantId());
        timeSlotsDto.setAttendantName(slots.getAttendant().getAttendantName());
        timeSlotsDto.setSlotStartTime(slots.getSlotStartTime());
        timeSlotsDto.setSlotEndTime(slots.getSlotEndTime());
        timeSlotsDto.setTimeSlotClusterId(slots.getTimeSlotCluster().getClusterId());
        timeSlotsDto.setAvailabilityInterval(slots.getAvailabilityInterval());
        timeSlotsDto.setAvailableDay(slots.getAvailableDay());
        timeSlotsDto.setColor(slots.getColor());
        timeSlotsDto.setAvailabilityIntervalUnit(slots.getAvailabilityIntervalUnit());
        timeSlotsDto.setAvailableDays(slots.getTimeSlotCluster().getAvailableDays());
        return timeSlotsDto;
    }


    private TimeSlotsDto mapWeekViewProjToDto(TimeSlotWeekViewProjection slots) {
        TimeSlotsDto timeSlotsDto=new TimeSlotsDto();
        timeSlotsDto.setId(slots.getId());
        timeSlotsDto.setSlotName(slots.getSlotName());
        timeSlotsDto.setAttendantId(slots.getAttendantId());
        timeSlotsDto.setAttendantName(slots.getAttendantName());
        timeSlotsDto.setSlotStartTime(OffsetDateTime.parse(slots.getSlotStartTime()));
        timeSlotsDto.setSlotEndTime(OffsetDateTime.parse(slots.getSlotEndTime()));
        timeSlotsDto.setTimeSlotClusterId(slots.getClusterId());
        timeSlotsDto.setColor(slots.getColor());
        timeSlotsDto.setSlotBooked(slots.getSlotBooked());
        return timeSlotsDto;
    }

    @Override
    public BaseResponseDto getAllTimeSlots(Integer pageNo, Integer pageSize, String sortBy, OffsetDateTime startDateOffset, OffsetDateTime endDateOffset, String timeZone) {
        BaseResponseDto responseDto;
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy).descending());

            List<TimeSlotWeekViewProjection> dataList = new ArrayList<>();
         /*   if (startDateOffset != null && endDateOffset != null) {
                endDateOffset = endDateOffset.withHour(23).withMinute(59).withSecond(59);
                ZoneId zoneId = ZoneId.of(timeZone);
                ZoneOffset offset = zoneId.getRules().getOffset(java.time.Instant.now());
                int offsetSeconds = offset.getTotalSeconds();
                if(offsetSeconds < 0)
                    endDateOffset = endDateOffset.plusSeconds(Math.abs(offsetSeconds));
                else
                    endDateOffset = endDateOffset.minusSeconds(offsetSeconds);
                dataList = timeSlotRepository.getAllTimeSlots(startDateOffset, endDateOffset,RetailerContext.getRetailer()
                );
            }*/

        if (startDateOffset != null && endDateOffset != null) {
            // Adjust endDate to be the last moment of the day (23:59:59)
           // endDateOffset = endDateOffset.withHour(23).withMinute(59).withSecond(59);
            endDateOffset= UserTimeZoneConversion.getEndOfDayInUTC(timeZone,endDateOffset);
            ZoneId zoneId = ZoneId.of(timeZone);
            Map<LocalDate, Integer> dailyOffsets = new HashMap<>();
            OffsetDateTime current = startDateOffset;

            while (!current.isAfter(endDateOffset)) {
                ZonedDateTime zonedDateTime = current.atZoneSameInstant(ZoneId.of(timeZone));
                int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
                int offsetDifferenceInMinutes= offsetInSeconds / 60;  // Offset in minutes
                /*ZoneOffset offset = zoneId.getRules().getOffset(current.toInstant());
               int offsetDifferenceInMinutes = offset.getTotalSeconds() / 60;*/
                OffsetDateTime dayStartUtc=  UserTimeZoneConversion.getStartOfDayInUTC(timeZone,current);
                OffsetDateTime dayEndUtc =UserTimeZoneConversion.getEndOfDayInUTC(timeZone,current);
                LocalDate convertedDate=dayStartUtc.plusMinutes(offsetDifferenceInMinutes).toLocalDate();
                dataList.addAll(timeSlotRepository.getAllTimeSlots(dayStartUtc, dayEndUtc, RetailerContext.getRetailer(),offsetDifferenceInMinutes,false));
                current = dayEndUtc.plusMinutes(1);
            }

        }

        responseDto = new BaseResponseDto(Status.SUCCESS, dataList);
        return responseDto;

    }

    @Override
    public BaseResponseDto getTimeSlotsOfADay(BookingDto bookingInfo, String timeZone) throws BadRequestException {
        OffsetDateTime appointmentOffsetDateTime=bookingInfo.getAppointmentDate();
        BaseResponseDto responseDto;
        //To get blocked dates of a particular retailer and checking with the appointment date
        LocalDate localDate = appointmentOffsetDateTime.toLocalDate();
        OffsetDateTime offsetDateTime=bookingInfo.getUtcAppointmentDate();
        List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByBlockDateAndRetailer(offsetDateTime.toLocalDate(), RetailerContext.getRetailer());
        if(!blockDateInfoList.isEmpty()){
            for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                if(blockDateInfo.getBlockStartTime() == null && blockDateInfo.getBlockEndTime() == null){
                    throw new BadRequestException("Bookings are not available for the selected date and time");
                }
            }
        }
        if(bookingInfo.getVenueId() == null) {
            throw new BadRequestException("No venue selected");
        }
        Venue currentVenue = venueRepository.findByVenueId(bookingInfo.getVenueId());
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(bookingInfo.getServiceId());
        if(requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }

        Attendant requiredAttendant = attendantRepository.findByAttendantId(bookingInfo.getAttendantId());
        if (
                requiredAttendant == null && bookingInfo.getAttendantId() != null
                        && RequestTypeContext.getRequestType().equals("Retailer")
        ) {
            throw new BadRequestException("Selected attendant is invalid");
        }

        List<TimeSlotProjection> timeSlotsList=timeSlotRepository.findByDateServiceVenueAttendantAndRetailer(bookingInfo.getUtcAppointmentDate().toLocalDate(),requiredService,currentVenue,requiredAttendant,RetailerContext.getRetailer());
        List<AppointmentSlotsDto> appointmentSlotsDtos=new ArrayList<>();
        for (TimeSlotProjection timeSlotProjection:timeSlotsList){
            AppointmentSlotsDto appointmentSlotsDto=new AppointmentSlotsDto();
            appointmentSlotsDto.setSlotDate(timeSlotProjection.getSlotStartTime().toLocalDate());
            appointmentSlotsDto.setSlotStartTime(timeSlotProjection.getSlotStartTime().plusMinutes(calculateOffset(timeZone)));
            appointmentSlotsDto.setSlotEndTime(timeSlotProjection.getSlotEndTime().plusMinutes(calculateOffset(timeZone)));
            appointmentSlotsDto.setAttendantId(timeSlotProjection.getAttendant().getAttendantId());
            appointmentSlotsDto.setSlotStartIsoDateTime(timeSlotProjection.getSlotStartTime().plusMinutes(calculateOffset(timeZone)).toString());
            appointmentSlotsDto.setSlotEndIsoDateTime(timeSlotProjection.getSlotEndTime().plusMinutes(calculateOffset(timeZone)).toString());
           // appointmentSlotsDto.setSlotStartIsoDateTime(timeSlotProjection.getSlotStartTime().plusMinutes(calculateOffset(timeZone)).atZoneSameInstant(ZoneId.of(timeZone)).format(DateTimeFormatter.ISO_DATE_TIME));
          //  appointmentSlotsDto.setSlotEndIsoDateTime(timeSlotProjection.getSlotEndTime().plusMinutes(calculateOffset(timeZone)).atZoneSameInstant(ZoneId.of(timeZone)).format(DateTimeFormatter.ISO_DATE_TIME));
            appointmentSlotsDtos.add(appointmentSlotsDto);
        }
        List<BlockDateInfo> blockDateInfos =blockDatesRepository.findByBlockDateAndRetailer(bookingInfo.getUtcAppointmentDate().toLocalDate(),RetailerContext.getRetailer());
        if(!blockDateInfos.isEmpty()){
           List<AppointmentSlotsDto> newFreeSlots=getUnblockedTimeSlots(blockDateInfos,appointmentSlotsDtos);
           responseDto= new BaseResponseDto(Status.SUCCESS,newFreeSlots);
        }else{
            responseDto= new BaseResponseDto(Status.SUCCESS,appointmentSlotsDtos);
        }
        return responseDto;
    }
    public List<AppointmentSlotsDto> getUnblockedTimeSlots(List<BlockDateInfo> blockDateInfoList, List<AppointmentSlotsDto> freeTimeSlots) throws BadRequestException {
        List<AppointmentSlotsDto> newFreeSlots = new ArrayList<>();
        if(!blockDateInfoList.isEmpty()){
            for(AppointmentSlotsDto slot: freeTimeSlots) {
                boolean isBlocked = false;
                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (
                            blockDateInfo.getBlockStartTime() != null
                                    && blockDateInfo.getBlockEndTime() != null
                                    && ((slot.getSlotStartTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotStartTime().isBefore(blockDateInfo.getBlockEndTime())) || (slot.getSlotEndTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotEndTime().isBefore(blockDateInfo.getBlockEndTime())) )
                    ) {
                        isBlocked = true;
                    }
                };
                if (!isBlocked) newFreeSlots.add(slot);
            };
        }
        else {
            return freeTimeSlots;
        }
        if (newFreeSlots.size() == 0) {
            throw new BadRequestException("No timeslots are available for appointment");
        }
        return newFreeSlots;
    }

    public List<TimeSlotMinimalProjection> getUnblockedTimeSlotsForCalendar(List<BlockDateInfo> blockDateInfoList, List<TimeSlotMinimalProjection> freeTimeSlots){
        List<TimeSlotMinimalProjection> newFreeSlots = new ArrayList<>();
        if(!blockDateInfoList.isEmpty()){
            for(TimeSlotMinimalProjection slot: freeTimeSlots) {
                boolean isBlocked = false;
                for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                    if (
                            blockDateInfo.getBlockStartTime() != null
                                    && blockDateInfo.getBlockEndTime() != null
                                    && ((slot.getSlotStartTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotStartTime().isBefore(blockDateInfo.getBlockEndTime())) || (slot.getSlotEndTime().isAfter(blockDateInfo.getBlockStartTime()) && slot.getSlotEndTime().isBefore(blockDateInfo.getBlockEndTime())) )
                    ) {
                        isBlocked = true;
                    }
                };
                if (!isBlocked) newFreeSlots.add(slot);
            };
        }
        else {
            return freeTimeSlots;
        }
        return newFreeSlots;
    }
//todo : this method is no longer used , as now slots are fetched based on attendant
    /*@Override
    public BaseResponseDto fetchAvailableSlotsOfAMonth(Integer serviceId, Integer venueId, Integer attendantId, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws BadRequestException {

            List<AvailableSlotsCounterDto> availableSlotsCounterList = new ArrayList<>();
            Integer offsetTimeDifference = calculateOffset(timeZone);
            List<BlockDateInfo> blockDateInfoList = blockDatesRepository.findByRetailerAndOffsetBlockDateBetween(RetailerContext.getRetailer(), startDate.toString(), endDate.toString());
            OffsetDateTime currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                AvailableSlotsCounterDto availableSlotsCounterDto = new AvailableSlotsCounterDto();
                availableSlotsCounterDto.setDate(currentDate);
                availableSlotsCounterDto.setAvailableSlots(0);
                boolean blocked = false;
                if (!blockDateInfoList.isEmpty()) {
                    for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                        if (blockDateInfo.getBlockStartTime() == null
                                && blockDateInfo.getBlockEndTime() == null
                                && currentDate.plusMinutes(offsetTimeDifference).toLocalDate().isEqual(OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).toLocalDate())
                            //  && !OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).isBefore(currentDate)
                            // &&!OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).isAfter(currentDate)
                        ) {
                            blocked = true;
                            availableSlotsCounterDto.setBlockFullDay(true);
                            break;
                        }
                    }
                }

                if (!blocked) {
                    availableSlotsCounterDto.setAvailableSlots(getAvailableSlotsCounterByDate(
                            serviceId,
                            venueId,
                            attendantId,
                            //  currentDate.atTime(OffsetTime.of(0, 0, 0, 0, ZoneOffset.of(OffsetContext.getOffset()))),
                            //todo :remove adding offset time difference once availability revamp is completed;
                            currentDate.plusMinutes(offsetTimeDifference),
                            blockDateInfoList, timeZone));
                    availableSlotsCounterDto.setBlockTimes(0);

                    for (BlockDateInfo blockDateInfo : blockDateInfoList) {
                        if (blockDateInfo.getBlockStartTime() != null && blockDateInfo.getBlockEndTime() != null) {
                            LocalDate localBlockStartDate = LocalDate.of(blockDateInfo.getBlockStartTime().plusMinutes(offsetTimeDifference).getYear(), blockDateInfo.getBlockStartTime().plusMinutes(offsetTimeDifference).getMonth(), blockDateInfo.getBlockStartTime().plusMinutes(offsetTimeDifference).getDayOfMonth());
                            LocalDate localBlockEndDate = LocalDate.of(blockDateInfo.getBlockEndTime().plusMinutes(offsetTimeDifference).getYear(), blockDateInfo.getBlockEndTime().plusMinutes(offsetTimeDifference).getMonth(), blockDateInfo.getBlockEndTime().plusMinutes(offsetTimeDifference).getDayOfMonth());
                            if (blockDateInfo.getBlockStartTime() != null
                                    && blockDateInfo.getBlockEndTime() != null
                                    && !localBlockStartDate.isBefore(currentDate.plusMinutes(offsetTimeDifference).toLocalDate())
                                    && !localBlockEndDate.isAfter(currentDate.plusMinutes(offsetTimeDifference).toLocalDate()))
                            // && !OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).isBefore(currentDate.plusMinutes(offsetTimeDifference))
                            //&&!OffsetDateTime.parse(blockDateInfo.getOffsetBlockDate()).plusMinutes(offsetTimeDifference).isAfter(currentDate.plusMinutes(offsetTimeDifference)))
                            {
                                availableSlotsCounterDto.setBlockTimes(availableSlotsCounterDto.getBlockTimes() + 1);
                            }
                        }
                    }
                }

                availableSlotsCounterList.add(availableSlotsCounterDto);
                currentDate = currentDate.plusDays(1);
            }
            BaseResponseDto responseDto=new BaseResponseDto<>(Status.SUCCESS,availableSlotsCounterList);
            return responseDto;
        }

        private Integer getAvailableSlotsCounterByDate(Integer serviceId, Integer venueId, Integer attendantId, OffsetDateTime offsetDateTime, List<BlockDateInfo> blockDateInfoList, String timeZone) throws BadRequestException {
        ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(TimeZoneContext.getTimeZone()));
        Venue currentVenue = venueRepository.findByVenueId(venueId);
        if (currentVenue == null) {
            throw new BadRequestException("Selected venue is invalid");
        }
        com.sayone.etailbookit.model.Service requiredService = serviceRepository.findByServiceId(serviceId);
        if(requiredService == null) {
            throw new BadRequestException("Selected service is invalid");
        }
        Attendant requiredAttendant = attendantRepository.findByAttendantId(attendantId);
        if (requiredAttendant == null && attendantId != null && RequestTypeContext.getRequestType().equals("Retailer")) {
            throw new BadRequestException("Selected attendant is invalid");
        }
        List<TimeSlotProjection> timeSlotsList=timeSlotRepository.findByDateServiceVenueAttendantAndRetailer(offsetDateTime.toLocalDate(),requiredService,currentVenue,requiredAttendant,RetailerContext.getRetailer());

        if (!timeSlotsList.isEmpty()) {
            List<TimeSlotProjection> newFreeSlots = getUnblockedTimeSlotsForCalendar(blockDateInfoList,timeSlotsList);
            return (int) newFreeSlots.stream().filter(t ->
                    t.getSlotStartTime().isAfter(currentTime.toOffsetDateTime())).count();
        }
        else
            return 0;
    }*/

    private List<TimeSlotProjection> filterSlots(List<TimeSlotProjection> timeSlotsOffset, OffsetDateTime offsetAddedDate,Integer offsetDifference,OffsetDateTime tempDate) {
        List<TimeSlotProjection> removedSlots=new ArrayList<>();
        if(offsetAddedDate.equals(tempDate)){
            return timeSlotsOffset;
        }
        else if(offsetDifference>0) {
            OffsetDateTime startOffsetDate = offsetAddedDate.plusDays(1);
            timeSlotsOffset.stream().forEach(t -> {
                if (t.getSlotStartTime().plusMinutes(offsetDifference).isAfter(startOffsetDate)){
                    removedSlots.add(t);
                }

            });
        }else{
            OffsetDateTime startOffsetDate = tempDate.minusDays(1);
            timeSlotsOffset.stream().forEach(t->{
                if(t.getSlotStartTime().plusMinutes(offsetDifference).isBefore(startOffsetDate)){
                    removedSlots.add(t);
                }
            });
        }
        timeSlotsOffset.removeAll(removedSlots);
        return timeSlotsOffset;
    }
    public Integer calculateOffset(String timeZone) {
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        return tz.getOffset(new Date().getTime()) / 1000 / 60;
    }

    public boolean checkBlockDate(List<BlockDateInfo> blockDateInfo, OffsetDateTime startTime, ServiceSlotsDto serviceSlotsDto,String retailer){
        boolean isBlockDate = false;
        if(!blockDateInfo.isEmpty()) {
            for (BlockDateInfo blockDate : blockDateInfo) {
                if (
                blockDate.getBlockStartTime() != null
                                && blockDate.getBlockEndTime() != null) {

                    OffsetDateTime endTime = getSlotEndTime(startTime, serviceSlotsDto);
                    OffsetDateTime slotStarTime=startTime.truncatedTo(ChronoUnit.MINUTES);
                    OffsetDateTime slotEndTime=endTime.truncatedTo(ChronoUnit.MINUTES);
                    OffsetDateTime blockStartTime=blockDate.getBlockStartTime().truncatedTo(ChronoUnit.MINUTES);
                    OffsetDateTime blockEndTime=blockDate.getBlockEndTime().truncatedTo(ChronoUnit.MINUTES);
                    if(blockStartTime.isEqual(slotStarTime) && blockEndTime.isEqual(slotEndTime)) {
                        isBlockDate = true;
                    }
                    if(slotStarTime.isAfter(blockStartTime) && slotStarTime.isBefore(blockEndTime)){
                        isBlockDate = true;
                    }
                    if(slotEndTime.isAfter(blockStartTime) && slotEndTime.isBefore(blockEndTime)){
                        isBlockDate = true;
                    }
                    LOGGER.info("********** Blocked Start Time is ::{} Blocked Date is ::{}", blockDate.getBlockStartTime(), blockDate.getBlockDate());
                }
                /*if (
                        blockDate.getBlockStartTime() != null
                                && blockDate.getBlockEndTime() != null
                                && ((startTime.isAfter(blockDate.getBlockStartTime()) && startTime.isBefore(blockDate.getBlockEndTime())) || (endTime.isAfter(blockDate.getBlockStartTime()) && endTime.isBefore(blockDate.getBlockEndTime())) )
                ) {
                    isBlockDate = true;
                }*/
                else{
                    isBlockDate = true;
                    break;
                }
            }
        }
        return isBlockDate;
    }
}
