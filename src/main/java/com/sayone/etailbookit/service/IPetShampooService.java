package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.dto.PetShampooDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface IPetShampooService {
    PetShampooDto addPetShampoo(PetShampooDto petShampooDto) throws EtailBookItException;

    PetShampooDto updatePetShampoo(PetShampooDto petShampooDto, int petShampooId) throws EtailBookItException;

    void deletePetShampoo(int petShampooId) throws EtailBookItException;

    PetShampooDto getPetShampooById(int petShampooId) throws EtailBookItException;

    //changed according to the index id order
    ConfigurationDto<PetShampooDto> getPetShampoos(Integer pageNo, Integer pageSize, String sortBy, String search);

    void bulkUpdateShampoo(ConfigurationDto<PetShampooDto> petShampooDtos) throws EtailBookItException;

    Page<PetShampooDto> getActiveShampoo(Integer pageNo, Integer pageSize, String sortBy, String search) throws EtailBookItException ;

    Page<PetShampooDto> getShampooByPagination(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search)throws EtailBookItException;
}
