package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.FeedingInformationDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;

import java.util.List;

public interface IFeedingInformationService {

    FeedingInformationDto updateFeedingInformation(FeedingInformationDto feedingInformationDto) throws EtailBookItException;

    FeedingInformationDto getFeedingInformation();

}
