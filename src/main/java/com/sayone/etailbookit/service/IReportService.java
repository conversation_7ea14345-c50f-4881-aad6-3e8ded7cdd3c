package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import com.sayone.etailbookit.dto.PaginatedResponse;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.CancelledAppointmentProjections;
import org.springframework.data.domain.Page;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

public interface IReportService {

    ByteArrayOutputStream getAllCancelledAppointments(Integer pageNo, Integer pageSize, String sortBy,boolean selectAll) throws EtailBookItException, IOException;

  //  PaginatedResponse<CancelledAppointmentProjections> getPaginatedCancelledAppointments(Integer pageNo, Integer pageSize, String sortBy)throws EtailBookItException;

    BaseResponseDto fetchAllAppointments(Integer pageNo, Integer pageSize, String sortBy)throws EtailBookItException;
}
