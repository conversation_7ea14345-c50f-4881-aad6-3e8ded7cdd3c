package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.VacationDto;
import com.sayone.etailbookit.exception.EtailBookItException;

import java.time.OffsetDateTime;

public interface IVacationService {

    void createVacation(VacationDto vacationDto)throws EtailBookItException;

    BaseResponseDto getAllVacationList(OffsetDateTime startDate, OffsetDateTime endDate, String timeZone)throws EtailBookItException;

    BaseResponseDto updateVacation(Integer vacationId,VacationDto vacationDto)throws EtailBookItException;

    BaseResponseDto deleteVacation(Integer vacationId)throws EtailBookItException;
}