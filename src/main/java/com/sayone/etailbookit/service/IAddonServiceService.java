package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.AddonServiceDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface IAddonServiceService {
    AddonServiceDto addAddonService(AddonServiceDto AddonServiceDto) throws EtailBookItException;

    AddonServiceDto updateAddonService(AddonServiceDto AddonServiceDto, int AddonServiceId) throws EtailBookItException;

    void deleteAddonService(int AddonServiceId) throws EtailBookItException;

    AddonServiceDto getAddonServiceById(int AddonServiceId) throws EtailBookItException;

    void bulkUpdateAddonService(List<AddonServiceDto> AddonServiceDtos) throws EtailBookItException;

    Page<AddonServiceDto> getAddonServices(Integer pageNo, Integer pageSize, String sortBy, Optional<String> search);

    Page<AddonServiceDto> getActiveService(Integer pageNo, Integer pageSize, String sortBy, String search) throws EtailBookItException;
}
