package com.sayone.etailbookit.service;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.*;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.data.domain.Page;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface AppointmentService {

    PaymentDto createAppointment(AppointmentDto appointmentDto, String timeZone) throws EtailBookItException;

    PaymentDto reopenedAppointment(int id, AppointmentDto appointmentDto, String timeZone) throws EtailBookItException;

    AppointmentDetailsProjection getAppointmentById(Integer id) throws EtailBookItException;

    BaseResponseDto getAllAppointment(Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer petType, Integer pet, Integer attendant, Integer venue, OffsetDateTime startDate, OffsetDateTime endDate, String timeZone) throws EtailBookItException;

    BaseResponseDto getAppointments(Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer pet, Integer attendant, Integer venue, String status, Integer petType, String search,Integer service, OffsetDateTime startDate, OffsetDateTime endDate,String timeZone) throws EtailBookItException;

    Boolean updateAppointment(int id, OffsetDateTime date, OffsetDateTime startTime, OffsetDateTime endTime, String timeZone,Integer createdCustomerId,String createdCustomerName,Integer updatedCustomerId,String updatedCustomerName) throws EtailBookItException;

//    Page<AppointmentListingProjection> getAppointmentsByDate(OffsetDateTime date, Boolean withCancel, Integer pageNo, Integer pageSize, String sortBy, Optional<String> search,String timeZone) throws EtailBookItException;
    Page<AppointmentListingProjection> getAppointmentsByDate1(OffsetDateTime date, Boolean withCancel, Integer pageNo, Integer pageSize, String sortBy, Optional<String> search,String timeZone) throws EtailBookItException;

    List<AppointmentSlotsDto> getAvailableAppointmentSlots(BookingDto appointmentDetails, Boolean autoBooking, String timeZone) throws EtailBookItException;

    List<AvailableSlotsCounterDto> getAvailableAppointmentSlotsCounterByDateRange(Integer serviceId,
                                                                                  Integer venueId,
                                                                                  //Integer attendantId,
                                                                                  Integer serviceDuration,
                                                                                  OffsetDateTime startDate,
                                                                                  OffsetDateTime endDate, String timeZone) throws EtailBookItException;

    Boolean updateOrderDetails(AppointmentDto order) throws EtailBookItException;

    void bulkUpdateOrderDetails(List<PaymentUpdateDto> orders) throws EtailBookItException;

    PaymentDto calculateServiceAmount(AppointmentDto appointmentDto, String timeZone) throws EtailBookItException;

    PaymentDetailsDto calculateServiceAmount(Integer AppointmentId);

    List<AppointmentPaymentProjection> getAppointmentsForCustomer(Integer Customer);

    void makeReadyToPay(Integer appointmentId);

    List<AppointmentDetailsProjection> getAllAppointmentsByDate(OffsetDateTime date, String timeZone) throws EtailBookItException;

    List<AppointmentUploadedDocumentsProjection> getAllUploadedAppointmentDocumentsByCustomerId(Integer pageNo, Integer pageSize, String sortBy, Integer customerId) throws EtailBookItException;

    void changeAppointmentTimeWithOffset();

    void addValuesToEndDate();

    void offsetRetailerMapping(List<OffsetRetailerMappingDto> offsetRetailerMappingDtos);

    void populateDataToAppointmentDateAndTime();

    void populateSource();

    PaymentDto insertGroomBarData(GroomBarAppointmentDto groomBarAppointmentDto) throws EtailBookItException;

    ByteArrayOutputStream getExportAppointments(Integer pageNo, Integer pageSize, String sortBy, Integer customer, Integer pet, Integer attendant, Integer venue, String status, Integer petType, String search, Integer service, OffsetDateTime startDate, OffsetDateTime endDate, boolean fetchAll,String timeZone) throws EtailBookItException, IOException;

    Map<String, List<AppointmentSlotsDto>> getAvailableAppointmentSlots1(BookingDto bookingInfo, boolean b, String timeZone, Integer daylightAddedOffsetMinutes)throws EtailBookItException;

    void fetchAppointmentsWithCorrectedTimeAfterDST()throws EtailBookItException;

    BaseResponseDto fetchAppointmentsAfterDST() throws EtailBookItException;

    BaseResponseDto getAppointmentsOfMonthView(OffsetDateTime startDate, OffsetDateTime endDate, String timeZone)throws EtailBookItException;

    void fetchAppointmentWithId(List<Integer> appointmentIds);

    BaseResponseDto manualResendOfAppointmentExport(String retailer,OffsetDateTime selectedDate,String timeZone) throws Exception;

    BaseResponseDto addAppointmentToWaitList(OffsetDateTime currentDate, String timeZone, String retailer,Integer pageNo,Integer pageSize,String sortBy);
}