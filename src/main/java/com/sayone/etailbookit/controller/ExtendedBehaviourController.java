package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.ExtendedBehaviourDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IExtendedBehaviourService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/extended-behaviour-information")
public class ExtendedBehaviourController {

    @Autowired
    IExtendedBehaviourService iExtendedBehaviourService;

    private static Logger LOGGER = LoggerFactory.getLogger(ExtendedBehaviourController.class);

    @GetMapping
    public BaseResponseDto getExtendedBehaviour() {
        LOGGER.info("@@@@Entered getExtendedBehaviour");
        ExtendedBehaviourDto extendedBehaviourDto = iExtendedBehaviourService.getExtendedBehaviours();
        LOGGER.info("@@@@Exited getExtendedBehaviour");
        return new BaseResponseDto(Status.SUCCESS, extendedBehaviourDto);
    }

    @PutMapping("/update")
    public BaseResponseDto updateExtendedBehaviour(@RequestBody ExtendedBehaviourDto extendedBehaviourDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateExtendedBehaviour");
        try {
            iExtendedBehaviourService.updateExtendedBehaviours(extendedBehaviourDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateExtendedBehaviour");
        return new BaseResponseDto(Status.SUCCESS);
    }

}