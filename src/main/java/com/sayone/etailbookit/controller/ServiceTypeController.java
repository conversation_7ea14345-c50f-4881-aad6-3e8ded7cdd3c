package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.ServiceTypeDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.ServiceType;
import com.sayone.etailbookit.projections.ServiceTypeProjection;
import com.sayone.etailbookit.service.IServiceTypeService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


@RestController
@RequestMapping("/api/v1/service-types")
public class ServiceTypeController {

    @Autowired
    IServiceTypeService iServiceTypeService;

    private static Logger LOGGER = LoggerFactory.getLogger(ServiceTypeController.class);

    @PostMapping("/new")
    public BaseResponseDto addServiceType(@ModelAttribute ServiceTypeDto serviceTypeDto) throws EtailBookItException ,Exception {
        LOGGER.info("@@@@Entered addServiceType");
        try {
            serviceTypeDto = iServiceTypeService.addServiceType(serviceTypeDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (IOException e) {
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited addServiceType");
        return new BaseResponseDto(Status.SUCCESS, serviceTypeDto);
    }

    @GetMapping
    public BaseResponseDto getAllServiceTypes(@RequestParam(defaultValue = "0") Integer pageNo,
                                              @RequestParam(defaultValue = "10") Integer pageSize,
                                              @RequestParam(defaultValue = "name") String sortBy,
                                              @RequestParam(required = false, defaultValue = "") String search) {
        LOGGER.info("@@@@Entered getAllServiceTypes");
        Page<ServiceTypeProjection> serviceTypes;
        serviceTypes = iServiceTypeService.getServiceTypes(pageNo, pageSize, sortBy, search);
        LOGGER.info("@@@@Exited getAllServiceTypes");
        return new BaseResponseDto(Status.SUCCESS, serviceTypes);
    }

    @GetMapping("/getAllServiceTypesWithServices")
    public BaseResponseDto getAllServiceTypesWithServices() {
        LOGGER.info("@@@@Entered getAllServiceTypesWithService");
        List<ServiceTypeProjection> serviceTypes;
        serviceTypes = iServiceTypeService.getServiceTypesWithServices();
        LOGGER.info("@@@@Exited getAllServiceTypesWithService");
        return new BaseResponseDto(Status.SUCCESS, serviceTypes);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getServiceTypeById(@PathVariable("id") int serviceTypeId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getServiceTypeById :::: " + serviceTypeId);
        ServiceTypeDto serviceTypeDto = null;
        try {
            serviceTypeDto = iServiceTypeService.getServiceTypeById(serviceTypeId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getServiceTypeById");
        return new BaseResponseDto(Status.SUCCESS, serviceTypeDto);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deleteServiceTypeById(@PathVariable("id") int serviceTypeId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deleteServiceTypeById :::: " + serviceTypeId);

        try {
            iServiceTypeService.deleteServiceType(serviceTypeId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (IOException e) {
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited deleteServiceTypeById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updateServiceType(@ModelAttribute ServiceTypeDto serviceTypeDto, @PathVariable("id") int serviceTypeId) throws EtailBookItException, Exception {
        LOGGER.info("@@@@Entered updateServiceType");

        try {
            serviceTypeDto = iServiceTypeService.updateServiceType(serviceTypeDto, serviceTypeId);
        }catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (IOException e) {
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited updateServiceType");
        return new BaseResponseDto(Status.SUCCESS, serviceTypeDto);
    }


    @PutMapping("/update/bulk")
    public BaseResponseDto bulkUpdateServiceType(@RequestBody List<ServiceTypeDto> serviceTypeDtos) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateAddonService");
        try {
            iServiceTypeService.bulkUpdateServiceType(serviceTypeDtos);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateAddonService");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getAllByActive")
    public BaseResponseDto getAllByActive(@RequestParam(defaultValue = "0") Integer pageNo,
                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                          @RequestParam(defaultValue = "name") String sortBy,
                                          @RequestParam(required = false, defaultValue = "") String search) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllByActive --------");
        Page<ServiceTypeDto> serviceTypeDtos;
        BaseResponseDto response ;
        try {
            serviceTypeDtos = iServiceTypeService.getActiveServiceType(pageNo,pageSize,sortBy,search);
            if(serviceTypeDtos.getTotalElements() != 0)
                response = new BaseResponseDto(Status.SUCCESS,serviceTypeDtos);
            else
                response = new BaseResponseDto(Status.FAILED,"No active ServiceType");
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllByActive --------");
        return response;

    }

    @GetMapping("/getServiceType")
    public BaseResponseDto getServiceTypeFromService(@RequestParam("serviceId")Integer serviceId,
                                                     @RequestParam("venueId")Integer venueId,
                                                     @RequestParam("attendantId")Integer attendantId) throws EtailBookItException {
        LOGGER.info(" --------Entered getServiceTypeFromService --------");
        List<ServiceTypeDto> serviceTypeDtos;
        BaseResponseDto response = null;
        try {
            serviceTypeDtos = iServiceTypeService.getServiceTypeFromService(serviceId,venueId,attendantId);
            if(!serviceTypeDtos.isEmpty())
                response = new BaseResponseDto(Status.SUCCESS,serviceTypeDtos);
            else
                response = new BaseResponseDto(Status.FAILED,"No active ServiceType");
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getServiceTypeFromService --------");
        return response;
    }


}