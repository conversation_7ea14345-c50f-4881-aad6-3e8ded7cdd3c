package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import com.sayone.etailbookit.dto.PaginatedResponse;
import com.sayone.etailbookit.dto.PetBirthdayDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.CancelledAppointmentProjections;
import com.sayone.etailbookit.service.IReportService;
import com.sayone.etailbookit.service.impl.ReportService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/reports")
public class ReportController {

    @Autowired
    IReportService iReportService;

    private static Logger LOGGER = LoggerFactory.getLogger(ReportController.class);

    @GetMapping("/cancelledAppointments")
    public ResponseEntity<byte[]> getCancelledAppointmentsReports(@RequestParam(defaultValue = "0") Integer pageNo,
                                                           @RequestParam(defaultValue = "10") Integer pageSize,
                                                           @RequestParam(defaultValue = "appoinmentNo") String sortBy,
                                                           @RequestParam(name="selectAll", defaultValue = "false")boolean selectAll) throws EtailBookItException , IOException {
        LOGGER.info("@@@@Entered Fetch  CancelledAppointments ");
            byte[] response;
            HttpHeaders headers = new HttpHeaders();
            try {
                response = iReportService.getAllCancelledAppointments(pageNo, pageSize, sortBy,selectAll).toByteArray();
                headers.add("Content-Disposition", "attachment; filename=Cancelled_Appointments.xlsx");
            } catch (EtailBookItException e) {
                LOGGER.error("Exception thrown while processing the request" + e);
                throw e;
            }
            LOGGER.info("@@@@Exited Fetch  CancelledAppointments ");
            return ResponseEntity.status(HttpStatus.OK).headers(headers).body(response);
    }

    @GetMapping("/getCancelledAppointments")
    public BaseResponseDto fetchCancelledAppointments(@RequestParam(defaultValue = "0") Integer pageNo,
                                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                                      @RequestParam(defaultValue = "appoinmentNo") String sortBy)throws EtailBookItException{
        LOGGER.info("@@@@Entered Fetch  CancelledAppointments ");
        BaseResponseDto response;
        try {
            response=iReportService.fetchAllAppointments(pageNo,pageSize,sortBy);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing the request"+e);
            throw e;
        }
        return response;
    }
}
