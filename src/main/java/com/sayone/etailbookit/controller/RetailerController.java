package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.RetailerListDto;
import com.sayone.etailbookit.service.IRetailerService;
import com.sayone.etailbookit.util.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/retailer")
public class RetailerController {

    @Autowired
    IRetailerService iRetailerService;

    @PostMapping("/addRetailer")
    public BaseResponseDto addRetailer(@RequestBody List<RetailerListDto> retailerListDtoList){
        iRetailerService.addRetailer(retailerListDtoList);
        return new BaseResponseDto<>(Status.SUCCESS);
    }
}
