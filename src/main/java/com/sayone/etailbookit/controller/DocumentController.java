package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.DocumentOptionDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.DocumentsProjection;
import com.sayone.etailbookit.projections.PetDocumentsProjection;
import com.sayone.etailbookit.service.IDocumentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/document")
public class DocumentController {


    @Autowired
    IDocumentService iDocumentService;

    private static Logger LOGGER = LoggerFactory.getLogger(DocumentController.class);

    @GetMapping()
    public Page<DocumentsProjection> getAllDocuments(@RequestParam(defaultValue = "0") Integer pageNo,
                                                     @RequestParam(defaultValue = "10") Integer pageSize,
                                                     @RequestParam(defaultValue = "name") String sortBy){
        Page<DocumentsProjection> response= iDocumentService.getAllDocument(pageNo, pageSize, sortBy);
        return response;
    }

    @PostMapping
    public BaseResponseDto createDocument(@ModelAttribute DocumentOptionDto documentOptionDto) throws Exception {

        BaseResponseDto response;
        LOGGER.info("**************** Entered Create Document ***********");
        try {
             response =iDocumentService.createDocument(documentOptionDto);
        }
        catch (Exception e){
            throw e;
        }
        LOGGER.info("**************** Exit Create Document ***********");
        return response;
    }

    @PutMapping("/{id}")
    public BaseResponseDto updateDocument(@ModelAttribute DocumentOptionDto documentOptionDto,@PathVariable("id") Integer id) throws Exception{
        BaseResponseDto response;
        LOGGER.info("**************** Entered Update Document ***********");
        try {
            response =iDocumentService.updateDocument(documentOptionDto,id);
        }
        catch (Exception e){
            throw e;
        }
        LOGGER.info("**************** Exit Update Document ***********");
        return response;
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deleteDocument(@PathVariable("id") Integer id) throws BadRequestException, EntityNotFoundException {
        BaseResponseDto response;
        LOGGER.info("**************** Entered Delete Document ***********");
        try {
            response =iDocumentService.deleteDocument(id);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (Exception e){
            throw e;
        }
            LOGGER.info("**************** Exit Delete Document ***********");
        return response;
    }

    @GetMapping("/petDocuments/{id}")
    public List<PetDocumentsProjection> getPetDocumentsByCustomer(@PathVariable("id") Integer customerId){
        List<PetDocumentsProjection> response= iDocumentService.getPetDocumentsByCustomer(customerId);
        return response;
    }

    @GetMapping("/active")
    public List<DocumentsProjection> getAllActiveDocuments(){
        List<DocumentsProjection> response= iDocumentService.getAllActiveDocument();
        return response;
    }
}
