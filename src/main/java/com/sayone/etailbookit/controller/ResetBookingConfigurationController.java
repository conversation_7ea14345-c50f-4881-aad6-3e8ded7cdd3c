package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.impl.ResetConfiguration;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/resetConfiguration")
public class ResetBookingConfigurationController {

    @Autowired
    ResetConfiguration resetConfiguration;
    private static Logger LOGGER = LoggerFactory.getLogger(ResetBookingConfigurationController.class);

    @GetMapping
    public BaseResponseDto resetConfiguration() throws BadRequestException {
        try {
            resetConfiguration.resetRetailerConfiguration(RetailerContext.getRetailer());
        }catch (EtailBookItException e){
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        return new BaseResponseDto(Status.SUCCESS);
    }
}
