package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.AccessTokenDto;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.SMSdto;
import com.sayone.etailbookit.dto.TwilioServiceDTO;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.TwilioService;
import com.sayone.etailbookit.projections.TwilioServiceProjection;
import com.sayone.etailbookit.service.ISendSmsService;
import com.sayone.etailbookit.util.Status;
import com.twilio.jwt.accesstoken.AccessToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/v1/twilio")
public class SMScontroller {

    @Autowired
    ISendSmsService iSendSmsService;
    private static Logger LOGGER = LoggerFactory.getLogger(SMScontroller.class);

    @PostMapping("/sendSMS")
    public BaseResponseDto sendSMS(@RequestBody SMSdto smSdto) throws IOException {

        LOGGER.info("%%%%%%%%% Entered sendSMS %%%%%%%%%%%%%%%%%%");

        String response;
        response=iSendSmsService.sendSMS(smSdto);

        LOGGER.info("%%%%%%%%% Exited sendSMS %%%%%%%%%%%%%%%%%%");
        return new BaseResponseDto<>(Status.SUCCESS,response);
    }
    @GetMapping("/getAccessToken/{serviceSID}")
    public BaseResponseDto getAccessToken(@PathVariable String serviceSID)throws EtailBookItException{
        LOGGER.info("%%%%%%%%% Entered getAccessToken %%%%%%%%%%%%%%%%%%");

        AccessTokenDto response;
            response = iSendSmsService.generateAccessToken(serviceSID);

        LOGGER.info("%%%%%%%%% Exited getAccessToken %%%%%%%%%%%%%%%%%%");
        return new BaseResponseDto(Status.SUCCESS,response);
    }
    @PostMapping("/conversation")
    public BaseResponseDto createConversation(@RequestBody TwilioServiceDTO twilioServiceDTO)throws EtailBookItException{
        LOGGER.info("%%%%%%%%% Entered createConversation %%%%%%%%%%%%%%%%%%");

        TwilioService twilioService=iSendSmsService.createConversation(twilioServiceDTO);

        LOGGER.info("%%%%%%%%% Exited createConversation %%%%%%%%%%%%%%%%%%");
        return  new BaseResponseDto(Status.SUCCESS,twilioService);
    }
    @GetMapping("/getConversation")
    public BaseResponseDto getConversation()throws EtailBookItException{
        LOGGER.info("%%%%%%%%% Entered getConversation %%%%%%%%%%%%%%%%%%");

        List<TwilioServiceProjection> twilioServiceProjections=iSendSmsService.getConversation();

        LOGGER.info("%%%%%%%%% Exited getConversation %%%%%%%%%%%%%%%%%%");
        return new BaseResponseDto(Status.SUCCESS,twilioServiceProjections);
    }
    @DeleteMapping("/deleteConversation/{conversationSID}")
    public BaseResponseDto deleteConversation(@PathVariable String  conversationSID)throws EtailBookItException{
        iSendSmsService.deleteConversation(conversationSID);
        return new BaseResponseDto(Status.SUCCESS);
    }
    @DeleteMapping("/deleteCustomerChatFromDatabase")
    public BaseResponseDto deleteCustomerChatFromDatabase()throws EtailBookItException{
        iSendSmsService.deleteCustomerChatFromDatabase();
        return new BaseResponseDto(Status.SUCCESS);
    }
}
