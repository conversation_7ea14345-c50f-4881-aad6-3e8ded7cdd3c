package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.SearchDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.projections.VenueDetailProjection;
import com.sayone.etailbookit.projections.VenueDropdownProjection;
import com.sayone.etailbookit.projections.VenueListingProjection;
import org.springframework.data.domain.Page;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.VenueDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IVenueService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/venue")
public class VenueController {

    @Autowired
    IVenueService iVenueService;

    private static Logger LOGGER = LoggerFactory.getLogger(VenueController.class);

    @PostMapping("/new")
    public BaseResponseDto addVenue(@RequestBody VenueDto venueDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered addVenue");
        try {
            venueDto = iVenueService.addVenue(venueDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited addVenue");
        return new BaseResponseDto(Status.SUCCESS,venueDto);
    }

    @GetMapping("/venuesByPagination")
    public BaseResponseDto venuesByPagination(@RequestParam(defaultValue = "0") Integer pageNo,
                                        @RequestParam(defaultValue = "10") Integer pageSize,
                                        @RequestParam(defaultValue = "internalName") String sortBy,
                                        @RequestParam Optional<String> search) {
        LOGGER.info("@@@@Entered venuesByPagination");
        Page<VenueListingProjection> venueList = iVenueService.venuesByPagination(pageNo, pageSize, sortBy,search);
        LOGGER.info("@@@@Exited venuesByPagination");
        return new BaseResponseDto(Status.SUCCESS, venueList);
    }


    @GetMapping
    public BaseResponseDto getAllVenues() {
        LOGGER.info("@@@@Entered getAllVenues");
        List<VenueDropdownProjection> venueDtos = iVenueService.getVenues();
        LOGGER.info("@@@@Exited getAllVenues");
        return new BaseResponseDto(Status.SUCCESS, venueDtos);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getVenueById(@PathVariable("id") int venueId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getVenueById :::: " + venueId);
        VenueDto venue;
        try {
            venue = iVenueService.getVenueById(venueId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getVenueById");
        return new BaseResponseDto(Status.SUCCESS,venue);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deleteVenueById(@PathVariable("id") int venueId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deleteVenueById :::: " + venueId);
        try {
            iVenueService.deleteVenue(venueId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deleteVenueById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updateVenue(@RequestBody VenueDto venueDto, @PathVariable("id") Integer venueId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateVenue");
        try {
            iVenueService.updateVenue(venueDto, venueId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateVenue");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PostMapping("/search")
    public BaseResponseDto getAttendantsForService(@RequestBody SearchDto searchDto) throws EntityNotFoundException {
        LOGGER.info("@@@@Entered getAttendantsForService");
        List<VenueDto> venueDtos = iVenueService.getVenuesForService(searchDto);
        LOGGER.info("@@@@Exited getAttendantsForService");
        return new BaseResponseDto(Status.SUCCESS, venueDtos);
    }

}