package com.sayone.etailbookit.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.VaccinationRecords;
import com.sayone.etailbookit.service.IPetTypeService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.kinesis.endpoints.internal.Value;

import java.io.IOException;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/pet-types")
public class PetTypeController {

    @Autowired
    IPetTypeService iPetTypeService;

    private static Logger LOGGER = LoggerFactory.getLogger(PetTypeController.class);

    @PostMapping("/new")
    public BaseResponseDto addPetType(@RequestParam("petType") String petType, @RequestParam("file") MultipartFile multipartFile) throws EtailBookItException ,Exception{
        LOGGER.info("@@@@Entered addPetType");
        try {
            ObjectMapper mapper = new ObjectMapper();
            PetTypeDto petTypeDto = mapper.readValue(petType, PetTypeDto.class);
            iPetTypeService.addPetType(petTypeDto,multipartFile);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        } catch (IOException e) {
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited addPetType");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/generalPetSize/{petTypeId}")
    public BaseResponseDto updateGeneralPetSize(@RequestBody UpdateGeneralPetSizeDto generalPetSizeDto,@PathVariable("petTypeId")int petTypeId) throws EtailBookItException, JsonProcessingException {

        iPetTypeService.updateGeneralPetSize(generalPetSizeDto,petTypeId);
        return new BaseResponseDto<>(Status.SUCCESS);
    }
    @GetMapping
    public BaseResponseDto getAllPetTypes(@RequestParam(defaultValue = "0") Integer pageNo,
                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                          @RequestParam(defaultValue = "name") String sortBy,
                                          @RequestParam(required = false, defaultValue = "") String search) {
        LOGGER.info("@@@@Entered getAllPetTypes");
        Page<PetTypeDto> petTypeDtos = iPetTypeService.getPetTypes(pageNo, pageSize, sortBy, search);
        LOGGER.info("@@@@Exited getAllPetTypes");
        return new BaseResponseDto(Status.SUCCESS, petTypeDtos);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getPetTypeById(@PathVariable("id") int petTypeId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getPetTypeById :::: " + petTypeId);
        PetTypeDto petTypeDto = null;
        try {
            petTypeDto = iPetTypeService.getPetTypeById(petTypeId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getPetTypeById");
        return new BaseResponseDto(Status.SUCCESS,petTypeDto);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deletePetTypeById(@PathVariable("id") int petTypeId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deletePetTypeById :::: " + petTypeId);
        try {
            iPetTypeService.deletePetType(petTypeId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (IOException e) {
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited deletePetTypeById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updatePetType(@RequestParam("petType") String petType, @RequestParam("file") Optional<MultipartFile> multipartFile, @PathVariable("id") int petTypeId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updatePetType");
        try {
            ObjectMapper mapper = new ObjectMapper();
            PetTypeDto petTypeDto = mapper.readValue(petType, PetTypeDto.class);
            iPetTypeService.updatePetType(petTypeDto, petTypeId,multipartFile);
        }catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (Exception e) {
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited updatePetType");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getActiveDetails/{id}")
    public BaseResponseDto getActiveDetails(@PathVariable("id") int petTypeId) throws EtailBookItException {

        LOGGER.info(" --------Entered getActiveDetails --------");
        PetTypeDto petTypeDtos;
        try {
            petTypeDtos = iPetTypeService.getActiveDetails(petTypeId);

        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getActiveDetails --------");
        return new BaseResponseDto(Status.SUCCESS,petTypeDtos);
    }

    @GetMapping("/{id}/temperaments")
    public BaseResponseDto getTemperaments( @PathVariable("id") int petTypeId) throws EntityNotFoundException {
        LOGGER.info("@@@@Entered getTemperaments");
        List<TemperamentDto> petTypeDtos = iPetTypeService.getTemperaments(petTypeId);
        LOGGER.info("@@@@Exited getTemperaments");
        return new BaseResponseDto(Status.SUCCESS, petTypeDtos);
    }

    @GetMapping("/{id}/vaccination-records")
    public BaseResponseDto getVaccinationRecords( @PathVariable("id") int petTypeId) throws EntityNotFoundException {
        LOGGER.info("@@@@Entered getVaccinationRecords");
        List<VaccinationRecordsDto> petTypeDtos = iPetTypeService.getVaccinationRecords(petTypeId);
        LOGGER.info("@@@@Exited getVaccinationRecords");
        return new BaseResponseDto(Status.SUCCESS, petTypeDtos);
    }

    @GetMapping("/{id}/sizes")
    public BaseResponseDto getSizes(@PathVariable("id") int petTypeId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getSizes :::: " + petTypeId);
        List<GeneralPetSizeDto> sizeDtos = null;
        try {
            sizeDtos = iPetTypeService.getSizes(petTypeId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getSizes");
        return new BaseResponseDto(Status.SUCCESS,sizeDtos);
    }

    //adding this API to add pet configuration to all the pet_types that were inserted into the DB based on the ticket BKI-22

    @GetMapping("/insertPetConfiguration/{petTypeId}")
    public BaseResponseDto insertPetConfiguration(@PathVariable("petTypeId") Integer petTypeId)throws EtailBookItException{
        try{
            iPetTypeService.insertPetConfiguration(petTypeId);
        }catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw  e;
        }
        return new BaseResponseDto(Status.SUCCESS);

    }

    @PutMapping("/updateGeneralPetSizes")
    public BaseResponseDto updateGeneralPetSizes()throws EtailBookItException{
        try{
            iPetTypeService.changeValuesOfGeneralPetSize();
        }catch (EtailBookItException e){
            throw e;
        }
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getPetList")
    public BaseResponseDto getPetList(@RequestParam("serviceId")Integer serviceId,
                                      @RequestParam("customerId")Integer customerId
                                      )throws EtailBookItException {
        List<PetAndPetTypeMappingDto> petList;
        try {
            petList = iPetTypeService.getPetList(serviceId, customerId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while processing the request" + e);
            throw e;
        }
        return new BaseResponseDto(Status.SUCCESS, petList);
    }
}