package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.BlockDateInfoDto;
import com.sayone.etailbookit.dto.BlockDateTimeDto;
import com.sayone.etailbookit.dto.BlockTimeDto;
import com.sayone.etailbookit.dto.RecurringBlockResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.BlockDateInfo;
import com.sayone.etailbookit.service.IBlockDateService;
import com.sayone.etailbookit.util.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/api/v1/block_dates")
public class BlockDateController {

    private static Logger LOGGER = LoggerFactory.getLogger(BlockDateController.class);

    @Autowired
    IBlockDateService blockDateService;

    @PostMapping("/create-with-time-range")
    //public BaseResponseDto saveBlockDatesByTimeRange(@RequestParam("blockDate") String blockDate, @RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) throws EtailBookItException {
    public BaseResponseDto saveBlockDatesByTimeRange(@RequestParam("startTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startTime, @RequestParam("endTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endTime) throws EtailBookItException {
            LOGGER.info(" --------Entered saveblockdates --------");
        try {
            blockDateService.saveBlockDates(startTime, endTime);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited saveblockdates --------");
        return new BaseResponseDto(Status.SUCCESS);

    }

    @PostMapping("/create-with-multipletime-range")
    public BaseResponseDto saveBlockDatesByTimeRanges(@RequestBody BlockDateTimeDto blockDateTimeDto,@RequestParam("tz") String timeZone)throws EtailBookItException {

        LOGGER.info(" --------Entered saveblockdates with multiple time ranges--------");
        List<BlockDateInfoDto> blockDateInfoList ;
        try {
            blockDateInfoList=  blockDateService.saveBlockDayWithTime(blockDateTimeDto,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited saveblockdates --------");
        return new BaseResponseDto(Status.SUCCESS,blockDateInfoList);

    }

    @PostMapping("/create")
    public BaseResponseDto SaveBlockDates(@RequestParam("blockDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime blockDate,@RequestParam("tz")String timeZone) throws EtailBookItException {
        LOGGER.info(" --------Entered saveblockdates --------");
        BlockDateInfo blockDateInfo = null;
        try {
            blockDateInfo = blockDateService.saveBlockDates(blockDate,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited saveblockdates --------");
        return new BaseResponseDto(Status.SUCCESS, blockDateInfo);
    }

    @GetMapping
    public BaseResponseDto getBlockDatesById(@RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
                                             @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,@RequestParam("tz")String timeZone) throws EtailBookItException {
        LOGGER.info(" --------Entered getblockdates --------");
        List<BlockDateInfoDto> blockDatesInfoList;
        try {
            blockDatesInfoList = blockDateService.getBlockDatesByMonth(startDate,endDate,timeZone);
            LOGGER.info(" ------- --------"+blockDatesInfoList);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getBlockdates");
        return new BaseResponseDto(Status.SUCCESS, blockDatesInfoList);
    }
    @DeleteMapping("/{id}")
    public BaseResponseDto DeleteBlockDates(@PathVariable("id") int blockDateId,@RequestParam("tz") String timeZone) throws EtailBookItException {
        LOGGER.info(" --------Entered deleteblockdates --------");
        try {
            blockDateService.deleteBlockDate(blockDateId,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited deleteblockdates --------");
        return new BaseResponseDto(Status.SUCCESS);

    }
    @GetMapping("populateDateToOffsetBlockDate")
    public BaseResponseDto populateDataToOffsetBlockDate(@RequestParam("retailer")String retailer){

        blockDateService.addDataToOffsetBlockDate(retailer);
        return  new BaseResponseDto<>(Status.SUCCESS);

    }
    @GetMapping("/fetchBlockTimes")
    public BaseResponseDto fetchBlockTimes(@RequestParam("selectedDate")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime selectedDate,
                                           @RequestParam("tz")String timeZone){
        LOGGER.info("***********Entered fetching block times of a selected date ******************");
        List<BlockDateInfoDto> blockDatesInfoList;
        try{
            blockDatesInfoList=blockDateService.findBlockTimesOfSelectedDate(selectedDate,timeZone);
        } catch (Exception e) {
            LOGGER.error("Exception thrown while processing the request ::{}",String.valueOf(e));
            throw new RuntimeException(e);
        }

        return new BaseResponseDto<>(Status.SUCCESS,blockDatesInfoList);

    }
    @PostMapping("/unblockTimes")
    public  BaseResponseDto unblockSelectedTimes(@RequestBody List<BlockDateInfoDto> blockTimeDtos,
                                                 @RequestParam("tz") String timeZone) throws EtailBookItException {
        LOGGER.info("******************* Entered unblocking block times ******************************");
        try{
            blockDateService.unblockSelectedTimes(blockTimeDtos,timeZone);
        } catch (Exception e) {
            LOGGER.error("Exception thrown while processing the request ::{}", String.valueOf(e));
            throw new EtailBookItException(e);
        }
       return new BaseResponseDto<>(Status.SUCCESS);
    }

    @PostMapping("/create-recurring")
    public BaseResponseDto saveRecurringBlockDates(@RequestBody BlockDateTimeDto blockDateTimeDto, @RequestParam("tz") String timeZone) throws EtailBookItException {
        LOGGER.info("-------- Entered save recurring block dates --------");
        RecurringBlockResponseDto response;
        try {
            response = blockDateService.saveRecurringBlockDayWithTime(blockDateTimeDto, timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: {}", e);
            throw e;
        }
        LOGGER.info("-------- Exited save recurring block dates --------");
        LOGGER.info("Created: {}, Skipped: {}", response.getTotalCreated(), response.getTotalSkipped());
        return new BaseResponseDto(Status.SUCCESS, response);
    }

}
