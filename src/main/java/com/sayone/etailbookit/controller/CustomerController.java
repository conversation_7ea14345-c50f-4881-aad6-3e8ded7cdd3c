package com.sayone.etailbookit.controller;

import com.fasterxml.jackson.databind.ser.Serializers;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.CustomerDto;
import com.sayone.etailbookit.dto.MergeCustomerDto;
import com.sayone.etailbookit.dto.PetCustomerDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Customer;
import com.sayone.etailbookit.projections.CustomerProjection;
import com.sayone.etailbookit.service.ICustomerService;
import com.sayone.etailbookit.service.IPetService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("api/v1/bookitCustomer")
public class CustomerController {

    private static Logger LOGGER = LoggerFactory.getLogger(CustomerController.class);

    @Autowired
    ICustomerService customerService;


    @Autowired
    IPetService iPetService;

    @PostMapping()
    public BaseResponseDto postCustomerDetails(@RequestBody List<CustomerDto> customers) throws EtailBookItException {
        LOGGER.info(" --------Entered PostCustomerDetails --------");
        try {
            customerService.postCustomerDetails(customers);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while processing the request::"+e);
            throw e;
        }
        LOGGER.info(" --------Exited PostCustomerDetails --------");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/updateCustomer")
    public BaseResponseDto updateCustomerDetails(@RequestBody List<CustomerDto> customers) throws EtailBookItException {
        LOGGER.info("---------Entered customer update-----------");
        try {
            customerService.updateCustomerDetails(customers);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing the request" + e);
            throw e;
        }
        LOGGER.info(" --------Exited Update CustomerDetails --------");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getCustomer/{customerId}")
    public BaseResponseDto getCustomer(@PathVariable("customerId") int customerId) throws EtailBookItException {
        LOGGER.info("@@@@@@@@ Entered get customers ");
        CustomerProjection customer = customerService.getCustomerDetails(customerId);
        LOGGER.info("@@@@@@@@ Exited get customer");
        return new BaseResponseDto(Status.SUCCESS,customer);
    }
    @GetMapping("/getAllcustomers")
    public BaseResponseDto getAllCustomers()throws EtailBookItException{
        List<Customer> customers=customerService.getAllCustomers();
        return new BaseResponseDto(Status.SUCCESS,customers);
    }

    // added this code to edit the pet based on customer id along with pet it based on the requirement from POS
    @DeleteMapping("/{customerId}/pet/{id}/delete")
    public BaseResponseDto deletePetById(@PathVariable("customerId") int customerId, @PathVariable("id") int petId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deletePetById :::: " + petId);
        try {
            iPetService.deletePetIdAndCustomerId(petId, customerId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deletePetById");
        return new BaseResponseDto(Status.SUCCESS);
    }


    // added this code to edit the pet based on customer id along with pet it based on the requirement from POS
    @PutMapping("/{customerId}/pet/{id}/update")
    public BaseResponseDto updatePetWithCustomerId(@ModelAttribute PetCustomerDto petCustomerDto, @PathVariable("customerId") int customerId, @PathVariable("id") int petId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updatePet based on customer id");
        try {
            iPetService.updatePetWithCustomer(petCustomerDto, petId, customerId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updatePet based on customer id");
        return new BaseResponseDto(Status.SUCCESS);
    }
    
    // added this code to create  the pet based on customer id  based on the requirement from POS
    @PostMapping("/{customerId}/pet/new")
    public  BaseResponseDto createPetWithCustomer(@RequestBody PetCustomerDto petCustomerDto,@PathVariable("customerId") int customerId) throws EtailBookItException{
        LOGGER.info("@@@@Entered create pet based on customer id");
        try {
            iPetService.createPetWithCustomer(petCustomerDto,customerId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited create pet based on customer id");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/mergeCustomers/{sourceCustomerId}")
    public BaseResponseDto getmergeCustomerDetails(@PathVariable("sourceCustomerId") int sourceCustomerId) throws  EtailBookItException{
      LOGGER.info("----------Entered get merge Customer details---------");
        MergeCustomerDto mergeCustomerDto;
        try {
           mergeCustomerDto = customerService.getPetandAppointmentDetails(sourceCustomerId);
      }catch (EtailBookItException e){
          LOGGER.error("Exception ocured while processing the request");
          throw e;
      }
        LOGGER.info("----------Exited get merge Customer details---------");
        return new BaseResponseDto(Status.SUCCESS,mergeCustomerDto);
    }

    @GetMapping("/mergeCustomers/{destinationCustomerId}/{sourceCustomerId}")
    public BaseResponseDto mergeCustomers(@PathVariable("destinationCustomerId") int destinationCustomerId,@PathVariable("sourceCustomerId") int sourceCustomerId)throws EtailBookItException{
      LOGGER.info("@@@@@@@@Entered MergeCustomer");
       try {
           customerService.mergeCustomers(destinationCustomerId, sourceCustomerId);
       }catch (EtailBookItException e){
        LOGGER.error("Exception while processing the request");
        throw e;
       }
       LOGGER.info("@@@@@@@@@@@@Exited Merge Customers");
        return  new BaseResponseDto(Status.SUCCESS);
    }
}
