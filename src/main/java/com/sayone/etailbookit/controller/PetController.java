package com.sayone.etailbookit.controller;

import com.amazonaws.services.ec2.model.CreateSpotDatafeedSubscriptionRequest;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.ser.Serializers;
import com.itextpdf.text.DocumentException;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.projections.PetDetailsProjection;
import com.sayone.etailbookit.projections.PetDropdownProjection;
import com.sayone.etailbookit.projections.PetListingProjection;
import com.sayone.etailbookit.service.IPetService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.security.GeneralSecurityException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/pet")
public class PetController {

    @Autowired
    IPetService iPetService;

    private static Logger LOGGER = LoggerFactory.getLogger(PetController.class);

    @PostMapping("/new")
    public BaseResponseDto addPet(@ModelAttribute PetDto petDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered addPet");
        try {
            iPetService.addPet(petDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited addPet");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/mergeAtb")
    public BaseResponseDto mergeAtbPetProfile(@RequestBody List<AtbpetProfileMergeDto> atbpetProfileMergeDtoList) throws EtailBookItException{
        LOGGER.info("-----Entered merge ATB pet profile ----- " );
        try{
            iPetService.mergeAtbPetProfile(atbpetProfileMergeDtoList);
        }catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        } catch (ParseException e) {
            LOGGER.error("Exception occured while parsing the date"+e);
        }
        LOGGER.info("-----Exited merge ATB pet profile ----- " );
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/mergeArizonawagnwash")
    public BaseResponseDto mergeArizonawagnwash(@RequestBody List<ArizonawagnwashPetProfileMergeDto> arizonawagnwashPetProfileMergeDtoList) throws EtailBookItException{
        LOGGER.info("-----Entered merge arizonawagnwash  pet profile ----- " );
        try{
            iPetService.mergeArizonawagnwash(arizonawagnwashPetProfileMergeDtoList);
        }catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        LOGGER.info("-----Exited merge arizonawagnwash pet profile ----- " );
        return new BaseResponseDto(Status.SUCCESS);
    }
    @PutMapping("/mergeAnyRetailerPetProfile")
    public BaseResponseDto mergePetPRofile(@RequestBody List<MergePetProfileDto> petProfileMergeDtoList) throws EtailBookItException{
        LOGGER.info("-----Entered merge   pet profile ----- " );
        try{
            iPetService.mergePetPRofile(petProfileMergeDtoList);
        }catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        LOGGER.info("-----Exited merge  pet profile ----- " );
        return new BaseResponseDto(Status.SUCCESS);
    }
    @DeleteMapping("/deleteWrongImport")
    public BaseResponseDto deleteWrongImport(@RequestBody List<MergePetProfileDto> petProfileMergeDtoList) throws EtailBookItException{
        LOGGER.info("-----Entered merge   pet profile ----- " );
        try{
            iPetService.deletePetsFromBadImport(petProfileMergeDtoList);
        }catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        LOGGER.info("-----Exited merge  pet profile ----- " );
        return new BaseResponseDto(Status.SUCCESS);
    }
    @GetMapping
    public BaseResponseDto getAllPets() {
        LOGGER.info("@@@@Entered getAllPets");
        List<PetDropdownProjection> petDtos = iPetService.getPets();
        LOGGER.info("@@@@Exited getAllPets");
        return new BaseResponseDto(Status.SUCCESS, petDtos);
    }

    @GetMapping("/petsByPagination")
    public BaseResponseDto petsByPagination(@RequestParam("customer") int customer,
                                            @RequestParam(defaultValue = "0") Integer pageNo,
                                            @RequestParam(defaultValue = "10") Integer pageSize,
                                            @RequestParam(defaultValue = "name") String sortBy,
                                            @RequestParam Optional<String> search) throws EtailBookItException {
        LOGGER.info("@@@@Entered petsByPagination");
        Page<PetListingProjection> pets = iPetService.getPetsByPagination(customer,pageNo, pageSize, sortBy,search);
        LOGGER.info("@@@@Exited petsByPagination");
        return new BaseResponseDto(Status.SUCCESS, pets);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getPetById(@PathVariable("id")int petId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getPetById :::: " + petId);
        PetDetailsProjection pet;
        try {
            pet = iPetService.getPetById(petId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getPetById");
        return new BaseResponseDto(Status.SUCCESS,pet);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deletePetById(@PathVariable("id") int petId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deletePetById :::: " + petId);
        try {
            iPetService.deletePet(petId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deletePetById");
        return new BaseResponseDto(Status.SUCCESS);
    }



    @PutMapping("/update/{id}")
    public BaseResponseDto updatePet(@ModelAttribute PetDto petDto, @PathVariable("id") int petId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updatePet");
        try {
            iPetService.updatePet(petDto, petId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updatePet");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/petsByCustomer/{customer}")
    public BaseResponseDto getPetsByCustomer(
            @PathVariable("customer") int customer,
            @RequestParam("withoutDeceasedDate") Optional<Boolean> withoutDeceasedDate,
            @RequestParam("search")Optional<String> search
    ) throws EtailBookItException {
        LOGGER.info("-----Entered getPetsByCustomer ----- " + customer);
        List<PetDropdownProjection> pets;
        try {
            pets = iPetService.getPetsByCustomer(customer, withoutDeceasedDate, search);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-----Exited getPetsByCustomer-----");
        return new BaseResponseDto(Status.SUCCESS,pets);

    }

    @GetMapping("/checkEligibility/")
    public BaseResponseDto checkEligibility(
            @RequestParam("pet") Integer pet,
            @RequestParam("attendant") Integer attendant,
            @RequestParam("venue") Integer venue
    ) throws EtailBookItException {
        LOGGER.info("-----Entered checkEligibility ----- " );
        Boolean eligible = false;
        try {
            eligible = iPetService.checkEligibility(pet, attendant, venue);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-----Exited checkEligibility-----");
        return new BaseResponseDto(Status.SUCCESS, eligible);

    }

    @PostMapping("/mergePets")
    public BaseResponseDto mergePetProfile(@RequestBody List<PetLegacyRetailerListDto> PetLegacyRetailerListDto) throws EtailBookItException{
        try {
            iPetService.mergePetProfile(PetLegacyRetailerListDto);
        }catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request");
            throw e;
        }
        return  new BaseResponseDto(Status.SUCCESS);

    }
    //commented out the esign part for pet documents based on the requirement for the ticket BKI-1056

   /*  @GetMapping("/petDocument/sign")
    public BaseResponseDto signPetDocument(@RequestParam("fileURL") String fileUrl,@RequestParam("petId")Integer petId,@RequestParam("documentId")Integer documentId,@RequestParam("name")String name) throws GeneralSecurityException, DocumentException, IOException, URISyntaxException, EtailBookItException {
      LOGGER.info("###########Entered sign petDocument #############");
         String signedFileUrl;
      try {
          signedFileUrl = iPetService.signPetDocument(fileUrl, petId, documentId, name);
      }catch(EtailBookItException e){
          LOGGER.error("Exception thrown while processing the request");
          throw e;
      }
        return new BaseResponseDto<>(Status.SUCCESS,signedFileUrl);
     }*/

    @GetMapping("/getPetBirthdayReport")
    public BaseResponseDto getBPetBirthdayReport(@RequestParam(defaultValue = "0") Integer pageNo,
                                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                                 @RequestParam(defaultValue = "name") String sortBy,
                                                 @RequestParam Optional<String> petName,
                                                 @RequestParam (name="dobMonth",required = false)Integer dobMonth,
                                                 @RequestParam (name="petIds",required = false)List<Integer> petIds,
                                                 @RequestParam(name="cusIds",required = false)List<Integer>cusIds,
                                                 @RequestParam(name="selectAll", defaultValue = "false")boolean selectAll)throws EtailBookItException{
        LOGGER.info("@@@@Entered petsBirthdayReport");
        if (selectAll) {
            List<PetBirthdayDto> response;
            try {
                response = iPetService.getPetReportForEcomm(pageNo, pageSize, sortBy, petName, dobMonth, petIds, cusIds);
            } catch (EtailBookItException e) {
                LOGGER.error("Exception thrown while processing the request" + e);
                throw e;
            }
            LOGGER.info("@@@@Exited petsBirthdayReport");
            return new BaseResponseDto(Status.SUCCESS, response, response.size());
        }
        else {
            PaginatedResponse<PetBirthdayDto> response;
            try {
                response = iPetService.getPetBirthdayReport(pageNo,pageSize,sortBy,petName,dobMonth,petIds,cusIds);
            }catch(EtailBookItException e){
                LOGGER.error("Exception thrown while processing the request"+e);
                throw e;
            }
            LOGGER.info("@@@@Exited petsBirthdayReport");
            return new BaseResponseDto(Status.SUCCESS, response);
        }
    }
    @GetMapping("/importExcelAndSave")
    public BaseResponseDto importExcelAndSave(@RequestParam("file") MultipartFile file) throws EtailBookItException, IOException, ParseException {
        List<ObjectNode> json;
        try {
            json = iPetService.importExcelAndSave(file);
        }catch (EtailBookItException | IOException | ParseException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        return  new BaseResponseDto(Status.SUCCESS, json);
    }

    @PostMapping("/bulkUpdateGeneralSizes")
    public BaseResponseDto bulkUpdateGeneralSizes(@RequestBody List<String> retailer){
        List<Integer> response;
        try {
            response = iPetService.bulkUpdateGeneralSizes(retailer);
        }catch (Exception e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        return  new BaseResponseDto(Status.SUCCESS, response);
    }

    @GetMapping("/sendEmail")
    public BaseResponseDto sendEmailToSignWaiver(@RequestParam("customerId")Integer customerId,
                                                 @RequestParam("petId")Integer petId){
        LOGGER.info("============Entered sign pet waiver of liability=================");
        BaseResponseDto response=new BaseResponseDto<>();
        try {
            response=iPetService.sendEmailToSignWaiver(customerId,petId);
        }catch (Exception e){
            LOGGER.error("Exception thrown while processing the request");
        }
        return new BaseResponseDto<>(Status.SUCCESS,response);
    }
}