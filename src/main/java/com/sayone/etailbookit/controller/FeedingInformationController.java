package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.FeedingInformationDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IFeedingInformationService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/api/v1/feeding-information")
public class FeedingInformationController {

    @Autowired
    IFeedingInformationService iFeedingInformationService;

    private static Logger LOGGER = LoggerFactory.getLogger(FeedingInformationController.class);

    @GetMapping
    public BaseResponseDto getFeedingInformation() {
        LOGGER.info("@@@@Entered getFeedingInformation");
        FeedingInformationDto feedingInformationDto = iFeedingInformationService.getFeedingInformation();
        LOGGER.info("@@@@Exited getFeedingInformation");
        return new BaseResponseDto(Status.SUCCESS, feedingInformationDto);
    }

    @PutMapping("/update")
    public BaseResponseDto updateFeedingInformation(@RequestBody FeedingInformationDto feedingInformationDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateFeedingInformation");
        try {
            feedingInformationDto =  iFeedingInformationService.updateFeedingInformation(feedingInformationDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateFeedingInformation");
        return new BaseResponseDto(Status.SUCCESS,feedingInformationDto);
    }

}