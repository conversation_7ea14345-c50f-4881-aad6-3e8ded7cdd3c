package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.service.IGoogleCalendarService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/google-calendar")
public class GoogleCalendarController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleCalendarController.class);

    @Autowired
    private IGoogleCalendarService googleCalendarService;

    /**
     * Invalidate Google Calendar sync for an attendant
     * Disables sync and clears all stored Google credentials (tokens, calendar ID)
     * This is useful when disconnecting the attendant's Google Calendar or revoking access
     * 
     * @param attendantId - ID of the attendant
     * @return Success message
     */
    @PostMapping("/invalidate-sync/{attendantId}")
    public BaseResponseDto invalidateSync(@PathVariable("attendantId") Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered invalidateSync for attendant: {}", attendantId);
        try {
            googleCalendarService.invalidateGoogleCalendarSync(attendantId);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Google Calendar sync has been invalidated and all credentials cleared for the attendant");
            LOGGER.info("@@@@Exited invalidateSync");
            return new BaseResponseDto(Status.SUCCESS, response);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while invalidating sync: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Send Google Calendar collaboration invitation to attendant
     * Only works if Google Calendar sync is enabled for the attendant
     * Email contains frontend URL with attendantId and retailer parameters
     * 
     * @param attendantId - ID of the attendant
     * @return Success message
     */
    @PostMapping("/invite-to-collab/{attendantId}")
    public BaseResponseDto inviteToCollab(@PathVariable("attendantId") Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered inviteToCollab for attendant: {}", attendantId);
        try {
            googleCalendarService.sendCalendarSyncInvitation(attendantId);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Google Calendar collaboration invitation has been sent to the attendant's email address");
            LOGGER.info("@@@@Exited inviteToCollab");
            return new BaseResponseDto(Status.SUCCESS, response);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while sending collaboration invitation: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Accept Google Calendar invitation and get authorization URL
     * Called by frontend when user clicks accept on the invitation from email
     * Frontend should set the retailer header from the email link parameters
     * 
     * @param attendantId - ID of the attendant from email link
     * @return BaseResponseDto containing the Google OAuth authorization URL
     */
    @PostMapping("/accept-invite/{attendantId}")
    public BaseResponseDto acceptInvite(@PathVariable("attendantId") Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered acceptInvite for attendant: {}", attendantId);
        try {
            String authorizationUrl = googleCalendarService.acceptCalendarInvitation(attendantId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Authorization URL generated successfully");
            response.put("authorizationUrl", authorizationUrl);
            response.put("attendantId", attendantId);
            response.put("retailer", RetailerContext.getRetailer());
            
            LOGGER.info("@@@@Exited acceptInvite - generated authorization URL for attendant {}", attendantId);
            return new BaseResponseDto(Status.SUCCESS, response);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while accepting invitation: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Complete OAuth2 authorization - called by frontend after receiving code from Google
     * Frontend receives the callback from Google, extracts code and state, then calls this endpoint
     * 
     * @param code - Authorization code from Google
     * @param attendantId - Attendant ID (extracted from state by frontend)
     * @return BaseResponseDto with sync completion status
     */
    @PostMapping("/complete-oauth")
    public BaseResponseDto completeOAuth(@RequestParam("code") String code,
                                         @RequestParam("attendantId") Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered completeOAuth for attendant: {}", attendantId);
        
        try {
            // Get retailer from header (set by frontend)
            String retailer = RetailerContext.getRetailer();
            if (retailer == null || retailer.trim().isEmpty()) {
                throw new EtailBookItException("Retailer context not found. Please ensure the retailer header is set.");
            }
            
            LOGGER.info("Processing OAuth completion for attendant {} with retailer {}", attendantId, retailer);
            
            // Handle OAuth callback - stores tokens and syncs appointments
            googleCalendarService.handleOAuthCallback(code, attendantId, retailer);
            
            LOGGER.info("@@@@Successfully completed OAuth for attendant: {}", attendantId);
            
            // Build success response
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Google Calendar sync has been successfully enabled!");
            response.put("attendantId", attendantId);
            response.put("retailer", retailer);
            
            return new BaseResponseDto(Status.SUCCESS, response);
            
        } catch (EtailBookItException e) {
            LOGGER.error("Business error during OAuth completion for attendant {}: {}", attendantId, e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("Unexpected error during OAuth completion for attendant {}: {}", attendantId, e.getMessage(), e);
            throw new EtailBookItException("Failed to complete Google Calendar authorization: " + e.getMessage());
        }
    }


    /**
     * Manually sync existing appointments to Google Calendar
     * Triggers async sync after validating attendant and appointments exist
     * 
     * @param attendantId - ID of the attendant
     * @return Success message indicating sync has been triggered
     */
    @PostMapping("/sync/{attendantId}")
    public BaseResponseDto syncAppointments(@PathVariable("attendantId") Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered syncAppointments for attendant: {}", attendantId);
        
        googleCalendarService.triggerAsyncSync(attendantId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Sync has been initiated successfully. Your appointments are being synced in the background.");
        response.put("attendantId", attendantId);
        response.put("status", "in_progress");
        
        LOGGER.info("@@@@Exited syncAppointments - async sync triggered for attendant {}", attendantId);
        return new BaseResponseDto(Status.SUCCESS, response);
    }

}

