package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.AddonServiceDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IAddonServiceService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/add-on-service")
public class AddonServiceController {

    @Autowired
    IAddonServiceService iAddonServiceService;

    private static Logger LOGGER = LoggerFactory.getLogger(AddonServiceController.class);

    @PostMapping("/new")
    public BaseResponseDto addAddonService(@RequestBody AddonServiceDto addonServiceDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered addAddonService");
        try {
            addonServiceDto = iAddonServiceService.addAddonService(addonServiceDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited addAddonService");
        return new BaseResponseDto(Status.SUCCESS, addonServiceDto);
    }

    @GetMapping
    public BaseResponseDto getAllAddonServices(@RequestParam(defaultValue = "0") Integer pageNo,
                                               @RequestParam(defaultValue = "10") Integer pageSize,
                                               @RequestParam(defaultValue = "name") String sortBy,
                                               @RequestParam Optional<String> search)throws EtailBookItException {
        LOGGER.info("@@@@Entered getAllAddonServices");
        Page<AddonServiceDto> addonServiceDtos = iAddonServiceService.getAddonServices(pageNo, pageSize, sortBy,search);
        LOGGER.info("@@@@Exited getAllAddonServices");
        return new BaseResponseDto(Status.SUCCESS, addonServiceDtos);
    }



    @GetMapping("/{id}")
    public BaseResponseDto getAddonServiceById(@PathVariable("id") int addonServiceId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getAddonServiceById :::: " + addonServiceId);
        AddonServiceDto addonServiceDto;
        try {
            addonServiceDto = iAddonServiceService.getAddonServiceById(addonServiceId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getAddonServiceById");
        return new BaseResponseDto(Status.SUCCESS, addonServiceDto);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deleteAddonServiceById(@PathVariable("id") int addonServiceId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deleteAddonServiceById :::: " + addonServiceId);
        try {
            iAddonServiceService.deleteAddonService(addonServiceId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deleteAddonServiceById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updateAddonService(@RequestBody AddonServiceDto addonServiceDto, @PathVariable("id") int addonServiceId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateAddonService");
        try {
            addonServiceDto = iAddonServiceService.updateAddonService(addonServiceDto, addonServiceId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateAddonService");
        return new BaseResponseDto(Status.SUCCESS, addonServiceDto);
    }

    @PutMapping("/update/bulk")
    public BaseResponseDto updateAddonService(@RequestBody List<AddonServiceDto> addonServiceDtos) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateAddonService");
        try {
            iAddonServiceService.bulkUpdateAddonService(addonServiceDtos);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateAddonService");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getAllByActive")
    public BaseResponseDto getAllByActive(@RequestParam(defaultValue = "0") Integer pageNo,
                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                          @RequestParam(defaultValue = "name") String sortBy,
                                          @RequestParam (required = false,defaultValue = "")String search) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllByActive --------");
        Page<AddonServiceDto> addonServiceDtos ;
        BaseResponseDto response ;
        try {
            addonServiceDtos = iAddonServiceService.getActiveService(pageNo,pageSize,sortBy,search);
            if(addonServiceDtos.getTotalElements() != 0)
                response = new BaseResponseDto(Status.SUCCESS,addonServiceDtos);
            else
                response = new BaseResponseDto(Status.FAILED,"No active AdOnService");
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllByActive --------");
        return response;

    }

}