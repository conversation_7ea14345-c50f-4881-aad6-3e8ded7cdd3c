package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.BookingDto;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.ITimeslotService;
import com.sayone.etailbookit.service.impl.AppointmentServiceImpl;
import com.sayone.etailbookit.service.impl.SlotCreationPublisher;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;

@RestController
@RequestMapping("/api/v1/timeslot")
public class TimeSlotController {

    private static Logger LOGGER = LoggerFactory.getLogger(TimeSlotController.class);

    @Autowired
    ITimeslotService iTimeslotService;

    @Autowired
    AppointmentServiceImpl appointmentServiceImpl;


    @PostMapping("/generateSlots")
    public BaseResponseDto generateSlotsForService(@RequestBody ServiceSlotsDto serviceSlotsDto, @RequestParam(value = "tz") String tz) throws EtailBookItException {

        LOGGER.info(" --------Entered generateSlotsForService --------");
        BaseResponseDto response;

        response = iTimeslotService.generateServiceAvailability(serviceSlotsDto,tz);
        LOGGER.info("-------- Exited generateSlotsForService --------");
        return response;
    }

    @PostMapping("/generate-slots")
    public BaseResponseDto generateSlots(@RequestBody ServiceSlotsDto serviceSlotsDto) throws EtailBookItException {

        LOGGER.info(" --------Entered generateSlotsForService --------");
        BaseResponseDto response;

        response = iTimeslotService.generateServiceAvailabilityBackgroundProcessing(serviceSlotsDto);
        LOGGER.info("-------- Exited generateSlotsForService --------");
        return response;
       // return ResponseEntity.ok(new BaseResponseDto(Status.SUCCESS, "Slot creation initiated."));
    }


    @GetMapping("/getSlotById")
    public BaseResponseDto getSlotById(@RequestParam Integer id,@RequestParam(value = "tz")String tz) throws EtailBookItException {

        LOGGER.info(" --------Entered getSlotsById --------");
        BaseResponseDto response;
        try {
             response = iTimeslotService.getSlotById(id,tz);
        }catch (EtailBookItException e){
            throw new EtailBookItException("Exception thrown while processing the request ::"+e);
        }

        LOGGER.info("-------- Exited getSlotsById --------");
        return response;
    }

    @DeleteMapping("/deleteSlotsById")
    public BaseResponseDto deleteSlotsById(@RequestParam Integer id,@RequestParam(value = "changeEntireRecurringSlots")boolean changeEntireRecurringSlots) throws EtailBookItException {

        LOGGER.info(" --------Entered getSlotsById --------");

        BaseResponseDto response = iTimeslotService.deleteSlotsById(id,changeEntireRecurringSlots);

        LOGGER.info("-------- Exited getSlotsById --------");
        return response;
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updateTimeSlot(@PathVariable("id") int id, @RequestBody ServiceSlotsDto serviceSlotsDto,@RequestParam(value = "changeEntireRecurringSlots")boolean changeEntireRecurringSlots,
                                          @RequestParam(value = "tz")String timeZone)throws EtailBookItException{
        LOGGER.info("--------------Entered updateSlot------------");

        BaseResponseDto responseDto=iTimeslotService.updateSlots(id,serviceSlotsDto,changeEntireRecurringSlots,timeZone);

        LOGGER.info("-----------Exited update slot---------");
        return responseDto;
    }

    @GetMapping("/onDate")
    public BaseResponseDto getTimeSlotsOnDate(@RequestParam("slotDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)OffsetDateTime slotDateTime, @RequestParam("tz") String timeZone,
                                              @RequestParam(defaultValue = "0") Integer pageNo,
                                              @RequestParam(defaultValue = "10") Integer pageSize,
                                              @RequestParam(defaultValue = "id") String sortBy)throws EtailBookItException{
        LOGGER.info("--------------Entered getSlots by date -------------");

        //BaseResponseDto responseDto=iTimeslotService.getSlotsByDate(slotDateTime,timeZone);
       // BaseResponseDto responseDto=iTimeslotService.getSlotsByDate1(slotDateTime,timeZone,pageNo,pageSize,sortBy);
        BaseResponseDto responseDto=iTimeslotService.getSlotsByDateNew(slotDateTime,timeZone,pageNo,pageSize,sortBy);

        LOGGER.info("-------------Exited get slot by Date--------");
        return  responseDto;
    }

    @GetMapping
    public BaseResponseDto getAllTimeSlots(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    ) throws EtailBookItException {

    LOGGER.info("--------------Entered get all time slots----------------");

    //BaseResponseDto responseDto=iTimeslotService.getAllTimeSlots(pageNo, pageSize, sortBy,startDate, endDate,timeZone);
    BaseResponseDto responseDto=iTimeslotService.getAllTimeSlots1(pageNo, pageSize, sortBy,startDate, endDate,timeZone);

    LOGGER.info("--------------Exited get all time slots-----------------");
    return responseDto;
    }

    @GetMapping("/timeSlotsOfAMonth")
    public BaseResponseDto getAllTimeSlotsOfAMonth(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    ) throws EtailBookItException {

        LOGGER.info("--------------Entered get all time slots of a month---------------");

        BaseResponseDto responseDto=iTimeslotService.getAllTimeSlotsofAMonth(pageNo, pageSize, sortBy,startDate, endDate,timeZone);

        LOGGER.info("--------------Exited get all time slots of a month-----------------");
        return responseDto;
    }


 /*   @GetMapping
    public BaseResponseDto getAllTimeSlotsOfAWeek(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    ) throws EtailBookItException {

        LOGGER.info("--------------Entered get all time slots----------------");

        BaseResponseDto responseDto=iTimeslotService.getAllTimeSlotsofAWeek(pageNo, pageSize, sortBy,startDate, endDate,timeZone);

        LOGGER.info("--------------Exited get all time slots-----------------");
        return responseDto;
    }*/


    //todo:this API is not using since the requirement is changed and the slots are fetched without attendant input parameter
    @GetMapping("/availableTimeSlotsByDate")
    public BaseResponseDto availableTimeSlotsByDate(
            @RequestParam("service") Integer serviceId,
            @RequestParam("venue") Integer venueId,
            @RequestParam(value = "attendant",required = false) Integer attendantId,
            @RequestParam(value = "date")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime appointmentDate,@RequestParam(value = "tz",required = false) String timeZone
    )throws EtailBookItException
    {
        LOGGER.info("--------------Entered get slots for a day ----------------");

        OffsetDateTime utcAppointmentDate=appointmentDate;
        Integer daylightSavingsMinutes=appointmentServiceImpl.checkDayLightSavings(timeZone,utcAppointmentDate);
        Integer offsetTimeDifference=appointmentServiceImpl.calculateOffset(timeZone,utcAppointmentDate);
        Integer daylightAddedOffsetMinutes=daylightSavingsMinutes+offsetTimeDifference;
        appointmentDate=appointmentDate.plusMinutes(daylightAddedOffsetMinutes);
        BookingDto bookingInfo = BookingDto.builder()
                .serviceId(serviceId)
                .venueId(venueId)
                .attendantId(attendantId)
               // .serviceDuration(serviceDuration)
                .appointmentDate(appointmentDate)
                .utcAppointmentDate(utcAppointmentDate)
                //.appointmentDate(OffsetDateTime.parse(appointmentDate + OffsetContext.getOffset()))
                .build();

        BaseResponseDto responseDto=iTimeslotService.getTimeSlotsOfADay(bookingInfo,timeZone);

        LOGGER.info("--------------Exited get slots for a day -----------------");
        return responseDto;

    }
    //todo:this API is not using since the requirement is changed and the slots availability is being fetched without attendant input parameter
 /*   @GetMapping("/fetchAvailableSlotsOfAMonth")
    public BaseResponseDto fetchAvailableSlotsOfAMonth(
            @RequestParam("service") Integer serviceId,
            @RequestParam("venue") Integer venueId,
            @RequestParam(value = "attendant",required = false) Integer attendantId,
            @RequestParam(value = "startDate")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime appointmentDate,
            @RequestParam(value = "endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,@RequestParam(value = "tz",required = false) String timeZone
    )throws EtailBookItException{
        LOGGER.info("-------------- Entered fetch slots of a month ---------------");

        BaseResponseDto responseDto=iTimeslotService.fetchAvailableSlotsOfAMonth(serviceId,venueId,attendantId,appointmentDate,endDate,timeZone);

        LOGGER.info("-------------- Exited fetch slots of a month ---------------");

        return  responseDto;
    }*/
    @GetMapping("/fetchSlotsByAttendant")
    public BaseResponseDto fetchSlotsByAttendant(
            @RequestParam("service") Integer serviceId,
            @RequestParam("venue") Integer venueId,
            @RequestParam(value = "date")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime slotStartTime,@RequestParam(value = "tz",required = false) String timeZone
    )throws EtailBookItException {
        LOGGER.info("--------------Entered fetchSlotsByAttendant ----------------");
        BaseResponseDto responseDto ;
        try {
           // responseDto = iTimeslotService.fetchSlotsByAttendant(serviceId, venueId, slotStartTime, timeZone);
            responseDto = iTimeslotService.fetchSlotsByAttendantNew(serviceId, venueId, slotStartTime, timeZone);
        } catch (Exception e) {
            LOGGER.error("Exception thrown while processing the request::" + e);
            throw e;
        }

        LOGGER.info("--------------Exited fetchSlotsByAttendant -----------------");
        return responseDto;

    }
    @GetMapping("/fetchSlotsByAttendantOfaMonth")
    public BaseResponseDto fetchSlotsByAttendantOfaMonth(
            @RequestParam("service") Integer serviceId,
            @RequestParam("venue") Integer venueId,
            @RequestParam(value = "startDate")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,@RequestParam(value = "tz",required = false) String timeZone
    )throws EtailBookItException {
        LOGGER.info("--------------Entered fetchSlotsAvailabilityByAttendant ----------------");
        BaseResponseDto responseDto ;
        try {
          //  responseDto = iTimeslotService.fetchSlotsByAttendantOfaMonth(serviceId, venueId, startDate, endDate,timeZone);
          //  responseDto = iTimeslotService.fetchSlotsByAttendantOfaMonthNew(serviceId, venueId, startDate, endDate,timeZone);
            responseDto = iTimeslotService.getSlotsByAttendantOfaMonth(serviceId, venueId, startDate, endDate,timeZone);
        } catch (Exception e) {
            LOGGER.error("Exception thrown while processing the request fetching slots availability by month::" + e);
            throw e;
        }

        LOGGER.info("--------------Exited fetchSlotsAvailabilityByAttendant -----------------");
        return responseDto;

    }

    @DeleteMapping("/deleteEntriesOfARetailer")
    public BaseResponseDto deleteEntriesOfARetailer(){
        LOGGER.info("--------------Entered deleting slots of a particular slots of a retailer::  ----------------");
        BaseResponseDto responseDto ;
        try {
            responseDto = iTimeslotService.deleteEntriesOfARetailer(RetailerContext.getRetailer());
        } catch (Exception e) {
            LOGGER.error("Exception thrown while processing the request of deleting slots of a particular slots of a retailer::" + e);
            throw e;
        }

        LOGGER.info("--------------Exited deleting slots of a particular slots of a retailer:: -----------------");
        return responseDto;

    }
}
