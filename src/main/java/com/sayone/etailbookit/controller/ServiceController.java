package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.ServiceDetailsDto;
import com.sayone.etailbookit.dto.ServiceDto;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.ServiceListingProjection;
import com.sayone.etailbookit.projections.ServiceNameListing;
import com.sayone.etailbookit.service.IService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/service")
public class ServiceController {

    private static Logger LOGGER = LoggerFactory.getLogger(ServiceController.class);

    @Autowired
    IService iService;

    @PostMapping("/new")
    public BaseResponseDto addService(@RequestBody ServiceDto serviceDto) throws EtailBookItException {

        LOGGER.info(" --------Entered addService --------");
        Boolean response;
        try {
            response = iService.addService(serviceDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited addService --------");
        if(response)
            return new BaseResponseDto(Status.SUCCESS);
        else
            return new BaseResponseDto(Status.FAILED);
    }

    @GetMapping("/{serviceId}")
    public BaseResponseDto getServiceById(@PathVariable("serviceId") int serviceId) throws EtailBookItException {

        LOGGER.info(" --------Entered getServiceById --------");
        ServiceDetailsDto service;
        try {
            service = iService.getServiceById(serviceId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getServiceById --------");
        return new BaseResponseDto(Status.SUCCESS,service);

    }

    @DeleteMapping("/{serviceId}")
    public BaseResponseDto deleteServiceById(@PathVariable("serviceId") int serviceId) throws EtailBookItException {

        LOGGER.info(" --------Entered deleteServiceById --------"+serviceId);
        try {
            iService.deleteServiceById(serviceId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited deleteServiceById --------");
        return new BaseResponseDto(Status.SUCCESS);

    }

    @GetMapping
    public BaseResponseDto getAllService() throws EtailBookItException {

        LOGGER.info(" --------Entered getAllService --------");
        List<ServiceNameListing> services;
        BaseResponseDto response;
        try {
            services = iService.getAllService();
            if(services != null && services.size() != 0)
                response = new BaseResponseDto(Status.SUCCESS, services);
            else
                response = new BaseResponseDto(Status.FAILED,"There is no Service Available");
        }
        catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllService --------");
        return response;

    }


    @GetMapping("/serviceByPagination")
    public BaseResponseDto getServiceByPagination( @RequestParam(defaultValue = "0") Integer pageNo,
                                                   @RequestParam(defaultValue = "10") Integer pageSize,
                                                   @RequestParam(defaultValue = "name") String sortBy,
                                                   @RequestParam Optional<String> search) throws EtailBookItException {

        LOGGER.info(" --------Entered getServiceByPagination --------");
        Page<ServiceListingProjection> services = iService.getServiceByPagination(pageNo, pageSize, sortBy,search);
        LOGGER.info("-------- Exited getServiceByPagination --------");
        return new BaseResponseDto(Status.SUCCESS, services);

    }

    @PutMapping("/update")
    public BaseResponseDto updateService(@RequestBody ServiceDto serviceDto) throws EtailBookItException {

        LOGGER.info(" --------Entered updateService --------"+serviceDto.getServiceId());
        Boolean response;
        try {
            response = iService.updateService(serviceDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited updateService --------");
        if(response)
            return new BaseResponseDto(Status.SUCCESS);
        else
            return new BaseResponseDto(Status.FAILED);

    }

    @GetMapping("/serviceType/{serviceTypeId}")
    public BaseResponseDto getServiceByServiceType(@PathVariable("serviceTypeId") int serviceTypeId) throws EtailBookItException {

        LOGGER.info(" --------Entered getServiceByServiceType --------");
        BaseResponseDto response;
        List<ServiceListingProjection> services;
        try {
            services = iService.getServiceByServiceType(serviceTypeId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getServiceByServiceType --------");
        if(services != null && services.size() != 0 )
            return  new BaseResponseDto(Status.SUCCESS,services);
        else
            return new BaseResponseDto(Status.FAILED, Collections.emptyList());

    }

    @GetMapping("/getAllByActive")
    public BaseResponseDto getAllByActive() throws EtailBookItException {

        LOGGER.info(" --------Entered getAllByActive --------");
        List<ServiceDto> serviceDtoList;
        BaseResponseDto response ;
        try {
            serviceDtoList = iService.getActiveService();
            if(serviceDtoList.size() != 0)
                response = new BaseResponseDto(Status.SUCCESS,serviceDtoList);
            else
                response = new BaseResponseDto(Status.FAILED,"No active Service");
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllByActive --------");
        return response;

    }

    @GetMapping("/syncServices")
    public BaseResponseDto syncServicesToPos() {
        LOGGER.info(" --------Entered syncServicesToPos --------");
        iService.syncServices();
        LOGGER.info("-------- Exited syncServicesToPos --------");
        return new BaseResponseDto(Status.SUCCESS);

    }
    @GetMapping("/clearInternalItemNumber")
    public BaseResponseDto clearInternalItemNumber() throws EtailBookItException{
        LOGGER.info("----------Entered clearInternalItemNumber-----------");
        Integer numberOfServices;
        try{
             numberOfServices=iService.updateInternalItemNumber();
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("----------Exited Internal Item Number-----------");
        return new BaseResponseDto(Status.SUCCESS,numberOfServices);
    }

    @GetMapping("/getServicesByRetailer")
    public BaseResponseDto getServicesByRetailer(@RequestParam(defaultValue = "0") Integer pageNo,
                                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                                 @RequestParam(defaultValue = "name") String sortBy,
                                                 @RequestParam Optional<String> search) throws EtailBookItException{
        Page<ServiceListingProjection> services;
        try {
            services=iService.getServiceByRetailer(pageNo,pageSize,sortBy,search, RetailerContext.getRetailer());
        }
       catch (EtailBookItException e){
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
       }
        return new BaseResponseDto<>(Status.SUCCESS,services);
    }



}
