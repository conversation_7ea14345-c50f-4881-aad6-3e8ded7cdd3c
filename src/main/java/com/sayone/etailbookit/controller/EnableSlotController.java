package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IEnableSlotService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/enableSlots")
public class EnableSlotController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EnableSlotController.class);

    @Autowired
    IEnableSlotService iEnableSlotService;
    
    @PutMapping("/createOrUpdate")
    public BaseResponseDto saveEnableSlotValue(@RequestParam(value = "isEnableSlot",required = false) boolean isEnableSlot) throws EtailBookItException {
        LOGGER.info("Creating or updating enable slot value: {} for retailer: {}", isEnableSlot, RetailerContext.getRetailer());
        
        try {
            iEnableSlotService.createOrUpdateEnableSlot(isEnableSlot);
            LOGGER.info("Successfully created/updated enable slot value for retailer: {}", RetailerContext.getRetailer());
            return new BaseResponseDto<>(Status.SUCCESS);
        } catch (EtailBookItException e) {
            LOGGER.error("Business logic error while creating/updating enable slot value for retailer {}: {}", 
                RetailerContext.getRetailer(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("Unexpected error while creating/updating enable slot value for retailer {}: {}", 
                RetailerContext.getRetailer(), e.getMessage(), e);
            throw new EtailBookItException("Failed to create or update enable slot value: " + e.getMessage());
        }
    }

    @GetMapping
    public BaseResponseDto getEnableSlotValue() throws EtailBookItException {
        LOGGER.info("Getting enable slot value for retailer: {}", RetailerContext.getRetailer());
        
        try {
            BaseResponseDto responseDto = iEnableSlotService.getEnableSlotValue(RetailerContext.getRetailer());
            LOGGER.info("Successfully retrieved enable slot value for retailer: {}", RetailerContext.getRetailer());
            return responseDto;
        } catch (EtailBookItException e) {
            LOGGER.error("Business logic error while getting enable slot value for retailer {}: {}", 
                RetailerContext.getRetailer(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("Unexpected error while getting enable slot value for retailer {}: {}", 
                RetailerContext.getRetailer(), e.getMessage(), e);
            throw new EtailBookItException("Failed to retrieve enable slot value: " + e.getMessage());
        }
    }
}
