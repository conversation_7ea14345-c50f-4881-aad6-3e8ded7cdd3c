package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.dto.PetShampooDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IPetShampooService;
import com.sayone.etailbookit.util.Status;
import com.sayone.etailbookit.util.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/pet-shampoo")
public class PetShampooController {

    @Autowired
    IPetShampooService iPetShampooService;

    private static Logger LOGGER = LoggerFactory.getLogger(PetShampooController.class);

    @PostMapping("/new")
    public BaseResponseDto addPetShampoo(@RequestBody PetShampooDto petShampooDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered addPetShampoo");
        try {
            petShampooDto = iPetShampooService.addPetShampoo(petShampooDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited addPetShampoo");
        return new BaseResponseDto(Status.SUCCESS,petShampooDto);
    }

    @GetMapping
    public BaseResponseDto getAllPetShampoos(@RequestParam(defaultValue = "0") Integer pageNo,
                                             @RequestParam(defaultValue = "10") Integer pageSize,
                                             @RequestParam(defaultValue = "name") String sortBy,
                                             @RequestParam(required = false, defaultValue = "") String search) {
        LOGGER.info("@@@@Entered getAllPetShampoos");
        ConfigurationDto<PetShampooDto> petShampooDtos;
        petShampooDtos = iPetShampooService.getPetShampoos(pageNo, pageSize, sortBy, search);
        LOGGER.info("@@@@Exited getAllPetShampoos");
        return new BaseResponseDto(Status.SUCCESS, petShampooDtos);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getPetShampooById(@PathVariable("id") int petShampooId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getPetShampooById :::: " + petShampooId);
        PetShampooDto petShampooDto = null;
        try {
            petShampooDto = iPetShampooService.getPetShampooById(petShampooId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getPetShampooById");
        return new BaseResponseDto(Status.SUCCESS,petShampooDto);
    }

    @GetMapping("/getShampooByPagination")
    public BaseResponseDto getShampooByPagination(@RequestParam(defaultValue = "0") Integer pageNo,
                                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                                  @RequestParam(defaultValue = "name") String sortBy,
                                                  @RequestParam Optional<String> search) throws EtailBookItException{
        LOGGER.info("@@@@@@@@@@@@@@@@ Entered getShampoo By Pagination");
        Page<PetShampooDto> petShampooDto;
        try {
            petShampooDto=iPetShampooService.getShampooByPagination(pageNo, pageSize, sortBy,search);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing request:::"+e);
            throw e;
        }
        LOGGER.info("@@@@@@@@@@@@ Exited get Shampoo by Pagination");
        return new BaseResponseDto<>(Status.SUCCESS,petShampooDto);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deletePetShampooById(@PathVariable("id") int petShampooId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deletePetShampooById :::: " + petShampooId);
        try {
            iPetShampooService.deletePetShampoo(petShampooId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deletePetShampooById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updatePetShampoo(@RequestBody PetShampooDto petShampooDto, @PathVariable("id") int petShampooId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updatePetShampoo");
        try {
            petShampooDto = iPetShampooService.updatePetShampoo(petShampooDto, petShampooId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updatePetShampoo");
        return new BaseResponseDto(Status.SUCCESS,petShampooDto);
    }

    @PutMapping("/update/bulk")
    public BaseResponseDto bulkUpdateServiceType(@RequestBody ConfigurationDto<PetShampooDto> petShampooDtos) throws EtailBookItException {
        LOGGER.info("@@@@Entered bulkUpdateServiceType");
        try {
            iPetShampooService.bulkUpdateShampoo(petShampooDtos);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited bulkUpdateServiceType");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getAllByActive")
    public BaseResponseDto getAllByActive(@RequestParam(defaultValue = "0") Integer pageNo,
                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                          @RequestParam(defaultValue = "name") String sortBy,
                                          @RequestParam (required = false,defaultValue = "")String search) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllByActive --------");
        BaseResponseDto response;
        Page<PetShampooDto> petShampooDtos ;
        try {
            petShampooDtos = iPetShampooService.getActiveShampoo(pageNo,pageSize,sortBy,search);
            if(petShampooDtos.getTotalElements()!= 0)
                response =  new BaseResponseDto(Status.SUCCESS,petShampooDtos);
            else
                response =  new BaseResponseDto(Status.FAILED,"No active Shampoo");

        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllByActive --------");
        return response;

    }

}