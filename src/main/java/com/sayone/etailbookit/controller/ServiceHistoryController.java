package com.sayone.etailbookit.controller;


import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.AppointmentHistoryListingProjection;
import com.sayone.etailbookit.service.IServiceHistoryService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;


import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/service")
public class ServiceHistoryController {

    private static Logger LOGGER = LoggerFactory.getLogger(ServiceHistoryController.class);

    @Autowired
    IServiceHistoryService iServiceHistoryService;


    @PostMapping("/{appointmentId}/start")
    public BaseResponseDto startService(@RequestParam("serviceStartAt") String serviceStartAt,@PathVariable("appointmentId") int appointmentId,
                                        @RequestParam(value = "updatedCustomerId",required = false) Integer updatedCustomerId,
                                        @RequestParam(value = "updatedCustomerName",required = false) String updatedCustomerName) throws EtailBookItException {
        LOGGER.info("@@@@Entered startService");
        PaymentDto paymentDetails;
        try {
            paymentDetails = iServiceHistoryService.startService(appointmentId,serviceStartAt,updatedCustomerId,updatedCustomerName);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited startService");

            return new BaseResponseDto(Status.SUCCESS,paymentDetails);
    }

    @PostMapping("/{appointmentId}/end")
    public BaseResponseDto endService(@ModelAttribute ServiceHistoryDto serviceHistoryDto, @PathVariable("appointmentId") int appointmentId) throws EtailBookItException {
        LOGGER.info("@@@@Entered endService");
        PaymentDto paymentResponse;
        try {
            paymentResponse = iServiceHistoryService.endService(serviceHistoryDto,appointmentId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw new EtailBookItException(e);
        }
        LOGGER.info("@@@@Exited endService");
            return new BaseResponseDto(Status.SUCCESS,paymentResponse);
    }

    @GetMapping("/appointmentHistory")
    public BaseResponseDto getAppointmentHistoryByCustomer(@RequestParam("customer") Integer customer,
                                                              @RequestParam(defaultValue = "0") Integer pageNo,
                                                              @RequestParam(defaultValue = "10") Integer pageSize,
                                                              @RequestParam(defaultValue = "id") String sortBy,
                                                              @RequestParam("search") Optional<String> search,
                                                              @RequestParam(value = "startDate", required = false) String startDate,
                                                              @RequestParam(value = "endDate", required = false) String endDate,
                                                              @RequestParam("petType") Optional<Integer> petType,
                                                              @RequestParam("serviceStatus") Optional<String> serviceStatus,
                                                              @RequestParam("service")Optional<String> service) throws EtailBookItException {

        LOGGER.info("-----Entered getAppointmentHistoryByCustomer ----- " + customer);
        Page<AppointmentHistoryListingProjection> appointments ;
        try {
            appointments = iServiceHistoryService.appointmentHistory(customer,pageNo, pageSize, sortBy,search, startDate,endDate,petType,serviceStatus,service);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-----Exited getAppointmentHistoryByCustomer-----");
        return new BaseResponseDto(Status.SUCCESS, appointments == null ? "No appointments found for customer" : appointments);

    }

    @GetMapping("/detailedHistory")
    public BaseResponseDto getDetailedHistory(@RequestParam("appointmentId") Integer appointmentId) throws EtailBookItException {

        LOGGER.info("-----Entered getDetailedHistory ----- " + appointmentId);
        DetailHistoryDto detailHistoryDto;
        try {
            detailHistoryDto = iServiceHistoryService.getDetailedHistory(appointmentId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-----Exited getDetailedHistory-----");
        return new BaseResponseDto(Status.SUCCESS, detailHistoryDto);

    }

    @GetMapping("/detailedNote")
    public BaseResponseDto getDetailedNote(@RequestParam("appointmentId") Integer appointmentId) throws EtailBookItException {

        LOGGER.info("-----Entered getDetailedNote ----- " + appointmentId);
        ServiceHistoryDto detailHistoryDto ;
        try {
            detailHistoryDto =  iServiceHistoryService.getDetailedNote(appointmentId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-----Exited getDetailedNote-----");
        return new BaseResponseDto(Status.SUCCESS, detailHistoryDto);

    }

    
    @PostMapping("/{appointmentId}/cancel")
    public BaseResponseDto cancelService( @PathVariable("appointmentId") int appointmentId,@RequestParam("tz") String timeZone, @RequestParam(value = "cancellationReason",required = false) String cancellationReason,
                                          @RequestParam(value = "updatedCustomerId",required = false) Integer updatedCustomerId,
                                          @RequestParam(value = "updatedCustomerName",required = false) String updatedCustomerName) throws EtailBookItException {
        LOGGER.info("-------Entered cancelService--------");
        Boolean response;
        try {
            response = iServiceHistoryService.cancelService(appointmentId,timeZone,cancellationReason,updatedCustomerId,updatedCustomerName);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw new EtailBookItException(e);
        }
        LOGGER.info("-------Exited startService-------");
        if(response)
            return new BaseResponseDto(Status.SUCCESS,"Appointment canceled successfully");
        else
            return new BaseResponseDto(Status.FAILED,"Failed to cancel Appointment ");
    }

    @PostMapping("/{appointmentId}/addNote")
    public BaseResponseDto addNote(@RequestBody NoteDto noteDto, @PathVariable int appointmentId) throws EtailBookItException{
        LOGGER.info("------------Entered Add Notes --------------");
        try {
            iServiceHistoryService.addNotes(appointmentId,noteDto);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occurred while processing request :::" + e);
            throw e;
        }catch (Exception e){
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw  new EtailBookItException(e);
        }

        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/{appoinmentId}/listNotes")
    public  BaseResponseDto listNotes(@PathVariable Integer appoinmentId) throws EtailBookItException {
        LOGGER.info("------------Entered List Notes -------------");
        ServiceHistoryNotesListingDto response;
        try {
            response=iServiceHistoryService.listNotes(appoinmentId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request :::" + e);
            throw e;
        }
        LOGGER.info("----------------Existed List Notes ----------");
        return new BaseResponseDto(Status.SUCCESS,response);
    }

    @GetMapping("/list-notes-by-pet-id/{petId}")
    public  BaseResponseDto listNotesByPetId(@RequestParam(defaultValue = "0") Integer pageNo,
                                             @RequestParam(defaultValue = "10") Integer pageSize,
                                             @PathVariable Integer petId) throws EtailBookItException {
        LOGGER.info("------------Entered List Notes -------------");
        Page<ServiceHistoryNotesDto> response;
        try {
            response=iServiceHistoryService.listNotesByPetId(petId, pageNo, pageSize);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request :::" + e);
            throw e;
        }
        LOGGER.info("----------------Existed List Notes ----------");
        return new BaseResponseDto(Status.SUCCESS,response);
    }

    @PutMapping("{id}/notes")
    public BaseResponseDto updateNotes (@PathVariable Integer id,@RequestBody NoteDto noteDto) throws EtailBookItException{
        LOGGER.info("------------Entered Update Notes -------------");
        try{
            iServiceHistoryService.updateNotes(id, noteDto);
        }
        catch (EtailBookItException e){
            LOGGER.info("Exception occurred while processing request ::: "+ e);
            throw e;
        }
        LOGGER.info("----------------Updated Notes ----------");
        return new BaseResponseDto(Status.SUCCESS);
    }
    @PostMapping("/{appointmentId}/waive")
    public BaseResponseDto waiveService(@PathVariable Integer appointmentId,
                                        @RequestParam(value = "updatedCustomerId",required = false) Integer updatedCustomerId,
                                        @RequestParam(value = "updatedCustomerName",required = false) String updatedCustomerName) throws EtailBookItException{
        LOGGER.info("***************** Entered waive service ***********************");
        Boolean response;
        try {
            response = iServiceHistoryService.waiveService(appointmentId,updatedCustomerId,updatedCustomerName);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing the request"+e);
            throw e;
        }

        LOGGER.info("************** Exited Waive Service ******************************");
        return new BaseResponseDto<>(Status.SUCCESS,response);
    }
    @PostMapping("/{appointmentId}/changeServiceStatus")
    public BaseResponseDto approveService(@PathVariable Integer appointmentId, @RequestParam ("serviceStatus") String serviceStatus,@RequestParam("tz") String timeZone, @RequestParam(value = "cancellationReason",required = false)String cancellationReason,@RequestParam(value = "rejectionReason",required = false)String rejectionReason,
                                          @RequestParam(value = "updatedCustomerId",required = false) Integer updatedCustomerId,
                                          @RequestParam(value = "updatedCustomerName",required = false) String updatedCustomerName) throws EtailBookItException{
        LOGGER.info("***************** Entered approve service ***********************");
        Boolean response;
        try {
            response = iServiceHistoryService.changeServiceStatus(appointmentId,serviceStatus,timeZone,cancellationReason,rejectionReason,updatedCustomerId,updatedCustomerName);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing the request"+e);
            throw e;
        }
        LOGGER.info("************** Exited approve Service ******************************");
        return new BaseResponseDto<>(Status.SUCCESS,response);
    }
}
