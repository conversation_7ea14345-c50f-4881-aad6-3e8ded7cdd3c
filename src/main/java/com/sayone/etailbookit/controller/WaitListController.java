package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.dto.WaitlistBulkUpdateRequest;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.WaitListEntryService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/v1/waitList")
public class WaitListController {
    private static Logger LOGGER = LoggerFactory.getLogger(WaitListController.class);

    @Autowired
    WaitListEntryService waitListEntryService;

    @PostMapping("/addToWaitList")
    public BaseResponseDto addToWaitList(@RequestBody WaitlistFromAppointmentRequest waitlistFromAppointmentRequest) throws EtailBookItException {
           LOGGER.info("******** Entered add to waitList*************");
            BaseResponseDto responseDto ;
            try {
               responseDto= waitListEntryService.addToWaitList(waitlistFromAppointmentRequest, RetailerContext.getRetailer());
            } catch (EtailBookItException e) {
                LOGGER.error("Exception thrown while adding customer to waitList ::{} ",e);
                throw e;
            }
            LOGGER.info("*********Exited add to waitList**********");
        return responseDto;
    }
    
    @GetMapping("/timeSlot/{timeSlotId}")
    public List<WaitlistEntryDto> getWaitlistEntriesByTimeSlot(@PathVariable Integer timeSlotId) throws EtailBookItException {
        LOGGER.info("******** Entered get waitlist entries by time slot*************");
        List<WaitlistEntryDto> responseDto;
        try {
            responseDto = waitListEntryService.getWaitlistEntriesByTimeSlot(timeSlotId, RetailerContext.getRetailer());
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while getting waitlist entries ::{} ", e);
            throw e;
        }
        LOGGER.info("*********Exited get waitlist entries by time slot**********");
        return responseDto;
    }
    
    @DeleteMapping("/{waitlistEntryId}/customer/{customerEcomId}")
    public BaseResponseDto removeCustomerFromWaitlist(@PathVariable Integer waitlistEntryId, 
                                                     @PathVariable Integer customerEcomId) throws EtailBookItException {
        LOGGER.info("******** Entered remove customer from waitlist*************");
        BaseResponseDto responseDto;
        try {
            responseDto = waitListEntryService.removeCustomerFromWaitlist(waitlistEntryId, customerEcomId, RetailerContext.getRetailer());
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while removing customer from waitlist ::{} ", e);
            throw e;
        }
        LOGGER.info("*********Exited remove customer from waitlist**********");
        return responseDto;
    }
    
    @PutMapping("/{waitlistEntryId}/notify")
    public BaseResponseDto markWaitlistAsNotified(@PathVariable Integer waitlistEntryId) throws EtailBookItException {
        LOGGER.info("******** Entered mark waitlist as notified*************");
        BaseResponseDto responseDto;
        try {
            responseDto = waitListEntryService.markWaitlistAsNotified(waitlistEntryId, RetailerContext.getRetailer());
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while marking waitlist as notified ::{} ", e);
            throw e;
        }
        LOGGER.info("*********Exited mark waitlist as notified**********");
        return responseDto;
    }
    
    @PutMapping("/{waitlistEntryId}/book")
    public BaseResponseDto markWaitlistAsBooked(@PathVariable Integer waitlistEntryId) throws EtailBookItException {
        LOGGER.info("******** Entered mark waitlist as booked*************");
        BaseResponseDto responseDto;
        try {
            responseDto = waitListEntryService.markWaitlistAsBooked(waitlistEntryId, RetailerContext.getRetailer());
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while marking waitlist as booked ::{} ", e);
            throw e;
        }
        LOGGER.info("*********Exited mark waitlist as booked**********");
        return responseDto;
    }
    
    @PutMapping("/bulkUpdate")
    public BaseResponseDto bulkUpdateWaitlist(@RequestBody WaitlistBulkUpdateRequest request) throws EtailBookItException {
        LOGGER.info("******** Entered bulk update waitlist*************");
        BaseResponseDto responseDto;
        try {
            responseDto = waitListEntryService.bulkUpdateWaitlist(request, RetailerContext.getRetailer());
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while bulk updating waitlist ::{} ", e);
            throw e;
        }
        LOGGER.info("*********Exited bulk update waitlist**********");
        return responseDto;
    }
    
    @GetMapping("/all")
    public BaseResponseDto getAllWaitlistEntries(@RequestParam(defaultValue = "0") int pageNo,
                                                           @RequestParam(defaultValue = "10") int pageSize,
                                                 @RequestParam(defaultValue = "id") String sortBy) {

        Page<WaitlistTableResponseDto> waitlistTableResponseDtoPage=waitListEntryService.findAllWaitListedEntries(RetailerContext.getRetailer(),pageNo,pageSize,sortBy);
        BaseResponseDto responseDto =new BaseResponseDto<>(Status.SUCCESS,waitlistTableResponseDtoPage);
        return  responseDto;

    }
    
    @PostMapping("/timeSlot/{timeSlotId}/notify")
    public BaseResponseDto notifyWaitlistCustomersForSlot(@PathVariable Integer timeSlotId,
                                                         @RequestParam(defaultValue = "UTC") String timeZone) throws EtailBookItException {
        LOGGER.info("******** Entered notify waitlist customers for slot*************");
        BaseResponseDto responseDto;
        try {
            responseDto = waitListEntryService.notifyWaitlistCustomersForSlotAvailability(timeSlotId, RetailerContext.getRetailer(), timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while notifying waitlist customers ::{} ", e);
            throw e;
        }
        LOGGER.info("*********Exited notify waitlist customers for slot**********");
        return responseDto;
    }
}
