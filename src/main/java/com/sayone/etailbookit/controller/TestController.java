package com.sayone.etailbookit.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping("/db-version")
    public String getDatabaseVersion() {
        return jdbcTemplate.queryForObject("SELECT version()", String.class);
    }
}
