package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.AttendantInfo;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.AttendantDto;
import com.sayone.etailbookit.dto.SearchDto;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.AttendantListingProjection;
import com.sayone.etailbookit.service.IAttendantService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/attendant")
public class AttendantController {

    @Autowired
    IAttendantService iAttendantService;

    private static Logger LOGGER = LoggerFactory.getLogger(AttendantController.class);

    @PostMapping("/new")
    public BaseResponseDto addAttendant(@RequestBody AttendantDto attendantDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered addAttendant");
        try {
            iAttendantService.addAttendant(attendantDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited addAttendant");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/attendantsByPagination")
    public BaseResponseDto getAttendantsByPagination(@RequestParam(defaultValue = "0") Integer pageNo,
                                            @RequestParam(defaultValue = "10") Integer pageSize,
                                            @RequestParam(defaultValue = "firstName") String sortBy,
                                            @RequestParam Optional<String> search) {
        LOGGER.info("@@@@Entered attendantsByPagination");
        Page<AttendantListingProjection> attendants = iAttendantService.getAttendantsByPagination(pageNo, pageSize, sortBy,search);
        LOGGER.info("@@@@Exited attendantsByPagination");
        return new BaseResponseDto(Status.SUCCESS, attendants);
    }

    @GetMapping
    public BaseResponseDto getAllAttendants() {
        LOGGER.info("@@@@Entered getAllAttendants");
        List<AttendantListingProjection> attendants = iAttendantService.getAttendants();
        LOGGER.info("@@@@Exited getAllAttendants");
        return new BaseResponseDto(Status.SUCCESS, attendants);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getAttendantById(@PathVariable("id") int attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getAttendantById :::: " + attendantId);
        AttendantDto attendant;
        try {
            attendant = iAttendantService.getAttendantById(attendantId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getAttendantById");
        return new BaseResponseDto(Status.SUCCESS,attendant);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deleteAttendantById(@PathVariable("id") int attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deleteAttendantById :::: " + attendantId);
        try {
            iAttendantService.deleteAttendant(attendantId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deleteAttendantById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updateAttendant(@RequestBody AttendantDto attendantDto, @PathVariable("id") Integer attendantId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateAttendant");
        try {
            attendantDto = iAttendantService.updateAttendant(attendantDto, attendantId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateAttendant");
        return new BaseResponseDto(Status.SUCCESS,attendantDto);
    }

    @PostMapping("/search")
    public BaseResponseDto getAttendantsForService(@RequestBody SearchDto searchDto) throws EntityNotFoundException {
        LOGGER.info("@@@@Entered getAttendantsForService");
        List<AttendantDto> attendants = iAttendantService.getAttendantsForService(searchDto);
        LOGGER.info("@@@@Exited getAttendantsForService");
        return new BaseResponseDto(Status.SUCCESS, attendants);
    }

    @GetMapping("/activeAttendants")
    public BaseResponseDto getActiveAttendants() throws EntityNotFoundException {
        LOGGER.info("------Entered getActiveAttendants------");
        List<AttendantListingProjection> attendants;
        try {
            attendants = iAttendantService.getActiveAttendants();
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("------Exited getActiveAttendants------");
        return new BaseResponseDto(Status.SUCCESS, attendants);
    }
    @GetMapping("/{venueId}/{serviceId}/getAttendantByVenue")
    public BaseResponseDto getAttendantByVenue(@PathVariable("venueId") Integer venueId,
                                               @PathVariable("serviceId")Integer serviceId)throws EtailBookItException{
        LOGGER.info("**********************Entered getAttendant By venue*****************");
        List<AttendantDto> attendants;
        try{
            attendants=iAttendantService.getAttendantsByVenue(venueId,serviceId);
        }catch (EtailBookItException e){
            LOGGER.error("Exception Occurred while processing the request");
            throw e;
        }
        LOGGER.info("********************Exited getAttendants By Venue ******************");
        return new BaseResponseDto<>(Status.SUCCESS,attendants);
    }
    @GetMapping("/fetchAttendantIdAndName")
    public BaseResponseDto fetchAttendantIdAndName(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "firstName") String sortBy,
            @RequestParam Optional<String> search
    )throws EtailBookItException{
        LOGGER.info("**********************Entered fetchAttendantIdAndName *****************");
        List<AttendantInfo> attendants;
        try{
            attendants=iAttendantService.fetchAttendantIdAndName(pageNo,pageSize,sortBy,search,RetailerContext.getRetailer());
        }catch (EtailBookItException e){
            LOGGER.error("Exception Occurred while processing the request");
            throw e;
        }
        LOGGER.info("********************Exited fetchAttendantIdAndName ******************");
        return new BaseResponseDto<>(Status.SUCCESS,attendants);
    }

    @GetMapping("/filterByVenue/{venueId}")
    public BaseResponseDto filterAttendantsByVenue(@PathVariable("venueId") Integer venueId) throws EtailBookItException {
        LOGGER.info("**********Entered filterAttendantsByVenue**********");
        List<AttendantDto> attendants;
        try {
            attendants = iAttendantService.filterAttendantsByVenue(venueId,RetailerContext.getRetailer());
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request: ", e);
            throw e;
        }
        LOGGER.info("**********Exited filterAttendantsByVenue**********");
        return new BaseResponseDto<>(Status.SUCCESS, attendants);
    }
}