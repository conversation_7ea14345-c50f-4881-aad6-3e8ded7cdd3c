package com.sayone.etailbookit.controller;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.profile.ProfileCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.AmazonS3URI;
import com.amazonaws.services.s3.model.*;
import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.WaiverOfLiability;
import com.sayone.etailbookit.service.IWaiverOfLiabilityService;
import com.sayone.etailbookit.service.impl.EsignService;
import com.sayone.etailbookit.service.impl.S3Integration;
import com.sayone.etailbookit.util.DataFileProperties;
import com.sayone.etailbookit.util.Status;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.security.*;
import java.security.cert.Certificate;
import java.util.Collections;
import java.util.Date;

import com.itextpdf.signatures.DigestAlgorithms;
import com.itextpdf.signatures.PdfSigner;
import com.itextpdf.text.DocumentException;

@RestController
@RequestMapping("/api/v1/waiver-of-liability-information")
public class WaiverOfLiabilityController {
    @Autowired
    IWaiverOfLiabilityService iWaiverOfLiabilityService;
    @Autowired
    EsignService esignService;

    @Autowired
    S3Integration s3Integration;


    public static final String RESULT_FILE = new String( "_signed.pdf");


    @Value("${esignservice.keystore}")
    private String KEYSTORE;

    @Value("${esignservice.keystore.password}")
    private String pass;

    private static Logger LOGGER = LoggerFactory.getLogger(WaiverOfLiabilityController.class);

    @GetMapping
    public BaseResponseDto getWaiverOfLiability() {
        LOGGER.info("@@@@Entered getWaiverOfLiability");
        WaiverOfLiability waiverOfLiability = iWaiverOfLiabilityService.getWaiverOfLiabilityInformation();
        System.out.println(waiverOfLiability);
        LOGGER.info("@@@@Exited getWaiverOfLiability");
        return new BaseResponseDto(Status.SUCCESS, waiverOfLiability);
    }

    @PutMapping("/update")
    public BaseResponseDto updateWaiverOfLiability(@ModelAttribute WaiverOfLiability waiverOfLiability) throws EtailBookItException {
        LOGGER.info("@@@@Entered updateWaiverOfLiability");
        try {
            iWaiverOfLiabilityService.updateWaiverOfLiabilityInformation(waiverOfLiability);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updateWaiverOfLiability");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/sign")
    public BaseResponseDto signDocument(@RequestParam("initialName") String initialName, @RequestParam("SRC") String SRC, @RequestParam("petId") Integer petId) throws EtailBookItException, GeneralSecurityException, IOException, URISyntaxException, DocumentException {
        LOGGER.info("----------Entered SignDocument------------");
        String fileUrl;
        String temp_outPutFile=s3Integration.downloadFile(SRC);

        BouncyCastleProvider provider = new BouncyCastleProvider();
        Security.addProvider(provider);
        KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
        ks.load(new FileInputStream(KEYSTORE), pass.toCharArray());
        String alias = ks.aliases().nextElement();
        PrivateKey pk = (PrivateKey) ks.getKey(alias, pass.toCharArray());
        Certificate[] chain = ks.getCertificateChain(alias);
        String dest = temp_outPutFile.split("\\.pdf")[0] + "_" + petId + "_" + new Date().getTime() + RESULT_FILE;

        LOGGER.info("Destination::" + dest);
        LOGGER.info("________before entering esign-----------");
        String documentType="WAIVER OF LIABILITY";
        fileUrl = esignService.sign(temp_outPutFile, dest, chain, pk, DigestAlgorithms.SHA256, provider.getName(), PdfSigner.CryptoStandard.CMS, initialName, petId,documentType);
        File sourceTempFile = new File(temp_outPutFile);
        sourceTempFile.delete();
        File destTempFile = new File(dest);
        destTempFile.delete();
        LOGGER.info("------------Exited SignDocument------------");
        return new BaseResponseDto(Status.SUCCESS, fileUrl);
    }

    @GetMapping("/getByPetId/{petId}")
    public BaseResponseDto getWaiverOfLiabilityByPet(@PathVariable("petId") int petId) {
        LOGGER.info("----------Entered get waiver of liability by petId-------- ");
        String fileUrl;
        try {
            fileUrl = iWaiverOfLiabilityService.getWaiverOfLiabilityInformationByPetId(petId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@@Exited get waiver of liability by petId@@@@@@@@@");
        return new BaseResponseDto(Status.SUCCESS, fileUrl);
    }

    @GetMapping("/getByAppointmentId/{appointmentId}")
    public BaseResponseDto getWaiverOfLiabilityByAppointment(@PathVariable("appointmentId") int appointmentId) {
        String fileUrl;
        try {
            fileUrl = iWaiverOfLiabilityService.getWaiverOfLiabilityInformationByAppointmentId(appointmentId);
        } catch (Exception e) {
            LOGGER.error("Exception occured while processing request:::" + e);
            throw e;
        }
        LOGGER.info("@@@@@Exited get waiver of liability by Appointment Id@@@@@@@@@");
        return new BaseResponseDto(Status.SUCCESS, fileUrl);
    }

}
