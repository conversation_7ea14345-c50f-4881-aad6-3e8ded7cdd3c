package com.sayone.etailbookit.controller;

import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/timezone")
public class TimezoneController {

    @GetMapping("/zonelist")
    public List<String> setTimezoneListnew(@RequestParam("search") String search) {
        List<String> timezones = new ArrayList<String>();
        Set<String> zoneIds = DateTimeZone.getAvailableIDs();
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("ZZ");
        String zonewithOffset;
        for (String zoneId : zoneIds) {
            String offset = dateTimeFormatter.withZone(DateTimeZone.forID(zoneId)).print(0);
            zonewithOffset = zoneId + "   (" + offset + ") ";
            timezones.add(zonewithOffset);
        }
        return search == null ? timezones : timezones.stream().filter(x -> x.contains(search)).collect(Collectors.toList());
    }

}
