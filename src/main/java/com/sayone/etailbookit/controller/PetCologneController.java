package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.dto.PetCologneDto;
import com.sayone.etailbookit.dto.PetShampooDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IPetCologneService;
import com.sayone.etailbookit.util.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/pet-cologne")
public class PetCologneController {

    @Autowired
    IPetCologneService iPetCologneService;

    private static Logger LOGGER = LoggerFactory.getLogger(PetCologneController.class);

    @PostMapping("/new")
    public BaseResponseDto addPetCologne(@RequestBody PetCologneDto petCologneDto) throws EtailBookItException {
        LOGGER.info("@@@@Entered addPetCologne");
        try {
            petCologneDto = iPetCologneService.addPetCologne(petCologneDto);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited addPetCologne");
        return new BaseResponseDto(Status.SUCCESS,petCologneDto);
    }

    @GetMapping
    public BaseResponseDto getAllPetColognes(@RequestParam(defaultValue = "0") Integer pageNo,
                                             @RequestParam(defaultValue = "10") Integer pageSize,
                                             @RequestParam(defaultValue = "name") String sortBy,
                                             @RequestParam(required = false, defaultValue = "") String search) {
        LOGGER.info("@@@@Entered getAllPetColognes");
        ConfigurationDto<PetCologneDto> petCologneDtos = iPetCologneService.getPetColognes(pageNo, pageSize, sortBy, search);
        LOGGER.info("@@@@Exited getAllPetColognes");
        return new BaseResponseDto(Status.SUCCESS, petCologneDtos);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getPetCologneById(@PathVariable("id") int petCologneId) throws EtailBookItException {
        LOGGER.info("@@@@Entered getPetCologneById :::: " + petCologneId);
        PetCologneDto serviceTypeDto = null;
        try {
            serviceTypeDto = iPetCologneService.getPetCologneById(petCologneId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited getPetCologneById");
        return new BaseResponseDto(Status.SUCCESS,serviceTypeDto);
    }

    @DeleteMapping("/{id}")
    public BaseResponseDto deletePetCologneById(@PathVariable("id") int petCologneId) throws EtailBookItException {
        LOGGER.info("@@@@Entered deletePetCologneById :::: " + petCologneId);
        try {
            iPetCologneService.deletePetCologne(petCologneId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited deletePetCologneById");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PutMapping("/update/{id}")
    public BaseResponseDto updatePetCologne(@RequestBody PetCologneDto petCologneDto, @PathVariable("id") int petCologneId) throws EtailBookItException {
        LOGGER.info("@@@@Entered updatePetCologne");
        try {
            petCologneDto = iPetCologneService.updatePetCologne(petCologneDto, petCologneId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited updatePetCologne");
        return new BaseResponseDto(Status.SUCCESS,petCologneDto);
    }

    @PutMapping("/update/bulk")
    public BaseResponseDto bulkUpdateServiceType(@RequestBody ConfigurationDto<PetCologneDto> petCologneDtos) throws EtailBookItException {
        LOGGER.info("@@@@Entered bulkUpdateServiceType");
        try {
            iPetCologneService.bulkUpdateCologne(petCologneDtos);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("@@@@Exited bulkUpdateServiceType");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/getAllByActive")
    public BaseResponseDto getAllByActive(@RequestParam(defaultValue = "0") Integer pageNo,
                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                          @RequestParam(defaultValue = "name") String sortBy,
                                          @RequestParam (required = false,defaultValue = "")String search) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllByActive --------");
        Page<PetCologneDto> petCologneDtos ;
        BaseResponseDto response ;
        try {
            petCologneDtos = iPetCologneService.getActivePetCologne(pageNo,pageSize,sortBy,search);
            if(petCologneDtos.getTotalElements() != 0)
                response = new BaseResponseDto(Status.SUCCESS,petCologneDtos);
            else
                response = new BaseResponseDto(Status.FAILED,"No active Cologne");
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllByActive --------");
        return response;

    }

    @GetMapping("/getcologneByPagination")
    public BaseResponseDto getShampooByPagination(@RequestParam(defaultValue = "0") Integer pageNo,
                                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                                  @RequestParam(defaultValue = "name") String sortBy,
                                                  @RequestParam Optional<String> search) throws EtailBookItException{
        LOGGER.info("@@@@@@@@@@@@@@@@ Entered getShampoo By Pagination");
        Page<PetCologneDto> petCologneDto;
        try {
            petCologneDto=iPetCologneService.getcologneByPagination(pageNo, pageSize, sortBy,search);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing request:::"+e);
            throw e;
        }
        LOGGER.info("@@@@@@@@@@@@ Exited get Shampoo by Pagination");
        return new BaseResponseDto<>(Status.SUCCESS,petCologneDto);
    }

}