package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.component.ScheduleAppointment;
import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.service.AppointmentService;
import com.sayone.etailbookit.service.impl.AppointmentServiceImpl;
import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.ResponseUtils;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Status;
import io.swagger.models.auth.In;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.zone.ZoneRules;
import java.util.*;

@RestController
@RequestMapping("/api/v1/appointment")
public class AppointmentController {

    private static Logger LOGGER = LoggerFactory.getLogger(AppointmentController.class);

    @Autowired
    AppointmentService appointmentService;

    @Autowired
    AppointmentServiceImpl appointmentServiceImpl;

    @Autowired
    ScheduleAppointment scheduleAppointment;


    @PostMapping("/create")
    public BaseResponseDto createAppointment(@ModelAttribute AppointmentDto appointmentDto,@RequestParam(value = "tz" ,required = false) String timeZone) throws EtailBookItException {

        LOGGER.info(" --------Entered createAppointment --------");
        PaymentDto response;
        try {
            response = appointmentService.createAppointment(appointmentDto,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited createAppointment --------");
        return new BaseResponseDto(Status.SUCCESS,response);
    }

    @PostMapping("/insertGroomBarData")
    public BaseResponseDto insertGroomBarData(@RequestBody GroomBarAppointmentDto groomBarAppointmentDto)throws EtailBookItException{
        LOGGER.info("---------------Entered  GroomBarData Insertion-------------------");
        PaymentDto response;
        try {
            response=appointmentService.insertGroomBarData(groomBarAppointmentDto);
        }catch (EtailBookItException e){
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-----------------Exited GroomBarData Insertion-------------------");

        return new BaseResponseDto(Status.SUCCESS,response);
    }

    @GetMapping("/{id}")
    public BaseResponseDto getAppointmentById(@PathVariable("id") int id) throws EtailBookItException {

        LOGGER.info(" --------Entered getAppointmentById --------");
        AppointmentDetailsProjection response;
        try {
            response = appointmentService.getAppointmentById(id);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAppointmentById --------");
        return new BaseResponseDto(Status.SUCCESS,ResponseUtils.convertObjectToMap(response));

    }
    @GetMapping(value = "/printAppointments")
    public BaseResponseDto printAppointments(@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime date,@RequestParam("tz")String timeZone) throws EtailBookItException {
        LOGGER.info("-------Entered print functionality");
        List<AppointmentDetailsProjection> appointmentDetails = new ArrayList<>();
        try {
            appointmentDetails= appointmentService.getAllAppointmentsByDate(date,timeZone);

        } catch (EtailBookItException e) {
            LOGGER.info("Exception occured while processing the request:::" + e);
            throw e;
        }
        LOGGER.info("-------Exited print Functionality----");
        if(!appointmentDetails.isEmpty())
            return new BaseResponseDto(Status.SUCCESS, appointmentDetails);
        else
            return new BaseResponseDto(Status.SUCCESS, "No Appointments on this date");
    }

    @GetMapping
    public BaseResponseDto getAllAppointment(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(value = "customer", required = false) Integer customer,
            @RequestParam(value = "petType", required = false) Integer petType,
            @RequestParam(value = "pet", required = false) Integer pet,
            @RequestParam(value = "attendant", required = false) Integer attendant,
            @RequestParam(value = "venue", required = false) Integer venue,
            @RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    ) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllAppointment --------");
        BaseResponseDto response;
        try {
            response = appointmentService.getAllAppointment(
                pageNo, pageSize, sortBy, customer, petType, pet, attendant, venue, startDate, endDate,timeZone
            );

        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllAppointment --------");
        return response;
    }

    @GetMapping("/getAppointmentsOfMonthView")
    public BaseResponseDto getAppointmentsOfMonthView(
            @RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    ) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllAppointments in month view --------");
        BaseResponseDto response;
        try {
            response = appointmentService.getAppointmentsOfMonthView(
                    startDate, endDate,timeZone
            );

        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllAppointment in month view--------");
        return response;
    }

    @GetMapping("/appointments")
    public BaseResponseDto getAppointments(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(value = "customer", required = false) Integer customer,
            @RequestParam(value = "pet", required = false) Integer pet,
            @RequestParam(value = "attendant", required = false) Integer attendant,
            @RequestParam(value = "venue", required = false) Integer venue,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "petType", required = false) Integer petType,
            @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "service", required = false) Integer service,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    ) throws EtailBookItException {

        LOGGER.info(" --------Entered getAllAppointment --------");
        BaseResponseDto response;
        try {
            response = appointmentService.getAppointments(pageNo, pageSize, sortBy, customer, pet, attendant, venue, status, petType, search,service, startDate, endDate,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAllAppointment --------");
        return response;
       // return new BaseResponseDto(Status.SUCCESS,ResponseUtils.convertObjectToMap(response));
    }


    @PutMapping("/update")
    public BaseResponseDto updateAppointment(@RequestParam("id") Integer id,
                                             @RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime date,
                                             @RequestParam("startTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startTime, @RequestParam("endTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endTime,
                                             @RequestParam("tz") String timeZone,
                                             @RequestParam(value = "createdCustomerId", required = false)Integer createdCustomerId,
                                             @RequestParam(value = "createdCustomerName",required = false) String createdCustomerName,
                                             @RequestParam(value = "updatedCustomerId",required = false) Integer updatedCustomerId,
                                             @RequestParam(value = "updatedCustomerName",required = false) String updatedCustomerName) throws EtailBookItException {

        LOGGER.info(" --------Entered updateAppointment --------"+id);
        Boolean response;
        try {
            response = appointmentService.updateAppointment(id,date,startTime,endTime,timeZone,createdCustomerId,createdCustomerName,updatedCustomerId,updatedCustomerName);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited updateAppointment --------");
        if(response)
            return new BaseResponseDto(Status.SUCCESS);
        else
            return new BaseResponseDto(Status.FAILED);

    }

    @PutMapping("/reopened")
    public BaseResponseDto reopenedAppointment(@RequestParam("id") Integer id, @ModelAttribute AppointmentDto appointmentDto,@RequestParam("tz")String timeZone) throws EtailBookItException {

        LOGGER.info(" --------Entered reopenedAppointment --------"+id);
        PaymentDto response;
        try {
            response = appointmentService.reopenedAppointment(id, appointmentDto,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited reopenedAppointment --------");
        return new BaseResponseDto(Status.SUCCESS,response);

    }

    @GetMapping("onDate")
    public BaseResponseDto getAppointmentByDate(@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)  OffsetDateTime date,
                                                   @RequestParam(defaultValue = "0") Integer pageNo,
                                                   @RequestParam(defaultValue = "10") Integer pageSize,
                                                   @RequestParam(defaultValue = "id") String sortBy,
                                                   @RequestParam(defaultValue = "false") Boolean withCancel,
                                                   @RequestParam("search") Optional<String> search,@RequestParam(value = "tz",required = false) String timeZone) throws EtailBookItException {

        LOGGER.info(" --------Entered getAppointmentByDate --------");
        Page<AppointmentListingProjection> appointmentList;
        try {
            appointmentList = appointmentService.getAppointmentsByDate1(date, withCancel, pageNo, pageSize, sortBy,search,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited getAppointmentByDate --------");
        if(appointmentList.hasContent())
            return new BaseResponseDto(Status.SUCCESS, appointmentList);
        else
            return new BaseResponseDto(Status.SUCCESS, "No Appointments found");
    }

    @GetMapping("available")
    public BaseResponseDto getAvailableSlots(@RequestParam("service") Integer serviceId,
                                             @RequestParam("venue") Integer venueId,
                                            // @RequestParam(value = "attendant",required = false) Integer attendantId,
                                             @RequestParam(value = "duration",required = false) Integer serviceDuration,
                                             @RequestParam(value = "date")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime appointmentDate,@RequestParam(value = "tz",required = false) String timeZone) throws EtailBookItException {

        OffsetDateTime utcAppointmentDate=appointmentDate;
       // Integer daylightSavingsMinutes=appointmentServiceImpl.checkDayLightSavings(timeZone,utcAppointmentDate);
        Integer offsetTimeDifference=appointmentServiceImpl.calculateOffset(timeZone,utcAppointmentDate);
       // Integer daylightAddedOffsetMinutes=daylightSavingsMinutes+offsetTimeDifference;
        appointmentDate=appointmentDate.plusMinutes(offsetTimeDifference);

        BookingDto bookingInfo = BookingDto.builder()
                .serviceId(serviceId)
                .venueId(venueId)
               // .attendantId(attendantId)
                .serviceDuration(serviceDuration)
                .appointmentDate(appointmentDate)
                .utcAppointmentDate(utcAppointmentDate)
                //.appointmentDate(OffsetDateTime.parse(appointmentDate + OffsetContext.getOffset()))
                .build();

        Map<String,List<AppointmentSlotsDto>> availableSlots = appointmentService.getAvailableAppointmentSlots1(bookingInfo, false,timeZone,offsetTimeDifference);
        return new BaseResponseDto(Status.SUCCESS,availableSlots);
    }

    @GetMapping("available-slots-by-date-range")
    public BaseResponseDto getAvailableSlots(@RequestParam("service") Integer serviceId,
                                             @RequestParam("venue") Integer venueId,
                                            // @RequestParam(value = "attendant",required = false) Integer attendantId,
                                             @RequestParam(value = "duration",required = false) Integer serviceDuration,
                                             @RequestParam(value = "startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
                                             @RequestParam(value = "endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,@RequestParam(value = "tz",required = false)String timeZone) throws EtailBookItException {

        //List<AvailableSlotsCounterDto> availableSlots = appointmentService.getAvailableAppointmentSlotsCounterByDateRange(serviceId, venueId, attendantId, serviceDuration, startDate, endDate,timeZone);
        List<AvailableSlotsCounterDto> availableSlots = appointmentService.getAvailableAppointmentSlotsCounterByDateRange(serviceId, venueId, serviceDuration, startDate, endDate,timeZone);
        return new BaseResponseDto(Status.SUCCESS,availableSlots);
    }


    @PostMapping("/updateOrderDetails")
    public BaseResponseDto updateOrderDetails(@RequestBody AppointmentDto order) throws EtailBookItException {

        LOGGER.info(" --------Entered updateOrderDetails --------");
        Boolean response;
        try {
            response = appointmentService.updateOrderDetails(order);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited updateOrderDetails --------");
        if(response)
            return new BaseResponseDto(Status.SUCCESS);
        else
            return new BaseResponseDto(Status.FAILED);

    }

    @PostMapping("/bulk-update-status")
    public BaseResponseDto bulkUpdateOrderDetails(@RequestBody List<PaymentUpdateDto> orders) throws EtailBookItException {

        LOGGER.info(" --------Entered bulkUpdateOrderDetails --------");
        Boolean response;
        try {
            appointmentService.bulkUpdateOrderDetails(orders);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited bulkUpdateOrderDetails --------");
        return new BaseResponseDto(Status.SUCCESS);
    }

    @PostMapping("/calculateServiceAmount")
    public BaseResponseDto calculateServiceAmount(@ModelAttribute AppointmentDto appointmentDto,@RequestParam("tz") String timeZone) throws EtailBookItException {

        LOGGER.info(" --------Entered calculateServiceAmount --------");
        PaymentDto PaymentDto;
        try {
            PaymentDto = appointmentService.calculateServiceAmount(appointmentDto,timeZone);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited calculateServiceAmount --------");

        return new BaseResponseDto(Status.SUCCESS, PaymentDto);

    }

    //API used by the new sale screen to add the items to cart, the price given in the response will be item total price used in item check out .
    @GetMapping("/get-details/{id}")
    public BaseResponseDto getAmount(@PathVariable("id") int id) {

        LOGGER.info(" --------Entered getAmount --------");
        PaymentDetailsDto PaymentDto = appointmentService.calculateServiceAmount(id);
        LOGGER.info("-------- Exited getAmount --------");

        return new BaseResponseDto(Status.SUCCESS, PaymentDto);

    }

    @GetMapping("/get-for-customer/{id}")
    public BaseResponseDto getUnpaidAppointmentsForCustomer(@PathVariable("id") int id) {

        LOGGER.info(" --------Entered getUnpaidAppointmentsForCustomer --------");
        List<AppointmentPaymentProjection> appointments = appointmentService.getAppointmentsForCustomer(id);
        LOGGER.info("-------- Exited getUnpaidAppointmentsForCustomer --------");

        return new BaseResponseDto(Status.SUCCESS, appointments);
    }

    @PostMapping("/makeReadyToPay/{id}")
    public BaseResponseDto makeReadytoPay(@PathVariable("id") int id) {

        LOGGER.info(" --------Entered makeReadytoPay --------");
        appointmentService.makeReadyToPay(id);
        LOGGER.info("-------- Exited makeReadytoPay --------");

        return new BaseResponseDto(Status.SUCCESS);
    }

    @GetMapping("/get-all-uploaded-appointment-documents-by-customer-id/{customerId}")
    public BaseResponseDto getAllUploadedAppointmentDocumentsByCustomerId(@RequestParam(defaultValue = "0") Integer pageNo,
                                                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                                                          @RequestParam(defaultValue = "id") String sortBy,
                                                                          @PathVariable("customerId") Integer customerId) throws EtailBookItException  {
        LOGGER.info("------------Entered List Of Required Service Documents -------------");
        List<AppointmentUploadedDocumentsProjection> response;
        try {
            response = appointmentService.getAllUploadedAppointmentDocumentsByCustomerId(pageNo, pageSize, sortBy, customerId);
        } catch (EtailBookItException e) {
            LOGGER.error("Exception occurred while processing request :::" + e);
            throw e;
        }
        LOGGER.info("----------------Existed List Of Required Service Documents ----------");
        return new BaseResponseDto(Status.SUCCESS,response);
    }
    @GetMapping("/changeAppointmentTimeWithOffset")
    public BaseResponseDto changeAppointmentTimeWithOffset(){

        appointmentService.changeAppointmentTimeWithOffset();
        return new BaseResponseDto(Status.SUCCESS);

    }
    @GetMapping("/addValuesToEndDate")
    public BaseResponseDto addValuesToEndDate(){
        appointmentService.addValuesToEndDate();
        return new BaseResponseDto<>(Status.SUCCESS);
    }

    @PostMapping("/offsetRetailerMapping")
    public BaseResponseDto offsetRetailerMapping(@RequestBody List<OffsetRetailerMappingDto> offsetRetailerMappingDtos){
        appointmentService.offsetRetailerMapping(offsetRetailerMappingDtos);
        return new BaseResponseDto<>(Status.SUCCESS);
    }

    @GetMapping("populateDataToAppointmentDateAndTime")
    public BaseResponseDto populateDataToAppointmentDateAndTime()
    {
        appointmentService.populateDataToAppointmentDateAndTime();
        return new BaseResponseDto<>();
    }

    @GetMapping("/exportAppointment")
    public ResponseEntity<byte[]> exportAppointment(
            @RequestParam(defaultValue = "0") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(value = "customer", required = false) Integer customer,
            @RequestParam(value = "pet", required = false) Integer pet,
            @RequestParam(value = "attendant", required = false) Integer attendant,
            @RequestParam(value = "venue", required = false) Integer venue,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "petType", required = false) Integer petType,
            @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "service", required = false) Integer service,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "fetchAll", required = false) boolean fetchAll,
            @RequestParam(value = "tz",required = false) String timeZone) throws EtailBookItException, IOException{

        LOGGER.info(" --------Entered exportAppointment --------");
        byte[] response;
        HttpHeaders headers = new HttpHeaders();
        try {
            response = appointmentService.getExportAppointments(pageNo, pageSize, sortBy, customer, pet, attendant, venue, status, petType, search,service, startDate, endDate, fetchAll,timeZone).toByteArray();
            headers.add("Content-Disposition", "attachment; filename=appointments.xlsx");
        } catch (EtailBookItException e ) {
            LOGGER.error("Exception occured while processing request ::: " + e);
            throw e;
        }
        LOGGER.info("-------- Exited exportAppointment --------");
        return ResponseEntity.status(HttpStatus.OK).headers(headers).body(response);
}
    @PostMapping("/populateSource")
    public BaseResponseDto populateSource (){
        appointmentService.populateSource();
        return new BaseResponseDto<>();
    }
  @GetMapping("/sendEmailWithCorrectedTime")
    public BaseResponseDto sendEmailWithCorrectedTime() throws EtailBookItException{
        LOGGER.info("*****************Entered fetching appointments with corrected time after DST****************");
        try {
            appointmentService.fetchAppointmentsWithCorrectedTimeAfterDST();
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        LOGGER.info("*****************Exited fetching appointments with corrected time after DST*************************");
        return new BaseResponseDto<>();
    }
  
    @GetMapping("/fetchAppointmentsAfterDST")
    public BaseResponseDto fetchAppointmentsAfterDST() throws EtailBookItException {
        BaseResponseDto responseDto;
        LOGGER.info("*****************Entered fetching appointments after DST****************");
        try {
            responseDto= appointmentService.fetchAppointmentsAfterDST();
        } catch (EtailBookItException e) {
            LOGGER.error("Exception thrown while processing the request"+e);
            throw e;
        }
        LOGGER.info("*****************Exited fetching appointments after DST*************************");
        return new BaseResponseDto<>();
    }

    @PostMapping("/fetchAppointmentWithId")
    public BaseResponseDto fetchAppointmentWithId(@RequestBody List<Integer> appointmentIds)throws EtailBookItException{

        LOGGER.info("*****************Entered fetching appointments after DST****************");

             appointmentService.fetchAppointmentWithId(appointmentIds);

        LOGGER.info("*****************Exited fetching appointments after DST*************************");
        return new BaseResponseDto<>();
    }

    //code just added to test the email trigger of export appointments of current day;
    /*@Autowired
    ScheduleAppointment scheduleAppointment;
    @GetMapping("/exportCurrentAppointments")
    public  BaseResponseDto exportCurrentAppointments() throws Exception {
        scheduleAppointment.processAppointmentsForToday(RetailerContext.getRetailer());
        return new BaseResponseDto<>();
    }*/

    @GetMapping("/manualResendAppointmentExport")
    public  BaseResponseDto manualResendAppointmentExport(@RequestParam(value = "selectedDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime selectedDate,
                                                          @RequestParam(value = "tz")String tz) throws Exception {
        BaseResponseDto response;
        response=appointmentService.manualResendOfAppointmentExport(RetailerContext.getRetailer(),selectedDate,tz);
        return response;
    }
    @GetMapping("/addToWaitList")
    public BaseResponseDto addAppointmentToWaitList(@RequestParam(value = "currentDate")@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime currentDate,
                                                    @RequestParam(value = "tz")String timeZone,
                                                    @RequestParam(defaultValue = "0") Integer pageNo,
                                                    @RequestParam(defaultValue = "10") Integer pageSize,
                                                    @RequestParam(defaultValue = "id") String sortBy) {
        BaseResponseDto response;
        response = appointmentService.addAppointmentToWaitList(currentDate, timeZone, RetailerContext.getRetailer(), pageNo, pageSize, sortBy);
        return new BaseResponseDto<>(Status.SUCCESS, response);
    }
    }
