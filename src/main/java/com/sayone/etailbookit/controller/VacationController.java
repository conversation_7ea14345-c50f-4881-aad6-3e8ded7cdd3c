package com.sayone.etailbookit.controller;

import com.sayone.etailbookit.dto.BaseResponseDto;
import com.sayone.etailbookit.dto.VacationDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.service.IVacationService;
import com.sayone.etailbookit.util.Status;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.OffsetDateTime;

@RestController
@RequestMapping("/api/v1/vacation")
public class VacationController {

    private static Logger LOGGER = LoggerFactory.getLogger(VacationController.class);

    @Autowired
    IVacationService iVacationService;
    @PostMapping("/create")
    public BaseResponseDto createVacation(@RequestBody VacationDto vacationDto)throws EtailBookItException{
        LOGGER.info("----------------Entered create vacation----------------");

        iVacationService.createVacation(vacationDto);

        LOGGER.info("---------------Exited create vacation------------------");
        return new BaseResponseDto<>(Status.SUCCESS);
    }

    @GetMapping
    public BaseResponseDto getAllVacationList(
            @RequestParam(value = "startDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @RequestParam(value = "endDate",required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @RequestParam(value = "tz",required = false) String timeZone
    )throws EtailBookItException{
        LOGGER.info("--------------------Entered get all vacation list-----------");

        BaseResponseDto responseDto=new BaseResponseDto<>();

        responseDto=iVacationService.getAllVacationList(startDate,endDate,timeZone);

        LOGGER.info("---------------------Exited get all vacation list");
        return responseDto;
    }
    @PutMapping("/update")
    public BaseResponseDto updateVacation(
            @RequestParam(value = "vacationId",required = false) Integer vacationId,
            @RequestBody VacationDto vacationDto)throws EtailBookItException{
        LOGGER.info("-------------Entered vacation update------------------");

        BaseResponseDto responseDto=new BaseResponseDto();

        responseDto=iVacationService.updateVacation(vacationId,vacationDto);

        LOGGER.info("-------------Exited vacation update------------------");
        return responseDto;
    }

    @DeleteMapping("/delete")
    public BaseResponseDto deleteVacation(@RequestParam(value = "vacationId")Integer vacationId)throws EtailBookItException{
        LOGGER.info("------------------Entered delete vacation----------------");

        BaseResponseDto responseDto=iVacationService.deleteVacation(vacationId);

        LOGGER.info("-------------------Exited deleteVacation-----------------");
        return responseDto;
    }
}