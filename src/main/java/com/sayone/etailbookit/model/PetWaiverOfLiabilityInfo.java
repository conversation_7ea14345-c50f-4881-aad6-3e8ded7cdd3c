package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "pet_waiver_of_liability_information")
public class PetWaiverOfLiabilityInfo {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    Integer id;

    @OneToOne(fetch = FetchType.LAZY,cascade = CascadeType.PERSIST)
    @JoinColumn(name = "pet_id", nullable = false)
    private Pet pet;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "waiver_of_liability_id", nullable = false)
    private WaiverOfLiability waiverOfLiability;

    @Column(name = "signed_file")
    private String signedFile;

    @Column(name = "retailer")
    private String retailer;
}
