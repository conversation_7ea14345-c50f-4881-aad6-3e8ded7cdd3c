package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(
    name = "service",
    indexes = {
        @Index(columnList = "retailer", name = "service_retailer")
    }
)
//@SQLDelete(sql = "UPDATE service SET deleted = true WHERE id=?")
//@Where(clause = "deleted=false")
public class Service implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer serviceId;

    @Column(name = "name")
    private String name;

    @Column(name = "internal_item_number",unique = true)
    private Long internalItemNumber;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "service_type")
    private ServiceType serviceType;

    @ManyToMany(mappedBy = "services",cascade = CascadeType.PERSIST)
    private Set<TimeSlots> timeSlots = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "pet_type")
    private PetType petType;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<GeneralPetSize> generalPetSize = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<Venue> venues = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<Attendant> attendants = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY,cascade =CascadeType.PERSIST)
    private  Set<Temperament> temperaments=new HashSet<>();

    @Column(name = "petparent_can_select_attendant")
    private Boolean petParentCanSelectAttendant = Boolean.FALSE;

    @Column(name = "schedule_type",nullable = false)
    private String scheduleType;

    @Column(name = "fixed_schedule_value")
    private Integer fixedScheduleValue;

    @Column(name = "fixed_schedule_unit")
    private String fixedScheduleUnit;

    //todo:Change field to number
    @Column(name = "variable_schedule_min_value")
    private String variableScheduleMinValue;

    //todo:Change field to number
    @Column(name = "variable_schedule_max_value")
    private String variableScheduleMaxValue;

    @Column(name = "max_overnights")
    private Integer maxOvernights;

    @Column(name = "petparent_can_select_datetime")
    private Boolean petparentCanSelectDatetime = Boolean.FALSE;

    @Column(name = "petparent_can_select_recurring_options")
    private Boolean petparentCanSelectRecurringOptions = Boolean.FALSE;

    @Column(name = "pre_buffer_mins")
    private Integer pre_buffer_mins;

    @Column(name = "post_buffer_mins")
    private Integer postBufferMins;

    @Column(name = "amount_per_event")
    private BigDecimal amountPerUnit;

    @Column(name = "amount_currency")
    private String amount_currency;

    @Column(name = "service_unit")
    private String serviceUnit;

    @Column(name = "is_tips_allowed")
    private Boolean isTipsAllowed = Boolean.FALSE;

    @Column(name = "minimum_fee")
    private BigDecimal minimumFee;

    @Column(name = "enable_late_onboarding_config")
    private Boolean enableLateOnboardingConfig = Boolean.FALSE;

    @Column(name = "late_onboarding_days")
    private Integer lateOnboardingDays;

    @Column(name = "late_onboarding_charge_interval")
    private String lateOnboardingChargeInterval;

    @Column(name = "late_onboarding_hourly_charge")
    private BigDecimal lateOnboardingHourlyCharge;

    @Column(name = "is_service_late_discount_allowed")
    private Boolean isServiceLateDiscountAllowed = Boolean.FALSE;

    @Column(name = "service_late_discount_days")
    private Integer serviceLateDiscountDays;

    @Column(name = "service_late_discount_percent")
    private BigDecimal serviceLateDiscountPercent;

    @Column(name = "is_sibling_discount_allowed")
    private Boolean isSiblingDiscountAllowed = Boolean.FALSE;


    @Column(name = "sibling_discount_type")
    private String siblingDiscountType;

    @Column(name = "sibling_discount_value")
    private BigDecimal siblingDiscountValue;

    @Column(name = "is_taxable")
    private Boolean isTaxable = Boolean.FALSE;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<AddonService> addonServices = new HashSet<>();

    @Column(name = "payment_at_time_of_booking")
    private Boolean paymentAtTimeOfBooking = Boolean.FALSE;

    @Column(name = "payment_at_beginning_of_service")
    private Boolean paymentAtBeginningOfService = Boolean.FALSE;

    @Column(name = "payment_after_service_completed")
    private Boolean paymentAfterServiceCompleted = Boolean.FALSE;

    @Column(name = "need_deposit_at_booking")
    private Boolean needDepositAtBooking = Boolean.FALSE;

    @Column(name = "deposit_amount_type")
    private String depositAmountType;

    @Column(name = "deposit_amount_value")
    private BigDecimal depositAmountValue;

    @Column(name = "charge_cancelation_fee")
    private Boolean chargeCancelationFee = Boolean.FALSE;

    @Column(name = "cancelation_amount_type")
    private String cancelationAmountType;

    @Column(name = "cancelation_amount_value")
    private BigDecimal cancelationAmountValue;

    @Column(name = "cancelation_buffer_value")
    private Integer cancelationBufferValue;

    @Column(name = "cancelation_buffer_unit")
    private String cancelationBufferUnit;

    @Column(name = "require_credit_card_on_file")
    private Boolean requireCreditCardOnFile = Boolean.FALSE;

    @Column(name = "require_participant_pet_name")
    private Boolean requireParticipantPetName = Boolean.FALSE;

    @Column(name = "require_participant_pet_type")
    private Boolean requireParticipantPetType = Boolean.FALSE;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<VaccinationRecords> availableParticipantVaccinations = new HashSet<>();

    @Column(name = "available_participant_weights")
    private Boolean availableParticipantWeights = Boolean.FALSE;

    @Column(name = "available_participant_temperaments")
    private Boolean availableParticipantTemperaments = Boolean.FALSE;

    @Column(name = "available_participant_allergies")
    private Boolean availableParticipantAllergies = Boolean.FALSE;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<DocumentOption> availableParticipantDocuments = new HashSet<>();

    @Column(name = "require_pet_personality")
    private Boolean requirePetPersonality = Boolean.FALSE;

    @Column(name = "require_pet_behaviour")
    private Boolean requirePetBehaviour = Boolean.FALSE;

    @Column(name = "require_biting_info")
    private Boolean requireBitingInfo = Boolean.FALSE;

    @Column(name="require_pet_threat_reactions")
    private  Boolean requirePetThreatReactions=Boolean.FALSE;

    @Column(name = "require_hair_length_info")
    private Boolean requireHairLengthInfo = Boolean.FALSE;

    @Column(name = "require_vet_info")
    private Boolean requireVetInfo = Boolean.FALSE;

    @Column(name = "require_notes")
    private Boolean requireNotes = Boolean.FALSE;

    @Column(name = "offer_route_tracking")
    private Boolean offerRouteTracking = Boolean.FALSE;

    @Column(name = "require_notes_post_service")
    private Boolean requireNotesPostService = Boolean.FALSE;

    @Column(name = "require_combs_and_blades_used_post_service")
    private Boolean requireCombsAndBladesUsedPostService = Boolean.FALSE;

    @Column(name = "require_shampoo_used_post_service")
    private Boolean requireShampooUsedPostService = Boolean.FALSE;

    @Column(name = "require_cologne_used_post_service")
    private Boolean requireCologneUsedPostService = Boolean.FALSE;

    @Column(name = "require_food_fed_post_service")
    private Boolean requireFoodFedPostService = Boolean.FALSE;

    @Column(name = "send_picture_to_pet_parent_post_service")
    private Boolean sendPictureToPetParentPostService = Boolean.FALSE;

    @Column(name = "send_report_card")
    private Boolean sendReportCard = Boolean.FALSE;

    @Column(name = "include_report_card_peed_times")
    private Boolean includeReportCardPeedTimes;

    @Column(name = "include_report_card_pooped_times")
    private Boolean includeReportCardPoopedTimes = Boolean.FALSE;

    @Column(name = "include_report_card_poop_description")
    private Boolean includeReportCardPoopDescription = Boolean.FALSE;

    @Column(name = "include_report_card_times_fed")
    private Boolean includeReportCardTimesFed = Boolean.FALSE;

    @Column(name = "include_report_card_water_bowl_filled")
    private Boolean includeReportCardWaterBowlFilled = Boolean.FALSE;

    @Column(name = "include_report_card_general_notes")
    private Boolean includeReportCardGeneralNotes = Boolean.FALSE;

    @Column(name = "is_active")
    private Boolean isActive = Boolean.FALSE;

    @Column(name = "pre_booking_value")
    private BigDecimal preBookingValue;

    @Column(name = "pre_booking_unit")
    private String preBookingUnit;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "availability_interval")
    private String availabilityInterval;

    @Column(name = "availability_interval_unit")
    private String availabilityIntervalUnit;

    @OneToMany(mappedBy = "service", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<ServiceAvailability> availabilityDays = new HashSet<>();

    @OneToMany(mappedBy = "service", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<ServiceBreedsInformation> serviceBreedsInformations = new HashSet<>();

    @Column(name = "shampoos_offered")
    private Boolean shampoosOffered;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<PetShampoo> shampoos;

    @Column(name = "cologne_offered")
    private Boolean colognesOffered;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<PetCologne> colognes;

    @Column(name = "addon_offered")
    private Boolean addonsOffered;

    @Column(name = "require_feeding_info")
    private Boolean requireFeedingInfo;

    @Column(name = "require_Desired_Hair_Length")
    private Boolean requireDesiredHairLength;

    @Column(name = "color")
    private String color;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

    public Service() {
        // Initialization logic, if any
    }

}
