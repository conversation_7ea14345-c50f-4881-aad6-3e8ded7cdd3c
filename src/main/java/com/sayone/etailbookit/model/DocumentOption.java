package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Data
@Entity
@Table(name = "document_option")
//@SQLDelete(sql = "UPDATE document_option SET deleted = true WHERE id=?")
public class DocumentOption implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer documentOptionId;

    @ManyToMany(mappedBy = "documentOptions",fetch = FetchType.LAZY,cascade = CascadeType.PERSIST)
    private Set<PetType> petType = new HashSet<>();

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "requires_description")
    private Boolean requireDescription = Boolean.FALSE;

    @Column(name = "requires_upload")
    private Boolean requireUpload = Boolean.FALSE;

    @Column(name = "requires_upload_esign")
    private Boolean requireUploadEsign = Boolean.FALSE;

    @Column(name = "active")
    private Boolean active = Boolean.FALSE;

    @Column(name = "fileURL")
    private String fileURL;

    @Column(name = "is_signed")
    private Boolean signed = Boolean.FALSE;

    @Column(name = "signed_fileURL")
    private String signedFileURL;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "indexvalue")
    private Integer indexValue;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}