package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "ScheduleMessages")
public class Scheduler {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @Column(name = "status")
    private String status;

    @Column(name = "schedulertype")
    private String schedulerType;

    @Column(name = "schedulerId")
    private Integer schedulerId;

    @Column(name = "date")
    private LocalDate localDate;

}