package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "appointment_waiver_of_liability_information")
public class AppointmentWaiverOfLiabilityInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
   private Integer id;

    @OneToOne(fetch = FetchType.LAZY,cascade = CascadeType.ALL)
    @JoinColumn(name = "appointment_id", nullable = false)
    private Appointment appointment;

    @Column(name = "file_url")
    private String fileUrl;

    @Column(name = "terms_of_service_file_url")
    private String termsOfServiceFileUrl;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "waiver_of_liability_id", nullable = false)
    private WaiverOfLiability waiverOfLiability;

    @Column(name = "retailer")
    private String retailer;
}
