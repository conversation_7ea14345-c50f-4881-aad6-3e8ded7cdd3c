package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Entity
@Table(
    name = "vaccination_record",
    indexes = {
        @Index(columnList = "pet_type_id", name = "vaccination_record_pet_type_id")
    }
)
/*@SQLDelete(sql = "UPDATE vaccination_record SET deleted = true WHERE id=?")
@Where(clause = "deleted=false")*/
public class VaccinationRecords implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer vaccinationRecordId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pet_type_id")
    private PetType petType;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "requires_date_administrated")
    private Boolean requireDateAdministrated = Boolean.FALSE;

    @Column(name = "requires_date_expires")
    private Boolean requireDateExpires = Boolean.FALSE;

    @Column(name = "requires_vaccination_document")
    private Boolean requireVaccinationDocument = Boolean.FALSE;

    @Column(name = "requires_upload_esign")
    private Boolean requireUploadEsign = Boolean.FALSE;

    @Column(name = "active")
    private Boolean active = Boolean.FALSE;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "indexvalue")
    private Integer indexValue;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}