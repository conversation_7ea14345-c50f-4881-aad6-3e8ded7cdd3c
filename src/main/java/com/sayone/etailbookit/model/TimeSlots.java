package com.sayone.etailbookit.model;

import com.sayone.etailbookit.util.OffsetDateTimeConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "time_slots")
@Data
@Where(clause = "deleted = false")
public class TimeSlots {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @Column(name = "slot_name")
    private String slotName;

    @Column(name = "slot_start_time")
    @Convert(converter = OffsetDateTimeConverter.class)
    private OffsetDateTime slotStartTime;

    @Column(name = "slot_end_time")
    @Convert(converter = OffsetDateTimeConverter.class)
    private OffsetDateTime slotEndTime;

    @Column(name="color")
    private String color;

    @Column(name="slot_date")
    private LocalDate slotDate;

    @ManyToMany(fetch = FetchType.LAZY,cascade = CascadeType.MERGE)
    private Set<Service> services = new HashSet<>();

    @OneToOne(fetch =FetchType.LAZY)
    @JoinColumn(name = "venue_id")
    private Venue venue;

    @OneToOne(fetch =FetchType.LAZY)
    @JoinColumn(name = "attendant_id")
    private Attendant attendant;

    @Column(name = "availableDay")
    private String availableDay;

    @Column(name = "availability_interval")
    private Integer availabilityInterval;

    @Column(name = "availability_interval_unit")
    private String availabilityIntervalUnit;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "time_slot_cluster_id")
    @EqualsAndHashCode.Exclude @ToString.Exclude
    private TimeSlotCluster timeSlotCluster;

    @Column(name="retailer")
    private String retailer;

    @Column(name = "slot_booked")
    private boolean slotBooked =Boolean.FALSE;
}
