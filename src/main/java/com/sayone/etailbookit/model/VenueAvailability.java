package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.OffsetTime;

@Data
@Entity
@Table(name = "venue_availability")
public class VenueAvailability implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer id;

    @Column(name = "available_day")
    private String availableDay;

    @Column(name = "availability_open_time", columnDefinition = "TIME")
    private OffsetTime availabilityOpenTime;

    @Column(name = "availability_close_time", columnDefinition = "TIME")
    private OffsetTime availabilityCloseTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "venue_id", nullable = false)
    private Venue venue;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

}
