package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Type;


import javax.persistence.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(name = "attendant")
//@SQLDelete(sql = "UPDATE attendant SET deleted = true WHERE id=?")
//@Where(clause = "deleted = false")
public class Attendant implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer attendantId;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "email")
    private String email;

    @Column(name = "phone_no")
    private String phoneNo;

    @Column(name = "title")
    private String title;

    @Column(name = "preferred_communications", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] preferredCommunications;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<ServiceType> serviceTypes = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Venue venue;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<AddonService> addonServices = new HashSet<>();

    @Column(name = "revenue_share")
    private Boolean revenueShare;

    @Column(name = "amount_per_hour")
    private BigDecimal amountPerHour;

    @Column(name = "amount_currency")
    private String amountCurrency;

    @Column(name = "amount_per_event")
    private BigDecimal amountPerEvent;

    @Column(name = "revenue_percent")
    private Integer revenuePercent;

    @Column(name = "tip_eligible")
    private Boolean tipEligible;

    @Column(name = "capacity_limit")
    private Integer capacityLimit;

    @OneToMany(mappedBy = "attendant", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<PetSizeConstraint> petSizeConstraints = new HashSet<>();

    @OneToMany(mappedBy = "attendant", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<PetSizeLimit> petSizeLimits = new HashSet<>();

    @OneToMany(mappedBy = "attendant", fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    private Set<AttendantPetTypes> attendantPetTypes = new HashSet<>();

    @ManyToMany(mappedBy = "attendants")
    private Set<Service> services = new HashSet<>();

    @Column(name = "active")
    private Boolean active = Boolean.FALSE;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "availability_interval")
    private String availabilityInterval;

    @Column(name = "availability_interval_unit")
    private String availabilityIntervalUnit;

    @OneToMany(mappedBy = "attendant", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<AttendantAvailability> availabilityDays = new HashSet<>();

    @Column(name = "color")
    private String color;

    @Column(name = "google_calendar_sync_enabled")
    private Boolean googleCalendarSyncEnabled = Boolean.FALSE;

    @Column(name = "google_refresh_token", columnDefinition = "text")
    private String googleRefreshToken;

    @Column(name = "google_calendar_id")
    private String googleCalendarId;

    @Column(name = "google_access_token", columnDefinition = "text")
    private String googleAccessToken;

    @Column(name = "google_token_expiry")
    private LocalDateTime googleTokenExpiry;

    @Column(name = "calendar_sync_in_progress")
    private Boolean calendarSyncInProgress;

    @Column(name = "google_calendar_authorized")
    private Boolean googleCalendarAuthorized;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

    public Attendant() {
        // Initialization logic, if any
    }

}