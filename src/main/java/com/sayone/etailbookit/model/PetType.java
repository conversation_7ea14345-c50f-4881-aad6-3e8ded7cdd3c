package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;


import javax.persistence.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(
    name = "pet_type",
    indexes = {
        @Index(columnList = "retailer", name = "pet_type_retailer")
    }
)
/*@SQLDelete(sql = "UPDATE pet_type SET deleted = true WHERE id=?")
@Where(clause = "deleted=false")*/
public class  PetType implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer petTypeId;

    @Column(name = "name")
    private String name;

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<HairLength> hairLengths = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<HairTexture> hairTextures = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<Combs> combs = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<Blades> blades = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<Allergies> allergies = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<VetInformation> vetInformation = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<DesiredHairLength> desiredHairLengths = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<Breed> breeds = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<Temperament> temperaments = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<VaccinationRecords> vaccinationRecords = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<EmergencyContactInfo> emergencyContactInfo = new HashSet<>();

    @ManyToMany (fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<DocumentOption> documentOptions = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<WeightRange> weightRanges = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<GeneralPetSize> generalPetSizes = new HashSet<>();

    @OneToMany(mappedBy = "petType", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    private Set<PetTypeConfiguration> petTypeConfigurations = new HashSet<>();

    @OneToMany(mappedBy = "petType")
    private Set<AttendantPetTypes> attendants;

    @OneToMany(mappedBy = "petType")
    private Set<VenuePetTypes> venues;

    @OneToMany(mappedBy = "petType")
    private Set<Service> services;

    @OneToMany(mappedBy = "petType")
    private Set<Pet> pets;

    @Column(name = "fileUrl")
    private String fileUrl;

    @Column(name = "active")
    private Boolean active ;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}