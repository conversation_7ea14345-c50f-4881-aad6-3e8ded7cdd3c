package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version v1.0
 * @since 28 Sep 2021 18:26
 */

@Data
@Entity
@Table(name = "appointment_vaccination_records")
public class AppointmentVaccinationInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id")
    private Appointment appointment;

    @Column(name = "file")
    private String file;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "vaccination_record_id")
    private VaccinationRecords vaccinationRecords;

    @Column(name = "date_expires")
    private LocalDate dateExpires;

    @Column(name = "date_administrated")
    private LocalDate dateAdministrated;

    @Column(name = "retailer")
    private String retailer;
}
