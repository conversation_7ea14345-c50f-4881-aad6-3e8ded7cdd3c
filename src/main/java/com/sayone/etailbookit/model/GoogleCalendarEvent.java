package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "google_calendar_event")
public class GoogleCalendarEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer id;

    @Column(name = "appointment_id", nullable = false)
    private Integer appointmentId;

    @Column(name = "attendant_id", nullable = false)
    private Integer attendantId;

    @Column(name = "google_event_id", nullable = false, length = 1024)
    private String googleEventId;

    @Column(name = "calendar_id")
    private String calendarId;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at", updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    public GoogleCalendarEvent() {
        // Default constructor
    }
}

