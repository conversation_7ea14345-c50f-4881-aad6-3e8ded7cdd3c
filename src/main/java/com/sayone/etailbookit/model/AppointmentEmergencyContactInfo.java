package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @since 27 Sep 2021 17:41
 */

@Data
@Entity
@Table(name = "appointment_emergency_contact_info")
public class AppointmentEmergencyContactInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id", nullable = false)
    private Appointment appointment;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "emergency_contact_id", nullable = false)
    private EmergencyContactInfo emergencyContactInfo;

    @Column(name = "value", nullable = false)
    private String value;

    @Column(name = "retailer")
    private String retailer;
}
