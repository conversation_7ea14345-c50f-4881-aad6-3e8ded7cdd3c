package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @since 22 Sep 2021 17:33
 */

@Data
@Entity
@Table(name = "appointment_documents")
public class AppointmentDocuments implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id", nullable = false)
    private Appointment appointment;

    @Column(name = "description")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "document_option_id", nullable = false)
    private DocumentOption documentOption;

    @Column(name = "file")
    private String file;

    @Column(name = "retailer")
    private String retailer;
}
