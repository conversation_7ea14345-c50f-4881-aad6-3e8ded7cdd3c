package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Data
@Table(name = "customer",uniqueConstraints= @UniqueConstraint(columnNames = {"ecom_id", "retailer"}))
public class Customer implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer id;

    @Column(name="ecom_id")
    private  Integer ecom_id;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name="retailer")
    private String retailer;

    @Column(name="is_active")
    private boolean is_active =Boolean.TRUE;
}
