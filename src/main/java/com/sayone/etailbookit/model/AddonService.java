package com.sayone.etailbookit.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(
    name = "addon_service",
    indexes = {
            @Index(columnList = "retailer", name = "addon_service_retailer")
    }
)
//@SQLDelete(sql = "UPDATE addon_service SET deleted = true WHERE id=?")
//@Where(clause = "deleted=false")
@Setter
@Getter
public class AddonService implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer addonServiceId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "tack_on_extra_minutes")
    private Integer tackOnExtraMinutes;

    @Column(name = "tack_on_extra_amount")
    private BigDecimal tackOnExtraAmount;

    @Column(name = "tack_on_extra_amount_currency")
    private String tackOnExtraAmountCurrency;

    @Column(name = "is_taxable")
    private Boolean isTaxable = Boolean.FALSE;

    @Column(name = "active")
    private Boolean active = Boolean.FALSE;

    @ManyToMany(mappedBy = "addonServices", cascade = CascadeType.PERSIST)
    private Set<Service> services = new HashSet<>();

    @ManyToMany(mappedBy = "addonServices", cascade = CascadeType.PERSIST)
    private Set<Attendant> attendants = new HashSet<>();

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "indexvalue")
    private Integer indexValue;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}