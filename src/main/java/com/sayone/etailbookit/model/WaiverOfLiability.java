package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "waiver_of_liability")
public class WaiverOfLiability {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer waiverOfLiabilityId;

    @Column(name = "display_waiver_of_liability")
    private Boolean displayWaiverOfLiability = Boolean.FALSE;

    @Column(name = "require_customer_signature")
    private Boolean requireCustomerSignature = Boolean.FALSE;

    @Column(name="require_for_service_booking")
    private Boolean requireForServiceBooking=Boolean.FALSE;

    @Column(name = "display_pdf_upload")
    private Boolean displayPDFUpload = Boolean.FALSE;

    @Column(name = "display_text_entry_field")
    private Boolean displayTextEntryField = Boolean.FALSE;

    @Column(name = "text_entry_field_value")
    private String textEntryFieldValue;

    @Column(name = "file_url")
    private String fileURL;

    @Transient
    private MultipartFile file;

    @Transient
    private Boolean active = Boolean.FALSE;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;
}
