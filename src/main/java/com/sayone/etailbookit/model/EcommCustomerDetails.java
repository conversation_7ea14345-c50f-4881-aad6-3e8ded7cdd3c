package com.sayone.etailbookit.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.OffsetDateTime;
import java.math.BigDecimal;

@Data
public class EcommCustomerDetails {
    private Integer baseuserPtrId;
    private String createdWithSessionKey;
    private String modifiedWithSessionKey;
    
    @JsonProperty("firstName")
    private String firstName;
    
    @JsonProperty("lastName")
    private String lastName;
    
    private String phone;
    private String picture;
    private String note;
    private String street1;
    private String street2;
    private String city;
    private String state;
    private String zipcode;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
    private String status;
    private String createdIy;
    private Integer defaultStoreId;
    private String modifiedBy;
    private Integer astroCustomerId;
    private String customRewardsId;
    private String customRewardsId2;
    private Integer astroInternalCustomerId;
    private BigDecimal storeCredit;
    private Boolean isHealthyRewardsEligible;
    private BigDecimal rewardCredit;
    private BigDecimal rewardMoney;
    private OffsetDateTime firstTransaction;
    private Boolean isTaxExempt;
    private OffsetDateTime lastTransaction;
    private BigDecimal totalTransaction;
    private BigDecimal customerCredit;
    private Boolean isCreditAccount;
    private BigDecimal customerDeposit;
    private String email;
    private Integer customerTypeId;
    private String customerTypeName;
    private CustomerTag customerTag;
    
    @Data
    public static class CustomerTag {
        private String[] tags;
    }
    
    // Legacy field mapping for backward compatibility
    @JsonProperty("first_name")
    public String getFirst_name() {
        return firstName;
    }
    
    @JsonProperty("last_name")
    public String getLast_name() {
        return lastName;
    }
}
