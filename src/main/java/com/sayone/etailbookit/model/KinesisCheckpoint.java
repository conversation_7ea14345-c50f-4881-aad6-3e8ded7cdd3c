package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Data
public class KinesisCheckpoint {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String streamName;

    @Column(nullable = false)
    private String shardId;

    @Column(nullable = false)
    private String sequenceNumber;

    @Column(nullable = false)
    private LocalDateTime updatedAt;
}

