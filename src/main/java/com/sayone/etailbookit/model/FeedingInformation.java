package com.sayone.etailbookit.model;

import com.vladmihalcea.hibernate.type.array.StringArrayType;
import lombok.Data;
import org.hibernate.annotations.*;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TypeDefs({
        @TypeDef(
                name = "string-array",
                typeClass = StringArrayType.class
        )
})

@Table(name = "feeding_information")
@Entity
@Data
public class FeedingInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer feedingInformationId;

    @Column(name = "display")
    private Boolean display;

    @Column(name = "feed_count")
    private Integer feedCount;

    @Column(name = "grain_free_recipes_yes", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFreeRecipesYes;

    @Column(name = "grain_full_recipes_yes", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFullRecipesYes;

    @Column(name = "grain_free_recipes_no", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFreeRecipesNo;

    @Column(name = "grain_full_recipes_no", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFullRecipesNo;

    @Column(name = "amount_per_extra_cup")
    private BigDecimal amountPerExtraCup;

    @Column(name = "amount_currency")
    private String amountCurrency;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

}