package com.sayone.etailbookit.model;

import com.vladmihalcea.hibernate.type.array.ListArrayType;
import lombok.Data;
import org.hibernate.annotations.*;

import javax.persistence.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@TypeDefs({
        @TypeDef(
                name = "list-array",
                typeClass = ListArrayType.class
        )
})

@Data
@Entity
@Table(name = "service_history")
public class ServiceHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    Integer id;

    @OneToOne(fetch = FetchType.LAZY,cascade = CascadeType.PERSIST)
    @JoinColumn(name = "appointment_id", nullable = false)
    private Appointment appointment;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "comb_id")
    private Combs comb;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "blade_id")
    private Blades blade;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "shampoo_id")
    private PetShampoo petShampoo;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "cologne_id")
    private PetCologne petCologne;

    @Column(name = "food_fed")
    private String foodFed;

    @Column(name = "times_fed")
    private Integer timesFed;

    @Column(name = "times_peed")
    private Integer timesPeed;

    @Column(name = "times_pooped")
    private Integer timesPooped;

    @Column(name = "poop_description")
    private String poopDescription;

    @Column(name = "photos", columnDefinition = "text[]")
    @Type(type = "list-array")
    private List<String> photos;

    @Column(name = "notes")
    private String notes;

    @Column(name = "is_filled_water_bowl")
    private String isFilledWaterBowl;

    @Column(name = "pictures_sent")
    private Boolean picturesSent;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "actual_variable_schedule_duration")
    private Integer actualVariableScheduleDuration;

    @Column(name = "actual_over_nights")
    private Integer actualOvernights;

    @Column(name = "cup_fed_per_time")
    private Integer cupFedPerTime;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

}