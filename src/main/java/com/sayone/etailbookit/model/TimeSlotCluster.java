package com.sayone.etailbookit.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import javax.persistence.*;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "time_slot_cluster")
@Data
public class TimeSlotCluster {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cluster_id", insertable = false)
    private Integer clusterId;

    @ElementCollection
    @CollectionTable(name = "time_slot_cluster_available_days", joinColumns = @JoinColumn(name = "time_slot_cluster_id"))
    @Column(name = "available_days")
    private List<String> availableDays;

    @JsonIgnore
    @OneToMany(mappedBy = "timeSlotCluster", cascade = {CascadeType.ALL})
    private Set<TimeSlots> timeSlots = new HashSet<>();
}
