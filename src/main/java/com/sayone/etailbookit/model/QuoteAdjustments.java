package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name="quote_adjustments")
@Getter
@Setter
public class QuoteAdjustments implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer quoteAdjustmentId;

    @Column(name = "quote_name")
    private String quoteName;

    @Column(name="quote_price")
    private BigDecimal quotePrice;

    @ManyToOne(fetch =FetchType.LAZY)
    @JoinColumn(name = "appointment_id")
    private Appointment appointment ;

    @Column(name = "active", nullable = false)
    private boolean active;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;


}
