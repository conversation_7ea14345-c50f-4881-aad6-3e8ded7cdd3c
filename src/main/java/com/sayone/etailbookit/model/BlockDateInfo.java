package com.sayone.etailbookit.model;

import com.sayone.etailbookit.util.OffsetDateTimeConverter;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@Data
@Entity
@Table(name = "blockdates")

public class BlockDateInfo {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @Column(name = "block_date")
    private LocalDate blockDate;

    @Column(name = "offset_block_date")
    private String offsetBlockDate;

    @Column(name = "block_start_time")
    @Convert(converter = OffsetDateTimeConverter.class)
    private OffsetDateTime blockStartTime;

    @Column(name = "block_end_time")
    @Convert(converter = OffsetDateTimeConverter.class)
    private OffsetDateTime blockEndTime;

    @Column(name = "retailer")
    private String retailer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "attendant_id")
    private Attendant attendant;

    @Enumerated(EnumType.STRING)
    @Column(name = "block_type")
    private BlockedTimeType blockType;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

    @Column(name = "recurring_group_id")
    private String recurringGroupId;

    public enum BlockedTimeType {
        STORE,
        ATTENDANT
    }
}
