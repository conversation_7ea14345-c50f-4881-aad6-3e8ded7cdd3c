package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "schema")
@Getter
@Setter
public class Schema {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @Column(name = "schema_name")
    private String schemaName;

    @Column(name = "bookit_enabled")
    private boolean bookitEnabled;

    @Column(name = "email")
    private String emailId;

}
