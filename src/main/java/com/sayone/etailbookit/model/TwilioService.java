package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name="twilio_service")
public class TwilioService implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    Integer id;

    @Column(name="service_SID")
    private String serviceSID;

    @Column(name="customer_id")
    private Integer customerID;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

}
