package com.sayone.etailbookit.model;

import com.vladmihalcea.hibernate.type.array.ListArrayType;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.*;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@TypeDefs({
        @TypeDef(
                name = "list-array",
                typeClass = ListArrayType.class
        )
})

@Getter
@Setter

@Table(
    name = "pet",
    indexes = {
        @Index(columnList = "retailer", name = "pet_retailer")
    }
)
//@SQLDelete(sql = "UPDATE pet SET deleted = true WHERE id=?")
//@Where(clause = "deleted=false")
public class Pet implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @Column(name = "customer_id")
    private Integer customerId;

    @Column(name = "name")
    private String name;

    @Column(name = "dob")
    private LocalDate dob;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pet_type_id")
    private PetType petType;

    @Column(name = "sex")
    private String sex;

    @Column(name = "spayed")
    private boolean spayed;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "hair_length_id")
    private HairLength hairLength;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "hair_texture_id")
    private HairTexture hairTexture;

    @Column(name = "color")
    private String color;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "weight_range_id")
    private WeightRange weightRange;

    @Column(name = "exactWeight")
    private BigDecimal exactWeight;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "temperament_id")
    private Temperament temperament;

    @Column(name = "feed_count")
    private Integer feedCount;

    @Column(name = "allergies_text")
    private String allergiesText;

    @Column(name = "decease_date")
    private String deceaseDate;

    @Column(name = "photos", columnDefinition = "text[]")
    @Type(type = "list-array")
    private List<String> photos;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<Allergies> allergies = new HashSet<>();

    @OneToMany(mappedBy = "pet", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private Set<PetVetInformation> petVetInformation = new HashSet<>();

    @OneToOne(fetch = FetchType.EAGER,mappedBy = "pet",cascade = CascadeType.PERSIST)
    private PetWaiverOfLiabilityInfo petWaiverOfLiabilityInfo;

    @OneToMany(mappedBy = "pet", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private Set<PetDocuments> petDocuments = new HashSet<>();

    @OneToMany(mappedBy = "pet", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private Set<PetEmergencyContactInfo> petEmergencyContactInfo = new HashSet<>();

    @OneToMany(mappedBy = "pet", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<PetVaccinationRecords> petVaccinationRecords = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<PersonalityParameter> personalityParameters = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<ThreatReaction> threatReactions = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<BittingHistory> bitingHistories = new HashSet<>();

    @OneToMany(mappedBy = "pet", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<PetBreedsInformation> petBreedsInformations = new HashSet<>();

    @Column(name = "grainFreeRecipes", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFreeRecipes;

    @Column(name = "grainFullRecipes", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFullRecipes;

    @Column(name = "bring_food")
    private boolean bringFood;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "weight_unit", columnDefinition="character varying(255) default 'lb'")
    private String weightUnit;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private GeneralPetSize size;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}