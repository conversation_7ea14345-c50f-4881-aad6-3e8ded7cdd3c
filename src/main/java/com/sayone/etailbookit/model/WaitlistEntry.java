package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "waitlist_entries")
@Data
public class WaitlistEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    // Link to time slot
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "time_slot_id", nullable = false)
    private TimeSlots timeSlot;

    // Optional link to booked appointment after waitlist
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id")
    private Appointment appointment;

    // Multiple customers linked via join table
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "waitlist_customers",
        joinColumns = @JoinColumn(name = "waitlist_entry_id"),
        inverseJoinColumns = {
            @JoinColumn(name = "customer_ecom_id", referencedColumnName = "ecom_id"),
            @JoinColumn(name = "retailer", referencedColumnName = "retailer")
        }
    )
    private Set<Customer> customers;

    @Column(name = "notified")
    private Boolean notified = Boolean.FALSE;

    @Column(name = "booked")
    private Boolean booked = Boolean.FALSE;

    @Column(name = "created_at", updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

}
