package com.sayone.etailbookit.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @since 28 Sep 2021 16:45
 */

@Data
@Entity
@Table(name = "appointment_vet_information")
public class AppointmentVetInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id", nullable = false)
    private Appointment appointment;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "vet_information_id", nullable = false)
    private VetInformation vetInformation;

    @Column(name = "value", nullable = false)
    private String value;

    @Column(name = "retailer")
    private String retailer;
}
