package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "service_breeds_info")
public class ServiceBreedsInformation implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", insertable = false, nullable = false)
  private Integer id;

  @Column(name = "duration_type")
  private String durationType;

  @Column(name = "duration")
  private BigDecimal duration;

  @Column(name = "charge_amount")
  private BigDecimal chargeAmount;

  @ManyToOne(fetch = FetchType.LAZY)
  @NotFound(action= NotFoundAction.IGNORE)
  @JoinColumn(name = "breed_id")
  private Breed breed;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "service_id")
  private Service service;

  @Column(name = "retailer")
  private String retailer;

  @Column(name = "created_at",updatable = false)
  @CreationTimestamp
  private LocalDateTime createdAt;

  @Column(name = "modified_at")
  @UpdateTimestamp
  private LocalDateTime modifiedAt;
}
