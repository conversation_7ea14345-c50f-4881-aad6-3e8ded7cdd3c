package com.sayone.etailbookit.model;

import com.vladmihalcea.hibernate.type.array.ListArrayType;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.*;

import javax.persistence.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@TypeDefs({
        @TypeDef(
                name = "list-array",
                typeClass = ListArrayType.class
        )
})

@Getter
@Setter
@Table(name = "venue")
//@SQLDelete(sql = "UPDATE venue SET deleted = true WHERE id=?")

public class Venue implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer venueId;

    @Column(name = "internal_name")
    private String internalName;

    @Column(name = "public_name")
    private String publicName;

    @Column(name = "ecommerce_store_id")
    private Integer ecommerceStoreId;

    @Column(name = "location_type")
    private String locationType;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    @JoinColumn(name = "location_address_id")
    private VenueAddress locationAddress;

    @Column(name = "support_simultaneous_bookings")
    private Boolean supportSimultaneousBookings;

    @Column(name = "participant_limit_service")
    private Integer participantLimitService;

    @Column(name = "extra_charge")
    private BigDecimal extraCharge;

    @Column(name = "extra_currency")
    private String extraCurrency;

    @Column(name = "availability_interval")
    private String availabilityInterval;

    @Column(name = "availability_interval_unit")
    private String availabilityIntervalUnit;

    @OneToMany(mappedBy = "venue", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<VenueAvailability> availabilityDays = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<ServiceType> serviceTypes = new HashSet<>();

    @OneToMany(mappedBy = "venue", fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    private Set<VenuePetTypes> venuePetTypes = new HashSet<>();

    @ManyToMany(mappedBy = "venues", cascade = CascadeType.PERSIST)
    private Set<Service> services = new HashSet<>();

    @Column(name = "active")
    private Boolean active ;

    @Type(type = "list-array")
    @Column(name = "mobile_zip_code", columnDefinition = "text[]")
    private List<String> mobileZipCode;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}