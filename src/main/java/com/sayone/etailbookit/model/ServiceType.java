package com.sayone.etailbookit.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Setter
@Getter
@Table(
    name = "service_type",
    indexes = {
        @Index(columnList = "retailer", name = "service_type_retailer")
    }
)
//@SQLDelete(sql = "UPDATE service_type SET deleted = true WHERE id=?")
public class ServiceType implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer serviceTypeId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "active", nullable = false)
    private boolean active;

    @ManyToMany(mappedBy = "serviceTypes", cascade = CascadeType.PERSIST)
    private Set<Attendant> attendants;

    @ManyToMany(mappedBy = "serviceTypes", cascade = CascadeType.PERSIST)
    private Set<Venue> venues;

    @OneToMany(mappedBy = "serviceType", cascade = CascadeType.PERSIST)
    private Set<Service> services;

    @Column(name = "fileUrl")
    private String fileUrl;

    @Column(name = "indexvalue")
    private Integer indexValue;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}