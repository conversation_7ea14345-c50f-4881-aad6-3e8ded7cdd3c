package com.sayone.etailbookit.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.management.ConstructorParameters;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "skipped_records")
@Getter
@Setter
public class SkippedRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "record_data", nullable = false, columnDefinition = "TEXT")
    private String recordData;

    @Column(name = "error_message", nullable = false, columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "processed_at", nullable = false)
    private LocalDateTime processedAt;

    @Column(name ="time_slot_cluster_id", nullable = false)
    private Integer timeSlotClusterId;

    public SkippedRecord() {
    }

    public SkippedRecord(String recordData, String errorMessage, LocalDateTime processedAt , Integer timeSlotClusterId) {
        this.recordData = recordData;
        this.errorMessage = errorMessage;
        this.processedAt = processedAt;
        this.timeSlotClusterId=timeSlotClusterId;
    }
}

