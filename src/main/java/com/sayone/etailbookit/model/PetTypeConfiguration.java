package com.sayone.etailbookit.model;

import com.sayone.etailbookit.util.DisplayType;
import com.sayone.etailbookit.util.PetTypeConfig;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Entity
@Table(
    name = "pet_type_config",
    indexes = {
        @Index(columnList = "pet_type_id", name = "pet_type_config_pet_type_id")
    }
)
public class PetTypeConfiguration implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer configurationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pet_type_id")
    private PetType petType;

    @Column(name = "name", nullable = false)
    private PetTypeConfig name;

    @Column(name = "display_type", nullable = false)
    private DisplayType displayType;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

}