package com.sayone.etailbookit.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(
    name = "pet_shampoo",
    indexes = {
            @Index(columnList = "retailer", name = "shampoo_retailer")
    }
)
//@SQLDelete(sql = "UPDATE pet_shampoo SET deleted = true WHERE id=?")
@Getter
@Setter
public class PetShampoo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false)
    private Integer id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "extra_charge")
    private Double extraCharge;

    @Column(name = "extra_currency")
    private String extraCurrency;

    @Column(name = "active", nullable = false)
    private boolean active;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "indexvalue")
    private Integer indexValue;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}