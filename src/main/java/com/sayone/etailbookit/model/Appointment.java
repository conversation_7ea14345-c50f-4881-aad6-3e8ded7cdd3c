package com.sayone.etailbookit.model;


import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.OffsetDateTimeConverter;
import com.sayone.etailbookit.util.ServiceStatus;
import com.sayone.etailbookit.util.TimeZoneContext;
import com.vladmihalcea.hibernate.type.array.ListArrayType;
import lombok.Getter;
import lombok.Setter;
import org.apache.tomcat.jni.Local;
import org.hibernate.annotations.*;
import org.joda.time.field.OffsetDateTimeField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Entity
@TypeDefs({
        @TypeDef(
                name = "list-array",
                typeClass = ListArrayType.class
        )
})

@Getter
@Setter
@Table(name = "appointment")

public class Appointment implements Serializable {

    private static final long serialVersionUID = 1L;

    private static Logger LOGGER = LoggerFactory.getLogger(Appointment.class);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer id;

    @Column(name = "customer_id")
    private Integer customerId;

    @Column(name = "appoinment_no")
    private String appoinmentNo;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "service_type")
    private ServiceType serviceType;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "service")
    private Service service;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "attendant")
    private Attendant attendant;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "venue")
    private Venue venue;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "shamppo")
    private PetShampoo shamppo;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "cologne")
    private PetCologne cologne;

    @Column(name = "appointment_date", nullable = false)
    private LocalDate date;

    @Column(name="appointment_end_date")
    private LocalDate endDate;

    @Column(name = "appointment_time", nullable = false)
    private OffsetTime time;

    @Column(name = "appointment_end_time")
    private OffsetTime endTime;

    @Column(name = "date_time_onboarding")
    private LocalDate dateTimeOnboarding;

    @Column(name = "recurring_enabled")
    private Boolean recurringEnabled;

    @Column(name = "have_sibilings")
    private Boolean haveSibilings;

    @Column(name = "sibiling_count")
    private Integer sibilingCount;

    @Column(name = "address_id")
    private Integer addressId;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<AddonService> addOnService = new HashSet<>();

    @OneToMany(mappedBy = "appointment", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private  Set<QuoteAdjustments> quoteAdjustments=new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "pet")
    private Pet pet;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pet_type_id")
    private PetType petType;

    @Column(name = "exactWeight")
    private BigDecimal exactWeight;

    @Column(name = "weightUnit", columnDefinition="character varying(255) default 'lb'")
    private String weightUnit;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "weight_range_id")
    private WeightRange weightRange;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "temperament_id")
    private Temperament temperament;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "hair_length_id")
    private HairLength hairLength;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "hair_texture_id")
    private HairTexture hairTexture;

    @OneToMany(mappedBy = "appointment", fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    private Set<AppointmentDesiredHairLengths> desiredHairLengths = new HashSet<>();

    @Column(name = "feeding_count")
    private Integer feedingCount;

    @Column(name = "bring_your_food", nullable = false)
    private Boolean bringYourFood;

    @Column(name = "grain_free_recipes", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFreeRecipes;

    @Column(name = "grain_full_recipes", columnDefinition = "text[]")
    @Type(type = "string-array")
    private String[] grainFullRecipes;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "service_cost")
    private BigDecimal serviceCost;

    @Column(name="quote_adjustment_price")
    private BigDecimal quoteAdjustmentPrice;

    @Column(name = "amountCurrency")
    private String amountCurrency;

    @Column(name = "tipAmount")
    private BigDecimal tipAmount;

    @Column(name = "tipAmountCurrency")
    private String tipAmountCurrency;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<Allergies> allergies = new HashSet<>();

    @OneToMany(mappedBy = "appointment", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<AppointmentEmergencyContactInfo> appointmentEmergencyContactInfo = new HashSet<>();

    @OneToMany(mappedBy = "appointment", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<AppointmentVetInformation> appointmentVetInformation = new HashSet<>();

    @ManyToMany(mappedBy = "appointment",fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<AppointmentVaccinationInformation> vaccinationInfo  = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<PersonalityParameter> personalityParameters = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Set<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers = new HashSet<>();

    @OneToMany(mappedBy = "appointment", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private  Set<AppointmentDocuments> otherDoc  = new HashSet<>();

    @OneToOne(fetch = FetchType.LAZY,mappedBy = "appointment",cascade = CascadeType.ALL)
    private AppointmentWaiverOfLiabilityInformation waiverOfLiabilityDoc;

    @Column(name = "service_status")
    private ServiceStatus serviceStatus;

    @Column(name = "cancellation_reason")
    private String cancellationReason;

    @Column(name = "rejection_reason")
    private String rejectionReason;

    @OneToOne(fetch = FetchType.EAGER,mappedBy = "appointment",cascade = CascadeType.PERSIST)
    private ServiceHistory serviceDetails;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "order_reference")
    private Long orderReference;

    @Column(name = "payment_status")
    private String paymentStatus;

    @Column(name = "variable_schedule_duration")
    private Integer variableScheduleDuration;

    @Column(name = "overnights")
    private Integer overnights;

    @Column(name = "discount_amount")
    private BigDecimal discountAmount;

    @Column(name = "service_start_at")
    @Convert(converter = OffsetDateTimeConverter.class)
    private OffsetDateTime serviceStartAt;

    @Column(name = "service_end_at")
    @Convert(converter = OffsetDateTimeConverter.class)
    private OffsetDateTime serviceEndAt;

    @Column(name="duration")
    private String duration;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "address")
    private String address;

    @OneToMany(mappedBy = "appointment",fetch = FetchType.LAZY,cascade = CascadeType.ALL)
    private List<ServiceHistoryAddNotes> addNotes;

    @Column(name = "note")
    private String note;

    @Column(name = "customer_name")
    private String customerName;

    @Column(name = "opt_in_for_sms")
    private Boolean optInForSms = Boolean.FALSE;

    @Column(name = "is_override")
    private Boolean isOverride = Boolean.FALSE;

    @Column(name = "is_manual_slots")
    private Boolean isManualSlots = Boolean.FALSE;

    @Column(name = "sms_phone_number")
    private String smsPhoneNumber;

    @Column(name="offset_appointment_date")
    private LocalDate offsetAppointmentDate;

    @Column(name="start_date_and_time")
    private String appointmentStartDateAndTime;

    @Column(name="end_date_and_time")
    private String appointmentEndDateAndTime;

    @Column(name ="source")
    private String source;

    @Column(name = "updated_customer_id")
    private Integer updatedCustomerId;

    @Column(name = "updated_customer_name")
    private String updatedCustomerName;

    @Column(name = "created_customer_id")
    private Integer createdCustomerId;

    @Column(name = "created_customer_name")
    private String createdCustomerName;

    @Column(name="time_slot_id")
    private  Integer timeSlotId;

    @Column(name = "payment_id")
    private String paymentId;

    @Column(name = "card_holder_name")
    private String cardHolderName;

    @Column(name = "waiver_acknowledged")
    private  boolean waiverAcknowledged = Boolean.TRUE;

    @Override
    public String toString() {
        return "Appointment{" +
                "id=" + id +
                '}';
    }

    public String getAppointmentStartDateAndTime(){
        return this.appointmentStartDateAndTime;
    }

    public String calculateDuration(Appointment appointment) {
        String duration;
        if (appointment.getStartTime() != null && appointment.getEndTime() != null) {
            Duration difference = Duration.ZERO;
            if (appointment.getStartTime() != null && appointment.getEndTime() != null)
                difference = Duration.between(OffsetDateTime.parse(appointment.getStartTime()), OffsetDateTime.parse(appointment.getEndTime()));
            if (appointment.getService() != null && !appointment.getService().getServiceBreedsInformations().isEmpty() && !appointment.getPet().getPetBreedsInformations().isEmpty()) {
                for (PetBreedsInformation petBreedsInformation: appointment.getPet().getPetBreedsInformations()) {
                    for (ServiceBreedsInformation serviceBreedsInformation : appointment.getService().getServiceBreedsInformations()) {
                        if (serviceBreedsInformation != null && petBreedsInformation != null && petBreedsInformation.getBreed() != null && serviceBreedsInformation.getBreed() != null  && petBreedsInformation.getBreed().getId().equals(serviceBreedsInformation.getBreed().getId()) && petBreedsInformation.getBreed().isActive()) {
                            if (serviceBreedsInformation.getDurationType() != null && serviceBreedsInformation.getDurationType().equalsIgnoreCase("MINUTE") && serviceBreedsInformation.getDuration() != null)
                                difference = difference.plusMinutes(serviceBreedsInformation.getDuration().longValue());
                            else if (serviceBreedsInformation.getDurationType() != null && serviceBreedsInformation.getDurationType().equalsIgnoreCase("HOUR") && serviceBreedsInformation.getDuration() != null)
                                difference = difference.plusHours(serviceBreedsInformation.getDuration().longValue());
                            else if (serviceBreedsInformation.getDurationType() != null && serviceBreedsInformation.getDurationType().equalsIgnoreCase("DAY") && serviceBreedsInformation.getDuration() != null)
                                difference = difference.plusDays(serviceBreedsInformation.getDuration().longValue());
                        }
                    }
                }
            }
            if (difference.toDays() >= 1) {
                duration = difference.toDays() + " " + "day" + (difference.toDays() > 1 ? "s":"");
            } else if (difference.toHours() >= 1) {
                long seconds=   difference.getSeconds();
                long HH = seconds / 3600;
                long MM = (seconds % 3600) / 60;
                long SS = seconds % 60;
               // duration=difference.toHoursPart()+ " " + "hour" + (difference.toHours() > 1 ? "s":"");
                //+(difference.toMinutesPart()>1?difference.plusMinutes(difference.toMinutesPart())+"minutes":"");
                if(difference.toMinutesPart()>1){
                    duration=seconds+ " " +"seconds";
                }else{
                    duration=difference.toHoursPart()+ " " + "hour" + (difference.toHours() > 1 ? "s":"");
                }
            } else {
                duration = difference.toMinutes() + " " + "minute" + (difference.toMinutes() > 1 ? "s":"");
            }
        } else {
            duration = appointment.getService().getScheduleType().equals("FIXED") ?
                appointment.getService().getFixedScheduleValue() + " " +
                        appointment.getService().getFixedScheduleUnit().toLowerCase() +
                        (appointment.getService().getFixedScheduleValue() > 1 ? "s":"")
                    :
                appointment.getService().getVariableScheduleMinValue() + "-" +
                    appointment.getService().getVariableScheduleMaxValue() + " " +
                    appointment.getService().getFixedScheduleUnit().toLowerCase() + "s" ;
        }
        return duration;
    }

    public String getStartTime() {
        //return convertToUserTime(OffsetDateTime.of(this.date, this.time.toLocalTime(), ZoneOffset.UTC));
        return convertToOffsetDateTime(OffsetDateTime.of(this.date, this.time.toLocalTime(), ZoneOffset.UTC));
    }

    public OffsetDateTime getOffsetStartTime(){
       LocalDate date= ZonedDateTime.parse(this.date.toString()+"T"+this.time.toString())
                .toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                .toLocalDate();
        return OffsetDateTime.of(date,this.time.toLocalTime(),ZoneOffset.UTC);
    }

    public String getEndTime() {

       LocalTime endTime;
       if(isOverride){
           endTime=this.endTime.toLocalTime();
       } else if (isManualSlots) {
           endTime=this.endTime.toLocalTime();
       } else if (this.getService().getScheduleType().equals("FIXED")){
             Integer time  = this.getService().getFixedScheduleValue();
            if(this.getService().getFixedScheduleUnit().equals("DAY")){
                endTime = this.time.plus(Duration.ofDays(time)).toLocalTime();
            }
            else if(this.getService().getFixedScheduleUnit().equals("HOUR")){
                endTime = this.time.plus(Duration.ofHours(time)).toLocalTime();
            }
            else{
                endTime = this.time.plus(Duration.ofMinutes(time)).toLocalTime();
            }
        }
        else {
             Integer time = Integer.parseInt(this.getService().getVariableScheduleMaxValue());
            if(this.getService().getFixedScheduleUnit().equals("DAY")){
                endTime = this.time.plus(Duration.ofDays(time)).toLocalTime();
            }
            else if(this.getService().getFixedScheduleUnit().equals("HOUR")){
                endTime = this.time.plus(Duration.ofHours(time)).toLocalTime();
            }
            else{
                endTime = this.time.plus(Duration.ofMinutes(time)).toLocalTime();
            }
        }
        if(this.endDate!=null){
            return convertToOffsetDateTime(OffsetDateTime.of(this.endDate,endTime,ZoneOffset.UTC));
        }else {
            // return convertToUserTime(OffsetDateTime.of(this.date,endTime,ZoneOffset.UTC));
            return convertToOffsetDateTime(OffsetDateTime.of(this.date, endTime, ZoneOffset.UTC));
        }
    }

    public OffsetDateTime getOffsetEndTime() {

        LocalTime endTime;
        if(isOverride){
            endTime=this.endTime.toLocalTime();
        }
        else if (isManualSlots) {
            endTime=this.endTime.toLocalTime();
        }
        else if (this.getService().getScheduleType().equals("FIXED")){
            Integer time  = this.getService().getFixedScheduleValue();
            if(this.getService().getFixedScheduleUnit().equals("DAY")){
                endTime = this.time.plus(Duration.ofDays(time)).toLocalTime();
            }
            else if(this.getService().getFixedScheduleUnit().equals("HOUR")){
                endTime = this.time.plus(Duration.ofHours(time)).toLocalTime();
            }
            else{
                endTime = this.time.plus(Duration.ofMinutes(time)).toLocalTime();
            }
        }
        else {
            Integer time = Integer.parseInt(this.getService().getVariableScheduleMaxValue());
            if(this.getService().getFixedScheduleUnit().equals("DAY")){
                endTime = this.time.plus(Duration.ofDays(time)).toLocalTime();
            }
            else if(this.getService().getFixedScheduleUnit().equals("HOUR")){
                endTime = this.time.plus(Duration.ofHours(time)).toLocalTime();
            }
            else{
                endTime = this.time.plus(Duration.ofMinutes(time)).toLocalTime();
            }
        }
        if(this.endDate!=null){
            LocalDate endDate=ZonedDateTime.parse(this.endDate.toString()+"T"+this.time.toString())
                    .toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                    .toLocalDate();
            return OffsetDateTime.of(endDate,endTime,ZoneOffset.UTC);
        }else {
            LocalDate date1=ZonedDateTime.parse(this.date.toString()+"T"+this.time.toString())
                    .toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                    .toLocalDate();
            // return convertToUserTime(OffsetDateTime.of(this.date,endTime,ZoneOffset.UTC));
           return OffsetDateTime.of(date1, endTime, ZoneOffset.UTC);
        }
    }

    public String convertToUserDate() {
        //Integer offsetTimeDifference=calculateOffset();
       return ZonedDateTime.parse(this.date.toString()+"T"+this.time.toString())
                .toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                .toLocalDate()
                .toString();
    //return getOffsetStartTime().plusMinutes(offsetTimeDifference).toLocalDate().toString();
    }

    public String convertToUserEndDate() {
        String appointmentEndDate=null;
        if(this.endDate!=null && this.endTime!=null){
            LocalDate endDate=this.endDate;
            appointmentEndDate= ZonedDateTime.parse(endDate.toString()+"T"+this.endTime.toString())
                    .toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                    .toLocalDate()
                    .toString();
        }

        return appointmentEndDate;
    }

    public String convertToLocalDateTime(OffsetDateTime time){
        ZonedDateTime zoned = time.atZoneSameInstant(ZoneId.of(OffsetContext.getOffset()));
        LocalDateTime localDateTime = zoned.toLocalDateTime();
        return convertToUserTime(localDateTime);
    }

    public String convertToUserTime(OffsetDateTime time) {
        String convertedTime = time.toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                .format(DateTimeFormatter.ofPattern("HH:mm:ss")).toString();
        return convertedTime.split("[+\\-Z]")[0];
    }
    public String convertToUserTime(LocalDateTime time) {
        String convertedTime = time.atOffset(ZoneOffset.of(OffsetContext.getOffset()))
                .format(DateTimeFormatter.ofPattern( "dd MMMM YYYY HH:mm:ss" )).toString();
        return convertedTime.split("[+\\-Z]")[0];
    }
    public String convertToOffsetDateTime(OffsetDateTime time){
        String convertedTime = time.toInstant().atOffset(ZoneOffset.of(OffsetContext.getOffset())).format(DateTimeFormatter.ofPattern("YYYY-MM-dd'T'HH:mm:ss'Z'")).toString();;
        return convertedTime;
    }

    public String convertCreatedAtToUTC(LocalDateTime time){
        String convertedTime = time.atOffset(ZoneOffset.of(OffsetContext.getOffset())).format(DateTimeFormatter.ofPattern("YYYY-MM-dd'T'HH:mm:ss'Z'")).toString();;
        return convertedTime;
    }

    /*public Integer calculateOffset() {
        TimeZone tz = TimeZone.getTimeZone(TimeZoneContext.getTimeZone());
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        java.util.TimeZone timeZone = calendar.getTimeZone();
        return new Date().getTimezoneOffset()*-1;
    }*/


    public OffsetDateTime getNewEndTime() {
        OffsetDateTime appointmentEndTime=null;
        LocalTime endTime;
        if(this.endTime!=null) {
            if (isOverride) {
                endTime = this.endTime.toLocalTime();
            } else if (this.getService().getScheduleType().equals("FIXED")) {
                Integer time = this.getService().getFixedScheduleValue();
                if (this.getService().getFixedScheduleUnit().equals("DAY")) {
                    endTime = this.time.plus(Duration.ofDays(time)).toLocalTime();
                } else if (this.getService().getFixedScheduleUnit().equals("HOUR")) {
                    endTime = this.time.plus(Duration.ofHours(time)).toLocalTime();
                } else {
                    endTime = this.time.plus(Duration.ofMinutes(time)).toLocalTime();
                }
            } else {
                Integer time = Integer.parseInt(this.getService().getVariableScheduleMaxValue());
                if (this.getService().getFixedScheduleUnit().equals("DAY")) {
                    endTime = this.time.plus(Duration.ofDays(time)).toLocalTime();
                } else if (this.getService().getFixedScheduleUnit().equals("HOUR")) {
                    endTime = this.time.plus(Duration.ofHours(time)).toLocalTime();
                } else {
                    endTime = this.time.plus(Duration.ofMinutes(time)).toLocalTime();
                }
            }

            appointmentEndTime= OffsetDateTime.of(this.date, endTime, ZoneOffset.UTC);
        }
    return  appointmentEndTime;
    }

    public boolean getWaiverAcknowledged() {
        return this.waiverAcknowledged;
    }
}
