package com.sayone.etailbookit.model;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Entity
@Table(
    name = "general_pet_size",
    indexes = {
        @Index(columnList = "retailer", name = "general_pet_size_retailer")
    }
)
//@SQLDelete(sql = "UPDATE general_pet_size SET deleted = true WHERE id=?")
//@Where(clause = "deleted=false")

public class GeneralPetSize implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, nullable = false)
    private Integer generalPetSizeId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pet_type_id")
    private PetType petType;

    @Column(name = "size", nullable = false)
    private String size;

    @Column(name = "weight_value")
    private Integer weightValue;

    @Column(name = "weight_unit")
    private String weightUnit;

    @Column(name = "retailer")
    private String retailer;

    @Column(name = "created_at",updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "modified_at")
    @UpdateTimestamp
    private LocalDateTime modifiedAt;

    @Column(name = "deleted")
    private Boolean deleted = Boolean.FALSE;

}