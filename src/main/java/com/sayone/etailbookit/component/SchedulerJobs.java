package com.sayone.etailbookit.component;

import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.sql.Date;
import java.time.*;
import java.util.UUID;

@Component
public class SchedulerJobs {
    @Autowired
    Scheduler quartScheduler;

    @Value("${scheduler.timeintervel}")
    Integer schedulerIntervel;

    private static Logger LOGGER = LoggerFactory.getLogger(SchedulerJobs.class);

    @PostConstruct
    public void scheduleTask() throws SchedulerException, InterruptedException {
        LOGGER.info("@@@@Entered SchedulerJobs class");
        Instant instant = Instant.now();
        //fixed time 8 15 am utc
        LocalTime newTime = LocalTime.parse("08:15:00.567891");
        ZonedDateTime dt = instant.atZone(ZoneOffset.UTC);
        dt = dt.with(newTime);
        instant = dt.toInstant();
        LOGGER.info("@@@@Instant"+instant);
        //create job
        JobDetail jobDetail = JobBuilder.newJob(ScheduleAppointment.class).
            withIdentity(UUID.randomUUID().toString(),"email-jobs").
            build();
        //create trigger
        Trigger trigger = TriggerBuilder.newTrigger()
            .withIdentity(jobDetail.getKey().getName(),"job2")
            .startAt(Date.from(instant))
                //temporary for testing - triggering in each 10 minutes
            .withSchedule(SimpleScheduleBuilder.simpleSchedule().withIntervalInMinutes(schedulerIntervel).repeatForever())
            .build();
        //schedule
		quartScheduler.scheduleJob(jobDetail,trigger);
        LOGGER.info("@@@@Exited SchedulerJob class");
    }
}
