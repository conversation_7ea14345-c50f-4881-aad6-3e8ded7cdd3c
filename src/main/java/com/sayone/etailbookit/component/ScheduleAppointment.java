package com.sayone.etailbookit.component;


import com.sayone.etailbookit.dto.RetailerDefaultTimeZoneMappingDto;
import com.sayone.etailbookit.dto.RetailerProfileDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.service.impl.ExcelExportService;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.ServiceStatus;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetTime;
import java.util.*;

import static com.fasterxml.jackson.databind.util.StdDateFormat.getDefaultTimeZone;

@Component
public class ScheduleAppointment extends QuartzJobBean {

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    PetVaccinationRecordRepository petVaccinationRecordRepository;

    @Autowired
    PetRepository petRepository;

    @Autowired
    CustomerEmailNotification customerEmailNotification;

    @Autowired
    SchedulerMessageRepository schedulerRepository;

    @Autowired
    SchemaRepository schemaRepository;

    @Autowired
    CustomerSMSNotification customerSMSNotification;

    @Autowired
    ExcelExportService excelExportService;

    @Autowired
    AuthenticationEcom authenticationEcom;

    @Value("${ecom.auth.uri}")
    String auth_Uri;

    RestTemplate restTemplate = new RestTemplate();
    private static Logger LOGGER = LoggerFactory.getLogger(ScheduleAppointment.class);

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        LOGGER.info("@@@@Entered ScheduleAppointment class");
                try {
                  //  appointmentScheduling();
                   // vaccinationScheduling();
                  //  exportCurrentDaysAppointment();
                }
              //  catch (EtailBookItException e) {
                  //  e.printStackTrace();
              //  }
                catch (Exception e) {
                    throw new RuntimeException(e);
                }
        LOGGER.info("@@@@Exited ScheduleAppointment class");
    }

    public void exportCurrentDaysAppointment() throws Exception {
        LOGGER.info("@@@@ Entered current dates appointment scheduling ");

        List<RetailerProfileDto> retailerDefaultTimeZone = getRetailerDefaultTimeZone();

        if (!retailerDefaultTimeZone.isEmpty()) {
            for (RetailerProfileDto retailerProfileDto : retailerDefaultTimeZone) {
                byte[] excelData = processAppointmentsForToday(retailerProfileDto.getSchema_name(), retailerProfileDto.getDefault_store_timezone());

                if (hasAppointmentRecords(excelData)) {  // Validate actual data rows
                    customerSMSNotification.exportAppointments(excelData, retailerProfileDto.getSchema_name());
                } else {
                    LOGGER.info("Skipping export for retailer: {} as no appointments are present.", retailerProfileDto.getSchema_name());
                }
            }
        }
    }

    // Helper method to check if Excel contains actual appointment records
    private boolean hasAppointmentRecords(byte[] excelData) {
        if (excelData == null || excelData.length == 0) {
            return false;
        }

        try (ByteArrayInputStream bis = new ByteArrayInputStream(excelData);
             Workbook workbook = new XSSFWorkbook(bis)) {

            Sheet sheet = workbook.getSheetAt(0);  // Get first sheet
            int rowCount = sheet.getPhysicalNumberOfRows();

            LOGGER.info("Excel file has {} rows (including headers)", rowCount);

            // Assuming first row is headers, check if there's at least one data row
            return rowCount > 1;
        } catch (IOException e) {
            LOGGER.error("Error reading Excel data", e);
            return false;
        }
    }


    public List<RetailerProfileDto> getRetailerDefaultTimeZone() throws EtailBookItException {
        String retailer = "public";
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(retailer);
        String accessToken = authToken.getBody().getAccess_token();

        String url = auth_Uri + "public/api/v1/bookit/bookit-retailers/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + accessToken);
        headers.add("X-TenantSchema", retailer);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        List<RetailerProfileDto> allRetailers = new ArrayList<>();

        while (url != null) {
            LOGGER.info("Fetching retailers from URL: {}", url);

            ResponseEntity<RetailerDefaultTimeZoneMappingDto> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    RetailerDefaultTimeZoneMappingDto.class
            );

            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                RetailerDefaultTimeZoneMappingDto response = responseEntity.getBody();
                allRetailers.addAll(response.getResults());
                url = response.getNext(); // Update the URL to fetch the next page
            } else {
                LOGGER.error("ERROR FROM ECOM API RESPONSE TO GET THE DEFAULT TIME ZONE");
                throw new EtailBookItException("Failed to fetch retailers");
            }
        }

        return allRetailers;
    }
    public byte[] processAppointmentsForToday(String retailer,String timeZone) throws Exception {
        LocalDate today=LocalDate.now();
        int pageSize = 50; // Batch size
        int pageNumber = 0;
        Integer offsetDifference;
        if(timeZone!=null){
            TimeZone tz = TimeZone.getTimeZone(timeZone);
             offsetDifference= tz.getOffset(new Date().getTime()) / 1000 / 60;
        }
       else{
            offsetDifference=0;
        }
        List<Appointment> appointments;
        List<Appointment> allAppointments = new ArrayList<>();
        do {
            Pageable pageable = PageRequest.of(pageNumber, pageSize);
            appointments = appointmentRepository
                    .findByDateServiceStatusAndRetailer(today,retailer, offsetDifference,pageable).getContent();
            allAppointments.addAll(appointments);
            pageNumber++;
        } while (!appointments.isEmpty());
        return excelExportService.exportAppointments(allAppointments,timeZone);
    }

    void appointmentScheduling() throws EtailBookItException {
        LOGGER.info("@@@@Entered Appointment scheduling");
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        List<Appointment> appointmentDates = appointmentRepository.findByDateAndServiceStatus(tomorrow, ServiceStatus.CANCELLED);
        if(!appointmentDates.isEmpty()){
            for(Appointment appointmentDateList : appointmentDates){
                Integer customerId = appointmentDateList.getCustomerId();
                OffsetTime time = appointmentDateList.getTime();
                String retailer = appointmentDateList.getRetailer();
                String serviceName = appointmentDateList.getServiceType().getName();
                Optional<Pet> petData = petRepository.findById(appointmentDateList.getPet().getId());
                String petName = null;
                if(petData.isPresent()){
                     petName = petData.get().getName();
                }
                //email sent integration
                //customerEmailNotification.serviceRemainderMail(customerId,serviceName,time,retailer,petName);
                customerSMSNotification.serviceRemainderSMS(customerId,serviceName,time,retailer,petName,appointmentDateList.getId(),appointmentDateList.getAttendant().getFirstName()+appointmentDateList.getAttendant().getLastName(),appointmentDateList.getAppointmentStartDateAndTime());
            }
        }
        LOGGER.info("@@@@Exited Appointment Scheduling");
    }
     void vaccinationScheduling() throws EtailBookItException {
        LOGGER.info("@@@@Entered vaccination Scheduling");
        LocalDate seventhDay = LocalDate.now().plusDays(7);
        List<PetVaccinationRecords> petVaccinationRecords = petVaccinationRecordRepository.findByDateExpires(seventhDay);
        if(!petVaccinationRecords.isEmpty()){
            for(PetVaccinationRecords petVaccinationRecordList : petVaccinationRecords){
                Integer petId = petVaccinationRecordList.getPet().getId();
                Optional<Pet> petData = petRepository.findById(petId);
                if(petData.isPresent()){
                    Integer customerId = petData.get().getCustomerId();
                    String petName = petData.get().getName();
                    LocalDate dateExpiry = petVaccinationRecordList.getDateExpires();
                    String petVaccinationName = petVaccinationRecordList.getVaccinationRecords().getName();
                   //customerEmailNotification.vaccinationRemainderMail(customerId,dateExpiry,petVaccinationName,petName,petData);
                   //customerSMSNotification.vaccinationRemainderSMS(customerId,dateExpiry,petVaccinationName,petName,petData);
                }
            }
        }
         LOGGER.info("@@@@Exited vaccination Scheduling");
    }
}
