package com.sayone.etailbookit.component;


import com.sayone.etailbookit.dto.AppointmentDto;
import com.sayone.etailbookit.dto.ServiceHistoryDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.service.impl.MailNotificationService;
import com.sayone.etailbookit.util.UserTimeZoneConversion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.OffsetTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Optional;

@Component
public class CustomerEmailNotification {

    @Autowired
    MailNotificationService mailNotificationService;

    @Autowired
    AuthenticationEcom authenticationEcom;

    @Autowired
    UserTimeZoneConversion userTimeZoneConversion;

    private static Logger LOGGER = LoggerFactory.getLogger(MailNotificationService.class);
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("hh:mm:ss a");

    public void setAppointmentMailNotification(AppointmentDto appointmentDto, Appointment appointment) throws EtailBookItException {
        LOGGER.info("@@@@Enterd setAppointmentMailNotification function");
        Integer customerId = appointment.getCustomerId();
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,appointment.getRetailer());
        String appointmentSubject = "Confirmation of your "+appointment.getServiceType().getName()
                +" appointment.";
        String appointmentSucessMessage = "<b>Confirmed!</b> You have booked a "
                +appointment.getServiceType().getName()+" service for "+appointment.getPet().getName()
                +" with "+customerRetailerDetails.get("retailerName")+" on "+appointment.getDate()
                +" at "+userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime())
                +"<br>Thank you,<br>Your team at <br><b>"+customerRetailerDetails.get("retailerName")+"</b></br>";

        String mailCcStore = customerRetailerDetails.get("retailerEmail");
        String mailoToCustomer = customerRetailerDetails.get("customerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailoToCustomer,mailFromRetailer,mailCcStore);
    }

    public void appointmentUpdateMailNotification(Appointment appointment) throws EtailBookItException {
        LOGGER.info("@@@@Enterd setAppointmentMailNotification function");
        Integer customerId = appointment.getCustomerId();
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,appointment.getRetailer());
        String appointmentSubject = "Confirmation of your "+appointment.getServiceType().getName()
                +" appointment.";
        String appointmentSucessMessage = "<b>Confirmed!</b> You have updated a booked "
                +appointment.getServiceType().getName()+" service for "+appointment.getPet().getName()
                +" with "+customerRetailerDetails.get("retailerName")+" on "+appointment.getDate()
                +" at "+userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime())
                +"<br>Thank you,<br>Your team at <br><b>"+customerRetailerDetails.get("retailerName")+"</b></br>";

        String mailCcStore = customerRetailerDetails.get("retailerEmail");
        String mailoToCustomer = customerRetailerDetails.get("customerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailoToCustomer,mailFromRetailer,mailCcStore);
    }

    public void serviceChangeMailNotification(Appointment appointment, LocalDate dateExist , OffsetTime timeExist, LocalDate date , OffsetTime time) throws EtailBookItException {
        LOGGER.info("@@@@Enterd serviceChangeMailNotification function");
        Integer customerId = appointment.getCustomerId();
        String petName = appointment.getPet().getName();
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,appointment.getRetailer());
        String appointmentSubject = "Confirmation of your "+appointment.getServiceType().getName()+" appointment.";
        String appointmentSucessMessage = "Your " +appointment.getServiceType().getName()
                +" service for "+petName+ " was changed from "+dateExist+","+userTimeZoneConversion.convertToUserTime(timeExist.toLocalTime())
                +" with "+customerRetailerDetails.get("retailerName")+" to "+date+","+userTimeZoneConversion.convertToUserTime(time.toLocalTime())
                +"<br>Thank you,<br>Your team at </br><br><b>"+customerRetailerDetails.get("retailerName")
                +"</b>";
        String mailToCustomer = customerRetailerDetails.get("customerEmail");
        String mailCc = customerRetailerDetails.get("retailerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailToCustomer,mailFromRetailer,mailCc);
    }

    public void cancelServiceMailNotification(Appointment appointment) throws EtailBookItException {
        LOGGER.info("@@@@Enterd cancelServiceMailNotification function");
        Integer customerId = appointment.getCustomerId();
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,appointment.getRetailer());
        String appointmentSubject = "Your "+appointment.getServiceType().getName()
                +" appointment has been cancelled.";
        String appointmentSucessMessage = "<b>Confirmed!</b> You have booked a "
                +appointment.getServiceType().getName()+" service for "
                +appointment.getPet().getName()+" with "+customerRetailerDetails.get("retailerName")
                +" on "+appointment.getDate()+" at "+userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime())
                +" has been cancelled .<br>Thank you,<br>Your team at,<br><b> "
                +customerRetailerDetails.get("retailerName")+"</b><br>";
        String mailToCustomer = customerRetailerDetails.get("customerEmail");
        String mailCcStore = customerRetailerDetails.get("retailerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailToCustomer,mailFromRetailer,mailCcStore);
    }

    public void endServiceMailNotification(ServiceHistoryDto serviceHistoryDto ,Appointment appointment,ServiceHistory serviceHistory) throws EtailBookItException {
        LOGGER.info("@@@@Enterd endServiceMailNotification function");
        Integer customerId = serviceHistory.getAppointment().getCustomerId();
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,appointment.getRetailer());
        String appointmentSubject = "Your "+appointment.getServiceType().getName()
                +" appointment has been completed.";
        String appointmentSucessMessage = "<b>Confirmed!</b> You have booked a "
                +appointment.getServiceType().getName()+ " service for "+appointment.getPet().getName()
                +" with "+customerRetailerDetails.get("retailerName")+"  has been complete."
                +"<br>Please see the service details below.<br><br>Shampoo:<b> "
                +serviceHistory.getAppointment().getShamppo().getName()+"</b><br>Cologne: <b>"
                +serviceHistory.getAppointment().getCologne().getName()
                +" </b><br>Thank you,<br>Your team at<br><b> "
                +customerRetailerDetails.get("retailerName")+"</b><br>";

        String mailToCustomer = customerRetailerDetails.get("customerEmail");
        String mailCcStore = customerRetailerDetails.get("retailerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailToCustomer,mailFromRetailer,mailCcStore);
    }

    public void serviceRemainderMail(Integer customerId, String serviceName, OffsetTime time, String retailer,String petName) throws EtailBookItException {
        LOGGER.info("@@@@Enterd serviceRemainderMail function");
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,retailer);
        String appointmentSubject = "Reminder! Your "+serviceName
                +" appointment for "+petName+" with  "+customerRetailerDetails.get("retailerName")+" is scheduled tomorrow at "
                +time.toLocalTime().format(dateTimeFormatter)+".";
        String appointmentSucessMessage = "<b>Reminder!</b> Your "+serviceName
                +" appointment for "+petName+" with"+customerRetailerDetails.get("retailerName")
                +" is scheduled tomorrow at "+time.toLocalTime().format(dateTimeFormatter)
                +".<br> Click here to change or cancel your appointment   "
                +"<a href=\"https://bookit-dev.etailpet.com/retailer/booking/schedule/create\" class=\"btn .btn-warning\">My Schedule</a> " +
                "<br>Thank you,<br>Your team at <br><b>"+customerRetailerDetails.get("retailerName")+"</b></br>";
        String mailTo = customerRetailerDetails.get("customerEmail");
        String mailCc = customerRetailerDetails.get("retailerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailTo,mailFromRetailer,mailCc);
    }

    public void vaccinationRemainderMail(Integer customerId, LocalDate dateExpiry, String petVaccinationName,String petName, Optional<Pet> petData) throws EtailBookItException {
        LOGGER.info("@@@@Enterd vaccinationRemainderMail function");
        HashMap<String, String> customerRetailerDetails = getAcessTokenAndCustomerEmailId(customerId,petData.get().getRetailer());
        String appointmentSubject = "The "+ petVaccinationName
                + " for "+ petName +" will expire soon!";
        String appointmentSucessMessage = "<b>Vaccination Reminder!</b> The "
                +petVaccinationName
                +" for "+petName+" will expire on "+dateExpiry
                +". Please upload your new vaccination record. Thank you,<br>Your team at </br><br><b>"
                +customerRetailerDetails.get("retailerName")+"</b></br>";
        String mailTo = customerRetailerDetails.get("customerEmail");
        String mailCc = customerRetailerDetails.get("retailerEmail");
        String mailFromRetailer = customerRetailerDetails.get("retailerEmail");
        sentEmail(appointmentSucessMessage,appointmentSubject,mailTo,mailFromRetailer,mailCc);
    }
    public HashMap<String, String> getAcessTokenAndCustomerEmailId(Integer customerId,String retailer) throws EtailBookItException {
        LOGGER.info("@@@@Enterd getAcessTokenAndCustomerEmailId function");
        //get access token from Etail plus
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(retailer);
        String acccessToken = authToken.getBody().getAccess_token();

        //get retailer info from ecom
        ResponseEntity<EcommRetailerDetails> retailerDeails = authenticationEcom.getRetailerDetails(acccessToken,retailer);
        String retailerName = retailerDeails.getBody().getName();
        String retailerEmail = retailerDeails.getBody().getEmail();
        //get customer email from ecom
        ResponseEntity<EcommCustomerDetails> customerDetails = authenticationEcom.getCustomerDeails(customerId, acccessToken, retailer);
        String mailTo = customerDetails.getBody().getEmail();
        String customerFirstName = customerDetails.getBody().getFirstName();
        String customerLastName = customerDetails.getBody().getLastName();
        HashMap<String, String> map = new HashMap<>();
        map.put("customerEmail" ,mailTo);
        map.put("customerName", customerFirstName + " " +customerLastName);
        map.put("retailerName", retailerName);
        map.put("retailerEmail", retailerEmail);
        return map;
    }
    public void sentEmail(String appointmentSucessMessage ,String appointmentSubject,String mailTo , String mailFrom ,String mailCc){
        LOGGER.info("@@@@Enterd sentEmail function");
        EmailRequest emailRequest = new EmailRequest();
        emailRequest.setText(appointmentSucessMessage);
        emailRequest.setSubject(appointmentSubject);
        emailRequest.setMailTo(mailTo);
        emailRequest.setMailFrom(mailFrom);
        emailRequest.setCc(mailCc);
        //sent mail
        mailNotificationService.sendMail(emailRequest);
    }
}
