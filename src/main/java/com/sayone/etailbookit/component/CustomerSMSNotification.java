package com.sayone.etailbookit.component;

import com.amazonaws.services.s3.AmazonS3;
import com.sayone.etailbookit.Batch.AppointmentReaderConfiguration;
import com.sayone.etailbookit.Batch.AppointmentWriterConfiguration;
import com.sayone.etailbookit.dto.ActivityLogDto;
import com.sayone.etailbookit.dto.AppointmentDto;
import com.sayone.etailbookit.dto.SMSNotificationDto;
import com.sayone.etailbookit.dto.ServiceHistoryDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.AuthResponse;
import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.ServiceHistory;
import com.sayone.etailbookit.util.*;
import com.sayone.etailbookit.repository.TimeSlotRepository;
import com.sayone.etailbookit.model.TimeSlots;
import com.sayone.etailbookit.model.Attendant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import com.sayone.etailbookit.model.EcommCustomerDetailsResponse;
import com.sayone.etailbookit.model.EcommCustomerDetails;
import com.sayone.etailbookit.model.EcommRetailerDetails;

@Component
public class CustomerSMSNotification {
    @Autowired
    AuthenticationEcom authenticationEcom;

    @Autowired
    UserTimeZoneConversion userTimeZoneConversion;

    @Autowired
    AppointmentWriterConfiguration appointmentWriterConfiguration;

    @Autowired
    AppointmentReaderConfiguration appointmentReaderConfiguration;

    @Value("${ecom.auth.uri}")
    String auth_Uri;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    DataFileProperties dataFileProperties;

    @Autowired
    TimeSlotRepository timeSlotRepository;

    RestTemplate restTemplate = new RestTemplate();
    private static Logger LOGGER = LoggerFactory.getLogger(CustomerSMSNotification.class);

    public void setAppointmentSmsNotiication(Appointment appointment,String code,String timeZone) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        //String url="https://posweb.dev.etailpet.com/sandyspetdepot/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto= new SMSNotificationDto();
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setCode(code);
        /*smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToUserDate(appointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTimeZone(appointment.getAppointmentStartDateAndTime(),timeZone));*/
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setPet_name(appointment.getPet().getName());
        smsNotificationDto.setService(appointment.getService().getName());
        smsNotificationDto.setService_type(appointment.getServiceType().getName());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName()+appointment.getAttendant().getLastName());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());

    }

    public void endServiceSMSNotification(ServiceHistoryDto serviceHistoryDto, Appointment appointment, ServiceHistory serviceHistory) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        //String url="https://posweb.dev.etailpet.com/sandyspetdepot/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto=new SMSNotificationDto();
        smsNotificationDto.setService_type(appointment.getServiceType().getName());
      //  smsNotificationDto.setShampoo(serviceHistoryDto.getPetShampoo().getName());
        //smsNotificationDto.setCologne(serviceHistoryDto.getPetCologne().getName());
        smsNotificationDto.setPet_name(appointment.getPet().getName());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName()+appointment.getAttendant().getLastName());
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_COMPLETION");
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());

    }

    public void serviceRemainderSMS(Integer customerId, String serviceType, OffsetTime time, String retailer, String petName, Integer appointmentId, String attendantName, String OffsetStartTime) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+retailer+"/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto=new SMSNotificationDto();
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_REMINDER_1_DAY");
        smsNotificationDto.setService_type(serviceType);
        smsNotificationDto.setPet_name(petName);
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(OffsetStartTime));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(time.toLocalTime()));
        smsNotificationDto.setCustomer_id(customerId);
        smsNotificationDto.setVendor_name(attendantName);
        smsNotificationDto.setStore_name(retailer);
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }


    public void serviceChangeSMSNotification(Appointment existingAppointment,Appointment currentAppointment,String timeZone) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto=new SMSNotificationDto();
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_RESCHEDULED");
        smsNotificationDto.setService_type(existingAppointment.getServiceType().getName());
        smsNotificationDto.setPet_name(existingAppointment.getPet().getName());
        /*smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTimeZone(currentAppointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToUserDate(currentAppointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setDate_exist(userTimeZoneConversion.convertToUserDate(existingAppointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setTime_exist(userTimeZoneConversion.convertToUserTimeZone(existingAppointment.getAppointmentStartDateAndTime(),timeZone));*/
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(currentAppointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(currentAppointment.getTime().toLocalTime()));
        smsNotificationDto.setDate_exist(userTimeZoneConversion.convertToOffsetDate(existingAppointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setTime_exist(userTimeZoneConversion.convertToUserTime(existingAppointment.getTime().toLocalTime()));
        smsNotificationDto.setCustomer_id(existingAppointment.getCustomerId());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setOld_vendor_name(existingAppointment.getAttendant().getFirstName()+existingAppointment.getAttendant().getLastName());
        smsNotificationDto.setVendor_name(currentAppointment.getAttendant().getFirstName()+currentAppointment.getAttendant().getLastName());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    public void cancelServiceSmsNotification(Appointment appointment,String timeZone) throws EtailBookItException{
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto=new SMSNotificationDto();
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_CANCELLATION");
        smsNotificationDto.setService_type(appointment.getServiceType().getName());
        smsNotificationDto.setPet_name(appointment.getPet().getName());
       /* smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTimeZone(appointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToUserDate(appointment.getAppointmentStartDateAndTime(),timeZone));*/
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName()+appointment.getAttendant().getLastName());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    public void vaccinationRemainderSMS(Integer customerId, LocalDate dateExpiry, String petVaccinationName, String petName, Optional<Pet> petData) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto=new SMSNotificationDto();
        smsNotificationDto.setCode("VACCINATION_REMINDER");
        smsNotificationDto.setPet_name(petName);
        smsNotificationDto.setCustomer_id(customerId);
        smsNotificationDto.setVaccination_name(petVaccinationName);
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setVaccination_expiry(dateExpiry.toString());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    public void appointmentApprove(Appointment appointment)throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url = auth_Uri + RetailerContext.getRetailer() + "/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_REQUEST");
        smsNotificationDto.setService_type(appointment.getServiceType().getName());
        smsNotificationDto.setPet_name(appointment.getPet().getName());
        smsNotificationDto.setAppointment_id(appointment.getId());
       /* smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTimeZone(appointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToUserDate(appointment.getAppointmentStartDateAndTime(),timeZone));*/
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName() + appointment.getAttendant().getLastName());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    public void appointmentReject(Appointment appointment) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url = auth_Uri + RetailerContext.getRetailer() + "/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
        smsNotificationDto.setCode("BOOKIT_APPOINTMENT_REJECTED");
        smsNotificationDto.setService_type(appointment.getServiceType().getName());
        smsNotificationDto.setPet_name(appointment.getPet().getName());
       /* smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTimeZone(appointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToUserDate(appointment.getAppointmentStartDateAndTime(),timeZone));*/
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setRejection_reason(appointment.getRejectionReason());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName() + appointment.getAttendant().getLastName());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    public void sendCorrectedTimeAfterDST(Appointment appointment) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+appointment.getRetailer()+"/api/v1/messages/send/";
        //String url="https://posweb.dev.etailpet.com/sandyspetdepot/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema",appointment.getRetailer());
        SMSNotificationDto smsNotificationDto= new SMSNotificationDto();
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setCode("ADHOC_EMAIL");
        smsNotificationDto.setSubject("Previous email confirmation appointment time in UTC time zone");
       // smsNotificationDto.setMessage("As the Daylight savings period ends on Nov 3rd, the updated time for your " +appointment.getService().getName()+" appointment for "+appointment.getPet().getName()+"  on " +userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime())+ " is "+userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()) +" PDT.");
        smsNotificationDto.setMessage("We have been made aware our most recent email had the time of the appointment reflected in UTC time zone rather than local time, we are resending the email once more with your appointment information reflected in local time.\n" +
                "We apologize for the confusion. \n" +
                "\n" +
                "Please see below for the accurate appointment time and date.\n" +
                "Service: "+appointment.getService().getName()+", "+
                "Pet: "+appointment.getPet().getName());
        /*smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToUserDate(appointment.getAppointmentStartDateAndTime(),timeZone));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTimeZone(appointment.getAppointmentStartDateAndTime(),timeZone));*/
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setPet_name(appointment.getPet().getName());
        smsNotificationDto.setService(appointment.getService().getName());
        smsNotificationDto.setService_type(appointment.getServiceType().getName());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setAppointment_id(appointment.getId());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName()+appointment.getAttendant().getLastName());
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());

    }

    public void exportAppointments(byte[] appointments,String retailer) throws Exception {
        LOGGER.info(" ***** Entered email sending of appointment export of the retailer- {}",retailer);
        LocalDate today=LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d MMM uuuu");
        String formattedDate = today.format(formatter);
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(retailer);
        String accessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+retailer+"/api/v1/messages/send/";
        // Prepare Headers
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        headers.add("X-TenantSchema", retailer);

        // Logging
        LOGGER.info("Authorization: {}", accessToken);
        LOGGER.info("Auth Token: {}", authToken);

        // Prepare JSON Data
        SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
        smsNotificationDto.setCode("DATA_REPORT");
        smsNotificationDto.setSubject("Daily Appointments "+formattedDate);
        smsNotificationDto.setMessage("Please see the attached file to view the appointments scheduled for today  " + formattedDate);

        // Prepare Multipart Request Body
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

        // JSON Data Part
        HttpHeaders jsonHeaders = new HttpHeaders();
        jsonHeaders.setContentType(MediaType.APPLICATION_JSON);
        body.add("jsonData", new HttpEntity<>(smsNotificationDto, jsonHeaders));

        // Excel File Part
        ByteArrayResource excelResource = new ByteArrayResource(appointments) {
            @Override
            public String getFilename() {
                return "appointments.xlsx"; // Ensure filename is sent
            }
        };
        HttpHeaders excelHeaders = new HttpHeaders();
        excelHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        excelHeaders.setContentDisposition(
                ContentDisposition.builder("form-data").name("file").filename("appointments.xlsx").build());
        body.add("file", new HttpEntity<>(excelResource, excelHeaders));

        // Final Request
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // Send Request
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        // Handle Response
        if (response.getStatusCode() != HttpStatus.OK) {
            LOGGER.info("ECOM API Failed: Response {} - {}", response.getBody(), response.getStatusCode());
            saveExportToS3(appointments, retailer);
        }
        else
            LOGGER.info("ECOM API Success: Response {} - {}", response.getBody(), response.getStatusCode());

    }

    private void saveExportToS3(byte[] appointments, String retailer) throws Exception {
            String fileName="appointment.xlsx";
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            MultipartFile appointmentExcel =ByteArrayToMultipartFile.convert(appointments,fileName,contentType);
            String awsUrl = FileUploadUtil.uploadFile(appointmentExcel, dataFileProperties.getBucketName(), amazonS3, dataFileProperties.getAwsEndpoint(), dataFileProperties.getAppointmentDailyExportPrefix(), FileType.ExcelFile);
            ResponseEntity<HashMap> response= sendFileToActivityLog(awsUrl,retailer);
            if(response.getStatusCode() ==HttpStatus.OK){
                sendEmailRegardingExcelInActivityLog(retailer);
            }

    }

    private void sendEmailRegardingExcelInActivityLog(String retailer) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+retailer+"/api/v1/messages/send/";
        //String url="https://posweb.dev.etailpet.com/sandyspetdepot/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema",retailer);
        SMSNotificationDto smsNotificationDto= new SMSNotificationDto();
        smsNotificationDto.setCode("ADHOC_EMAIL");
        smsNotificationDto.setSubject("Daily Appointment Export in ActivityLog");
        smsNotificationDto.setMessage("We encountered an error in sending the excel export of the list of appointments scheduled today. Please see the activity log of the ProductManager for the excel");
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    private ResponseEntity<HashMap> sendFileToActivityLog(String awsUrl, String retailer) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+"/batch/api/v1/products/create/activity-log";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema",retailer);
        ActivityLogDto activityLogDto=new ActivityLogDto();
        activityLogDto.setSchema(retailer);
        activityLogDto.setEmail("");
        activityLogDto.setDetails("BOOKIT_DAILY_APPOINTMENT_EXPORT");
        activityLogDto.setFileUrl(awsUrl);
        activityLogDto.setUserName("");
        HttpEntity<ActivityLogDto> request = new HttpEntity<>(activityLogDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("POS API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("POS API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());

        return responseEntity;
    }

    private HttpHeaders getJsonHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    private HttpHeaders getExcelHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.builder("attachment").filename("appointments.xlsx").build());
        return headers;
    }


    public void exportAppointmentsForSelectedDate(byte[] appointments, String retailer, OffsetDateTime selectedDate) throws Exception {
        LOGGER.info(" ***** Entered email sending of appointment export of the retailer of the selected date - {}",retailer);
        LocalDate today= selectedDate.toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d MMM uuuu");
        String formattedDate = today.format(formatter);
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(retailer);
        String accessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+retailer+"/api/v1/messages/send/";
        // Prepare Headers
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        headers.add("X-TenantSchema", retailer);

        // Logging
        LOGGER.info("Authorization: {}", accessToken);
        LOGGER.info("Auth Token: {}", authToken);

        // Prepare JSON Data
        SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
        smsNotificationDto.setCode("DATA_REPORT");
        smsNotificationDto.setSubject("Daily Appointments "+formattedDate);
        // smsNotificationDto.setMessage("PFA the excel export of today's appointment of the retailer :: " + retailer);

        // Prepare Multipart Request Body
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

        // JSON Data Part
        HttpHeaders jsonHeaders = new HttpHeaders();
        jsonHeaders.setContentType(MediaType.APPLICATION_JSON);
        body.add("jsonData", new HttpEntity<>(smsNotificationDto, jsonHeaders));

        // Excel File Part
        ByteArrayResource excelResource = new ByteArrayResource(appointments) {
            @Override
            public String getFilename() {
                return "appointments.xlsx"; // Ensure filename is sent
            }
        };
        HttpHeaders excelHeaders = new HttpHeaders();
        excelHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        excelHeaders.setContentDisposition(
                ContentDisposition.builder("form-data").name("file").filename("appointments.xlsx").build());
        body.add("file", new HttpEntity<>(excelResource, excelHeaders));

        // Final Request
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // Send Request
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        // Handle Response
        if (response.getStatusCode() != HttpStatus.OK) {
            LOGGER.info("ECOM API Failed: Response {} - {}", response.getBody(), response.getStatusCode());
            saveExportToS3(appointments, retailer);
        }
        else
            LOGGER.info("ECOM API Success: Response {} - {}", response.getBody(), response.getStatusCode());
    }

    public void notifyRetailerOnWaiverSignature(Integer petId) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        //String url="https://posweb.dev.etailpet.com/sandyspetdepot/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authorization:::"+acccessToken);
        LOGGER.info("@@@@@@@@@@@@@@@@@@@@ authToken::"+authToken);
        headers.add("X-TenantSchema",RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto= new SMSNotificationDto();
        smsNotificationDto.setCode("ADHOC_EMAIL");
        smsNotificationDto.setSubject("Customer signed the waiver of liability ");
        smsNotificationDto.setMessage("The customer has signed the waiver of liability for the pet with pet id "+petId);
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());

    }

    public void slotReleaseNotification(Appointment appointment, String timeZone) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
        smsNotificationDto.setCode("ADHOC_EMAIL");
        
        smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
        smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        smsNotificationDto.setVendor_name(appointment.getAttendant().getFirstName()+appointment.getAttendant().getLastName());
        
        // Add slot start time if time slot exists
        if (appointment.getTimeSlotId() != null) {
            try {
                Optional<TimeSlots> timeSlotOptional = timeSlotRepository.findById(appointment.getTimeSlotId());
                if (timeSlotOptional.isPresent()) {
                    com.sayone.etailbookit.model.TimeSlots timeSlot = timeSlotOptional.get();
                    if (timeSlot.getSlotStartTime() != null) {
                        smsNotificationDto.setSlot_start_time(userTimeZoneConversion.convertToUserTime(timeSlot.getSlotStartTime().toLocalTime()));
                    }
                }
            } catch (Exception e) {
                LOGGER.warn("Could not fetch time slot information for appointment {}: {}", appointment.getId(), e.getMessage());
            }
        }
        
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }

    /**
     * Send Google Calendar sync invitation email to attendant
     * @param attendant The attendant to send invitation to
     * @param authorizationUrl The Google OAuth authorization URL
     * @throws EtailBookItException if notification fails
     */
    public void sendGoogleCalendarSyncInvitation(Attendant attendant, String authorizationUrl) throws EtailBookItException {
        LOGGER.info("@@@@Entered sendGoogleCalendarSyncInvitation for attendant: {}", attendant.getAttendantId());
        
        try {
            ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
            String acccessToken = authToken.getBody().getAccess_token();
            String url = auth_Uri + RetailerContext.getRetailer() + "/api/v1/messages/send/";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Authorization", "Bearer " + acccessToken);
            headers.add("X-TenantSchema", RetailerContext.getRetailer());
            
            SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
            smsNotificationDto.setCode("ADHOC_EMAIL");
            smsNotificationDto.setSubject("Google Calendar Sync Invitation - " + attendant.getFirstName() + " " + attendant.getLastName());
            smsNotificationDto.setMessage("You have been invited to collaborate on Google Calendar. Please click the link below to accept the invite: " + authorizationUrl + "\n\nAttendant Email: " + attendant.getEmail());
            smsNotificationDto.setCustomer_id(attendant.getAttendantId());
            
            HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
            ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
            
            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
            } else {
                LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
            }
            
        } catch (Exception e) {
            LOGGER.error("Error sending Google Calendar sync invitation for attendant {}: {}", 
                        attendant.getAttendantId(), e.getMessage(), e);
            throw new EtailBookItException("Failed to send Google Calendar sync invitation: " + e.getMessage());
        }
    }
    
    /**
     * Send notification to waitlist customers when a slot becomes available
     * @param appointment The appointment object containing customer and slot information
     * @param timeZone The timezone for notification formatting
     * @param timeSlot The time slot that became available
     * @throws EtailBookItException if notification fails
     */
    public void sendWaitlistAvailabilityNotification(Appointment appointment, String timeZone, TimeSlots timeSlot) throws EtailBookItException {
        ResponseEntity<AuthResponse> authToken = authenticationEcom.getAuthentication(RetailerContext.getRetailer());
        String acccessToken = authToken.getBody().getAccess_token();
        String url =  auth_Uri+RetailerContext.getRetailer()+"/api/v1/messages/send/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer "+acccessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        SMSNotificationDto smsNotificationDto = new SMSNotificationDto();
        smsNotificationDto.setCode("ADHOC_EMAIL");
        smsNotificationDto.setSubject("Slot is now available for booking");
        
        // Get customer information from external API
        String customerName = "Customer";
        try {
            ResponseEntity<EcommCustomerDetails> customerDetailsResponse = authenticationEcom.getCustomerDeails(
                appointment.getCustomerId(), 
                acccessToken, 
                RetailerContext.getRetailer()
            );
            if (customerDetailsResponse.getBody() != null && customerDetailsResponse.getBody()!= null) {
                EcommCustomerDetails customerDetails = customerDetailsResponse.getBody();
                customerName = customerDetails.getFirstName() + " " + customerDetails.getLastName();
            }
        } catch (Exception e) {
            LOGGER.warn("Could not fetch customer information for customer ID {}: {}", appointment.getCustomerId(), e.getMessage());
        }
        
        // Format time and date information
        String timeAndDate = "";
        if (timeSlot != null && timeSlot.getSlotStartTime() != null) {
            String slotTime = userTimeZoneConversion.convertToUserTimeZone(timeSlot.getSlotStartTime(),timeZone);
            String slotDate = userTimeZoneConversion.convertToUserDate(timeSlot.getSlotStartTime(),timeZone);
            timeAndDate = slotTime + " on " + slotDate;
        } else {
            String appointmentTime = userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime());
            String appointmentDate = userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime());
            timeAndDate = appointmentTime + " on " + appointmentDate;
        }
        
        // Get store information
        String storeName = RetailerContext.getRetailer();
        // Fetch store phone from retailer details API, fallback to default if not found
        String storePhone = "+13155632009"; // Default Twilio number as fallback
        try {
            ResponseEntity<EcommRetailerDetails> retailerDetailsResponse = authenticationEcom.getRetailerDetails(
                acccessToken,
                RetailerContext.getRetailer()
            );
            if (retailerDetailsResponse.getBody() != null && retailerDetailsResponse.getBody().getPhone() != null
                && !retailerDetailsResponse.getBody().getPhone().trim().isEmpty()) {
                storePhone = retailerDetailsResponse.getBody().getPhone();
            }
        } catch (Exception e) {
            LOGGER.warn("Could not fetch retailer phone for retailer {}: {}", RetailerContext.getRetailer(), e.getMessage());
        }
        // Build the dynamic message
        String message = String.format("The slot at %s you are on the waitlist for with %s is now available for booking, please call %s to book your slot now!",
                timeAndDate, storeName, storePhone);

        smsNotificationDto.setMessage(message);
        
      //  smsNotificationDto.setAppointment_date(userTimeZoneConversion.convertToOffsetDate(appointment.getAppointmentStartDateAndTime()));
      //  smsNotificationDto.setAppointment_time(userTimeZoneConversion.convertToUserTime(appointment.getTime().toLocalTime()));
        smsNotificationDto.setCustomer_id(appointment.getCustomerId());
        smsNotificationDto.setStore_name(RetailerContext.getRetailer());
        
        // Add slot information
        if (timeSlot != null) {
            if (timeSlot.getSlotStartTime() != null) {
                smsNotificationDto.setSlot_start_time(userTimeZoneConversion.convertToUserTime(timeSlot.getSlotStartTime().toLocalTime()));
            }
            if (timeSlot.getSlotName() != null) {
                smsNotificationDto.setService_type(timeSlot.getSlotName());
            }
        }
        
        HttpEntity<SMSNotificationDto> request = new HttpEntity<>(smsNotificationDto, headers);
        ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, HashMap.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK)
            LOGGER.info("ECOM API Failed: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
        else
            LOGGER.info("ECOM API Success: Response {} - {}", responseEntity.getBody(), responseEntity.getStatusCode());
    }
}
