package com.sayone.etailbookit.component;

import com.amazonaws.services.kinesis.AmazonKinesis;
import com.amazonaws.services.kinesis.model.*;
import com.amazonaws.services.kinesis.model.Record;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayone.etailbookit.dto.ServiceSlotsDto;
import com.sayone.etailbookit.event.SlotCreationEvent;
import com.sayone.etailbookit.event.SlotUpdateEvent;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.KinesisCheckpoint;
import com.sayone.etailbookit.model.SkippedRecord;
import com.sayone.etailbookit.model.TimeSlotCluster;
import com.sayone.etailbookit.model.TimeSlots;
import com.sayone.etailbookit.repository.CheckpointRepository;
import com.sayone.etailbookit.repository.SkippedRecordRepository;
import com.sayone.etailbookit.repository.TimeSlotClusterRepository;
import com.sayone.etailbookit.repository.TimeSlotRepository;
import com.sayone.etailbookit.service.impl.TimeSlotService;
import lombok.var;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;


import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;

@EnableAsync
@Component
public class SlotCreationConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(SlotCreationConsumer.class);

    @Value("${kinesis.stream-name}")
    private String streamName;

    @Value("${kinesis.shard.id.slots}")
    private String slotGenerationShardId; // Read the shard ID from environment variable

    private final AmazonKinesis kinesisClient;
    private final TimeSlotService slotCreationService;
    private final ObjectMapper objectMapper;
    private final TimeSlotClusterRepository timeSlotClusterRepository;
    private final CheckpointRepository checkpointRepository;
    private final SkippedRecordRepository skippedRecordRepository;

    public SlotCreationConsumer(AmazonKinesis kinesisClient,
                                TimeSlotService slotCreationService,
                                ObjectMapper objectMapper,
                                TimeSlotClusterRepository timeSlotClusterRepository,
                                CheckpointRepository checkpointRepository,
                                SkippedRecordRepository skippedRecordRepository, TimeSlotRepository timeSlotRepository) {
        this.kinesisClient = kinesisClient;
        this.slotCreationService = slotCreationService;
        this.objectMapper = objectMapper;
        this.timeSlotClusterRepository = timeSlotClusterRepository;
        this.checkpointRepository = checkpointRepository;
        this.skippedRecordRepository = skippedRecordRepository;
    }

    // Thread-safe queue to hold slot creation and update events
    private final BlockingQueue<Object> slotEventQueue = new LinkedBlockingQueue<>();

    @Async
    @EventListener
    public void handleSlotCreationEvent(SlotCreationEvent event) {
        try {
            LOGGER.info("Received SlotCreationEvent: Attendant {} in cluster {}",
                    event.getAttendantId(), event.getTimeSlotClusterId());

            enqueueEvent(event);
        } catch (Exception e) {
            LOGGER.error("Error processing SlotCreationEvent", e);
        }
    }

    @Async
    @EventListener
    public void handleSlotUpdateEvent(SlotUpdateEvent event) {
        try {
            LOGGER.info("Received SlotUpdateEvent: Updating slots for attendant {} in cluster {}",
                    event.getAttendantId(), event.getTimeSlotClusterId());

            enqueueEvent(event);
        } catch (Exception e) {
            LOGGER.error("Error processing SlotUpdateEvent", e);
        }
    }

    private void enqueueEvent(Object event) {
        LOGGER.info("Event received ");
        slotEventQueue.offer(event);
    }

    @PostConstruct
    //@EventListener(ApplicationReadyEvent.class)
    public void initKinesisPolling() {
        LOGGER.info("Starting Kinesis polling thread...");
        new Thread(this::pollKinesisStream).start();
    }

    // @Scheduled(fixedDelay = 5000)
    public void pollKinesisStream() {
        try {
            while (true) {
                LOGGER.info("Polling Kinesis stream...");
                // Wait for a signal that new slots need processing
                Object event = slotEventQueue.take(); // blocks until event is added
                LOGGER.info("Event received inside polling kinesis stream: {}", event);
                if (event instanceof SlotCreationEvent) {
                    LOGGER.info("Processing SlotCreationEvent...");
                    processSlotCreation((SlotCreationEvent) event);
                } else if (event instanceof SlotUpdateEvent) {
                    LOGGER.info("Processing SlotUpdateEvent...");
                    processSlotUpdate((SlotUpdateEvent) event);
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("Kinesis polling thread interrupted", e);
        } catch (Exception e) {
            LOGGER.error("Error in Kinesis polling loop", e);
        }
    }

    private void processSlotUpdate(SlotUpdateEvent event) {
        LOGGER.info("Handling slot update for attendant {} in cluster {}",
                event.getAttendantId(), event.getTimeSlotClusterId());

        DescribeStreamRequest describeStreamRequest = new DescribeStreamRequest().withStreamName(streamName);
        List<Shard> shards = kinesisClient.describeStream(describeStreamRequest)
                .getStreamDescription()
                .getShards();

        // Filter for the configured shard ID
        String formattedShardId = "shardId-" + String.format("%012d", Integer.parseInt(slotGenerationShardId));
        LOGGER.info("Formatted shard ID: {}", formattedShardId);
        Shard targetShard = shards.stream()
                .filter(shard -> shard.getShardId().equals(formattedShardId))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("Configured shard ID not found: " + slotGenerationShardId));

        LOGGER.info("Processing slot update for shard: {}", targetShard.getShardId());

        String shardIterator = getShardIterator(targetShard.getShardId());

        while (shardIterator != null) {
            try {
                GetRecordsRequest getRecordsRequest = new GetRecordsRequest()
                        .withShardIterator(shardIterator)
                        .withLimit(25);

                GetRecordsResult result = kinesisClient.getRecords(getRecordsRequest);
                List<Record> records = result.getRecords();
                LOGGER.info("Fetched {} records from Kinesis for slot update", records.size());

                if (!records.isEmpty()) {
                    processRecords(records, targetShard.getShardId());
                    String lastSequenceNumber = records.get(records.size() - 1).getSequenceNumber();
                    saveCheckpoint(targetShard.getShardId(), lastSequenceNumber);
                }

                // Get next shard iterator
                shardIterator = result.getNextShardIterator();

                if (shardIterator == null) {
                    LOGGER.warn("Shard iterator is null. Ending slot update consumption.");
                    break;
                }

                Thread.sleep(2000); // Delay to prevent excessive requests

            } catch (ExpiredIteratorException ex) {
                LOGGER.warn("Shard iterator expired in slot update . Fetching new iterator from checkpoint...");
                shardIterator = getShardIterator(targetShard.getShardId()); // Reset from checkpoint
            } catch (Exception e) {
                LOGGER.error("Error while fetching records from Kinesis for slot update", e);
            }
        }
    }


    /*public void signalSlotCreation() {
        slotCreationSignalQueue.offer(Boolean.TRUE);
    }*/
    private void processSlotCreation(SlotCreationEvent event) {
        LOGGER.info("Handling slot creation for attendant {} in cluster {}",
                event.getAttendantId(), event.getTimeSlotClusterId());

        DescribeStreamRequest describeStreamRequest = new DescribeStreamRequest().withStreamName(streamName);
        List<Shard> shards = kinesisClient.describeStream(describeStreamRequest)
                .getStreamDescription()
                .getShards();
        // Filter for the configured shard ID
        String formattedShardId = "shardId-" + String.format("%012d", Integer.parseInt(slotGenerationShardId));
        LOGGER.info("Formatted shard ID: {}", formattedShardId);
        Shard targetShard = shards.stream()
                .filter(shard -> shard.getShardId().equals(formattedShardId))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("Configured shard ID not found: " + slotGenerationShardId));

        LOGGER.info("Processing shard: {}", targetShard.getShardId());

        String shardIterator = getShardIterator(targetShard.getShardId());

        while (shardIterator != null) {
            try {
                GetRecordsRequest getRecordsRequest = new GetRecordsRequest()
                        .withShardIterator(shardIterator)
                        .withLimit(25);

                GetRecordsResult result = kinesisClient.getRecords(getRecordsRequest);
                List<Record> records = result.getRecords();
                LOGGER.info("Fetched {} records from Kinesis", records.size());


                if (!records.isEmpty()) {
                    processRecords(records,targetShard.getShardId());
                    String lastSequenceNumber = records.get(records.size() - 1).getSequenceNumber();
                    saveCheckpoint(targetShard.getShardId(), lastSequenceNumber);
                }

                // Get next shard iterator
                shardIterator = result.getNextShardIterator();

                if (shardIterator == null) {
                    LOGGER.warn("Shard iterator is null. Ending consumption.");
                    break;
                }

                Thread.sleep(2000); // Delay to prevent excessive requests

            } catch (ExpiredIteratorException ex) {
                LOGGER.warn("Shard iterator expired. Fetching new iterator from checkpoint...");
                shardIterator = getShardIterator(targetShard.getShardId());
            } catch (Exception e) {
                LOGGER.error("Error while fetching records from Kinesis", e);
            }
        }
    }
    private String getShardIterator(String shardId) {
        Optional<KinesisCheckpoint> checkpoint = checkpointRepository.findByStreamNameAndShardId(streamName, shardId);

        if (checkpoint.isPresent()) {
            LOGGER.info("Found checkpoint for shard {}: Sequence number = {}", shardId, checkpoint.get().getSequenceNumber());
            return getShardIteratorFromCheckpoint(shardId, checkpoint.get().getSequenceNumber());
        } else {
            LOGGER.warn("No checkpoint found for shard {}. Starting from TRIM_HORIZON.",shardId);
            return getInitialShardIterator(shardId);
        }
    }
    private String getShardIteratorFromCheckpoint(String shardId, String sequenceNumber) {
        LOGGER.info("Entering getShardIteratorFromCheckpoint");
        GetShardIteratorRequest shardIteratorRequest = new GetShardIteratorRequest()
                .withStreamName(streamName)
                .withShardId(shardId)
                .withShardIteratorType(ShardIteratorType.AFTER_SEQUENCE_NUMBER)
                .withStartingSequenceNumber(sequenceNumber);

        return kinesisClient.getShardIterator(shardIteratorRequest).getShardIterator();
    }

    private String getInitialShardIterator(String shardId) {
        LOGGER.info("Entering getInitialShardIterator");
        GetShardIteratorRequest shardIteratorRequest = new GetShardIteratorRequest()
                .withStreamName(streamName)
                .withShardId(shardId)
                .withShardIteratorType(ShardIteratorType.LATEST);

        return kinesisClient.getShardIterator(shardIteratorRequest).getShardIterator();
    }

    @Transactional
    private void processRecords(List<Record> records,String shardId) {
        LOGGER.info("Entered  processRecords");
        String lastSequenceNumber = null;

        for (Record record : records) {
            Integer timeSlotClusterId = null;
            try {
                // Extract sequence number
                String sequenceNumber = record.getSequenceNumber();
                LOGGER.info("Processing record with sequence number: {}", sequenceNumber);

                String data = new String(record.getData().array(), StandardCharsets.UTF_8);
                var event = objectMapper.readValue(data, Map.class);

                LOGGER.info("Entering event processing");
                // Process the event
                ServiceSlotsDto serviceSlotsDto = objectMapper.convertValue(event.get("serviceSlotsDto"), ServiceSlotsDto.class);
                Integer attendantId = (Integer) event.get("attendantId");
                timeSlotClusterId = (Integer) event.get("timeSlotClusterId");
                Object retailerRaw = event.get("retailer");
                String retailer;

                if (retailerRaw instanceof String) {
                    retailer = (String) retailerRaw;
                } else if (retailerRaw instanceof byte[]) {
                    retailer = new String((byte[]) retailerRaw, StandardCharsets.UTF_8);
                } else {
                    retailer = objectMapper.convertValue(retailerRaw, String.class);
                }

                System.out.println("Final retailer value: " + retailer + " (Type: " + retailer.getClass().getName() + ")");


                Optional<TimeSlotCluster> timeSlotCluster = timeSlotClusterRepository.findById(timeSlotClusterId);

                if (timeSlotCluster.isPresent()) {
                    LOGGER.info("Entering Slot generation");
                    slotCreationService.generateSlots(attendantId, serviceSlotsDto, timeSlotCluster.get(),retailer);
                } else {
                    throw new EntityNotFoundException("TimeSlotCluster not found with id: " + timeSlotClusterId);
                }

                lastSequenceNumber = record.getSequenceNumber();// Update last sequence number
                LOGGER.info("Processed lastSequenceNumber is {}",lastSequenceNumber);
                LOGGER.info("Slot created for attendantId={} with timeSlotClusterId={}", attendantId,timeSlotClusterId);

            } catch (Exception e) {
                LOGGER.error("Failed to process record: {}", record, e);
                saveSkippedRecord(record, e, timeSlotClusterId);
            }
        }

        // Save the last sequence number as the checkpoint
        if (lastSequenceNumber != null) {
            LOGGER.info("Saving checkpoint for shard {}: {}", shardId, lastSequenceNumber);
            saveCheckpoint(shardId, lastSequenceNumber);
        }
    }
    private void processSlotUpdateEvent(Map<String, Object> event) throws EtailBookItException {
        LOGGER.info("Processing slot update event...");

        ServiceSlotsDto serviceSlotsDto = objectMapper.convertValue(event.get("serviceSlotsDto"), ServiceSlotsDto.class);
        Integer attendantId = (Integer) event.get("attendantId");
        Integer timeSlotClusterId = (Integer) event.get("timeSlotClusterId");
        String retailer = (String) event.get("retailer");

        Optional<TimeSlotCluster> timeSlotCluster = timeSlotClusterRepository.findById(timeSlotClusterId);

        if (timeSlotCluster.isPresent()) {
            // Generate new slots
            slotCreationService.generateSlots(attendantId, serviceSlotsDto, timeSlotCluster.get(), retailer);

            LOGGER.info("Slots updated for attendantId={} in timeSlotClusterId={}", attendantId, timeSlotClusterId);
        } else {
            throw new EntityNotFoundException("TimeSlotCluster not found with id: " + timeSlotClusterId);
        }
    }

    private void saveSkippedRecord(Record record, Exception exception, Integer timeSlotClusterId) {
        String recordData = new String(record.getData().array(), StandardCharsets.UTF_8);
        String errorMessage = exception.getMessage();

        SkippedRecord skippedRecord = new SkippedRecord(recordData, errorMessage, LocalDateTime.now(), timeSlotClusterId);
        skippedRecordRepository.save(skippedRecord);

        LOGGER.warn("Skipped record saved to database: {}", skippedRecord);
    }

    public void reprocessSkippedRecords() {
        List<SkippedRecord> skippedRecords = skippedRecordRepository.findAll();

        for (SkippedRecord skippedRecord : skippedRecords) {
            try {
                String recordData = skippedRecord.getRecordData();
                Record record = objectMapper.readValue(recordData, Record.class); // Recreate the Record object
                processSingleRecord(record); // Retry processing
                skippedRecordRepository.delete(skippedRecord); // Remove record after successful processing
            } catch (Exception e) {
                LOGGER.error("Failed to reprocess skipped record: {}", skippedRecord, e);
            }
        }
    }

    private void saveCheckpoint(String shardId, String sequenceNumber) {
        KinesisCheckpoint checkpoint = checkpointRepository.findByStreamNameAndShardId(streamName, shardId)
                .orElse(new KinesisCheckpoint());
        checkpoint.setStreamName(streamName);
        checkpoint.setShardId(shardId);
        checkpoint.setSequenceNumber(sequenceNumber);
        checkpoint.setUpdatedAt(LocalDateTime.now());

        checkpointRepository.save(checkpoint);
        LOGGER.info("Checkpoint updated for shard {}: {}", shardId, sequenceNumber);
    }


    @Retryable(
            value = { TransientDataAccessException.class, RuntimeException.class },
            maxAttempts = 3,
            backoff = @Backoff(delay = 2000)
    )
    public void processSingleRecord(Record record) throws JsonProcessingException, EtailBookItException {
        try {
            String data = new String(record.getData().array());
            var event = objectMapper.readValue(data, Map.class);

            ServiceSlotsDto serviceSlotsDto = objectMapper.convertValue(event.get("serviceSlotsDto"), ServiceSlotsDto.class);
            Integer attendantId = (Integer) event.get("attendantId");
            Integer timeSlotClusterId = (Integer) event.get("timeSlotClusterId");
            String retailer=(String)event.get("retailer");

            TimeSlotCluster timeSlotCluster = timeSlotClusterRepository.findById(timeSlotClusterId)
                    .orElseThrow(() -> new EntityNotFoundException("TimeSlotCluster not found with id: " + timeSlotClusterId));

            slotCreationService.generateSlots(attendantId, serviceSlotsDto, timeSlotCluster,retailer);

        } catch (Exception e) {
            // Log and rethrow exception to trigger retry
            LOGGER.error("Error processing record: {}", record, e);
            throw e;
        }
    }
}


