package com.sayone.etailbookit.component;

import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.util.DisplayType;
import com.sayone.etailbookit.util.PetTypeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class HouseKeeping {

    @Autowired
    PetRepository petRepository;

    @Autowired
    WeightRangeRepository weightRangeRepository;

    @Autowired
    GeneralPetSizeRepository generalPetSizeRepository;

    @Autowired
    AppointmentRepository appointmentRepository;

    @Autowired
    CustomerEmailNotification customerEmailNotification;

    @Autowired
    PetTypeRepository petTypeRepository;

    @Autowired
    VenuePetTypesRepository venuePetTypesRepository;

    @Autowired
    AttendantPetTypesRepository attendantPetTypesRepository;

    @Autowired
    PetTypeConfigurationRepository petTypeConfigurationRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(HouseKeeping.class);

//    @EventListener(ApplicationReadyEvent.class)
    public void fixPetSize() throws Exception {

        List<Pet> petList = petRepository.findAll();
        HashMap<PetType, List<GeneralPetSize>> petTypeMap = new HashMap<>();

        //get distinct pet types
        petList.forEach(
                p -> {
                    if(!petTypeMap.containsKey(p.getPetType())) {
                        petTypeMap.put(p.getPetType(), new ArrayList<>());
                    }
                }
        );

        //get general pet sizes for each pet type
        petTypeMap.forEach(
                (pt, gps) -> {
                    Set<GeneralPetSize> generalPetSizes = generalPetSizeRepository.findByPetType(pt);
                    List<GeneralPetSize> generalPetSizesSorted = generalPetSizes.stream().sorted(Comparator.comparing(GeneralPetSize::getWeightValue)).collect(Collectors.toList());
                    petTypeMap.put(pt, generalPetSizesSorted);
                }
        );

        petList.forEach(
            pet -> {
                List<GeneralPetSize> generalPetSizes = petTypeMap.get(pet.getPetType());
                if (pet.getWeightRange() != null) {
                    Optional<WeightRange> weightRange = weightRangeRepository.findById(pet.getWeightRange().getWeightRangeId());
                    pet.setWeightRange(weightRange.get());
                    //determining general pet size
                    for (GeneralPetSize generalPetSize: generalPetSizes) {
                        int min = weightRange.get().getMinValue().intValue();
                        int max = weightRange.get().getMaxValue().intValue();
                        int size = generalPetSize.getWeightValue();
                        if(min <= size && size <= max) {
                            pet.setSize(generalPetSize);
                            break;
                        }
                    }
                    if(pet.getSize() == null) {
                        pet.setSize(generalPetSizes.get(generalPetSizes.size() - 1));
                    }
                }
                else if(pet.getExactWeight() != null && !pet.getExactWeight().equals(BigDecimal.ZERO)) {
                    //determining general pet size
                    for (GeneralPetSize generalPetSize: generalPetSizes) {
                        int size = generalPetSize.getWeightValue();
                        if(pet.getExactWeight().intValue() <= size) {
                            pet.setSize(generalPetSize);
                        }
                    }
                }
                petRepository.save(pet);
            }
        );

    }


//    @EventListener(ApplicationReadyEvent.class)
    public void fixCustomerNames( ) throws EtailBookItException {
        //fetch appointments from db
        List<Appointment> appointmentWithoutCustomer = appointmentRepository.findAppointmentsWithoutName();
        Map<Integer, String> idRetailer = new HashMap<Integer, String>();
        Map<Integer, String> idName = new HashMap<Integer, String>();
        for(Appointment appointment : appointmentWithoutCustomer) {
            if (!idRetailer.containsKey(appointment.getCustomerId())) {
                idRetailer.put(appointment.getCustomerId(), appointment.getRetailer());
            }
        }
        //fetch name for customer id
        for (Integer id : idRetailer.keySet()) {
            HashMap<String, String> customerDetails = customerEmailNotification.getAcessTokenAndCustomerEmailId(
                    id, idRetailer.get(id)
            );
            idName.put(id, customerDetails.get("customerName"));
        }
        LOGGER.info(String.valueOf(idName.size()));
        //store to db
        for (Appointment appointment : appointmentWithoutCustomer) {
            appointment.setCustomerName(idName.get(appointment.getCustomerId()));
            appointmentRepository.save(appointment);
        }
    }

//    @EventListener(ApplicationReadyEvent.class)
    public void fixGeneralPetSizes() {
        List<PetType> petTypes = petTypeRepository.getPetTypesWithoutSizes();
        LOGGER.info("No of pet types without size ::" + petTypes.size());
        List<String> sizes = Arrays.asList("Small", "Medium", "Large");
        petTypes.forEach(
                pt -> {
                    Set<GeneralPetSize> generalPetSizes = new HashSet<>();
                    AtomicInteger weight = new AtomicInteger(10);
                    sizes.forEach( s -> {
                        GeneralPetSize generalPetSize = new GeneralPetSize();
                        generalPetSize.setSize(s);
                        generalPetSize.setPetType(pt);
                        generalPetSize.setWeightValue(weight.get());
                        weight.addAndGet(10);
                        generalPetSize.setWeightUnit("KG");
                        generalPetSize.setRetailer(pt.getRetailer());
                        generalPetSizes.add(generalPetSize);
                    });
                    pt.setGeneralPetSizes(generalPetSizes);
                    petTypeRepository.save(pt);
                }
        );
        List<VenuePetTypes> venuePetTypes = venuePetTypesRepository.getVenuePetTypesWithoutSize();
        LOGGER.info("No of venue pet types without size ::" + venuePetTypes.size());
        venuePetTypes.forEach(
                vpt -> {
                    Set<GeneralPetSize> generalPetSizes = generalPetSizeRepository.findByPetType(vpt.getPetType());
                    vpt.setGeneralPetSizes(generalPetSizes);
                    venuePetTypesRepository.save(vpt);
                }
        );
        List<AttendantPetTypes> attendantPetTypes = attendantPetTypesRepository.getAttendantPetTypesWithoutSize();
        LOGGER.info("No of attendant pet types without size ::" + attendantPetTypes.size());
        attendantPetTypes.forEach(
                apt -> {
                    Set<GeneralPetSize> generalPetSizes = generalPetSizeRepository.findByPetType(apt.getPetType());
                    apt.setGeneralPetSizes(generalPetSizes);
                    attendantPetTypesRepository.save(apt);
                }
        );
        List<PetType> petTypesForConfig = petTypeRepository.findAll();
        petTypesForConfig.forEach(
                pt -> {
                    PetTypeConfiguration petTypeConfiguration = petTypeConfigurationRepository.findByNameAndPetTypeAndRetailer(PetTypeConfig.GENERAL_PET_SIZE, pt, "sandyspetdepot");
                    if(petTypeConfiguration == null) {
                        petTypeConfiguration = new PetTypeConfiguration();
                        petTypeConfiguration.setDisplayType(DisplayType.OPTION);
                        petTypeConfiguration.setName(PetTypeConfig.GENERAL_PET_SIZE);
                        petTypeConfiguration.setPetType(pt);
                        petTypeConfiguration.setRetailer("sandyspetdepot");
                        petTypeConfigurationRepository.save(petTypeConfiguration);
                    }
                }
        );
    }

}