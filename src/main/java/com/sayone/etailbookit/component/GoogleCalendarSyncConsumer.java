package com.sayone.etailbookit.component;

import com.sayone.etailbookit.event.AppointmentSyncEvent;
import com.sayone.etailbookit.service.IGoogleCalendarService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@EnableAsync
@Component
public class GoogleCalendarSyncConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleCalendarSyncConsumer.class);

    @Autowired
    private IGoogleCalendarService googleCalendarService;

    /**
     * Handle appointment sync event asynchronously
     * Uses @TransactionalEventListener with AFTER_COMMIT phase to ensure the 
     * calendarSyncInProgress flag is committed to the database BEFORE this handler executes.
     * This prevents race conditions where concurrent requests might not see the flag as true.
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleAppointmentSyncEvent(AppointmentSyncEvent event) {

        Integer attendantId = event.getAttendant().getAttendantId();

        LOGGER.info("@@@@Received AppointmentSyncEvent - Attendant: {}, Retailer: {}, Appointments: {}", 
            attendantId, event.getRetailer(), event.getAppointments().size());
        
        try {
            int syncedCount = googleCalendarService.syncAppointments(
                event.getAttendant(), 
                event.getRetailer(), 
                event.getAppointments()
            );
            LOGGER.info("@@@@Async sync completed successfully for attendant {} - Synced {} appointments", 
                attendantId, syncedCount);
        } catch (Exception e) {
            LOGGER.error("Error during async appointment sync for attendant {}: {}", 
                attendantId, e.getMessage(), e);
        } finally {
            // Always reset the sync flag, even if sync failed
            // This runs in a separate transaction (REQUIRES_NEW) to ensure it commits even if the main sync transaction rolled back
            LOGGER.info("Resetting calendarSyncInProgress flag for attendant {}", attendantId);
            googleCalendarService.resetSyncFlag(attendantId);
        }
    }
}

