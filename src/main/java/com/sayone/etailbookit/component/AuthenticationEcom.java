package com.sayone.etailbookit.component;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.AuthResponse;
import com.sayone.etailbookit.model.EcommCustomerDetails;
import com.sayone.etailbookit.model.EcommCustomerDetailsResponse;
import com.sayone.etailbookit.model.EcommRetailerDetails;
import com.sayone.etailbookit.model.UserAuth;
import com.sayone.etailbookit.dto.POSCustomerResponseDto;
import com.sayone.etailbookit.dto.POSAuthResponseDto;
import java.util.List;
import com.sayone.etailbookit.util.RetailerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class AuthenticationEcom {

    @Value("${ecom.auth.uri}")
    String auth_Uri;
    @Value("${ecom.auth.client.id}")
    String clientId;
    @Value("${ecom.auth.client.secret}")
    String clientSecret;
  /*  @Value("${pos.api.base.url}")
    String posBaseUri;
    @Value("${pos.auth.client.id}")
    String posClientId;
    @Value("${pos.auth.client.secret}")
    String posClientSecret;*/


    RestTemplate restTemplate = new RestTemplate();
    private static Logger LOGGER = LoggerFactory.getLogger(AuthenticationEcom.class);

    public ResponseEntity<AuthResponse> getAuthentication(String x_tenantSchema) throws EtailBookItException {
        LOGGER.info("@@@@Entered Authentication");
        String authUri = auth_Uri+"/public/oauth2/token/";
        //String authUri = "https://posweb.dev.etailpet.com/public/oauth2/token/";
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = null;
        UserAuth auth = new UserAuth();
        auth.setClientId(clientId);
        auth.setClientSecret(clientSecret);
        ResponseEntity<AuthResponse> response;
        //Converting the Object to JSONString
        try {
            jsonString = mapper.writeValueAsString(auth);
            HttpHeaders headers = new HttpHeaders();
            headers.set("X-TenantSchema",x_tenantSchema);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<String>(jsonString,headers);
            //building url with query param
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(authUri)
                    .queryParam("client_id", clientId)
                    .queryParam("client_secret",clientSecret)
                    .queryParam("grant_type","client_credentials");
            response = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.POST,
                    request,
                    AuthResponse.class
            );
            if(response == null){
                throw new BadRequestException("No response from Etail plus");
            }else{
                LOGGER.info("GOT THE AuthResponse FROM ETAIL PLUS ");
            }
        }catch (Exception e){
            throw new EtailBookItException(e.getMessage());
        }
        LOGGER.info("@@@@Exited Authentication"+response);
        return response;
    }

    public ResponseEntity<EcommCustomerDetails> getCustomerDeails(Integer customerId , String accessToken ,String schema) throws BadRequestException {
        LOGGER.info("@@@@Entered Customer Details");
        String customerUri = auth_Uri+"auth/api/v1/customer/"+customerId+"/details/";
        LOGGER.info("@@@@@ Got the customerUri"+customerUri);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer "+accessToken);
        headers.add("X-TenantSchema", RetailerContext.getRetailer());
        HttpEntity<String> request = new HttpEntity<String>(headers);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(customerUri)
                .queryParam("id", customerId);
        ResponseEntity<EcommCustomerDetails> response = restTemplate.exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                request,
                EcommCustomerDetails.class
        );
        if(!response.hasBody()){
            throw new BadRequestException("No response from Etail plus");
        }
        LOGGER.info("@@@@Exited Customer Details"+response);
        return response;

    }

    public ResponseEntity<EcommRetailerDetails> getRetailerDetails(String accessToken, String schema) throws BadRequestException {
        LOGGER.info("@@@@Entered Retailer Details");
        String retailerUri = auth_Uri+schema+"/api/v1/bookit/retailer/";
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer "+accessToken);
        HttpEntity<String> request = new HttpEntity<String>(headers);
        ResponseEntity<EcommRetailerDetails> response = restTemplate.exchange(retailerUri, HttpMethod.GET, request, EcommRetailerDetails.class);
        LOGGER.info("@@@@Exited Retailer details"+response);
        return response;

    }

    /**
     * Get customer list by IDs using a specific base URI (for POS API calls)
     */
    public ResponseEntity<POSCustomerResponseDto> getCustomerListByIdsWithBaseUri(List<Integer> customerIds, String accessToken, String schema, String baseUri) throws BadRequestException {
        LOGGER.info("@@@@Entered Customer List by IDs with custom base URI: " + baseUri);
        
        // Build URL with query parameters for multiple customer IDs
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(baseUri + "/auth/api/v1/customer/list-by-ids");
        
        // Add each customer ID as a separate query parameter
        for (Integer customerId : customerIds) {
            uriBuilder.queryParam("customerIds", customerId);
        }
        
        String customerUri = uriBuilder.toUriString();
        LOGGER.info("Customer list URI: " + customerUri);
        
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("X-TenantSchema", schema);
        
        HttpEntity<String> request = new HttpEntity<String>(headers);
        
        try {
            ResponseEntity<POSCustomerResponseDto> response = restTemplate.exchange(
                    customerUri,
                    HttpMethod.GET,
                    request,
                    POSCustomerResponseDto.class
            );
            
            if (!response.hasBody()) {
                throw new BadRequestException("No response from POS API");
            }
            
            LOGGER.info("@@@@Exited Customer List by IDs with custom base URI: " + response);
            return response;
        } catch (Exception e) {
            LOGGER.error("Failed to fetch customer details from POS API: " + e.getMessage());
            throw new BadRequestException("Failed to fetch customer details from POS API: " + e.getMessage());
        }
    }

    /** this method is no longer needed
     * Get customer list by IDs using POS API with automatic POS authentication
     */
    /*public ResponseEntity<POSCustomerResponseDto> getCustomerListByIdsWithPOSAuth(List<Integer> customerIds, String schema) throws BadRequestException {
        LOGGER.info("@@@@Entered Customer List by IDs with POS Authentication");
        
        try {
            // Get POS-specific access token
            String posAccessToken = getPOSAccessToken(schema);
            
            // Call the main method with POS access token
            return getCustomerListByIds(customerIds, posAccessToken, schema);
        } catch (Exception e) {
            LOGGER.error("Failed to get POS access token: " + e.getMessage());
            throw new BadRequestException("Failed to authenticate with POS API: " + e.getMessage());
        }
    }*/

 /*   *//** this method is no longer needed as we are getting the token from bookit API headers.
     * Get access token specifically for POS API
     *//*
    private String getPOSAccessToken(String schema) throws BadRequestException {
        LOGGER.info("@@@@Entered POS Authentication");
        
        try {
            // POS API authentication endpoint
            String posAuthEndpoint = posBaseUri + "/auth/api/v1/auth/oauth2/token";
            
            // Create request body for POS API
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode requestBody = mapper.createObjectNode();
            requestBody.put("clientId",posClientId);
            requestBody.put("clientSecret",posClientSecret);
            requestBody.put("retailer", schema);
            
            String jsonString = mapper.writeValueAsString(requestBody);
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-pos-device-id", "eTailPet");
            headers.set("X-TenantSchema", schema);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> request = new HttpEntity<String>(jsonString, headers);
            
            LOGGER.info("POS Authentication request to: " + posAuthEndpoint);
            LOGGER.info("POS Authentication request body: " + jsonString);
            
            ResponseEntity<POSAuthResponseDto> response = restTemplate.exchange(
                    posAuthEndpoint,
                    HttpMethod.POST,
                    request,
                    POSAuthResponseDto.class
            );
            
            if (response == null || response.getBody() == null) {
                throw new BadRequestException("No response from POS authentication");
            }
            
            if (response.getBody().getResponseData() == null || response.getBody().getResponseData().getToken() == null) {
                throw new BadRequestException("No token in POS authentication response");
            }
            
            LOGGER.info("@@@@Exited POS Authentication successfully");
            return response.getBody().getResponseData().getToken();
            
        } catch (Exception e) {
            LOGGER.error("POS Authentication failed: " + e.getMessage());
            throw new BadRequestException("POS Authentication failed: " + e.getMessage());
        }
    }*/
}

