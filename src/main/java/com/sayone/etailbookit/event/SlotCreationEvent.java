package com.sayone.etailbookit.event;

import com.sayone.etailbookit.dto.ServiceSlotsDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class SlotCreationEvent extends ApplicationEvent {
    private final ServiceSlotsDto serviceSlotsDto;
    private final Integer attendantId;
    private final Integer timeSlotClusterId;
    private final String retailer;

    public SlotCreationEvent(Object source, ServiceSlotsDto serviceSlotsDto, Integer attendantId, Integer timeSlotClusterId,String retailer) {
        super(source);
        this.serviceSlotsDto = serviceSlotsDto;
        this.attendantId = attendantId;
        this.timeSlotClusterId = timeSlotClusterId;
        this.retailer=retailer;
    }
}