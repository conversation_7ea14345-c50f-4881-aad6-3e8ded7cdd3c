package com.sayone.etailbookit.event;

import com.sayone.etailbookit.dto.ServiceSlotsDto;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

public class SlotUpdateEvent extends ApplicationEvent {
    private final ServiceSlotsDto serviceSlotsDto;
    private final Integer attendantId;
    private final Integer timeSlotClusterId;
    private final String retailer;

    public SlotUpdateEvent(Object source, ServiceSlotsDto serviceSlotsDto, Integer attendantId, Integer timeSlotClusterId, String retailer) {
        super(source);
        this.serviceSlotsDto = serviceSlotsDto;
        this.attendantId = attendantId;
        this.timeSlotClusterId = timeSlotClusterId;
        this.retailer = retailer;
    }

    public ServiceSlotsDto getServiceSlotsDto() {
        return serviceSlotsDto;
    }

    public Integer getAttendantId() {
        return attendantId;
    }

    public Integer getTimeSlotClusterId() {
        return timeSlotClusterId;
    }

    public String getRetailer() {
        return retailer;
    }
}
