package com.sayone.etailbookit.event;

import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.Attendant;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

@Getter
public class AppointmentSyncEvent extends ApplicationEvent {
    
    private final Attendant attendant;
    private final String retailer;
    private final List<Appointment> appointments;

    public AppointmentSyncEvent(Object source, Attendant attendant, String retailer, List<Appointment> appointments) {
        super(source);
        this.attendant = attendant;
        this.retailer = retailer;
        this.appointments = appointments;
    }
}

