package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.CustomerDto;
import com.sayone.etailbookit.dto.WaitlistEntryDto;
import com.sayone.etailbookit.model.Customer;
import com.sayone.etailbookit.model.WaitlistEntry;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class WaitlistEntryMapper {

    public WaitlistEntryDto toDto(WaitlistEntry entity) {
        if (entity == null) {
            return null;
        }

        WaitlistEntryDto dto = new WaitlistEntryDto();
        dto.setWaitListEntryId(entity.getId());
        dto.setTimeSlotId(entity.getTimeSlot() != null ? entity.getTimeSlot().getId() : null);
        dto.setAppointmentId(entity.getAppointment() != null ? entity.getAppointment().getId() : null);
        dto.setNotified(entity.getNotified());
        dto.setBooked(entity.getBooked());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setModifiedAt(entity.getModifiedAt());

        if (entity.getCustomers() != null) {
            dto.setCustomers(entity.getCustomers().stream()
                    .map(this::customerToDto)
                    .collect(Collectors.toSet()));
        }

        return dto;
    }

    private CustomerDto customerToDto(Customer customer) {
        if (customer == null) {
            return null;
        }

        CustomerDto dto = new CustomerDto();
        dto.setId(customer.getEcom_id());
        dto.setFirstName(customer.getFirstName());
        dto.setLastName(customer.getLastName());
        dto.setPhoneNumber(customer.getPhoneNumber());
        dto.setIsActive(customer.is_active());

        return dto;
    }
} 