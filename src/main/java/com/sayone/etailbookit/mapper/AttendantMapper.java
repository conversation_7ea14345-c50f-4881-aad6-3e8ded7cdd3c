package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.AttendantDetailsProjection;
import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

public class AttendantMapper {

    public static AttendantDto toAttendantDto(Attendant attendant) {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(Attendant.class, AttendantDto.class).addMappings(mp->{
            mp.skip(AttendantDto::setPetSizeConstraints);
            mp.skip(AttendantDto::setServiceTypes);
            mp.skip(AttendantDto::setAddonServices);
            mp.skip(AttendantDto::setAttendantPetTypes);
            mp.skip(AttendantDto::setPetSizeLimits);
        });
        AttendantDto attendantDto = modelMapper.map(attendant, AttendantDto.class);

        if(attendant.getActive() == null)
            attendantDto.setActive(true);
        else
            attendantDto.setActive(attendant.getActive());

        List<ServiceTypeDto> serviceTypeDtos=new ArrayList<>();
        List<AddonServiceDto> addonServices=new ArrayList<>();
        for(ServiceType serviceType:attendant.getServiceTypes()){
            ServiceTypeDto serviceTypeDto = new ServiceTypeDto();
            serviceTypeDto.setId(serviceType.getServiceTypeId());
            serviceTypeDto.setName(serviceType.getName());
            serviceTypeDto.setActive(serviceType.isActive());
            serviceTypeDtos.add(serviceTypeDto);
        }

        for(AddonService addonService:attendant.getAddonServices()){
            AddonServiceDto addonServiceDto = new AddonServiceDto();
            addonServiceDto.setId(addonService.getAddonServiceId());
            addonServiceDto.setName(addonService.getName());
            addonServiceDto.setActive(addonService.getActive());
            addonServices.add(addonServiceDto);
        }

        List<AttendantPetTypeDto> attendantPetTypeDtoList=new ArrayList<>();
        for (AttendantPetTypes attendantPetTypes:attendant.getAttendantPetTypes()){
            List<GeneralPetSizeDto> generalPetSizeIds=new ArrayList<>();
            List<TemperamentDto> temperamentIds=new ArrayList<>();
            for(Temperament temperament:attendantPetTypes.getTemperaments()){
                TemperamentDto temperamentDto = new TemperamentDto();
                temperamentDto.setId(temperament.getTemperamentId());
                temperamentDto.setName(temperament.getName());
                temperamentDto.setActive(temperament.getActive());
                temperamentIds.add(temperamentDto);
            }
            for(GeneralPetSize generalPetSize:attendantPetTypes.getGeneralPetSizes()){
                GeneralPetSizeDto generalPetSizeDto = new GeneralPetSizeDto();
                generalPetSizeDto.setId(generalPetSize.getGeneralPetSizeId());
                generalPetSizeDto.setSize(generalPetSize.getSize());
                generalPetSizeDto.setWeightUnit(generalPetSize.getWeightUnit());
                generalPetSizeDto.setWeightValue(generalPetSize.getWeightValue());
                generalPetSizeIds.add(generalPetSizeDto);
            }

            AttendantPetTypeDto attendantPetTypeDto = new AttendantPetTypeDto();
            if(attendantPetTypes.getPetType()!=null){
                attendantPetTypeDto.setId(attendantPetTypes.getPetType().getPetTypeId());
                attendantPetTypeDto.setName(attendantPetTypes.getPetType().getName());
            }
            attendantPetTypeDto.setGeneralPetSize(generalPetSizeIds);
            attendantPetTypeDto.setTemperamentList(temperamentIds);
            attendantPetTypeDtoList.add(attendantPetTypeDto);
        }

        List<PetSizeConstraintDto> petSizeConstraintDtoList = new ArrayList<>();
        for (PetSizeConstraint petSizeConstraint:attendant.getPetSizeConstraints()){
            PetSizeConstraintDto petSizeConstraintDto = new PetSizeConstraintDto();
            petSizeConstraintDto.setId(petSizeConstraint.getId());
            petSizeConstraintDto.setCapacity(petSizeConstraint.getCapacity());
            if (petSizeConstraint.getPetType() != null && petSizeConstraint.getPetType().getPetTypeId() != null)
                petSizeConstraintDto.setPetTypeId(petSizeConstraint.getPetType().getPetTypeId());
            if (petSizeConstraint.getGeneralPetSize() != null && petSizeConstraint.getGeneralPetSize().getGeneralPetSizeId() != null)
                petSizeConstraintDto.setGeneralPetSizeId(petSizeConstraint.getGeneralPetSize().getGeneralPetSizeId());
            petSizeConstraintDtoList.add(petSizeConstraintDto);
        }
        attendantDto.setPetSizeConstraints(petSizeConstraintDtoList);

        List<PetSizeLimitDto> petSizeLimitDtoList = new ArrayList<>();
        for (PetSizeLimit petSizeLimit:attendant.getPetSizeLimits()){
            PetSizeLimitDto petSizeLimitDto = new PetSizeLimitDto();
            petSizeLimitDto.setId(petSizeLimit.getId());
            petSizeLimitDto.setCapacity(petSizeLimit.getCapacity());
            if (petSizeLimit.getPetType() != null && petSizeLimit.getPetType().getPetTypeId() != null)
                petSizeLimitDto.setPetTypeId(petSizeLimit.getPetType().getPetTypeId());
            if (petSizeLimit.getGeneralPetSize() != null && petSizeLimit.getGeneralPetSize().getGeneralPetSizeId() != null)
                petSizeLimitDto.setGeneralPetSizeId(petSizeLimit.getGeneralPetSize().getGeneralPetSizeId());
            petSizeLimitDtoList.add(petSizeLimitDto);
        }
        attendantDto.setPetSizeLimits(petSizeLimitDtoList);

        attendantDto.setAddonServices(addonServices);
        attendantDto.setServiceTypes(serviceTypeDtos);
        attendantDto.setAttendantPetTypes(attendantPetTypeDtoList);
        return attendantDto ;
    }

    public static AttendantDto toAttendantDto(AttendantDetailsProjection attendant) {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(AttendantDetailsProjection.class, AttendantDto.class)
                .addMappings(a -> {
                    a.skip(AttendantDto::setServiceTypes);
                    a.skip(AttendantDto::setAddonServices);
                    a.skip(AttendantDto::setAttendantPetTypes);
                    a.skip(AttendantDto::setAvailabilityDays);
                    a.skip(AttendantDto::setPetSizeConstraints);
                });
        AttendantDto attendantDto = modelMapper.map(attendant, AttendantDto.class);

        List<ServiceTypeDto> serviceTypes=new ArrayList<>();
        List<AddonServiceDto> addonServices=new ArrayList<>();

        for(AttendantDetailsProjection.ServiceType serviceType:attendant.getServiceTypes()){
            ServiceTypeDto serviceTypeDto = new ServiceTypeDto();
            serviceTypeDto.setId(serviceType.getId());
            serviceTypeDto.setName(serviceType.getName());
            serviceTypeDto.setActive(serviceType.getActive());
            serviceTypeDto.setFileUrl(serviceType.getFileUrl());
            serviceTypes.add(serviceTypeDto);
        }

        for(AttendantDetailsProjection.AddonService addonService:attendant.getAddonServices()){
            AddonServiceDto addonServiceDto = new AddonServiceDto();
            addonServiceDto.setId(addonService.getId());
            addonServiceDto.setName(addonService.getName());
            addonServiceDto.setActive(addonService.getActive());
            addonServiceDto.setIsTaxable(addonService.getIsTaxable());
            addonServiceDto.setTackOnExtraAmount(addonService.getTackOnExtraAmount());
            addonServiceDto.setTackOnExtraAmountCurrency(addonService.getTackOnExtraAmountCurrency());
            addonServiceDto.setTackOnExtraMinutes(addonService.getTackOnExtraMinutes());
            addonServices.add(addonServiceDto);
        }

        List<AttendantPetTypeDto> attendantPetTypeDtoList=new ArrayList<>();
        for (AttendantDetailsProjection.AttendantPetType APT:attendant.getAttendantPetTypes()){
            List<GeneralPetSizeDto> generalPetSizeIds=new ArrayList<>();
            List<TemperamentDto> temperamentIds=new ArrayList<>();
            for(AttendantDetailsProjection.Temperament temperament:APT.getTemperamentList()){
                TemperamentDto temperamentDto = new TemperamentDto();
                temperamentDto.setId(temperament.getId());
                temperamentDto.setName(temperament.getName());
                temperamentDto.setActive(temperament.getActive());
                temperamentIds.add(temperamentDto);
            }
            for(AttendantDetailsProjection.GeneralPetSize generalPetSize:APT.getGeneralPetSize()){
                GeneralPetSizeDto generalPetSizeDto = new GeneralPetSizeDto();
                generalPetSizeDto.setId(generalPetSize.getId());
                generalPetSizeDto.setSize(generalPetSize.getSize());
                generalPetSizeDto.setWeightUnit(generalPetSize.getWeightUnit());
                generalPetSizeDto.setWeightValue(generalPetSize.getWeightValue());
                generalPetSizeIds.add(generalPetSizeDto);
            }

            AttendantPetTypeDto attendantPetTypeDto = new AttendantPetTypeDto();
            attendantPetTypeDto.setId(APT.getId());
            attendantPetTypeDto.setName(APT.getName());
            attendantPetTypeDto.setGeneralPetSize(generalPetSizeIds);
            attendantPetTypeDto.setTemperamentList(temperamentIds);
            attendantPetTypeDtoList.add(attendantPetTypeDto);
        }


        List<AvailabilityDto> availabilityList = new ArrayList<>();
        for(AttendantDetailsProjection.Availability availability : attendant.getAvailabilityDays()) {
            AvailabilityDto availabilityDto = new AvailabilityDto();
            availabilityDto.setAvailableDay(availability.getAvailableDay());
            availabilityDto.setAvailabilityOpenTime(
                    OffsetTime.parse(
                                    availability.getAvailabilityOpenTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                            )
                            .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                            .toString().split("[+\\-Z]")[0]
            );
            availabilityDto.setAvailabilityCloseTime(
                    OffsetTime.parse(
                                    availability.getAvailabilityCloseTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                            )
                            .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                            .toString().split("[+\\-Z]")[0]
            );
            availabilityList.add(availabilityDto);
        }

        List<PetSizeConstraintDto> petSizeConstraintDtoList = new ArrayList<>();
        for (AttendantDetailsProjection.PetSizeConstraint petSizeConstraint:attendant.getPetSizeConstraints()){
            PetSizeConstraintDto petSizeConstraintDto = new PetSizeConstraintDto();
            petSizeConstraintDto.setId(petSizeConstraint.getId());
            petSizeConstraintDto.setCapacity(petSizeConstraint.getCapacity());
            if (petSizeConstraint.getPetType() != null && petSizeConstraint.getPetType().getId() != null)
                petSizeConstraintDto.setPetTypeId(petSizeConstraint.getPetType().getId());
            if (petSizeConstraint.getGeneralPetSize() != null && petSizeConstraint.getGeneralPetSize().getId() != null)
                petSizeConstraintDto.setGeneralPetSizeId(petSizeConstraint.getGeneralPetSize().getId());
            petSizeConstraintDtoList.add(petSizeConstraintDto);
        }
        attendantDto.setPetSizeConstraints(petSizeConstraintDtoList);

        List<PetSizeLimitDto> petSizeLimitDtoList = new ArrayList<>();
        for (AttendantDetailsProjection.PetSizeLimit petSizeLimit:attendant.getPetSizeLimits()){
            PetSizeLimitDto petSizeLimitDto = new PetSizeLimitDto();
            petSizeLimitDto.setId(petSizeLimit.getId());
            petSizeLimitDto.setCapacity(petSizeLimit.getCapacity());
            if (petSizeLimit.getPetType() != null && petSizeLimit.getPetType().getId() != null)
                petSizeLimitDto.setPetTypeId(petSizeLimit.getPetType().getId());
            if (petSizeLimit.getGeneralPetSize() != null && petSizeLimit.getGeneralPetSize().getId() != null)
                petSizeLimitDto.setGeneralPetSizeId(petSizeLimit.getGeneralPetSize().getId());
            petSizeLimitDtoList.add(petSizeLimitDto);
        }
        attendantDto.setPetSizeLimits(petSizeLimitDtoList);

        attendantDto.setAvailabilityDays(availabilityList);
        attendantDto.setAddonServices(addonServices);
        attendantDto.setServiceTypes(serviceTypes);
        attendantDto.setAttendantPetTypes(attendantPetTypeDtoList);
        return attendantDto ;
    }

    public static Attendant toAttendantEntity(AttendantDto attendantDto) throws BadRequestException {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(AttendantDto.class, Attendant.class).addMappings(mp -> {
            mp.skip(Attendant::setAttendantId);
            mp.skip(Attendant::setVenue);
            mp.skip(Attendant::setPetSizeConstraints);
            mp.skip(Attendant::setCalendarSyncInProgress);
            mp.skip(Attendant::setGoogleCalendarAuthorized);
        });
        Attendant attendant = modelMapper.map(attendantDto, Attendant.class);
        attendant.setAvailabilityInterval(attendantDto.getAvailabilityInterval());
        attendant.setAvailabilityIntervalUnit(attendantDto.getAvailabilityIntervalUnit());
        // setting available time of this attendant
        if (Utils.isNotEmpty(attendantDto.getAvailabilityDays())) {
            Set<AttendantAvailability> attendantAvailabilities = new HashSet();

            AttendantAvailability availability;
            for(AvailabilityDto attendantAvailability : attendantDto.getAvailabilityDays()){
                availability = new AttendantAvailability();
                availability.setAvailabilityCloseTime(
                        OffsetTime.parse(attendantAvailability.getAvailabilityCloseTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailabilityOpenTime(
                        OffsetTime.parse(attendantAvailability.getAvailabilityOpenTime() + OffsetContext.getOffset())
                                .withOffsetSameInstant(ZoneOffset.UTC)
                );
                availability.setAvailableDay(attendantAvailability.getAvailableDay());
                availability.setAttendant(attendant);
                availability.setRetailer(RetailerContext.getRetailer());
                attendantAvailabilities.add(availability);
            }
            attendant.setAvailabilityDays(attendantAvailabilities);
        }

        attendant.setRetailer(RetailerContext.getRetailer());
        attendant.setActive(true);

        return attendant;
    }

    public static List<AttendantDto> toAttendantDtoList(List<Attendant> source) {

        return source
                .stream()
                .map(AttendantMapper::toAttendantDto)
                .collect(Collectors.toList());

    }
}
