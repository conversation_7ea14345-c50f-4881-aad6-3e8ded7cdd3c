package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.PetShampooDto;
import com.sayone.etailbookit.model.PetShampoo;
import com.sayone.etailbookit.util.RetailerContext;
import org.modelmapper.ModelMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

public class PetShampooMapper {

    public static PetShampooDto toPetShampooDto(PetShampoo petShampoo){
        return new ModelMapper().map(petShampoo, PetShampooDto.class);
    }

    public static List<PetShampooDto> toPetShampooDtoList(List<PetShampoo> source) {

        return source
                .stream()
                .map(element -> toPetShampooDto(element))
                .collect(Collectors.toList());
    }

    public static PetShampoo toPetShampooEntity(PetShampooDto petShampooDto) {
        PetShampoo petShampoo = new ModelMapper().map(petShampooDto, PetShampoo.class);
        petShampoo.setRetailer(RetailerContext.getRetailer());
        return petShampoo;
    }

    public static List<PetShampoo> toPetShampooList(List<PetShampooDto> petShampooDtos) {

        return petShampooDtos
                .stream()
                .map(element -> toPetShampooEntity(element))
                .collect(Collectors.toList());
    }
}
