package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.BittingHistoryDto;
import com.sayone.etailbookit.dto.PersonalityParameterDto;
import com.sayone.etailbookit.dto.ThreatReactionDto;
import com.sayone.etailbookit.dto.UnfriendlyBehaviourTriggerDto;
import com.sayone.etailbookit.model.BittingHistory;
import com.sayone.etailbookit.model.PersonalityParameter;
import com.sayone.etailbookit.model.ThreatReaction;
import com.sayone.etailbookit.model.UnfriendlyBehaviourTrigger;
import org.modelmapper.ModelMapper;

import java.util.List;
import java.util.stream.Collectors;

public class ExtendedBehaviourMapper {

    public static UnfriendlyBehaviourTriggerDto toUnfriendlyBehaviourTriggerDto(UnfriendlyBehaviourTrigger UnfriendlyBehaviourTrigger){
        return new ModelMapper().map(UnfriendlyBehaviourTrigger, UnfriendlyBehaviourTriggerDto.class);
    }

    public static List<UnfriendlyBehaviourTriggerDto> toUnfriendlyBehaviourTriggerDtoList(List<UnfriendlyBehaviourTrigger> unfriendlyBehaviourTriggers) {

        return unfriendlyBehaviourTriggers
                .stream()
                .map(element -> toUnfriendlyBehaviourTriggerDto(element))
                .collect(Collectors.toList());
    }

    public static UnfriendlyBehaviourTrigger toUnfriendlyBehaviourTriggerEntity(UnfriendlyBehaviourTriggerDto unfriendlyBehaviourTriggerDto){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(UnfriendlyBehaviourTriggerDto.class,UnfriendlyBehaviourTrigger.class);
        return modelMapper.map(unfriendlyBehaviourTriggerDto, UnfriendlyBehaviourTrigger.class);
    }

    public static List<UnfriendlyBehaviourTrigger> toUnfriendlyBehaviourTriggerList(List<UnfriendlyBehaviourTriggerDto> unfriendlyBehaviourTriggers) {
        return unfriendlyBehaviourTriggers
                .stream()
                .map(element -> toUnfriendlyBehaviourTriggerEntity(element))
                .collect(Collectors.toList());
    }

    public static ThreatReactionDto toThreatReactionDto(ThreatReaction threatReaction){
        return new ModelMapper().map(threatReaction, ThreatReactionDto.class);
    }

    public static List<ThreatReactionDto> toThreatReactionDtoList(List<ThreatReaction> threatReactions) {

        return threatReactions
                .stream()
                .map(element -> toThreatReactionDto(element))
                .collect(Collectors.toList());
    }

    public static ThreatReaction toThreatReactionEntity(ThreatReactionDto threatReaction){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(ThreatReactionDto.class,ThreatReaction.class);
        return modelMapper.map(threatReaction, ThreatReaction.class);
    }

    public static List<ThreatReaction> toThreatReactionList(List<ThreatReactionDto> threatReactionDtos) {
        return threatReactionDtos
                .stream()
                .map(element -> toThreatReactionEntity(element))
                .collect(Collectors.toList());
    }

    public static PersonalityParameterDto toPersonalityParameterDto(PersonalityParameter personalityParamter){
        return new ModelMapper().map(personalityParamter, PersonalityParameterDto.class);
    }

    public static List<PersonalityParameterDto> toPersonalityParameterDtoList(List<PersonalityParameter> personalityParamters) {

        return personalityParamters
                .stream()
                .map(element -> toPersonalityParameterDto(element))
                .collect(Collectors.toList());
    }

    public static PersonalityParameter toPersonalityParameterEntity(PersonalityParameterDto PersonalityParameter){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(PersonalityParameterDto.class,PersonalityParameter.class);
        return modelMapper.map(PersonalityParameter, PersonalityParameter.class);
    }

    public static List<PersonalityParameter> toPersonalityParameterList(List<PersonalityParameterDto> PersonalityParameterDtos) {
        return PersonalityParameterDtos
                .stream()
                .map(element -> toPersonalityParameterEntity(element))
                .collect(Collectors.toList());
    }

    public static BittingHistoryDto toBittingHistoryDto(BittingHistory BittingHistory){
        return new ModelMapper().map(BittingHistory, BittingHistoryDto.class);
    }

    public static List<BittingHistoryDto> toBittingHistoryDtoList(List<BittingHistory> bittingHistories) {

        return bittingHistories
                .stream()
                .map(element -> toBittingHistoryDto(element))
                .collect(Collectors.toList());
    }

    public static BittingHistory toBittingHistoryEntity(BittingHistoryDto bittingHistory){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(BittingHistoryDto.class,BittingHistory.class);
        return modelMapper.map(bittingHistory, BittingHistory.class);
    }

    public static List<BittingHistory> toBittingHistoryList(List<BittingHistoryDto> bittingHistoryDtos) {
        return bittingHistoryDtos
                .stream()
                .map(element -> toBittingHistoryEntity(element))
                .collect(Collectors.toList());
    }


}
