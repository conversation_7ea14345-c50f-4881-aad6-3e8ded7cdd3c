package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.VenueDetailProjection;
import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.util.Utils;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;

import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public class VenueMapper {

    public static VenueDto toVenueDto(Venue venue) {
        ModelMapper modelMapper = new ModelMapper();
        VenueDto venueDto = modelMapper.map(venue, VenueDto.class);

        if (venueDto.getLocationAddress() != null) {
            VenueAddressDto venueAddressDto = modelMapper.map(venueDto.getLocationAddress(), VenueAddressDto.class);
            venueDto.setLocationAddress(venueAddressDto);
        }

        if(venue.getActive() == null)
            venueDto.setActive(true);
        else
            venueDto.setActive(venue.getActive());

        List<VenuePetTypeDto> petTypeDtos=new ArrayList<>();
        for (VenuePetTypes venuePetTypes:venue.getVenuePetTypes()){
            VenuePetTypeDto petTypeDto=new VenuePetTypeDto();
            if(venuePetTypes.getPetType()!=null){
                petTypeDto.setId(venuePetTypes.getPetType().getPetTypeId());
                petTypeDto.setName(venuePetTypes.getPetType().getName());
            }
            Set<GeneralPetSize> generalPetSizes=venuePetTypes.getGeneralPetSizes();
            Set<Temperament> temperaments=venuePetTypes.getTemperaments();
            if(Utils.isNotEmpty(generalPetSizes)){
                petTypeDto.setGeneralPetSizes(GeneralPetSizeMapper
                        .toGeneralPetSizeDtoList(generalPetSizes));
            }

            if(Utils.isNotEmpty(temperaments)){
                petTypeDto.setTemperaments(PetTypeMapper.
                        getConfigDtos(Temperament.class, TemperamentDto.class, temperaments));
            }

            petTypeDtos.add(petTypeDto);
        }

        venueDto.setPetTypes(petTypeDtos);

        return venueDto ;
    }

   /* public static VenueDto toVenueDto(VenueDetailProjection venue) {
        ModelMapper modelMapper = new ModelMapper();

        // Create type map and skip all collections explicitly
        TypeMap<VenueDetailProjection, VenueDto> typeMap =
                modelMapper.createTypeMap(VenueDetailProjection.class, VenueDto.class);

        typeMap.addMappings(m -> {
            m.skip(VenueDto::setAvailabilityDays); // skip collection
        });

        VenueDto venueDto = typeMap.map(venue);

        // Build availability list manually
        List<AvailabilityDto> availabilityList = new ArrayList<>();
        if (venue.getAvailabilityDays() != null) {
            for (VenueDetailProjection.VenueAvailability availability : venue.getAvailabilityDays()) {
                AvailabilityDto dto = new AvailabilityDto();
                dto.setAvailableDay(availability.getAvailableDay());

                dto.setAvailabilityOpenTime(
                        OffsetTime.parse(
                                        availability.getAvailabilityOpenTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                                )
                                .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                                .toString().split("[+\\-Z]")[0]
                );

                dto.setAvailabilityCloseTime(
                        OffsetTime.parse(
                                        availability.getAvailabilityCloseTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                                )
                                .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                                .toString().split("[+\\-Z]")[0]
                );

                availabilityList.add(dto);
            }
        }

        venueDto.setAvailabilityDays(availabilityList);

        return venueDto;
    }*/

    public static VenueDto toVenueDto(VenueDetailProjection venue) {
        if (venue == null) return null;

        VenueDto dto = new VenueDto();

        // Scalars
        dto.setId(venue.getId());
        dto.setInternalName(venue.getInternalName());
        dto.setPublicName(venue.getPublicName());
        dto.setLocationType(venue.getLocationType());
        dto.setSupportSimultaneousBookings(venue.getSupportSimultaneousBookings());
        dto.setParticipantLimitService(venue.getParticipantLimitService());
        dto.setExtraCharge(venue.getExtraCharge());
        dto.setExtraCurrency(venue.getExtraCurrency());
        dto.setAvailabilityInterval(venue.getAvailabilityInterval());
        dto.setAvailabilityIntervalUnit(venue.getAvailabilityIntervalUnit());
        dto.setEcommerceStoreId(venue.getEcommerceStoreId());
        dto.setActive(venue.getActive());
        dto.setMobileZipCode(venue.getMobileZipCode());
        dto.setDeleted(Boolean.TRUE.equals(venue.getDeleted()));

        // Address
        if (venue.getLocationAddress() != null) {
            VenueAddressDto addr = new VenueAddressDto();
            addr.setStreet1(venue.getLocationAddress().getStreet1());
            addr.setStreet2(venue.getLocationAddress().getStreet2());
            addr.setCity(venue.getLocationAddress().getCity());
            addr.setState(venue.getLocationAddress().getState());
            addr.setZipcode(venue.getLocationAddress().getZipcode());
            addr.setCountry(venue.getLocationAddress().getCountry());
            dto.setLocationAddress(addr);
        }

        // Availability Days (DST adjustment preserved from your old code)
        List<AvailabilityDto> availabilityList = new ArrayList<>();
        if (venue.getAvailabilityDays().size()!=0) {
            for (VenueDetailProjection.VenueAvailability availability : venue.getAvailabilityDays()) {
                AvailabilityDto a = new AvailabilityDto();
                a.setAvailableDay(availability.getAvailableDay());
                a.setAvailabilityOpenTime(
                        OffsetTime.parse(
                                        availability.getAvailabilityOpenTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                                )
                                .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                                .toString().split("[+\\-Z]")[0]
                );
                a.setAvailabilityCloseTime(
                        OffsetTime.parse(
                                        availability.getAvailabilityCloseTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                                )
                                .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                                .toString().split("[+\\-Z]")[0]
                );
                availabilityList.add(a);
            }
        }
        dto.setAvailabilityDays(availabilityList);

        // Service Types
        List<ServiceTypeDto> serviceTypes = new ArrayList<>();
        if (venue.getServiceTypes().size()!=0) {
            for (VenueDetailProjection.ServiceType st : venue.getServiceTypes()) {
                if(st.getId()==null) {
                    continue;
                }
                    ServiceTypeDto s = new ServiceTypeDto();
                    s.setId(st.getId());
                    s.setName(st.getName());
                    s.setActive(st.getActive());
                    s.setFileUrl(st.getFileUrl());
                    s.setDeleted(st.getDeleted());
                    serviceTypes.add(s);
                }
        }
        dto.setServiceTypes(serviceTypes);

        // ServiceTypeIds (just extract IDs)
        dto.setServiceTypeIds(
                serviceTypes.stream()
                        .map(ServiceTypeDto::getId)
                        .collect(Collectors.toList())
        );

        // Pet Types
        List<VenuePetTypeDto> petTypes = new ArrayList<>();
        if (venue.getPetTypes().size()!=0) {
            for (VenueDetailProjection.VenuePetType pt : venue.getPetTypes()) {
                if (pt.getId() == null) {
                    continue;
                }
                    VenuePetTypeDto vpt = new VenuePetTypeDto();
                    vpt.setId(pt.getId());
                    vpt.setName(pt.getName());

                // Temperaments
                if (pt.getTemperaments() != null) {
                    vpt.setTemperaments(
                            pt.getTemperaments().stream()
                                    .map(t -> new TemperamentDto(t.getId(), t.getName(), t.getActive()))
                                    .collect(Collectors.toList())
                    );
                }

                // General Pet Sizes
                if (pt.getGeneralPetSizes() != null) {
                    vpt.setGeneralPetSizes(
                            pt.getGeneralPetSizes().stream()
                                    .map(g -> new GeneralPetSizeDto(g.getId(), g.getSize(), g.getWeightValue(), g.getWeightUnit()))
                                    .collect(Collectors.toList())
                    );
                }

                petTypes.add(vpt);
            }
            }

        dto.setPetTypes(petTypes);

        return dto;
    }


    public static List<VenueDto> toVenueDtoList(List<Venue> source) {
        return source
                .stream()
                .map(VenueMapper::toVenueDto)
                .collect(Collectors.toList());
    }

    public static Venue toVenueEntity(VenueDto venueDto) {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(VenueDto.class, Venue.class).addMappings(mp -> mp.skip(Venue::setVenueId));

        Venue venue = modelMapper.map(venueDto, Venue.class);
        if (venueDto.getLocationAddress() != null) {
            VenueAddress venueAddress = modelMapper.map(venueDto.getLocationAddress(), VenueAddress.class);
            venue.setLocationAddress(venueAddress);
        }
        venue.setRetailer(RetailerContext.getRetailer());
        System.out.println(venue);
        return venue;
    }
}
