package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.util.DisplayType;
import com.sayone.etailbookit.util.PetTypeConfig;
import com.sayone.etailbookit.util.RetailerContext;
import com.sayone.etailbookit.validator.Validator;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

public class PetTypeMapper {

    public static PetTypeDto toPetTypeDto(PetType petType) {
        PetTypeDto petTypeDto=new PetTypeDto();
        petTypeDto.setId(petType.getPetTypeId());
        petTypeDto.setName(petType.getName());
        petTypeDto.setFileUrl(petType.getFileUrl());

        if(petType.getActive() == null)
            petTypeDto.setActive(true);
        else
            petTypeDto.setActive(petType.getActive());

        //adding TEMPERAMENT details to getall pet_type
        Set<PetTypeConfiguration> configs = petType.getPetTypeConfigurations();
        for (PetTypeConfiguration config : configs) {
            PetTypeConfigDto petTypeConfig = new PetTypeConfigDto();
            petTypeConfig.setDisplayType(config.getDisplayType());
            if(config.getName().name().equalsIgnoreCase("TEMPERAMENT")){
                List<TemperamentDto> temperaments = getConfigDtos(Temperament.class, TemperamentDto.class, petType.getTemperaments());
                petTypeConfig.setOptions(temperaments);
                petTypeDto.setTemperaments(petTypeConfig);
            }
        }

        return petTypeDto;
    }

    public static PetTypeDto toCompletePetTypeDto(PetType petType) {
        ModelMapper modelMapper = new ModelMapper();
        Set<PetTypeConfiguration> configs = petType.getPetTypeConfigurations();
        PetTypeDto petTypeDto = modelMapper.map(petType, PetTypeDto.class);
        if(petType.getActive() == null)
            petTypeDto.setActive(true);
        else
            petTypeDto.setActive(petType.getActive());
        for (PetTypeConfiguration config : configs) {
            PetTypeConfigDto petTypeConfig = new PetTypeConfigDto();
            petTypeConfig.setDisplayType(config.getDisplayType());
            switch (config.getName()) {
                case HAIR_LENGTH:
                    List<HairLengthDto> hairLengthDtos = getConfigDtos(HairLength.class, HairLengthDto.class, petType.getHairLengths());
                    //Commenting the code according to in the following switch cases as per the requirement in the ticket BKI-1213
                  /*  List<HairLengthDto> hairLengthDtosSorted = hairLengthDtos.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    hairLengthDtosSorted.addAll(hairLengthDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<HairLengthDto> hairLengthDtosSorted=hairLengthDtos.stream().filter(s->s.getName()!=null).sorted((o1,o2)->o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    hairLengthDtosSorted.addAll(hairLengthDtos.stream().filter(s->s.getName()==null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(hairLengthDtosSorted);
                    petTypeDto.setHairLengths(petTypeConfig);
                    break;

                case HAIR_TEXTURE:
                    List<HairTextureDto> hairTextureDtos = getConfigDtos(HairTexture.class, HairTextureDto.class, petType.getHairTextures());
                   /* List<HairTextureDto> hairTextureDtosSorted = hairTextureDtos.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    hairTextureDtosSorted.addAll(hairTextureDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(hairTextureDtosSorted);*/
                    List<HairTextureDto> hairTextureDtosSorted=hairTextureDtos.stream().filter(s->s.getName()!=null).sorted((o1,o2)->o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    hairTextureDtosSorted.addAll(hairTextureDtos.stream().filter(s->s.getName()==null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(hairTextureDtosSorted);
                    petTypeDto.setHairTextures(petTypeConfig);
                    break;

                case COMBS_BLADES:
                    List<CombsDto> combsDtos = getConfigDtos(Combs.class, CombsDto.class, petType.getCombs());
                    List<BladesDto> bladesDtos = getConfigDtos(Blades.class, BladesDto.class, petType.getBlades());
                  /*  List<CombsDto> combsDtosSorted = combsDtos.stream().filter(s -> s.getIndexValue() != null).
                        sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    combsDtosSorted.addAll(combsDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/

                    List<CombsDto> combsDtosSorted = combsDtos.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    combsDtosSorted.addAll(combsDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));

                    /*List<BladesDto> bladesDtosSorted = bladesDtos.stream().filter(s -> s.getIndexValue() != null).
                        sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    bladesDtosSorted.addAll(bladesDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<BladesDto> bladesDtosSorted = bladesDtos.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    bladesDtosSorted.addAll(bladesDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));

                    CombsBladesDto combsBlades = new CombsBladesDto();
                    combsBlades.setCombs(combsDtosSorted);
                    combsBlades.setBlades(bladesDtosSorted);
                    combsBlades.setDisplayType(petTypeConfig.getDisplayType());
                    petTypeDto.setCombsBlades(combsBlades);
                    break;

                case DESIRED_HAIR_LENGTH:
                    List<DesiredHairLengthDto> desiredHairLengths = getConfigDtos(DesiredHairLength.class, DesiredHairLengthDto.class, petType.getDesiredHairLengths());
                   /* List<DesiredHairLengthDto> desiredHairLengthsSorted = desiredHairLengths.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    desiredHairLengthsSorted.addAll(desiredHairLengths.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<DesiredHairLengthDto> desiredHairLengthsSorted = desiredHairLengths.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    desiredHairLengthsSorted.addAll(desiredHairLengths.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(desiredHairLengthsSorted);
                    petTypeDto.setDesiredHairLengths(petTypeConfig);
                    break;

                case BREEDS:
                    List<BreedDto> breeds = getConfigDtos(Breed.class, BreedDto.class, petType.getBreeds());
                    List<BreedDto> breedsSorted = breeds.stream().filter(s -> s.getName() != null).
                            sorted(Comparator.comparing(BreedDto::getName)).collect(Collectors.toList());
                    breedsSorted.addAll(breeds.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(breedsSorted);
                    petTypeDto.setBreeds(petTypeConfig);
                    break;

                case ALLERGIES:
                    List<AllergiesDto> allergiesDtos = getConfigDtos(Allergies.class, AllergiesDto.class, petType.getAllergies());
                    /*List<AllergiesDto> allergiesDtosSorted = allergiesDtos.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    allergiesDtosSorted.addAll(allergiesDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<AllergiesDto> allergiesDtosSorted = allergiesDtos.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    allergiesDtosSorted.addAll(allergiesDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(allergiesDtosSorted);
                    petTypeDto.setAllergies(petTypeConfig);
                    break;

                case VET_INFO:
                    List<VetInformationDto> vetInformationDtos = getConfigDtos(VetInformation.class, VetInformationDto.class, petType.getVetInformation());
                    /*List<VetInformationDto> vetInformationDtosSorted = vetInformationDtos.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    vetInformationDtosSorted.addAll(vetInformationDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<VetInformationDto> vetInformationDtosSorted = vetInformationDtos.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    vetInformationDtosSorted.addAll(vetInformationDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(vetInformationDtosSorted);
                    petTypeDto.setVetInformation(petTypeConfig);
                    break;

                case VACCINATION_RECORDS:
                    List<VaccinationRecordsDto> vaccinationRecordsDtos = getConfigDtos(VaccinationRecords.class, VaccinationRecordsDto.class, petType.getVaccinationRecords());
                    /*List<VaccinationRecordsDto> vaccinationRecordsDtosSorted = vaccinationRecordsDtos.stream().filter(s -> s.getIndexValue() != null).
                        sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    vaccinationRecordsDtosSorted.addAll(vaccinationRecordsDtos.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<VaccinationRecordsDto> vaccinationRecordsDtosSorted = vaccinationRecordsDtos.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    vaccinationRecordsDtosSorted.addAll(vaccinationRecordsDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(vaccinationRecordsDtosSorted);
                    petTypeDto.setVaccinationRecords(petTypeConfig);
                    break;

                case TEMPERAMENT:
                    List<TemperamentDto> temperaments = getConfigDtos(Temperament.class, TemperamentDto.class, petType.getTemperaments());
                    /*List<TemperamentDto> temperamentsSorted = temperaments.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    temperamentsSorted.addAll(temperaments.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<TemperamentDto> temperamentsSorted = temperaments.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    temperamentsSorted.addAll(temperaments.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(temperamentsSorted);
                    petTypeDto.setTemperaments(petTypeConfig);
                    break;

                case EMERGENCY_CONTACT_INFO:
                    List<EmergencyContactInfoDto> emergencyContactInfo = getConfigDtos(EmergencyContactInfo.class, EmergencyContactInfoDto.class, petType.getEmergencyContactInfo());
                    /*List<EmergencyContactInfoDto> emergencyContactInfoSorted = emergencyContactInfo.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    emergencyContactInfoSorted.addAll(emergencyContactInfo.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<EmergencyContactInfoDto> emergencyContactInfoSorted = emergencyContactInfo.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    emergencyContactInfoSorted.addAll(emergencyContactInfo.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(emergencyContactInfoSorted);
                    petTypeDto.setEmergencyContactInfo(petTypeConfig);
                    break;

                case DOCUMENT_OPTIONS:
                    List<DocumentOptionDto> documentOptions = getConfigDtos(DocumentOption.class, DocumentOptionDto.class, petType.getDocumentOptions());
                    /*List<DocumentOptionDto> documentOptionsSorted = documentOptions.stream().filter(s -> s.getIndexValue() != null).
                            sorted((o1, o2) -> o1.getIndexValue().compareTo(o2.getIndexValue())).collect(Collectors.toList());
                    documentOptionsSorted.addAll(documentOptions.stream().filter(s -> s.getIndexValue() == null).collect(Collectors.toList()));*/
                    List<DocumentOptionDto> documentOptionsSorted = documentOptions.stream().filter(s -> s.getName() != null).
                            sorted((o1, o2) -> o1.getName().compareTo(o2.getName())).collect(Collectors.toList());
                    documentOptionsSorted.addAll(documentOptions.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
                    petTypeConfig.setOptions(documentOptionsSorted);
                    petTypeDto.setDocumentOptions(petTypeConfig);
                    break;

                case WEIGHT_RANGE:
                    List<WeightRangeDto> weightRangeDtos = getConfigDtos(WeightRange.class, WeightRangeDto.class, petType.getWeightRanges());
                    petTypeConfig.setOptions(weightRangeDtos);
                    petTypeDto.setWeightRanges(petTypeConfig);
                    break;

                case GENERAL_PET_SIZE:
                    List<GeneralPetSizeDto> generalPetSizes = getConfigDtos(GeneralPetSize.class, GeneralPetSizeDto.class, petType.getGeneralPetSizes());
                    List<GeneralPetSizeDto> generalPetSizeSorted=generalPetSizes.stream().sorted((o1,o2)->o1.getSize().compareTo(o2.getSize())).collect(Collectors.toList());
                    petTypeConfig.setOptions(generalPetSizeSorted);
                    petTypeDto.setGeneralPetSizes(petTypeConfig);
                    break;

                case DECEASE_DATE:
                    petTypeDto.setDeceaseDate(petTypeConfig);
                    break;

            }
        }
        return petTypeDto;
    }

    public static <S, D> List<D> getConfigDtos(Class<S> source, Class<D> destination, Collection<S> configs) {
        ModelMapper modelMapper = new ModelMapper();
        TypeMap<S, D> typeMaps = modelMapper.typeMap(source, destination);
        List<D> configDtos = configs.stream()
                .map(typeMaps::map)
                .collect(Collectors.toList());
        return configDtos;
    }

    public static List<PetTypeDto> toPetTypeDtoList(List<PetType> source) {

        return source
                .stream()
                .map(element -> toPetTypeDto(element))
                .collect(Collectors.toList());
    }

    public static PetType toPetTypeEntity(PetTypeDto petTypeDto,HairLengthRepository hairLengthRepository,
                                          HairTextureRepository hairTextureRepository,
                                          DesiredHairLengthRepository desiredHairLengthRepository,
                                          AllergiesRepository allergiesRepository,
                                          CombsRepository combsRepository,BladesRepository bladesRepository,
                                          DocumentOptionRepository documentOptionRepository,TemperamentRepository temperamentRepository,
                                          VaccinationRecordsRepository vaccinationRecordsRepository,
                                          VetInformationRepository vetInformationRepository,
                                          EmergencyContactInfoRepository emergencyContactInfoRepository,BreedRepository breedRepository) throws Exception {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setAmbiguityIgnored(true);
        Set<PetTypeConfiguration> petTypeConfigurations = new HashSet<>();

        PetType petType = modelMapper.map(petTypeDto, PetType.class);

        if (petTypeDto.getHairLengths() != null) {
            petType.setHairLengths(toHairLengthEntityList(petTypeDto.getHairLengths().getOptions(), petType,hairLengthRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.HAIR_LENGTH, petTypeDto.getHairLengths().getDisplayType(), petType));
        }

        if (petTypeDto.getHairTextures() != null) {
            petType.setHairTextures(toHairTextureEntityList(petTypeDto.getHairTextures().getOptions(), petType ,hairTextureRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.HAIR_TEXTURE, petTypeDto.getHairTextures().getDisplayType(), petType));
        }

        if (petTypeDto.getCombsBlades() != null) {
            petType.setCombs(toCombsEntityList(petTypeDto.getCombsBlades().getCombs(), petType, combsRepository ));
            petType.setBlades(toBladesEntityList(petTypeDto.getCombsBlades().getBlades(), petType,bladesRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.COMBS_BLADES, petTypeDto.getCombsBlades().getDisplayType(), petType));
        }

        if (petTypeDto.getVetInformation() != null) {
            petType.setVetInformation(toVetInformationEntityList(petTypeDto.getVetInformation().getOptions(), petType, vetInformationRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.VET_INFO, petTypeDto.getVetInformation().getDisplayType(), petType));

        }

        if (petTypeDto.getAllergies() != null) {
            petType.setAllergies(toAllergiesEntityList(petTypeDto.getAllergies().getOptions(), petType, allergiesRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.ALLERGIES, petTypeDto.getAllergies().getDisplayType(), petType));
        }

        if (petTypeDto.getDesiredHairLengths() != null) {
            petType.setDesiredHairLengths(toDesiredHairLengthEntityList(petTypeDto.getDesiredHairLengths().getOptions(), petType,desiredHairLengthRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.DESIRED_HAIR_LENGTH, petTypeDto.getDesiredHairLengths().getDisplayType(), petType));
        }

        if (petTypeDto.getBreeds() != null) {
            petType.setBreeds(toBreedEntityList(petTypeDto.getBreeds().getOptions(), petType,breedRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.BREEDS, petTypeDto.getBreeds().getDisplayType(), petType));
        }

        if (petTypeDto.getTemperaments() != null) {
            petType.setTemperaments(toTemperamentEntityList(petTypeDto.getTemperaments().getOptions(), petType,temperamentRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.TEMPERAMENT, petTypeDto.getTemperaments().getDisplayType(), petType));
        }

        if (petTypeDto.getVaccinationRecords() != null) {
            petType.setVaccinationRecords(toVaccinationRecordsEntityList(petTypeDto.getVaccinationRecords().getOptions(), petType, vaccinationRecordsRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.VACCINATION_RECORDS, petTypeDto.getVaccinationRecords().getDisplayType(), petType));
        }

        if (petTypeDto.getDocumentOptions() != null) {
            petType.setDocumentOptions(toDocumentOptionList(petTypeDto.getDocumentOptions().getOptions(), petType,documentOptionRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.DOCUMENT_OPTIONS, petTypeDto.getDocumentOptions().getDisplayType(), petType));
        }

        if (petTypeDto.getEmergencyContactInfo() != null) {
            petType.setEmergencyContactInfo(toEmergencyContactInfoEntityList(petTypeDto.getEmergencyContactInfo().getOptions(), petType,emergencyContactInfoRepository));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.EMERGENCY_CONTACT_INFO, petTypeDto.getEmergencyContactInfo().getDisplayType(), petType));
        }

        if (petTypeDto.getWeightRanges() != null) {
            petType.setWeightRanges(toWeightRangeList(petTypeDto.getWeightRanges().getOptions(), petType));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.WEIGHT_RANGE, petTypeDto.getWeightRanges().getDisplayType(), petType));
        }

        if(petTypeDto.getGeneralPetSizes() != null) {
            petType.setGeneralPetSizes(toGeneralPetSizeList(petTypeDto.getGeneralPetSizes().getOptions(), petType));
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.GENERAL_PET_SIZE, petTypeDto.getGeneralPetSizes().getDisplayType(), petType));

        }

        if (petTypeDto.getDeceaseDate() != null) {
            petTypeConfigurations.add(toPetTypeConfigurationEntity(PetTypeConfig.DECEASE_DATE, petTypeDto.getDeceaseDate().getDisplayType(), petType));
        }
        petTypeConfigurations.forEach( config -> config.setRetailer(RetailerContext.getRetailer()) );
        petType.setPetTypeConfigurations(petTypeConfigurations);
        petType.setRetailer(RetailerContext.getRetailer());
        return petType;
    }

    public static Set<VaccinationRecords> toVaccinationRecordsEntityList(List<VaccinationRecordsDto> vaccinationRecordsDtos, PetType petType, VaccinationRecordsRepository vaccinationRecordsRepository) {
        Set<VaccinationRecords> vaccinationRecords = new HashSet<>();
        if (vaccinationRecords != null) {
            for (VaccinationRecordsDto vaccinationRecordsDto : vaccinationRecordsDtos) {
                VaccinationRecords vaccinationRecord = new VaccinationRecords();
                vaccinationRecord.setVaccinationRecordId(vaccinationRecordsDto.getId());
                vaccinationRecord.setName(vaccinationRecordsDto.getName());
                vaccinationRecord.setActive(vaccinationRecordsDto.isActive());
               /* if(vaccinationRecordsDto.getIndexValue() == null){
                    Integer lastIndexId = vaccinationRecordsRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        vaccinationRecord.setIndexValue(lastIndexId+1);
                    }else{
                        vaccinationRecord.setIndexValue(1);
                    }
                }else {
                    vaccinationRecord.setIndexValue(vaccinationRecordsDto.getIndexValue());
                }*/
                vaccinationRecord.setRequireDateAdministrated(vaccinationRecordsDto.isRequireDateAdministrated());
                vaccinationRecord.setRequireDateExpires(vaccinationRecordsDto.isRequireDateExpires());
                vaccinationRecord.setRequireVaccinationDocument(vaccinationRecordsDto.isRequireVaccinationDocument());
                vaccinationRecord.setRequireUploadEsign(vaccinationRecordsDto.isRequireUploadEsign());
                vaccinationRecord.setPetType(petType);
                vaccinationRecord.setRetailer(RetailerContext.getRetailer());
                vaccinationRecords.add(vaccinationRecord);
            }
        }
        return vaccinationRecords;
    }

    public static Set<Temperament> toTemperamentEntityList(List<TemperamentDto> temperamentDtos, PetType petType, TemperamentRepository temperamentRepository){
        Set<Temperament> temperaments = new HashSet<>();
        if (temperaments != null) {
            for (TemperamentDto temperamentDto : temperamentDtos) {
                Temperament temperament = new Temperament();
                temperament.setTemperamentId(temperamentDto.getId());
                temperament.setName(temperamentDto.getName());
                temperament.setActive(temperamentDto.isActive());
               /* if(temperamentDto.getIndexValue() == null){
                    Integer lastIndexId = temperamentRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        temperament.setIndexValue(lastIndexId+1);
                    }else{
                        temperament.setIndexValue(1);
                    }
                }else {
                    temperament.setIndexValue(temperamentDto.getIndexValue());
                }*/
                temperament.setRetailer(RetailerContext.getRetailer());
                temperament.setPetType(petType);
                temperaments.add(temperament);
            }
        }
        return temperaments;
    }

    public static PetTypeConfiguration toPetTypeConfigurationEntity(PetTypeConfig configName, DisplayType displayType, PetType petType) {
        PetTypeConfiguration petTypeConfiguration = new PetTypeConfiguration();
        petTypeConfiguration.setDisplayType(displayType);
        petTypeConfiguration.setName(configName);
        petTypeConfiguration.setPetType(petType);
        petTypeConfiguration.setRetailer(RetailerContext.getRetailer());
        return petTypeConfiguration;
    }


    public static Set<HairTexture> toHairTextureEntityList(List<HairTextureDto> hairTextureDtos, PetType petType,HairTextureRepository hairTextureRepository) {
        Set<HairTexture> hairTextures = new HashSet<>();
        if (hairTextureDtos != null) {
            for (HairTextureDto hairTextureDto : hairTextureDtos) {
                HairTexture hairTexture = new HairTexture();
                hairTexture.setHairTextureId(hairTextureDto.getId());
                hairTexture.setName(hairTextureDto.getName());
                hairTexture.setActive(hairTextureDto.isActive());
               /* if(hairTextureDto.getIndexValue() == null){
                    Integer lastIndexId = hairTextureRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        hairTexture.setIndexValue(lastIndexId+1);
                    }else{
                        hairTexture.setIndexValue(1);
                    }
                }else {
                    hairTexture.setIndexValue(hairTextureDto.getIndexValue());
                }*/
                hairTexture.setPetType(petType);
                hairTexture.setRetailer(RetailerContext.getRetailer());
                hairTextures.add(hairTexture);
            }
        }
        return hairTextures;
    }

    public static Set<HairLength> toHairLengthEntityList(List<HairLengthDto> hairLengthDtos, PetType petType, HairLengthRepository hairLengthRepository) {
        Set<HairLength> hairLengths = new HashSet<>();
        if (hairLengthDtos != null) {
            for (HairLengthDto hairLengthDto : hairLengthDtos) {
                HairLength hairLength = new HairLength();
                hairLength.setHairLengthId(hairLengthDto.getId());
                hairLength.setName(hairLengthDto.getName());
                hairLength.setActive(hairLengthDto.isActive());
               /* if(hairLengthDto.getIndexValue() == null){
                Integer lastIndexId = hairLengthRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        hairLength.setIndexValue(lastIndexId+1);
                    }else{
                        hairLength.setIndexValue(1);
                    }
                }else {
                    hairLength.setIndexValue(hairLengthDto.getIndexValue());
                }*/
                hairLength.setPetType(petType);
                hairLength.setRetailer(RetailerContext.getRetailer());
                hairLengths.add(hairLength);
            }
        }
        return hairLengths;
    }

    public static Set<Combs> toCombsEntityList(List<CombsDto> combsDtos, PetType petType, CombsRepository combsRepository) {
        Set<Combs> combs = new HashSet<>();
        if (combsDtos != null) {
            for (CombsDto combsDto : combsDtos) {
                Combs comb = new Combs();
                comb.setId(combsDto.getId());
                comb.setName(combsDto.getName());
                comb.setActive(combsDto.isActive());
               /* if(combsDto.getIndexValue() == null){
                    Integer lastIndexId = combsRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        comb.setIndexValue(lastIndexId+1);
                    }else{
                        comb.setIndexValue(1);
                    }
                }else {
                    comb.setIndexValue(combsDto.getIndexValue());
                }*/
                comb.setPetType(petType);
                comb.setRetailer(RetailerContext.getRetailer());
                combs.add(comb);
            }
        }
        return combs;
    }

    public static Set<Blades> toBladesEntityList(List<BladesDto> bladesDtos, PetType petType,BladesRepository bladesRepository) {
        Set<Blades> blades = new HashSet<>();
        if (bladesDtos != null) {
            for (BladesDto bladesDto : bladesDtos) {
                Blades blade = new Blades();
                blade.setId(bladesDto.getId());
                blade.setName(bladesDto.getName());
                blade.setActive(bladesDto.isActive());
               /* if(bladesDto.getIndexValue() == null){
                    Integer lastIndexId = bladesRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        blade.setIndexValue(lastIndexId+1);
                    }else{
                        blade.setIndexValue(1);
                    }
                }else {
                    blade.setIndexValue(bladesDto.getIndexValue());
                }*/
                blade.setPetType(petType);
                blade.setRetailer(RetailerContext.getRetailer());
                blades.add(blade);
            }
        }
        return blades;
    }

    public static Set<VetInformation> toVetInformationEntityList(List<VetInformationDto> vetInformationDtos, PetType petType,VetInformationRepository vetInformationRepository){
        Set<VetInformation> vetInformations = new HashSet<>();
        if (vetInformationDtos != null) {
            for (VetInformationDto vetInformationDto : vetInformationDtos) {
                VetInformation vetInformation = new VetInformation();
                vetInformation.setId(vetInformationDto.getId());
                vetInformation.setName(vetInformationDto.getName());
                vetInformation.setActive(vetInformationDto.isActive());
               /* if(vetInformationDto.getIndexValue() == null){
                    Integer lastIndexId = vetInformationRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        vetInformation.setIndexValue(lastIndexId+1);
                    }else{
                        vetInformation.setIndexValue(1);
                    }
                }else {
                    vetInformation.setIndexValue(vetInformationDto.getIndexValue());
                }*/
                vetInformation.setPetType(petType);
                vetInformation.setRetailer(RetailerContext.getRetailer());
                vetInformations.add(vetInformation);
            }
        }
        return vetInformations;
    }

    public static Set<Allergies> toAllergiesEntityList(List<AllergiesDto> allergiesDtos, PetType petType,AllergiesRepository allergiesRepository) {
        Set<Allergies> allergies = new HashSet<>();
        if (allergiesDtos != null) {
            for (AllergiesDto allergiesDto : allergiesDtos) {
                Allergies allergy = new Allergies();
                allergy.setAllergyId(allergiesDto.getId());
                allergy.setName(allergiesDto.getName());
                allergy.setActive(allergiesDto.isActive());
               /* if(allergiesDto.getIndexValue() == null){
                    Integer lastIndexId = allergiesRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        allergy.setIndexValue(lastIndexId+1);
                    }else{
                        allergy.setIndexValue(1);
                    }
                }else {
                    allergy.setIndexValue(allergiesDto.getIndexValue());
                }*/
                allergy.setPetType(petType);
                allergy.setRetailer(RetailerContext.getRetailer());
                allergies.add(allergy);
            }
        }
        return allergies;
    }

    public static Set<DocumentOption> toDocumentOptionList(List<DocumentOptionDto> documentOptionDtos, PetType petType, DocumentOptionRepository documentOptionRepository) throws Exception {
        Set<DocumentOption> documentOptions = new HashSet<>();
        if (documentOptionDtos != null) {
            for (DocumentOptionDto documentOptionDto : documentOptionDtos) {
                DocumentOption documentOption;
                if (documentOptionDto.getId() != null) {
                    documentOption = documentOptionRepository.findByDocumentOptionId(documentOptionDto.getId());
                } else {
                    Validator.validateDocument(true, documentOptionDto);
                    documentOption = new DocumentOption();
                    documentOption.setName(documentOptionDto.getName());
                    documentOption.setActive(documentOptionDto.isActive());
                    documentOption.setRequireDescription(documentOptionDto.isRequireDescription());
                    documentOption.setRequireUpload(documentOptionDto.isRequireUpload());
                    documentOption.setRequireUploadEsign(documentOptionDto.isRequireUploadEsign());
                    documentOption.setRetailer(RetailerContext.getRetailer());
                    documentOptionRepository.save(documentOption);
                }
              /*  if(documentOptionDto.getIndexValue() == null){
                    Integer lastIndexId = documentOptionRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        documentOption.setIndexValue(lastIndexId+1);
                    }else{
                        documentOption.setIndexValue(1);
                    }
                }else {
                    documentOption.setIndexValue(documentOptionDto.getIndexValue());
                }*/
                documentOptions.add(documentOption);
            }
        }
        return documentOptions;
    }


    public static Set<EmergencyContactInfo> toEmergencyContactInfoEntityList(List<EmergencyContactInfoDto> emergencyContactInfoDtos, PetType petType, EmergencyContactInfoRepository emergencyContactInfoRepository) {
        Set<EmergencyContactInfo> emergencyContactInfos = new HashSet<>();
        if (emergencyContactInfoDtos != null) {
            for (EmergencyContactInfoDto emergencyContactInfoDto : emergencyContactInfoDtos) {
                EmergencyContactInfo emergencyContactInfo = new EmergencyContactInfo();
                emergencyContactInfo.setEmergencyContactInfoId(emergencyContactInfoDto.getId());
                emergencyContactInfo.setName(emergencyContactInfoDto.getName());
                emergencyContactInfo.setActive(emergencyContactInfoDto.isActive());
              /*  if(emergencyContactInfoDto.getIndexValue() == null){
                    Integer lastIndexId = emergencyContactInfoRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        emergencyContactInfo.setIndexValue(lastIndexId+1);
                    }else{
                        emergencyContactInfo.setIndexValue(1);
                    }
                }else {
                    emergencyContactInfo.setIndexValue(emergencyContactInfoDto.getIndexValue());
                }*/
                emergencyContactInfo.setPetType(petType);
                emergencyContactInfo.setRetailer(RetailerContext.getRetailer());
                emergencyContactInfos.add(emergencyContactInfo);
            }
        }
        return emergencyContactInfos;
    }

    public static Set<DesiredHairLength> toDesiredHairLengthEntityList(List<DesiredHairLengthDto> desiredHairLengthDtos, PetType petType,DesiredHairLengthRepository desiredHairLengthRepository){
        Set<DesiredHairLength> desiredHairLengths = new HashSet<>();
        if (desiredHairLengthDtos != null) {
            for (DesiredHairLengthDto desiredHairLengthDto : desiredHairLengthDtos) {
                DesiredHairLength desiredHairLength = new DesiredHairLength();
                desiredHairLength.setDesiredHairLengthId(desiredHairLengthDto.getId());
                desiredHairLength.setName(desiredHairLengthDto.getName());
                desiredHairLength.setActive(desiredHairLengthDto.isActive());
               /* if(desiredHairLengthDto.getIndexValue() == null){
                    Integer lastIndexId = desiredHairLengthRepository.getByLastIndexValueByRetailer(petType.getRetailer());
                    if(lastIndexId != null){
                        desiredHairLength.setIndexValue(lastIndexId+1);
                    }else{
                        desiredHairLength.setIndexValue(1);
                    }
                }else {
                    desiredHairLength.setIndexValue(desiredHairLengthDto.getIndexValue());
                }*/
                desiredHairLength.setPetType(petType);
                desiredHairLength.setRetailer(RetailerContext.getRetailer());
                desiredHairLengths.add(desiredHairLength);
            }
        }
        return desiredHairLengths;
    }

    public static Set<Breed> toBreedEntityList(List<BreedDto> breedDtos, PetType petType,BreedRepository breedRepository) {
        Set<Breed> breeds = new HashSet<>();
        if (breedDtos != null) {
            for (BreedDto breedDto : breedDtos) {
                Breed existingBreed = breedRepository.findById(breedDto.getId())
                        .orElse(new Breed());

                existingBreed.setName(breedDto.getName());
                existingBreed.setActive(breedDto.isActive());
                existingBreed.setPetType(petType);
                existingBreed.setRetailer(RetailerContext.getRetailer());
                existingBreed.setDeleted(Boolean.FALSE);
                breeds.add(existingBreed);
            }
        }
        return breeds;
    }

    public static Set<WeightRange> toWeightRangeList(List<WeightRangeDto> weightRangeDtos, PetType petType) {
        Set<WeightRange> weightRanges = new HashSet<>();
        if (weightRangeDtos != null) {
            for (WeightRangeDto weightRangeDto : weightRangeDtos) {
                WeightRange weightRange = new WeightRange();
                weightRange.setWeightRangeId(weightRangeDto.getId());
                weightRange.setMinValue(weightRangeDto.getMinValue());
                weightRange.setMaxValue(weightRangeDto.getMaxValue());
                if(weightRangeDto.getWeightUnit() != null)
                    weightRange.setWeightUnit(weightRangeDto.getWeightUnit());
                weightRange.setPetType(petType);
                weightRange.setRetailer(RetailerContext.getRetailer());
                weightRanges.add(weightRange);
            }
        }
        return weightRanges;
    }

    public static Set<GeneralPetSize> toGeneralPetSizeList(List<GeneralPetSizeDto> generalPetSizesDto, PetType petType) throws BadRequestException {
        Set<GeneralPetSize> generalPetSizes = new HashSet<>();
        if (generalPetSizesDto != null) {
            Set<String> sizeSet = new HashSet<>();
            for (GeneralPetSizeDto generalPetSizeDto : generalPetSizesDto) {
                String sizeNormalized = generalPetSizeDto.getSize().trim().toLowerCase();
                if (!sizeSet.add(sizeNormalized)) {
                    throw new BadRequestException("Duplicate pet size entry found: " + generalPetSizeDto.getSize());
                }
                GeneralPetSize generalPetSize = new GeneralPetSize();
                generalPetSize.setGeneralPetSizeId(generalPetSizeDto.getId());
                generalPetSize.setSize(generalPetSizeDto.getSize());
                generalPetSize.setWeightValue(generalPetSizeDto.getWeightValue());
                generalPetSize.setWeightUnit(generalPetSizeDto.getWeightUnit());
                generalPetSize.setPetType(petType);
                generalPetSize.setRetailer(RetailerContext.getRetailer());
                generalPetSizes.add(generalPetSize);
            }
        }
        return generalPetSizes;
    }

    public static PetTypeDto toActiveValues(PetType petType) {
        ModelMapper modelMapper = new ModelMapper();
        Set<PetTypeConfiguration> configs = petType.getPetTypeConfigurations();
        PetTypeDto petTypeDto = modelMapper.map(petType, PetTypeDto.class);

        for (PetTypeConfiguration config : configs) {
            PetTypeConfigDto petTypeConfig = new PetTypeConfigDto();
            petTypeConfig.setDisplayType(config.getDisplayType());
            switch (config.getName()) {
                case HAIR_LENGTH:
                    List<HairLengthDto> hairLengthDtos = new ArrayList<>();
                    if (petType.getHairLengths() != null) {
                        petType.getHairLengths().stream().forEach(hairLength ->{
                            if(hairLength.isActive()){
                                HairLengthDto singleHair = modelMapper.map(hairLength, HairLengthDto.class);

                                hairLengthDtos.add(singleHair);
                            }
                        });
                    }
                    petTypeConfig.setOptions(hairLengthDtos);
                    petTypeDto.setHairLengths(petTypeConfig);
                    break;

                case HAIR_TEXTURE:
                    List<HairTextureDto> hairTextureDtos = new ArrayList<>();
                    if (petType.getHairTextures() != null) {
                        petType.getHairTextures().stream().forEach(hairTexture ->{
                            if(hairTexture.isActive()){
                                HairTextureDto singleTexture = modelMapper.map(hairTexture, HairTextureDto.class);
                                hairTextureDtos.add(singleTexture);
                            }
                        });
                    }
                    petTypeConfig.setOptions(hairTextureDtos);
                    petTypeDto.setHairTextures(petTypeConfig);
                    break;

                case VET_INFO:
                    if (petType.getVetInformation() != null) {
                        List<VetInformationDto> vetInformationDtos = new ArrayList<>();
                        petType.getVetInformation().stream().forEach(vetInformation ->{
                            VetInformationDto singleVetInfo = modelMapper.map(vetInformation, VetInformationDto.class);
                            vetInformationDtos.add(singleVetInfo);
                        });
                        petTypeConfig.setOptions(vetInformationDtos);
                        petTypeDto.setVetInformation(petTypeConfig);
                    }
                    break;

                case VACCINATION_RECORDS:
                    if (petType.getVaccinationRecords() != null) {
                        List<VaccinationRecordsDto> vaccinationRecordsDtos = new ArrayList<>();
                        petType.getVaccinationRecords().stream().forEach(vaccinationRecords ->{
                            VaccinationRecordsDto singleVaccInfo = modelMapper.map(vaccinationRecords, VaccinationRecordsDto.class);
                            vaccinationRecordsDtos.add(singleVaccInfo);
                        });
                        petTypeConfig.setOptions(vaccinationRecordsDtos);
                        petTypeDto.setVaccinationRecords(petTypeConfig);
                    }
                    break;

                case TEMPERAMENT:
                    if (petType.getTemperaments() != null) {
                        List<TemperamentDto> temperaments = new ArrayList<>();
                        petType.getTemperaments().stream().forEach(temperament ->{
                            TemperamentDto singleTemperament = modelMapper.map(temperament, TemperamentDto.class);
                            temperaments.add(singleTemperament);
                        });
                        petTypeConfig.setOptions(temperaments);
                        petTypeDto.setTemperaments(petTypeConfig);
                    }
                    break;

                case DOCUMENT_OPTIONS:
                    if (petType.getDocumentOptions() != null) {
                        List<DocumentOptionDto> documentOptions = new ArrayList<>();
                        petType.getDocumentOptions().stream().forEach(documentOption ->{
                            DocumentOptionDto singleDoc = modelMapper.map(documentOption, DocumentOptionDto.class);
                            documentOptions.add(singleDoc);
                        });
                        petTypeConfig.setOptions(documentOptions);
                        petTypeDto.setDocumentOptions(petTypeConfig);
                    }
                    break;

                case WEIGHT_RANGE:

                    if (petType.getWeightRanges() != null) {
                        List<WeightRangeDto> weightRangeDtos = new ArrayList<>();
                        petType.getWeightRanges().stream().forEach(weightRange ->{
                            WeightRangeDto singleWeightRange = modelMapper.map(weightRange, WeightRangeDto.class);
                            weightRangeDtos.add(singleWeightRange);
                        });
                        petTypeConfig.setOptions(weightRangeDtos);
                        petTypeDto.setWeightRanges(petTypeConfig);
                    }
                    break;
            }
            petTypeDto.setFileUrl(petType.getFileUrl());
        }
        return petTypeDto;
    }
}
