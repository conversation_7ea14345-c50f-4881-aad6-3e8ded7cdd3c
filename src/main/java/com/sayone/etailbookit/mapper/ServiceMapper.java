package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.util.OffsetContext;
import com.sayone.etailbookit.util.TimeZoneContext;
import com.sayone.etailbookit.util.Utils;
import io.netty.util.internal.ObjectUtil;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.util.ObjectUtils;

import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


public class ServiceMapper {

    public static Service toServiceEntity(ServiceDto serviceDto) throws BadRequestException {
        Service service;
        try{
        ModelMapper modelMapper = new ModelMapper();
            modelMapper.getConfiguration()
                    .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(ServiceDto.class, Service.class).addMappings(mp -> {
            mp.skip(Service::setServiceId);
            mp.skip(Service::setGeneralPetSize);
            mp.skip(Service::setVenues);
            mp.skip(Service::setAttendants);
            mp.skip(Service::setAddonServices);
            mp.skip(Service::setAvailableParticipantVaccinations);
            mp.skip(Service::setAvailableParticipantDocuments);
            mp.skip(Service::setPetType);
            mp.skip(Service::setPetType);
            mp.skip(Service::setServiceType);
            mp.skip(Service::setTemperaments);
        });
            service = modelMapper.map(serviceDto, Service.class);

        } catch (Exception e){
            throw new BadRequestException("Invalid service format");
        }

        return service;
    }

    public static ServiceDto toServiceDto(Service service) {
        ServiceDto serviceDto;
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(Service.class, ServiceDto.class).addMappings(mp -> {
            mp.skip(ServiceDto::setGeneralSizes);
            mp.skip(ServiceDto::setVenues);
            mp.skip(ServiceDto::setAddonServicesOffered);
            mp.skip(ServiceDto::setAvailableParticipantVaccinations);
            mp.skip(ServiceDto::setAvailableParticipantDocuments);
            mp.skip(ServiceDto::setPetType);
            mp.skip(ServiceDto::setServiceType);

        });
        serviceDto = modelMapper.map(service, ServiceDto.class);

        if (service.getServiceType() != null) {
            ServiceTypeDto serviceTypeDto = modelMapper.map(serviceDto.getServiceType(), ServiceTypeDto.class);
            serviceTypeDto.setId(service.getServiceType().getServiceTypeId());
            serviceDto.setServiceType(serviceTypeDto);

        }
        if (service.getPetType() != null) {
            PetTypeDto petTypeDto = modelMapper.map(serviceDto.getPetType(), PetTypeDto.class);
            petTypeDto.setId(service.getPetType().getPetTypeId());
            serviceDto.setPetType(petTypeDto);
        }

        List<DocumentOptionDto> documentOptionList = new ArrayList<>();
        if(service.getAvailableParticipantDocuments() != null){
            service.getAvailableParticipantDocuments().stream().forEach(documentOption -> {
                DocumentOptionDto documentOptionDto = modelMapper.map(documentOption,DocumentOptionDto.class);
                documentOptionDto.setId(documentOption.getDocumentOptionId());
                documentOptionDto.setName(documentOption.getName());
                documentOptionList.add(documentOptionDto);
            });
        }
        serviceDto.setAvailableParticipantDocuments(documentOptionList);

        if(Utils.isNotEmpty(service.getAddonServices())){
            serviceDto.setAddonServicesOffered(AddonServiceMapper.toAddonServiceDtoList
                    (service.getAddonServices()));
        }

        List<VaccinationRecordsDto> vaccinationRecordsDtoList = new ArrayList<>();
        if(service.getAvailableParticipantVaccinations() != null){
            service.getAvailableParticipantVaccinations().stream().forEach(vaccinationRecords -> {
                VaccinationRecordsDto vaccinationRecordsDto = modelMapper.map(vaccinationRecords,VaccinationRecordsDto.class);
                vaccinationRecordsDto.setId(vaccinationRecords.getVaccinationRecordId());
                vaccinationRecordsDto.setName(vaccinationRecords.getName());
                vaccinationRecordsDtoList.add(vaccinationRecordsDto);
            });
        }
        serviceDto.setAvailableParticipantVaccinations(vaccinationRecordsDtoList);

        List<GeneralPetSizeDto> generalPetSizeDtoList = new ArrayList<>();
        if (service.getGeneralPetSize() != null) {
            service.getGeneralPetSize().stream().forEach(generalPetSize -> {
                GeneralPetSizeDto generalPetSizeDto = modelMapper.map(generalPetSize, GeneralPetSizeDto.class);
                generalPetSizeDto.setId(generalPetSize.getGeneralPetSizeId());
                generalPetSizeDtoList.add(generalPetSizeDto);
            });
        }
        serviceDto.setGeneralSizes(generalPetSizeDtoList);


        List<VenueDto> venueDtoList = new ArrayList<>();
        if (service.getVenues() != null) {
            service.getVenues().stream().forEach(venue -> {
                VenueDto venueDto = modelMapper.map(venue, VenueDto.class);
                venueDto.setId(venue.getVenueId());
                venueDtoList.add(venueDto);
            });
        }
        serviceDto.setVenues(venueDtoList);
        return serviceDto ;
    }

    public static ServiceDetailsDto toServiceDtoNew(Service service) {
        ServiceDetailsDto serviceDto;
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(Service.class, ServiceDetailsDto.class).addMappings(s -> {
            s.skip(ServiceDetailsDto::setAvailabilityDays);
            s.map(Service::getPre_buffer_mins, ServiceDetailsDto::setPreBufferMins);
            s.map(Service::getAmount_currency, ServiceDetailsDto::setAmountCurrency);
            s.map(Service::getPetParentCanSelectAttendant, ServiceDetailsDto::setPetparentCanSelectAttendant);
            s.skip(ServiceDetailsDto::setGeneralSizes);
            s.skip(ServiceDetailsDto::setTemperaments);
            s.skip(ServiceDetailsDto::setVenues);
            s.skip(ServiceDetailsDto::setAvailableParticipantVaccinations);
            s.skip(ServiceDetailsDto::setAvailableParticipantDocuments);
            s.skip(ServiceDetailsDto::setServiceBreedInfos);
            s.skip(ServiceDetailsDto::setAddonServices);
            s.skip(ServiceDetailsDto::setShampoos);
            s.skip(ServiceDetailsDto::setColognes);
            s.skip(ServiceDetailsDto::setAttendants);
        });
        serviceDto = modelMapper.map(service, ServiceDetailsDto.class);

        if (serviceDto.getServiceType() == null) {
            serviceDto.setServiceType(new ServiceTypeDto());
        }

        if (service.getServiceType() != null && service.getServiceType().getServiceTypeId() != null) {
            serviceDto.getServiceType().setId(service.getServiceType().getServiceTypeId());
        } else {
            serviceDto.getServiceType().setId(null);
        }
        if(service.getPetType()!=null){
            serviceDto.getPetType().setId(service.getPetType().getPetTypeId());
        }


        List<VenueDto> venueDtoList = new ArrayList<>();
        for (Venue venue :service.getVenues()){
            VenueDto venueDto = modelMapper.map(venue, VenueDto.class);
            venueDto.setId(venue.getVenueId());
            venueDtoList.add(venueDto);
        }
        serviceDto.setVenues(venueDtoList);

        List<AttendantDto> attendants=new ArrayList<>();
        for (Attendant attendant:service.getAttendants()){
            if(!attendant.getDeleted()){
                AttendantDto attendantDto=modelMapper.map(attendant,AttendantDto.class);
                attendants.add(attendantDto);
            }
        }
        serviceDto.setAttendants(attendants);

        List<DocumentOptionDto> documentOptionList = new ArrayList<>();
        if(service.getAvailableParticipantDocuments() != null){
            service.getAvailableParticipantDocuments().stream().forEach(documentOption -> {
                DocumentOptionDto documentOptionDto = modelMapper.map(documentOption,DocumentOptionDto.class);
                documentOptionDto.setId(documentOption.getDocumentOptionId());
                documentOptionDto.setName(documentOption.getName());
                documentOptionList.add(documentOptionDto);
            });
        }
        serviceDto.setAvailableParticipantDocuments(documentOptionList);

        List<VaccinationRecordsDto> vaccinationRecordsDtoList = new ArrayList<>();
        if(service.getAvailableParticipantVaccinations() != null){
            service.getAvailableParticipantVaccinations().stream().forEach(vaccinationRecords -> {
                VaccinationRecordsDto vaccinationRecordsDto = modelMapper.map(vaccinationRecords,VaccinationRecordsDto.class);
                vaccinationRecordsDto.setId(vaccinationRecords.getVaccinationRecordId());
                vaccinationRecordsDto.setName(vaccinationRecords.getName());
                vaccinationRecordsDtoList.add(vaccinationRecordsDto);
            });
        }
        serviceDto.setAvailableParticipantVaccinations(vaccinationRecordsDtoList);

        if(service.getAddonServices() != null && !service.getAddonServices().isEmpty()){
            serviceDto.setAddonServices(
                    AddonServiceMapper.toAddonServiceDtoList(service.getAddonServices())
            );
        }

        List<GeneralPetSizeDto> generalPetSizeDtoList = new ArrayList<>();
        if (service.getGeneralPetSize() != null) {
            service.getGeneralPetSize().forEach(generalPetSize -> {
                GeneralPetSizeDto generalPetSizeDto = modelMapper.map(generalPetSize, GeneralPetSizeDto.class);
                generalPetSizeDto.setId(generalPetSize.getGeneralPetSizeId());
                generalPetSizeDtoList.add(generalPetSizeDto);
            });
        }
        serviceDto.setGeneralSizes(generalPetSizeDtoList);

        List<TemperamentDto> TemperamentList = new ArrayList<>();
        if (service.getTemperaments() != null) {
            service.getTemperaments().forEach(temperament -> {
                TemperamentDto temperamentDto = modelMapper.map(temperament, TemperamentDto.class);
                temperamentDto.setId(temperament.getTemperamentId());
                TemperamentList.add(temperamentDto);
            });
        }
        serviceDto.setTemperaments(TemperamentList);

        List<AvailabilityDto> availabilityDtoList = new ArrayList<>();;
        for(ServiceAvailability availability : service.getAvailabilityDays()) {
            AvailabilityDto availabilityDto = new AvailabilityDto();
            availabilityDto.setAvailableDay(availability.getAvailableDay());
            availabilityDto.setAvailabilityOpenTime(
                    OffsetTime.parse(
                                    availability.getAvailabilityOpenTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                            )
                            .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                            .toString().split("[+\\-Z]")[0]
            );
            availabilityDto.setAvailabilityCloseTime(
                    OffsetTime.parse(
                                    availability.getAvailabilityCloseTime().toString().split("[+\\-Z]")[0] + ZoneOffset.UTC
                            )
                            .withOffsetSameInstant(ZoneOffset.of(OffsetContext.getOffset()))
                            .toString().split("[+\\-Z]")[0]
            );
            availabilityDtoList.add(availabilityDto);
        }
        serviceDto.setAvailabilityDays(availabilityDtoList);

        serviceDto.setAddonsOffered(service.getAddonsOffered());
        serviceDto.setShampoosOffered(service.getShampoosOffered());
        serviceDto.setColognesOffered(service.getColognesOffered());
        serviceDto.setRequireFeedingInfo(service.getRequireFeedingInfo());
        serviceDto.setRequireDesiredHairLength(service.getRequireDesiredHairLength());
        serviceDto.setRequireThreatReactions(service.getRequirePetThreatReactions());

        List<AddonServiceDto> addonServiceDtos = new ArrayList<AddonServiceDto>();
        List<AddonServiceDto> addOnServiceSorted=new ArrayList<>();
        if(service.getAddonServices() != null) {
            service.getAddonServices().forEach(addonService -> {
               AddonServiceDto addonServiceDto = new AddonServiceDto();
               addonServiceDto.setId(addonService.getAddonServiceId());
               addonServiceDto.setName(addonService.getName());
              // addonServiceDto.setIndexValue(addonService.getIndexValue());
               addonServiceDto.setActive(addonService.getActive());
               addonServiceDtos.add(addonServiceDto);
            });
            //addonServiceDtos.sort(Comparator.comparing(AddonServiceDto::getName));
             addOnServiceSorted = addonServiceDtos.stream().filter(s -> s.getName() != null).
                    sorted((o1, o2) -> o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase())).collect(Collectors.toList());
            addOnServiceSorted.addAll(addonServiceDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
        }
        serviceDto.setAddonServices(addOnServiceSorted);

        List<PetShampooDto> petShampooDtos = new ArrayList<PetShampooDto>();
        List<PetShampooDto> petShampooSorted=new ArrayList<>();
        if(service.getShampoos() != null) {
            service.getShampoos().forEach(shampoo -> {
                PetShampooDto petShampooDto = new PetShampooDto();
                petShampooDto.setId(shampoo.getId());
                petShampooDto.setName(shampoo.getName());
                petShampooDto.setActive(shampoo.isActive());
                petShampooDtos.add(petShampooDto);
            });
            //petShampooDtos.sort(Comparator.comparing(PetShampooDto::getName));
            petShampooSorted = petShampooDtos.stream().filter(s -> s.getName() != null).
                    sorted((o1, o2) -> o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase())).collect(Collectors.toList());
            petShampooSorted.addAll(petShampooDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
        }
        serviceDto.setShampoos(petShampooSorted);

        List<PetCologneDto> petCologneDtos = new ArrayList<PetCologneDto>();
        List<PetCologneDto> petCologneSoted=new ArrayList<>();
        if(service.getColognes() != null) {
            service.getColognes().forEach(cologne -> {
                PetCologneDto petCologneDto = new PetCologneDto();
                petCologneDto.setId(cologne.getId());
                petCologneDto.setName(cologne.getName());
                petCologneDto.setActive(cologne.isActive());
                petCologneDtos.add(petCologneDto);
            });
           // petCologneDtos.sort(Comparator.comparing(PetCologneDto::getName));
            petCologneSoted = petCologneDtos.stream().filter(s -> s.getName() != null).
                    sorted((o1, o2) -> o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase())).collect(Collectors.toList());
            petCologneSoted.addAll(petCologneDtos.stream().filter(s -> s.getName() == null).collect(Collectors.toList()));
        }
        serviceDto.setColognes(petCologneSoted);

        List<ServiceBreedInfoDto> serviceBreedInfoDtos = new ArrayList<>();
        if(service.getServiceBreedsInformations() != null && service.getServiceBreedsInformations().size() > 0) {
            for (ServiceBreedsInformation breedInfo: service.getServiceBreedsInformations()) {
                if (breedInfo.getBreed() == null) continue;
                ServiceBreedInfoDto serviceBreedInfoDto = new ServiceBreedInfoDto();
                serviceBreedInfoDto.setId(breedInfo.getId());
                serviceBreedInfoDto.setBreedId(breedInfo.getBreed().getId());
                serviceBreedInfoDto.setBreedName(breedInfo.getBreed().getName());
                serviceBreedInfoDto.setDuration(breedInfo.getDuration());
                serviceBreedInfoDto.setDurationType(breedInfo.getDurationType());
                serviceBreedInfoDto.setChargeAmount(breedInfo.getChargeAmount());
                serviceBreedInfoDtos.add(serviceBreedInfoDto);
            };
        }
        serviceDto.setServiceBreedInfos(serviceBreedInfoDtos);

        return serviceDto;
    }

    public static List<ServiceDto> toServiceDtoList(List<Service> serviceList) {

        return serviceList
                .stream()
                .map(element -> toServiceDto(element))
                .collect(Collectors.toList());
    }


}
