package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.AddonServiceDto;
import com.sayone.etailbookit.model.AddonService;
import org.modelmapper.ModelMapper;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class AddonServiceMapper {

    public static AddonServiceDto toAddonServiceDto(AddonService generalPetSize){
        return new ModelMapper().map(generalPetSize, AddonServiceDto.class);
    }

    public static List<AddonServiceDto> toAddonServiceDtoList(List<AddonService> source) {
        return source
                .stream()
                .map(element -> toAddonServiceDto(element))
                .collect(Collectors.toList());
    }

    public static List<AddonServiceDto> toAddonServiceDtoList(Collection<AddonService> source) {
        return source
                .stream()
                .map(element -> toAddonServiceDto(element))
                .collect(Collectors.toList());
    }

    public static List<AddonService> toAddonServiceEntityList(List<AddonServiceDto> source) {
        return source
                .stream()
                .map(element -> toAddonServiceEntity(element))
                .collect(Collectors.toList());
    }

    public static AddonService toAddonServiceEntity(AddonServiceDto generalPetSize){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(AddonServiceDto.class,AddonService.class).addMappings(mp -> {
            mp.skip(AddonService::setAddonServiceId);
        });
        return modelMapper.map(generalPetSize, AddonService.class);
    }
}
