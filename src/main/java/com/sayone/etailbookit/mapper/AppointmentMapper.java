package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.repository.*;
import io.sentry.protocol.App;
import org.springframework.data.domain.Page;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


public class AppointmentMapper {

    private static Logger LOGGER = LoggerFactory.getLogger(AppointmentMapper.class);

    public static Appointment toAppointmentEntity(AppointmentDto appointmentDto) throws BadRequestException {

        Appointment appointment;
        try {
            ModelMapper modelMapper = new ModelMapper();
            modelMapper.getConfiguration()
                    .setMatchingStrategy(MatchingStrategies.STRICT);
            modelMapper.typeMap(AppointmentDto.class, Appointment.class).addMappings(mp -> {
                mp.skip(Appointment::setId);
                mp.skip(Appointment::setServiceType);
                mp.skip(Appointment::setService);
                mp.skip(Appointment::setAttendant);
                mp.skip(Appointment::setVenue);
                mp.skip(Appointment::setShamppo);
                mp.skip(Appointment::setCologne);
                mp.skip(Appointment::setAddOnService);
                mp.skip(Appointment::setPet);
                mp.skip(Appointment::setPetType);
                mp.skip(Appointment::setWeightRange);
                mp.skip(Appointment::setTemperament);
                mp.skip(Appointment::setHairLength);
                mp.skip(Appointment::setHairTexture);
                mp.skip(Appointment::setAllergies);
                mp.skip(Appointment::setDesiredHairLengths);
                mp.skip(Appointment::setAllergies);
                mp.skip(Appointment::setVaccinationInfo);
                mp.skip(Appointment::setOtherDoc);
                mp.skip(Appointment::setAppointmentVetInformation);
                mp.skip(Appointment::setAppointmentEmergencyContactInfo);
                mp.skip(Appointment::setWaiverOfLiabilityDoc);
             //   mp.skip(Appointment::setFeedingCount);
             //   mp.skip(Appointment::setBringYourFood);
                mp.skip(Appointment::setPersonalityParameters);
                mp.skip(Appointment::setUnfriendlyBehaviourTriggers);
                mp.skip(Appointment::setNote);
                mp.skip(Appointment::setSource);
                mp.skip(Appointment::setIsManualSlots);
            });
            appointment = modelMapper.map(appointmentDto, Appointment.class);

        } catch (Exception e) {
            throw new BadRequestException("Invalid service format");
        }
        return appointment;

    }

    public static List<CancelledAppointmentsDto> toCancelledAppointmentDtoList(List<Appointment> source) {
        return source
                .stream()
                .map(element -> toCancelledAppointmentDto(element))
                .collect(Collectors.toList());
    }

    public static CancelledAppointmentsDto toCancelledAppointmentDto(Appointment appointment){
        return new ModelMapper().map(appointment, CancelledAppointmentsDto.class);
    }

}
