package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.FeedingInformationDto;
import com.sayone.etailbookit.model.FeedingInformation;
import org.modelmapper.ModelMapper;

import java.util.List;
import java.util.stream.Collectors;

public class FeedingInformationMapper {

    public static FeedingInformationDto toFeedingInformationDto(FeedingInformation feedingInformation){
        return feedingInformation != null ? new ModelMapper().map(feedingInformation, FeedingInformationDto.class) : new FeedingInformationDto();
    }

    public static List<FeedingInformationDto> toFeedingInformationDtoList(List<FeedingInformation> feedingInformations) {

        return feedingInformations
                .stream()
                .map(element -> toFeedingInformationDto(element))
                .collect(Collectors.toList());
    }

    public static FeedingInformation toFeedingInformationEntity(FeedingInformationDto feedingInformation){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(FeedingInformationDto.class,FeedingInformation.class);
        return modelMapper.map(feedingInformation, FeedingInformation.class);
    }

    public static List<FeedingInformation> toFeedingInformationList(List<FeedingInformationDto> feedingInformationDtos) {
        return feedingInformationDtos
                .stream()
                .map(element -> toFeedingInformationEntity(element))
                .collect(Collectors.toList());
    }
}
