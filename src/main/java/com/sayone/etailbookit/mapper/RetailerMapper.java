package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.RetailerListDto;
import com.sayone.etailbookit.model.Schema;
import org.modelmapper.ModelMapper;

import java.util.List;
import java.util.stream.Collectors;

public class RetailerMapper {


    public static List<Schema> toSchemaEntityList(List<RetailerListDto> source) {
        return source
                .stream()
                .map(RetailerMapper::toSchemaEntity)
                .collect(Collectors.toList());
    }

    public static Schema toSchemaEntity(RetailerListDto retailer){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(RetailerListDto.class, Schema.class).addMappings(mapper -> {
            mapper.skip(Schema::setId); // Skip mapping the ID field
        });
        return modelMapper.map(retailer, Schema.class);
    }
}
