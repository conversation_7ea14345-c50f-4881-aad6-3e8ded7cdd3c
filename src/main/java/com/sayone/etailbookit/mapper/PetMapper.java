package com.sayone.etailbookit.mapper;

import com.sayone.etailbookit.dto.PetDto;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.util.RetailerContext;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class PetMapper {

    public static Pet toPetEntity(PetDto petDto) throws BadRequestException {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        modelMapper.typeMap(PetDto.class, Pet.class).addMappings(mp -> {
            mp.skip(Pet::setPetVaccinationRecords);
        });
        Pet pet = modelMapper.map(petDto, Pet.class);
        pet.setRetailer(RetailerContext.getRetailer());
        if(petDto.getDob()!=null) {
            try {
                pet.setDob(LocalDate.parse(petDto.getDob()));
            } catch (Exception e) {
                throw new BadRequestException("Invalid date format");
            }
        }
        return pet;
    }

}
