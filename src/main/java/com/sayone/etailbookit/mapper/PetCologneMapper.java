package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.PetCologneDto;
import com.sayone.etailbookit.model.PetCologne;
import com.sayone.etailbookit.util.RetailerContext;
import org.modelmapper.ModelMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

public class PetCologneMapper {

    public static PetCologneDto toPetCologneDto(PetCologne petCologne){
        return new ModelMapper().map(petCologne, PetCologneDto.class);
    }

    public static List<PetCologneDto> toPetCologneDtoList(List<PetCologne> source) {

        return source
                .stream()
                .map(element -> toPetCologneDto(element))
                .collect(Collectors.toList());
    }

    public static PetCologne toPetCologneEntity(PetCologneDto petCologneDto){
        PetCologne petCologne = new ModelMapper().map(petCologneDto, PetCologne.class);
        petCologne.setRetailer(RetailerContext.getRetailer());
        return petCologne;
    }

    public static List<PetCologne> toPetCologneList(List<PetCologneDto> petCologneDtos) {
        return petCologneDtos
                .stream()
                .map(element -> toPetCologneEntity(element))
                .collect(Collectors.toList());
    }
}
