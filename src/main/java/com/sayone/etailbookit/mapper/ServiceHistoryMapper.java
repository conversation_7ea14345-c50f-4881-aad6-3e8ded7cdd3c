package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.ServiceHistory;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.util.RetailerContext;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.time.LocalDateTime;

public class ServiceHistoryMapper {

    public static ServiceHistoryDto toServiceHistoryDto(ServiceHistoryProjection serviceHistory){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        return modelMapper.map(serviceHistory, ServiceHistoryDto.class);
    }

    public static ServiceHistory toServiceHistoryEntity(ServiceHistoryDto serviceHistoryDto){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        ServiceHistory serviceHistory = modelMapper.map(serviceHistoryDto, ServiceHistory.class);
        serviceHistory.setRetailer(RetailerContext.getRetailer());
        return serviceHistory;
    }

    public static DetailHistoryDto toDetailedHistoryDto(Appointment appointmentValues) {

        DetailHistoryDto detailHistory = new DetailHistoryDto();
        if (appointmentValues != null) {
            ModelMapper modelMapper = new ModelMapper();
            modelMapper.getConfiguration()
                    .setMatchingStrategy(MatchingStrategies.STRICT);
            modelMapper.typeMap(AppointmentProjection.class, DetailHistoryDto.class).addMappings(mp -> {
                mp.skip(DetailHistoryDto::setAppointmentId);
            });
            detailHistory = modelMapper.map(appointmentValues, DetailHistoryDto.class);

            detailHistory.setAppointmentId(appointmentValues.getId());
            detailHistory.setOrderId(appointmentValues.getAppoinmentNo());
            detailHistory.setStatus(appointmentValues.getServiceStatus());

            if (appointmentValues.getServiceType() != null)
                detailHistory.setServiceType(appointmentValues.getServiceType().getName());

            if (appointmentValues.getService() != null) {
                detailHistory.setServiceName(appointmentValues.getService().getName());

                if (appointmentValues.getService().getMaxOvernights() != null && appointmentValues.getService().getMaxOvernights() != 0)
                    detailHistory.setDuration(appointmentValues.getService().getMaxOvernights().toString());
                if (appointmentValues.getService().getVariableScheduleMaxValue() != null && appointmentValues.getService().getVariableScheduleMaxValue() != null)
                    detailHistory.setDuration(appointmentValues.getService().getVariableScheduleMinValue() + " - " + appointmentValues.getService().getVariableScheduleMaxValue());
                if (appointmentValues.getService().getFixedScheduleValue() != null)
                    detailHistory.setDuration(appointmentValues.getService().getFixedScheduleValue().toString());
                if (appointmentValues.getService().getFixedScheduleUnit() != null)
                    detailHistory.setDurationInterval(appointmentValues.getService().getFixedScheduleUnit());
            }
            if (appointmentValues.getAttendant() != null)
                detailHistory.setAttendantName(appointmentValues.getAttendant().getFirstName());

            if (appointmentValues.getVenue() != null)
                detailHistory.setVenue(appointmentValues.getVenue().getPublicName());

        }
        return detailHistory;
    }

}
