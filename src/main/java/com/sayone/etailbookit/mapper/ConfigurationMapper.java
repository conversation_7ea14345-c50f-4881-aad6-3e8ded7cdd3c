package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.ConfigurationDto;
import com.sayone.etailbookit.model.Configuration;
import org.modelmapper.ModelMapper;

import java.util.List;
import java.util.stream.Collectors;

public class ConfigurationMapper {

    public static ConfigurationDto toConfigurationDto(Configuration generalPetSize){
        return new ModelMapper().map(generalPetSize, ConfigurationDto.class);
    }

    public static List<ConfigurationDto> toConfigurationDtoList(List<Configuration> source) {
        return source
                .stream()
                .map(element -> toConfigurationDto(element))
                .collect(Collectors.toList());
    }

    public static List<Configuration> toConfigurationEntityList(List<ConfigurationDto> source) {
        return source
                .stream()
                .map(element -> toConfigurationEntity(element))
                .collect(Collectors.toList());
    }

    public static Configuration toConfigurationEntity(ConfigurationDto generalPetSize){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(ConfigurationDto.class,Configuration.class);
        return modelMapper.map(generalPetSize, Configuration.class);
    }
}
