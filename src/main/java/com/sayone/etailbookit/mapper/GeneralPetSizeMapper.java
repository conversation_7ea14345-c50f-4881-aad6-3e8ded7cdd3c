package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.GeneralPetSizeDto;
import com.sayone.etailbookit.model.GeneralPetSize;
import com.sayone.etailbookit.util.RetailerContext;
import org.modelmapper.ModelMapper;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class GeneralPetSizeMapper {

    public static GeneralPetSizeDto toGeneralPetSizeDto(GeneralPetSize generalPetSize){
        return new ModelMapper().map(generalPetSize, GeneralPetSizeDto.class);
    }

    public static List<GeneralPetSizeDto> toGeneralPetSizeDtoList(Collection<GeneralPetSize> source) {
        return source
                .stream()
                .map(element -> toGeneralPetSizeDto(element))
                .collect(Collectors.toList());
    }

    public static List<GeneralPetSize> toGeneralPetSizeEntityList(List<GeneralPetSizeDto> source) {
        return source
                .stream()
                .map(element -> toGeneralPetSizeEntity(element))
                .collect(Collectors.toList());
    }

    public static GeneralPetSize toGeneralPetSizeEntity(GeneralPetSizeDto generalPetSizeDto){
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.typeMap(GeneralPetSizeDto.class,GeneralPetSize.class);
        GeneralPetSize generalPetSize = modelMapper.map(generalPetSizeDto, GeneralPetSize.class);
        generalPetSize.setRetailer(RetailerContext.getRetailer());
        return generalPetSize;
    }
}
