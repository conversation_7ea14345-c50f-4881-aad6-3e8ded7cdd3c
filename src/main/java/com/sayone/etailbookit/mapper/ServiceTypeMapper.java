package com.sayone.etailbookit.mapper;


import com.sayone.etailbookit.dto.ServiceTypeDto;
import com.sayone.etailbookit.model.ServiceType;
import com.sayone.etailbookit.util.RetailerContext;
import org.modelmapper.ModelMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

public class ServiceTypeMapper {

    public static ServiceTypeDto toServiceTypeDto(ServiceType serviceType){
        return new ModelMapper().map(serviceType, ServiceTypeDto.class);
    }

    public static List<ServiceTypeDto> toServiceTypeDtoList(List<ServiceType> serviceTypes) {

        return serviceTypes
                .stream()
                .map(element -> toServiceTypeDto(element))
                .collect(Collectors.toList());
    }

    public static ServiceType toServiceTypeEntity(ServiceTypeDto serviceTypeDto){
        ModelMapper modelMapper = new ModelMapper();
        ServiceType serviceType = modelMapper.map(serviceTypeDto, ServiceType.class);
        serviceType.setRetailer(RetailerContext.getRetailer());
        return serviceType;
    }

    public static List<ServiceType> toServiceTypeList(List<ServiceTypeDto> serviceTypeDtos) {
        return serviceTypeDtos
                .stream()
                .map(element -> toServiceTypeEntity(element))
                .collect(Collectors.toList());
    }
}
