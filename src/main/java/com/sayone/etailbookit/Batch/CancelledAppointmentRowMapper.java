package com.sayone.etailbookit.Batch;
import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class CancelledAppointmentRowMapper implements RowMapper<CancelledAppointmentsDto> {

    @Override
    public CancelledAppointmentsDto mapRow(ResultSet rs, int rowNum) throws SQLException {
        CancelledAppointmentsDto cancelledAppointmentsDto = new CancelledAppointmentsDto();
        cancelledAppointmentsDto.setCustomerName(rs.getString("customer_name"));
        cancelledAppointmentsDto.setService(rs.getString("service_name"));
        cancelledAppointmentsDto.setVenue(rs.getString("venue_name"));
        cancelledAppointmentsDto.setAppointmentDateTime(rs.getString("appointment_date_time"));
        cancelledAppointmentsDto.setPetName(rs.getString("pet_name"));
        cancelledAppointmentsDto.setAppointmentCancellationRate(rs.getBigDecimal("cancellation_value"));
        return cancelledAppointmentsDto;
    }
}
