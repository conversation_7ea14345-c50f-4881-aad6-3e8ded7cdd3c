package com.sayone.etailbookit.Batch;

import com.sayone.etailbookit.dto.PetBirthdayDto;
import com.sayone.etailbookit.projections.CancelledAppointmentProjections;
import lombok.Data;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class WriterConfiguration {

    private List<PetBirthdayDto> items = new ArrayList<>();

    @Bean
    public ItemWriter<PetBirthdayDto> writer(){
        return items :: addAll;
    }

    public List<PetBirthdayDto> getItems(){
        List<PetBirthdayDto> petBirthdayDtoList = new ArrayList<>(items);
        items.clear();
        return petBirthdayDtoList;
    }
}