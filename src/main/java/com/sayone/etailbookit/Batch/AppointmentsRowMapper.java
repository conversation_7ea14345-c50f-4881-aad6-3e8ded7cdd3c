package com.sayone.etailbookit.Batch;

import com.sayone.etailbookit.dto.BatchAppointmentDto;
import org.springframework.jdbc.core.RowMapper;
import java.sql.ResultSet;
import java.sql.SQLException;

public class AppointmentsRowMapper implements RowMapper<BatchAppointmentDto> {
    @Override
    public BatchAppointmentDto mapRow(ResultSet rs, int rowNum) throws SQLException {
        BatchAppointmentDto batchAppointmentDto = new BatchAppointmentDto();
        batchAppointmentDto.setId(rs.getInt("id"));
        batchAppointmentDto.setCustomerName(rs.getString("customerName"));
        batchAppointmentDto.setService(rs.getString("service"));
        batchAppointmentDto.setPetName(rs.getString("petName"));
        batchAppointmentDto.setOrderId(rs.getLong("orderId"));
        batchAppointmentDto.setEventId(rs.getString("eventId"));
        batchAppointmentDto.setServiceStartAt(rs.getString("serviceStartAt"));
        batchAppointmentDto.setServiceEndAt(rs.getString("serviceEndAt"));
        batchAppointmentDto.setDuration(rs.getString("duration"));
        batchAppointmentDto.setDate(rs.getString("date"));
        batchAppointmentDto.setAmount(rs.getBigDecimal("amount"));
        return batchAppointmentDto;
    }
}
