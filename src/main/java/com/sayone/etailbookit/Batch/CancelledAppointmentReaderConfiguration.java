package com.sayone.etailbookit.Batch;

import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import lombok.Data;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
@Configuration
@Data
public class CancelledAppointmentReaderConfiguration {

    @Autowired
    DataSource dataSource;

    private String retailer;

    @Bean
    @StepScope
    public JdbcCursorItemReader<CancelledAppointmentsDto> cancelledAppointmentProjectionsItemReader() {
        JdbcCursorItemReader<CancelledAppointmentsDto> cursorItemReader = new JdbcCursorItemReader<>();
        cursorItemReader.setDataSource(dataSource);
        String query = cancelledAppointmentQuery();
        cursorItemReader.setSql(query);
        cursorItemReader.setRowMapper(new CancelledAppointmentRowMapper());
        cursorItemReader.setVerifyCursorPosition(false);
        return cursorItemReader;
    }

    private String cancelledAppointmentQuery() {
            StringBuilder queryBuilder = new StringBuilder("SELECT s.name AS service_name,s.cancelation_amount_value AS cancellation_value,v.internal_name AS venue_name,p.name AS pet_name,a.customer_name AS customer_name,a.start_date_and_time AS appointment_date_time FROM appointment a JOIN     service s ON a.service = s.id JOIN venue v ON a.venue = v.id JOIN  pet p ON a.pet = p.id where a.retailer='"+retailer+"'" );
            return queryBuilder.toString();
    }
}
