package com.sayone.etailbookit.Batch;

import com.sayone.etailbookit.dto.BatchAppointmentDto;
import com.sayone.etailbookit.util.AppointmentExcelGenerator;
import lombok.Data;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

@Configuration
@Data
public class AppointmentWriterConfiguration {
    @Autowired
    AppointmentExcelGenerator appointmentExcelGenerator;

    private List<BatchAppointmentDto> items = new ArrayList<>();

    @Bean
    public ItemWriter<BatchAppointmentDto> appointmentWriter(){
        return items :: addAll;
    }

    private void writeWorkbookToOutputStream(Workbook workbook, OutputStream outputStream) throws IOException {
        try {
            workbook.write(outputStream);
            outputStream.flush();
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }

    public ByteArrayOutputStream getGeneratedContent() throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Appointments");
        sheet.setDefaultColumnWidth(15);
        appointmentExcelGenerator.createHeaderRow(sheet);
        for(BatchAppointmentDto item : items){
            appointmentExcelGenerator.createDataRow(sheet, item);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        writeWorkbookToOutputStream(workbook, outputStream);
        items.clear();
        return outputStream;
    }
}
