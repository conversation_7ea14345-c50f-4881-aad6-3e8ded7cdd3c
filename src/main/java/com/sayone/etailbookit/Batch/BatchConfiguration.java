package com.sayone.etailbookit.Batch;

import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import com.sayone.etailbookit.dto.BatchAppointmentDto;
import com.sayone.etailbookit.dto.PetBirthdayDto;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.projections.CancelledAppointmentProjections;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableBatchProcessing
public class BatchConfiguration {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private AppointmentReaderConfiguration appointmentReaderConfiguration;

    @Autowired
    private AppointmentWriterConfiguration appointmentWriterConfiguration;
    @Autowired
    private ReaderConfiguration petReaderConfiguration;

    @Autowired
    private WriterConfiguration pettWriterConfiguration;

    @Autowired
    private CancelledAppointmentReaderConfiguration cancelledAppointmentReaderConfiguration;

    @Autowired
    private CancelledAppointmentWritereConfiguration cancelledAppointmentWritereConfiguration;

     public Step appointmentStep() throws EtailBookItException {
         return stepBuilderFactory.get("appointmentStep")
                .<BatchAppointmentDto, BatchAppointmentDto>chunk(500)
                .reader(appointmentReaderConfiguration.appointmentReader())
                .writer(appointmentWriterConfiguration.appointmentWriter())
                .build();
    }

    public Step petStep() throws EtailBookItException {
        return stepBuilderFactory.get("petStep")
                .<PetBirthdayDto, PetBirthdayDto>chunk(500)
                .reader(petReaderConfiguration.reader())
                .writer(pettWriterConfiguration.writer())
                .build();
    }

    public Step cancelledAppointmentStep() throws EtailBookItException {
        return stepBuilderFactory.get("cancelledAppointmentStep")
                .<CancelledAppointmentsDto,CancelledAppointmentsDto>chunk(100)
                .reader(cancelledAppointmentReaderConfiguration.cancelledAppointmentProjectionsItemReader())
                .writer(cancelledAppointmentWritereConfiguration.appointmentProjectionsItemWriter())
                .build();
    }

      @Bean
    public Job appointmentJob() throws EtailBookItException {
        return jobBuilderFactory.get("myJob")
                .incrementer(new RunIdIncrementer())
                .flow(appointmentStep())
                .end()
                .build();
    }
    @Bean
    public Job petJob() throws EtailBookItException {
        return jobBuilderFactory.get("myJob")
                .incrementer(new RunIdIncrementer())
                .flow(petStep())
                .end()
                .build();
    }

    @Bean
    public Job cancelledAppointmentJob() throws EtailBookItException{
        return jobBuilderFactory.get("cancelledAppointmentJob")
                .incrementer(new RunIdIncrementer())
                .flow(cancelledAppointmentStep())
                .end()
                .build();
    }
}
