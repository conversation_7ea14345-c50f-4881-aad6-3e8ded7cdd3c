package com.sayone.etailbookit.Batch;

import com.sayone.etailbookit.dto.BatchAppointmentDto;
import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import com.sayone.etailbookit.util.CancelledAppointmentExcelGenerator;
import lombok.Data;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

@Configuration
@Data
public class CancelledAppointmentWritereConfiguration {

    @Autowired
    CancelledAppointmentExcelGenerator appointmentExcelGenerator;

    private List<CancelledAppointmentsDto> cancelledAppointmentList = new ArrayList<>();

    @Bean
    public ItemWriter<CancelledAppointmentsDto> appointmentProjectionsItemWriter(){return cancelledAppointmentList::addAll;}

    public List<CancelledAppointmentsDto> getCancelledApppointments(){

        List<CancelledAppointmentsDto> cancelledAppointmentProjections = new ArrayList<>(cancelledAppointmentList);
        cancelledAppointmentList.clear();
        return cancelledAppointmentProjections;

    }

    private void writeWorkbookToOutputStream(Workbook workbook, OutputStream outputStream) throws IOException {
        try {
            workbook.write(outputStream);
            outputStream.flush();
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }

    public ByteArrayOutputStream getGeneratedContent() throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Cancelled_Appointments");
        sheet.setDefaultColumnWidth(15);
        appointmentExcelGenerator.createHeaderRow(sheet);
        for(CancelledAppointmentsDto item : cancelledAppointmentList){
            appointmentExcelGenerator.createDataRow(sheet, item);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        writeWorkbookToOutputStream(workbook, outputStream);
        cancelledAppointmentList.clear();
        return outputStream;
    }
}
