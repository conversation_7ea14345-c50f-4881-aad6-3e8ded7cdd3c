package com.sayone.etailbookit.Batch;
import com.sayone.etailbookit.dto.PetBirthdayDto;
import org.springframework.jdbc.core.RowMapper;
import java.sql.ResultSet;
import java.sql.SQLException;

public class PetRowMapper implements RowMapper<PetBirthdayDto> {
    @Override
    public PetBirthdayDto mapRow(ResultSet rs, int rowNum) throws SQLException {
        PetBirthdayDto petBirthdayDto = new PetBirthdayDto();
        petBirthdayDto.setId(rs.getInt("id"));
        petBirthdayDto.setCustomer_id(rs.getInt("customer_id"));
        petBirthdayDto.setDob(rs.getString("dob"));
        petBirthdayDto.setName(rs.getString("name"));
        return petBirthdayDto;
    }
}
