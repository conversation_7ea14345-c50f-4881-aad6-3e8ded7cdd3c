package com.sayone.etailbookit.Batch;
import com.sayone.etailbookit.dto.PetBirthdayDto;
import com.sayone.etailbookit.projections.CancelledAppointmentProjections;
import lombok.Data;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;
import java.util.List;
import java.util.Optional;

@Configuration
@Data
public class ReaderConfiguration {

    @Autowired
    DataSource dataSource;

    private Optional<String> petName;
    private Integer petDob;
    private List<Integer> petIds;
    private List<Integer> cusIds;
    private String retailer;
    private String sortBy;

    @Bean
    @StepScope
    public JdbcCursorItemReader<PetBirthdayDto> reader() {
        JdbcCursorItemReader<PetBirthdayDto> cursorItemReader = new JdbcCursorItemReader<>();
        cursorItemReader.setDataSource(dataSource);
        String query = buildQuery();
        cursorItemReader.setSql(query);
        cursorItemReader.setRowMapper(new PetRowMapper());
        cursorItemReader.setVerifyCursorPosition(false);
        return cursorItemReader;
    }


    private String buildQuery() {
        StringBuilder queryBuilder = new StringBuilder("Select p.id, p.customer_id, p.name, p.dob FROM Pet p WHERE retailer = '"+retailer+"' AND deleted = false AND " );

        appendConditionWithNullCheck(queryBuilder, "name", petName.orElse(""));
        appendConditionWithNullCheck(queryBuilder, "EXTRACT(month  FROM dob)",  (petDob != null) ? petDob.toString() : null);

        if (queryBuilder.toString().endsWith(" AND ")) {
            queryBuilder.delete(queryBuilder.length() - " AND ".length(), queryBuilder.length());
        }
        if (queryBuilder.toString().endsWith(" WHERE ")) {
            queryBuilder.delete(queryBuilder.length() - " WHERE ".length(), queryBuilder.length());
        }

        appendConditionForListSearch(queryBuilder, "id", petIds);
        appendConditionForListSearch(queryBuilder, "customer_id", cusIds);
        appendConditionToSort(queryBuilder, sortBy);
        return queryBuilder.toString();
    }

    private void appendConditionWithNullCheck(StringBuilder queryBuilder, String paramName, String paramValue) {
        if (paramValue != null && !paramValue.isEmpty()) {
            queryBuilder.append(paramName).append(" = '").append(paramValue).append("' AND ");
        }
    }

    private void appendConditionForListSearch(StringBuilder queryBuilder, String paramName, List<Integer> list){
        if(list != null && !list.isEmpty()) {
            queryBuilder.append(" OR ").append(paramName).append(" IN ").append("(");
            boolean firstValue = true;
            for(Integer id : list){
                if(!firstValue){
                    queryBuilder.append(",");
                }
                queryBuilder.append(id);
                firstValue = false;
            }
            queryBuilder.append(")");
        }
    }

    private void appendConditionToSort(StringBuilder queryBuilder, String paramValue){
        if (paramValue != null && !paramValue.isEmpty()) {
            queryBuilder.append(" ORDER BY ").append(paramValue);
        }
    }
}
