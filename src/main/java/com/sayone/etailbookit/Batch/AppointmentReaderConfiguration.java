package com.sayone.etailbookit.Batch;

import com.amazonaws.services.medialive.model.Offering;
import com.sayone.etailbookit.dto.BatchAppointmentDto;
import com.sayone.etailbookit.util.ServiceStatus;
import lombok.Data;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Objects;

@Configuration
@Data
public class AppointmentReaderConfiguration {

    @Autowired
    DataSource dataSource;

    private Integer customer;
    private Integer pet;
    private Integer attendant;
    private Integer venue;
    private String status;
    private Integer petType;
    private String search;
    private Integer service;
    private String startDate;
    private String endDate;
    private String retailer;
    private boolean fetchAll;

    @Bean
    @StepScope
    public JdbcCursorItemReader<BatchAppointmentDto> appointmentReader() {
        JdbcCursorItemReader<BatchAppointmentDto> cursorItemReader = new JdbcCursorItemReader<>();
        cursorItemReader.setDataSource(dataSource);
        String query = buildQuery();
        cursorItemReader.setSql(query);
        cursorItemReader.setRowMapper(new AppointmentsRowMapper());
        return cursorItemReader;
    }

    private String buildQuery() {
        StringBuilder queryBuilder = new StringBuilder("SELECT a.id, a.customer_name AS customerName, a.service, p.name AS petName, a.order_reference AS orderId, a.appoinment_no AS eventId, a.service_start_at AS serviceStartAt, a.service_end_at AS serviceEndAt, a.duration, a.appointment_date AS date, a.amount FROM appointment a JOIN pet p ON a.pet = p.id JOIN service_type s ON a.service_type = s.id JOIN venue v ON a.venue = v.id WHERE a.retailer = '"+retailer+"' AND ");
        appendConditionWithNullCheck(queryBuilder, "a.customer_id", customer);
        appendConditionWithNullCheck(queryBuilder, "pet", pet);
        appendConditionWithNullCheck(queryBuilder, "attendant", attendant);
        appendConditionWithNullCheck(queryBuilder, "venue", venue);
        appendEnumConditionWithNullCheck(queryBuilder, "service_status", status);
        appendConditionWithNullCheck(queryBuilder, "petType", petType);
        appendSearchConditionWithNullCheck(queryBuilder);
        appendConditionWithNullCheck(queryBuilder, "service", service);
        appendDateConditionWithNullCheck(queryBuilder, "start_date_and_time", startDate, endDate);

        if (queryBuilder.toString().endsWith(" AND ")) {
            queryBuilder.delete(queryBuilder.length() - " AND ".length(), queryBuilder.length());
        }
        if (queryBuilder.toString().endsWith(" WHERE ")) {
            queryBuilder.delete(queryBuilder.length() - " WHERE ".length(), queryBuilder.length());
        }
        return queryBuilder.toString();
    }

    private void appendConditionWithNullCheck(StringBuilder queryBuilder, String paramName, Integer paramValue) {
        if (paramValue != null) {
            queryBuilder.append(paramName).append(" = '").append(paramValue).append("' AND ");
        }
    }

    private void appendDateConditionWithNullCheck(StringBuilder queryBuilder, String paramName, String startDate, String endDate) {
        if (startDate != null && endDate != null) {
            queryBuilder.append(paramName).append(" >= '").append(startDate).append("' AND ").append(paramName).append("<= '").append(endDate).append("' AND ");
        }
        if (startDate != null && endDate == null) {
            queryBuilder.append(paramName).append(" >= '").append(startDate).append("' AND ");
        }
        if (startDate == null && endDate != null) {
            queryBuilder.append(paramName).append("<= '").append(endDate).append("' AND ");
        }
    }

    private void appendSearchConditionWithNullCheck(StringBuilder queryBuilder){
        if(search != null && !search.isEmpty()){
            queryBuilder.append("(").append("customer_name =").append("'").append(search).append("'").append(" or ").append("p.name =").append("'").append(search).append("'").append(" or ").append("s.name =").append("'").append(search).append("'").append(" or ").append("v.internal_name =").append("'").append(search).append("'").append(" or ").append("v.public_Name =").append("'").append(search).append("'").append(")").append(" AND ");
        }
    }
    private void appendEnumConditionWithNullCheck(StringBuilder queryBuilder, String paramName, String paramValue) {
        if (paramValue != null && !Objects.equals(paramValue, "")) {
            queryBuilder.append(paramName).append(" = '").append(ServiceStatus.valueOf(paramValue).ordinal()).append("' AND ");
        }
    }
}
