package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;

public interface PetDropdownProjection {

    Integer getId();
    String getName();
    String getPhotos();
    LocalDate getDob();
    @Value("#{target.petType}")
    PetType getPetTypeDetails();
    Boolean getDeleted();

    interface PetType{
        @Value("#{target.petTypeId}")
        Integer getId();
        @Value(("#{target.name}"))
        String getName();
    }

}
