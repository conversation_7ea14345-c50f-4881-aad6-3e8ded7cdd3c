package com.sayone.etailbookit.projections;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sayone.etailbookit.model.PetBreedsInformation;
import com.sayone.etailbookit.model.WeightRange;
import com.sayone.etailbookit.util.ServiceStatus;
import com.sun.istack.NotNull;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

public interface
AppointmentListingProjection {

    @Value("#{target.id}")
    Integer getAppointmentId();
    Integer getCustomerId();
    String getCustomerName();
    @Value("#{target.appoinmentNo}")
    String getAppointmentNo();
    @Value("#{target.service.name}")
    String getService();
    @Value("#{target.serviceType?.name}")
    String getServiceType();
    @Value("#{target.waiverOfLiabilityDoc}")
    AppointmentWaiverOfLiability getAppointmentWaiverOfLiability();
    @Value("#{target.service.color}")
    String getServiceColor();
    @Value("#{target.venue.publicName}")
    String getVenue();
    @Value("#{target.venue.venueId}")
    String getVenueId();
    @Value("#{target.pet}")
    Pet getPetDetails();
    @Value("#{target.attendant}")
    Attendant getAttendantDetails();
    @Value("#{target.convertToUserDate()}")
    String getDate();
    @Value("#{target.getStartTime()}")
    String  getStartTime();
    @Value("#{target.getOffsetStartTime()}")
    OffsetDateTime getOffsetStartTime();
    @Value("#{target.getOffsetEndTime()}")
    OffsetDateTime getOffsetEndTime();
    @Value("#{target.getEndTime()}")
    String getEndTime();
    @Value("#{target.calculateDuration(target)}")
    String getDuration();
    @Value("#{target.serviceStartAt != null ? target.serviceStartAt.toString() : new String()}")
    String getServiceStartAt();
    @Value("#{target.serviceEndAt != null ? target.serviceEndAt.toString() : new String()}")
    String getServiceEndAt();
    ServiceStatus getServiceStatus();
    String getCancellationReason();
    @Value("#{target.weightRange}")
    WeightRange getWeightRangeDetails();
    String getPaymentStatus();
    String getUpdatedCustomerName();
    String getCreatedCustomerName();
    Integer getCreatedCustomerId();
    Integer getUpdatedCustomerId();

    Boolean getOptInForSms();

    String getSmsPhoneNumber();

    String getNote();
    LocalDate setOffsetAppointmentDate(LocalDate date);
    LocalDate getOffsetAppointmentDate();
    BigDecimal getExactWeight();
    String getWeightUnit();
    Boolean getWaiverAcknowledged();

    interface Pet {
        Integer getId();
        String getName();
        @Value("#{target.petType?.name}")
        String getPetType();
        List<String> getPhotos();
        @Value("#{target.exactWeight == null ? new java.math.BigDecimal('0.00') : target.exactWeight}")
        BigDecimal getExactWeight();
        @Value("#{target.weightRange}")
        WeightRange getWeightRange();
        PetSize getSize();
        @Value("#{target.petBreedsInformations != null && !target.petBreedsInformations.isEmpty() ? target.petBreedsInformations : new java.util.ArrayList()}")
        List<PetBreedsInformation> getPetBreedsInformations();
    }

    interface  Attendant{
        @Value("#{target.attendantId}")
        Integer getId();
        @Value("#{target.firstName + ' ' + target.lastName}")
        String  getAttendantName();
        @Value("#{target.color}")
        String getColor();
        @Value("#{target.deleted}")
        boolean getDeleted();
    }

    interface WeightRange {
        @Value("#{target.weightRangeId}")
        Integer getId();
        BigDecimal getMinValue();
        BigDecimal getMaxValue();
        String getWeightUnit();
    }

    interface PetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }

    interface AppointmentWaiverOfLiability {
        @Value("#{target.id}")
        Integer getId();
        @Value("#{target.fileUrl}")
        String getFileUrl();
    }

    interface PetBreedsInformation {
        Integer getId();
        @Value("#{target.breed?.id}")
        Integer getBreedId();
        @Value("#{target.breed?.name}")
        String getBreedName();
        @Value("#{target.breed?.deleted}")
        Boolean getDeleted();
    }

}