package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

public interface VenueListingProjection {

    @Value("#{target.venueId}")
    Integer getId();
    String getInternalName();
    String getPublicName();
    String getLocationType();
    Integer getParticipantLimitService();
    BigDecimal getExtraCharge();
    String getExtraCurrency();
    String getAvailabilityInterval();
    String getAvailabilityIntervalUnit();
    Boolean getActive();
    Boolean getDeleted();
}
