package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;
import java.time.OffsetDateTime;
import java.util.List;

public interface TimeSlotProjection {

    Integer getId();

    String getSlotName();

    @Value("#{target.slotStartTime}")
    OffsetDateTime getSlotStartTime();

    @Value("#{target.slotEndTime}")
    OffsetDateTime getSlotEndTime();

    String getAvailableDay();

    Integer getAvailabilityInterval();

    String getAvailabilityIntervalUnit();

    Boolean getDeleted();

    @Value("#{target.services}")
    List<Service> getService();

    Venue getVenue();

    Attendant getAttendant();

    String getColor();

    TimeSlotCluster getTimeSlotCluster();

    interface Service {
        @Value("#{target.serviceId}")
        Integer getServiceId();

        @Value("#{target.name}")
        String getServiceName();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getVenueId();

        @Value("#{target.publicName}")
        String getVenueName();
    }

    interface Attendant{
        @Value("#{target.attendantId}")
        Integer getAttendantId();

        @Value("#{target.firstName}")
        String getAttendantName();

        @Value("#{target.deleted}")
        Boolean getDeleted();
    }

    interface TimeSlotCluster {
        @Value("#{target.clusterId}")
        Integer getClusterId();

        @Value("#{target.availableDays}")
        List<String> getAvailableDays();
    }

}
