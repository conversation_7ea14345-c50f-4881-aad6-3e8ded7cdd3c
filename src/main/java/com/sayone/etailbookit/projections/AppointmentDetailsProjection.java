package com.sayone.etailbookit.projections;

import com.sayone.etailbookit.util.ServiceStatus;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

public interface AppointmentDetailsProjection {
    Integer getId();
    Integer getCustomerId();
    Integer getAddressId();
    String getAppoinmentNo();
    Long getOrderReference();

    ServiceType getServiceType();
    Service getService();
    Attendant getAttendant();
    Venue getVenue();
    @Value("#{target.shamppo}")
    Shampoo getShampoo();
    Cologne getCologne();
    List<Addon> getAddOnService();
    @Value("#{target.convertToUserDate()}")
    String getDate();
    @Value("#{target.endDate != null ? target.endDate.toString() : new String()}")
    String getEndDate();
    @Value("#{target.getStartTime()}")
    String getStartTime();
    @Value("#{target.getEndTime()}")
    String getEndTime();
    String getUpdatedCustomerName();
    String getCreatedCustomerName();
    Integer getCreatedCustomerId();
    Integer getUpdatedCustomerId();
    @Value("#{target.getOffsetStartTime()}")
    OffsetDateTime getOffsetStartTime();
    @Value("#{target.getOffsetEndTime()}")
    OffsetDateTime getOffsetEndTime();
    Integer getTimeSlotId();

    @Value("#{target.createdAt != null ? target.convertCreatedAtToUTC(target.createdAt) : new String()}")
    String getCreatedAt();

    @Value("#{target.serviceStartAt != null ? target.convertToLocalDateTime(target.serviceStartAt) : new String()}")
    String getStartDateAndTime();
    @Value("#{target.serviceEndAt != null ? target.convertToLocalDateTime(target.serviceEndAt) : new String()}")
    String getEndDateAndTime();
    Boolean getRecurringEnabled();
    LocalDate getDateTimeOnboarding();
    Boolean getHaveSibilings();
    Integer getSibilingCount();
    Boolean getWaiverAcknowledged();
    String getNote();
    String getCustomerName();
    BigDecimal getExactWeight();
    String getWeightUnit();
    LocalDate setOffsetAppointmentDate(LocalDate date);
    LocalDate getOffsetAppointmentDate();
    String getCancellationReason();

    Pet getPet();
    @Value("#{target.petType}")
    PetType getPetTypeDetails();
    @Value("#{target.weightRange}")
    WeightRange getWeightRangeDetails();
    @Value("#{target.temperament}")
    Temperament getTemperamentDetails();
    @Value("#{target.hairLength}")
    HairLength getHairLengthDetails();
    @Value("#{target.hairTexture}")
    HairTexture getHairTextureDetails();
    List<DesiredHairLength> getDesiredHairLengths();

    List<Allergy> getAllergies();

    @Value("#{target.vaccinationInfo}")
    List<VaccineInfo> getVaccinationInfo();
    @Value("#{target.quoteAdjustments}")
    List<quoteAdjustmentInfo> getQuoteAdjustments();
    @Value("#{target.waiverOfLiabilityDoc}")
    WaiverOfLiabilityInfo getWaiverOfLiabilityInfo();

    Integer getFeedingCount();
    Boolean getBringYourFood();
    List<String> getGrainFreeRecipes();
    List<String> getGrainFullRecipes();

    List<Personality> getPersonalityParameters();
    List<Behaviour> getUnfriendlyBehaviourTriggers();

    BigDecimal getAmount();
    BigDecimal getServiceCost();
    String getAmountCurrency();
    BigDecimal getTipAmount();
    String getTipAmountCurrency();

    @Value("#{target.appointmentVetInformation}")
    List<VetInfo> getVetInformation();
    @Value("#{target.appointmentEmergencyContactInfo}")
    List<EmergencyInfo> getEmergencyInfo();

    List<AppointmentDocuments> getOtherDoc();
    ServiceStatus getServiceStatus();
    String getPaymentStatus();
    @Value("#{target.serviceStartAt != null ? target.serviceStartAt.toString() : new String()}")
    String getServiceStartAt();
    @Value("#{target.serviceEndAt != null ? target.serviceEndAt.toString() : new String()}")
    String getServiceEndAt();
    @Value("#{target.calculateDuration(target)}")
    String getDuration();
    String getAddress();
    //add service history

    Boolean getOptInForSms();

    String getSmsPhoneNumber();

    interface ServiceType {
        @Value("#{target.serviceTypeId}")
        Integer getId();
        String getName();
        Boolean getDeleted();
    }

    interface Service {
        Integer getServiceId();
        String getName();
        String getScheduleType();
        Integer getFixedScheduleValue();
        String getFixedScheduleUnit();
        String getVariableScheduleMinValue();
        String getVariableScheduleMaxValue();
        Boolean getDeleted();
        List<ServiceBreedsInformations> getServiceBreedsInformations();
    }

    interface ServiceBreedsInformations {
        Integer getId();

        String getDurationType();

        String getDuration();

        BigDecimal getChargeAmount();

        @Value("#{target.breed.name}")
        String getBreedName();

        @Value("#{target.breed.id}")
        Integer getBreedId();

        @Value("#{target.breed.deleted}")
        Boolean getDeleted();
    }

    interface Attendant {
        Integer getAttendantId();
        String getFirstName();
        String getLastName();
        Boolean getDeleted();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getId();
        String getInternalName();
        String getPublicName();
        Boolean getDeleted();
        List<String> getMobileZipCode();
    }

    interface Shampoo {
        Integer getId();
        String getName();
        Double getExtraCharge();
    }

    interface Cologne {
        Integer getId();
        String getName();
        Double getExtraCharge();
    }

    interface Addon {
        @Value("#{target.addonServiceId}")
        Integer getId();
        String getName();
        BigDecimal getTackOnExtraAmount();
    }

    interface DesiredHairLength {
        Integer getId();
        @Value("#{target.desiredHairLength.name}")
        String getName();
        String getValue();
    }

    interface Allergy {
        @Value("#{target.allergyId}")
        Integer getId();
        String getName();
    }

    interface VetInfo {
        Integer getId();
        @Value("#{target.vetInformation.name}")
        String getName();
        String getValue();
    }

    interface EmergencyInfo {
        Integer getId();
        @Value("#{target.emergencyContactInfo.name}")
        String getName();
        String getValue();
    }

    interface VaccineInfo {
        Integer getId();
        @Value("#{target.vaccinationRecords.name}")
        String getName();
        @Value("#{target.dateExpires != null ? target.dateExpires.toString() : ''}")
        String getDateExpires();
        @Value("#{target.dateAdministrated != null ? target.dateAdministrated.toString() : ''}")
        String getDateAdministrated();
        String getFile();
    }

    interface WaiverOfLiabilityInfo {
        Integer getId();

        @Value("#{target.waiverOfLiability.waiverOfLiabilityId}")
        Integer getWaiverOfLiabilityId();

        @Value("#{target.waiverOfLiability.displayWaiverOfLiability}")
        Boolean getDisplayWaiverOfLiability();

        @Value("#{target.waiverOfLiability.requireCustomerSignature}")
        Boolean getRequireCustomerSignature();

        @Value("#{target.waiverOfLiability.displayPDFUpload}")
        Boolean getDisplayPDFUpload();

        String getFileUrl();

        String getTermsOfServiceFileUrl();
    }

    interface  quoteAdjustmentInfo{
        Integer getQuoteAdjustmentId();
        String getQuoteName();
        BigDecimal getQuotePrice();
    }
    interface Personality {
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface Behaviour {
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface Pet {
        Integer getId();
        String getName();
        String getSex();
        Boolean getSpayed();
        String getColor();
        List<String> getPhotos();
        @Value("#{target.exactWeight == null ? new java.math.BigDecimal('0.00') : target.exactWeight}")
        BigDecimal getExactWeight();
        @Value("#{target.weightRange}")
        AppointmentListingProjection.WeightRange getWeightRange();
        @Value("#{target.petType}")
        PetDetailsProjection.PetType getPetTypeDetails();
        List<PetDetailsProjection.PetBreedsInformation> getPetBreedsInformations();
    }

    interface PetType {
        @Value("#{target.petTypeId}")
        Integer getId();
        String getName();
        String getFileUrl();
        Boolean getActive();
    }

    interface WeightRange {
        @Value("#{target.weightRangeId}")
        Integer getId();
        BigDecimal getMinValue();
        BigDecimal getMaxValue();
        String getWeightUnit();
    }

    interface Temperament {
        @Value("#{target.temperamentId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface HairLength {
        @Value("#{target.hairLengthId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface HairTexture {
        @Value("#{target.hairTextureId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface AppointmentDocuments {
        @Value("#{target.documentOption.documentOptionId}")
        Integer getId();
        @Value("#{target.documentOption.name}")
        String getName();
        @Value("#{target.documentOption.requireDescription}")
        Boolean getRequireDescription();
        @Value("#{target.documentOption.requireUpload}")
        Boolean getRequireUpload();
        String getDescription();
        String getFile();
        @Value("#{target.documentOption.active}")
        Boolean getActive();
    }

}
