package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.util.List;

public interface AppointmentUploadedDocumentsProjection {
    Integer getId();
    Integer getCustomerId();
    String getAppoinmentNo();

    @Value("#{target.vaccinationInfo}")
    List<VaccineInfo> getVaccinationInfo();

    @Value("#{target.waiverOfLiabilityDoc}")
    WaiverOfLiabilityInfo getWaiverOfLiabilityInfo();

    List<AppointmentDocuments> getOtherDoc();

    interface WaiverOfLiabilityInfo {
        Integer getId();

        String getFileUrl();

        String getTermsOfServiceFileUrl();
    }

    interface VaccineInfo {
        Integer getId();
        @Value("#{target.vaccinationRecords.name}")
        String getName();
        String getFile();
    }

    interface AppointmentDocuments {
        @Value("#{target.documentOption.documentOptionId}")
        Integer getId();
        @Value("#{target.documentOption.name}")
        String getName();
        String getFile();
    }
}
