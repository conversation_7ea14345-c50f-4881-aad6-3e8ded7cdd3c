package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.time.OffsetTime;
import java.util.List;

public interface AttendantModelAvailabilityProjections {

    @Value("#{target.attendantId}")
    Integer getAttendantId();
    Integer getCapacityLimit();

    List<Availability> getAvailabilityDays();


    interface Availability {
        String getAvailableDay();
        OffsetTime getAvailabilityOpenTime();
        OffsetTime getAvailabilityCloseTime();
    }
}
