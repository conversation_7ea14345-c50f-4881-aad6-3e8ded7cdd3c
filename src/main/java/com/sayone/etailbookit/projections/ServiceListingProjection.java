package com.sayone.etailbookit.projections;

import com.sayone.etailbookit.model.GeneralPetSize;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.List;

public interface ServiceListingProjection {

    Integer getServiceId();
    String getName();
    PetTypeProjection getPetType();
    ServiceTypeProjection getServiceType();
    String getScheduleType();
    Boolean getPaymentAtTimeOfBooking();
    Boolean getPaymentAtBeginningOfService();
    Boolean getPaymentAfterServiceCompleted();
    BigDecimal getAmountPerUnit();
    String getServiceUnit();
    Boolean getIsTaxable();
    Boolean getDeleted();
   // List<GeneralPetSizeProjection> getGeneralPetSize();
    List<Temperment> getTemperaments();
    List<GeneralPetSize> getGeneralPetSize();
    Boolean getChargeCancelationFee();
    String getCancelationAmountType();
    BigDecimal getCancelationAmountValue();

    interface Temperment {
        @Value("#{target.getTemperamentId()}")
        Integer getId();
        String getName();

        Boolean getActive();

        Integer getIndexValue();
    }

    interface GeneralPetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }
}
