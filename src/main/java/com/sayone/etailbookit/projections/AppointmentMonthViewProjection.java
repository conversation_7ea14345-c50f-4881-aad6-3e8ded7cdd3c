package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.time.OffsetDateTime;

public interface AppointmentMonthViewProjection {

    String getService();  // Service name

    String getServiceColor();  // Service color

    String getDate();

    Integer getRemainingAppointments();

    String getStartTime();

    OffsetDateTime getOffsetStartTime();

    OffsetDateTime getOffsetEndTime();

    String getEndTime();

    String getUpdatedCustomerName();
    String getCreatedCustomerName();
    Integer getCreatedCustomerId();
    Integer getUpdatedCustomerId();

}
