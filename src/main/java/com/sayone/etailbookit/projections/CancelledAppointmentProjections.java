package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;

public interface CancelledAppointmentProjections {
    Integer getId();
    Integer getCustomerId();
    String  getCustomerName();
    Attendant getAttendant();
    Venue getVenue();
    Service getService();
    Pet getPet();
    String getAppointmentStartDateAndTime();


    interface Service {
        Integer getServiceId();
        String getName();
        BigDecimal getcancelationAmountValue();
    }

    interface Attendant {
        Integer getAttendantId();
        String getFirstName();
        String getLastName();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getId();
        String getInternalName();
        String getPublicName();
    }

    interface Pet {
        String getName();
    }
}
