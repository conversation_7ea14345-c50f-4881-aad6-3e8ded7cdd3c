package com.sayone.etailbookit.projections;


import com.sayone.etailbookit.model.Attendant;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;
import java.time.OffsetDateTime;

public interface VacationProjection {
    Integer getId();
    @Value("#{target.Attendant}")
    Attendant getAttendant();
    @Value("#{target.vacationStartTime.toString()}")
    String getVacationStartTime();
    @Value("#{target.vacationEndTime.toString()}")
    String getVacationEndTime();
    LocalDate getVacationStartDate();
    LocalDate getVacationEndDate();

    interface Attendant{
        @Value("#{target.AttendantId}")
        Integer getAttendantId();
        @Value("#{target.firstName + ' ' + target.lastName}")
        String getName();
    }


}