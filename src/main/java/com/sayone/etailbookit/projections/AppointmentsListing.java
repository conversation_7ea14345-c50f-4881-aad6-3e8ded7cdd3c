package com.sayone.etailbookit.projections;


import com.sayone.etailbookit.util.ServiceStatus;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.List;

public interface AppointmentsListing {

    @Value("#{target.id}")
    Integer getAppointmentId();
    Integer getCustomerId();
    String getCustomerName();
    Long getOrderReference();
    @Value("#{target.appoinmentNo}")
    String getAppointmentNo();
    @Value("#{target.serviceType.name}")
    String getServiceType();
    @Value("#{target.service.name}")
    String getService();
    @Value("#{target.attendant.firstName + ' ' + target.attendant.lastName}")
    String getAttendant();
    @Value("#{target.attendant.attendantId}")
    String getAttendantId();
    @Value("#{target.venue.publicName}")
    String getVenue();
    @Value("#{target.venue.venueId}")
    String getVenueId();
    @Value("#{target.pet.name}")
    String getPetName();
    @Value("#{target.convertToUserDate()}")
    String getDate();
    @Value("#{target.convertToUserEndDate()}")
    String getEndDate();
    @Value("#{target.getStartTime()}")
    String getStartTime();
    @Value("#{target.getEndTime()}")
    String getEndTime();
    @Value("#{target.calculateDuration(target)}")
    String getDuration();
    @Value("#{" +
            "(target.service.pre_buffer_mins != null ? target.service.pre_buffer_mins : 0)" +
            "+(target.service.postBufferMins != null ? target.service.postBufferMins : 0)" +
    "}")
    Integer getBufferTime();
    @Value("#{target.shamppo != null ? target.shamppo.name : new String() }")
    String getShampoo();
    @Value("#{target.cologne != null ? target.cologne.name : new String() }")
    String getCologne();
    @Value("#{target.serviceStartAt != null ? target.serviceStartAt.toString() : new String()}")
    String getServiceStartAt();
    @Value("#{target.serviceEndAt != null ? target.serviceEndAt.toString() : new String()}")
    String getServiceEndAt();
    BigDecimal getAmount();
    String getAmountCurrency();
    @Value("#{target.tipAmount != null ? target.tipAmount : 0}")
    BigDecimal getTipAmount();
    @Value("#{target.tipAmountCurrency != null ? target.tipAmountCurrency : new String ()}")
    String getTipAmountCurrency();
    ServiceStatus getServiceStatus();
    String getUpdatedCustomerName();
    String getCreatedCustomerName();
    Integer getCreatedCustomerId();
    Integer getUpdatedCustomerId();

    String getCancellationReason();
    String getRejectionReason();
    String getPaymentStatus();
    String[] getGrainFullRecipes();
    String[] getGrainFreeRecipes();
    @Value("#{target.getAddNotes() != null && target.getAddNotes().size() > 0 ? true : false}")
    Boolean getIsServiceNotesPresent();
    @Value("#{target.quoteAdjustments}")
    List<AppointmentDetailsProjection.quoteAdjustmentInfo> getQuoteAdjustments();


    interface  quoteAdjustmentInfo{
        Integer getQuoteAdjustmentId();
        String getQuoteName();
        BigDecimal getQuotePrice();
    }

}