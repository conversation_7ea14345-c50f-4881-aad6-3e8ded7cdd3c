package com.sayone.etailbookit.projections;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@JsonInclude(JsonInclude.Include.NON_NULL)
public interface PetDetailsProjection extends Serializable {

    Integer getId();
    String getName();
    @Value("#{target.customerId}")
    Integer getCustomerId();
    @Value("#{target.dob}")
    LocalDate getDob();
    @Value("#{target.petType.petTypeId}")
    Integer getPetTypeId();
    String getSex();
    Boolean getSpayed();
    String getColor();
    Optional<BigDecimal> getExactWeight();
    Optional<String> getWeightUnit();
    Integer getFeedCount();
    List<String> getPhotos();
    List<Allergy> getAllergies();
    List<VetInfo> getPetVetInformation();
    List<EmergencyInfo> getPetEmergencyContactInfo();
    List<VaccineInfo> getPetVaccinationRecords();
    List<Personality> getPersonalityParameters();
    List<Behaviour> getUnfriendlyBehaviourTriggers();
    List<Threat> getThreatReactions();
    List<BittingHistory> getbitingHistories();
    List<PetDocuments> getPetDocuments();
    PetWaiverOfLiabilityInfo getPetWaiverOfLiabilityInfo();
    @Value("#{target.hairLength}")
    HairLength getHairLengthDetails();
    @Value("#{target.hairTexture}")
    HairTexture getHairTextureDetails();
    @Value("#{target.petType}")
    PetType getPetTypeDetails();
    @Value("#{target.weightRange}")
    WeightRange getWeightRangeDetails();
    @Value("#{target.temperament}")
    Temperament getTemperamentDetails();
    Boolean getBringFood();
    @Value("#{target.allergiesText}")
    String getAllergiesText();
    @Value("#{target.deceaseDate}")
    String getDeceaseDate();
    @Value("#{target.grainFreeRecipes}")
    String[] getGrainFreeRecipes();
    @Value("#{target.grainFullRecipes}")
    String[] getGrainFullRecipes();
    Boolean getDeleted();
    PetSize getSize();

    List<PetBreedsInformation> getPetBreedsInformations();

    interface PetType {
        @Value("#{target.petTypeId}")
        Integer getId();
        String getName();
        String getFileUrl();
        Boolean getDeleted();
    }

    interface PetBreedsInformation {
        Integer getId();
        @Value("#{target.breed.id}")
        Integer getBreedId();

        @Value("#{target.breed.name}")
        String getBreedName();
        @Value("#{target.breed.deleted}")
        Boolean getDeleted();
    }

    interface Allergy {
        @Value("#{target.allergyId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface  VetInfo {
        @Value("#{target.vetInformation.id}")
        Integer getId();
        @Value("#{target.vetInformation.name}")
        String getName();
        String getValue();
        @Value("#{target.vetInformation.active}")
        Boolean getActive();
    }

    interface VaccineInfo {
        @Value("#{target.vaccinationRecords.vaccinationRecordId}")
        Integer getId();
        @Value("#{target.vaccinationRecords.name}")
        String getName();
        LocalDate getDateExpires();
        LocalDate getDateAdministrated();
        @Value("#{target.vaccinationRecords.requireDateAdministrated}")
        Boolean getRequireDateAdministrated();
        @Value("#{target.vaccinationRecords.requireDateExpires}")
        Boolean getRequireDateExpires();
        @Value("#{target.vaccinationRecords.active}")
        Boolean getActive();
        @Value("#{target.file}")
        String getFileURL();
    }

    interface EmergencyInfo {
        @Value("#{target.emergencyContactInfo.emergencyContactInfoId}")
        Integer getId();
        @Value("#{target.emergencyContactInfo.name}")
        String getName();
        String getValue();
        @Value("#{target.emergencyContactInfo.active}")
        Boolean getActive();
    }

    interface Personality {
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface Behaviour {
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface Threat {
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface BittingHistory {
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface PetDocuments {
        @Value("#{target.documentOption.documentOptionId}")
        Integer getId();
        @Value("#{target.documentOption.name}")
        String getName();
        @Value("#{target.documentOption.requireDescription}")
        Boolean getRequireDescription();
        @Value("#{target.documentOption.requireUpload}")
        Boolean getRequireUpload();
        @Value("#{target.documentOption.signed}")
        Boolean getSigned();
        @Value("#{target.documentOption.signedFileURL}")
        String getSignedFileURL();
        String getDescription();
        String getFile();
        @Value("#{target.documentOption.active}")
        Boolean getActive();
    }

    interface PetWaiverOfLiabilityInfo {
        @Value("#{target.id}")
        Integer getId();
        @Value("#{target.waiverOfLiability.waiverOfLiabilityId}")
        Integer getWaiverOfLiabilityId();
        @Value("#{target.signedFile}")
        String getSignedFile();
        @Value("#{target.waiverOfLiability.requireCustomerSignature}")
        Boolean getRequireCustomerSignature();
    }

    interface WeightRange {
        @Value("#{target.weightRangeId}")
        Integer getId();
        BigDecimal getMinValue();
        BigDecimal getMaxValue();
        String getWeightUnit();
    }

    interface Temperament {
        @Value("#{target.temperamentId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface HairTexture {
        @Value("#{target.hairTextureId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface HairLength {
        @Value("#{target.hairLengthId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface PetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }

}
