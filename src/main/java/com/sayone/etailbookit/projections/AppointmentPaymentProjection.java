package com.sayone.etailbookit.projections;


import com.sayone.etailbookit.util.ServiceStatus;
import org.springframework.beans.factory.annotation.Value;

import java.time.OffsetDateTime;
import java.util.List;

public interface AppointmentPaymentProjection {

    @Value("#{target.id}")
    Integer getAppointmentId();
    Integer getCustomerId();
    @Value("#{target.service.name}")
    String getService();
    @Value("#{target.service.internalItemNumber}")
    String getInternalItemNumber();
    @Value("#{target.attendant.firstName + ' ' + target.attendant.lastName}")
    String getAttendant();
    @Value("#{target.attendant.attendantId}")
    String getAttendantId();
    @Value("#{target.pet.name}")
    String getPet();
    @Value("#{target.convertToUserDate()}")
    String getDate();
    @Value("#{target.getStartTime()}")
    String getStartTime();
    @Value("#{target.getEndTime()}")
    String getEndTime();
    ServiceStatus getServiceStatus();



}