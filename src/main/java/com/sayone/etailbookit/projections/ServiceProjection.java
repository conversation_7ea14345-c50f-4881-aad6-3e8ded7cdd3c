package com.sayone.etailbookit.projections;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.OffsetTime;
import java.util.List;
import java.util.Optional;

@JsonInclude(JsonInclude.Include.NON_NULL)
public interface ServiceProjection extends Serializable {

    Integer getServiceId();
    String getName();
    ServiceType getServiceType();
    PetType getPetType();
    @Value("#{target.generalPetSize}")
    List<PetSize> getGeneralSizes();
    List<Venue> getVenues();
    List<Attendant> getAttendants();
    List<Addon> getAddonServices();
    @Value("#{target.isActive}")
    Optional<Boolean> getIsActive();
    List<Availability> getAvailabilityDays();
    Integer getFixedScheduleValue();
    String getFixedScheduleUnit();
    String getVariableScheduleMinValue();
    String getVariableScheduleMaxValue();
    Boolean getPetparentCanSelectDatetime();
    String getServiceUnit();
    BigDecimal getMinimumFee();
    Boolean getEnableLateOnboardingConfig();
    Integer getLateOnboardingDays();
    String getLateOnboardingChargeInterval();
    BigDecimal getLateOnboardingHourlyCharge();
    Boolean getIsServiceLateDiscountAllowed();
    Integer getServiceLateDiscountDays();
    BigDecimal getServiceLateDiscountPercent();
    Boolean getIsSiblingDiscountAllowed();
    String getSiblingDiscountType();
    BigDecimal getSiblingDiscountValue();
    Boolean getIsTaxable();
    Boolean getPaymentAtTimeOfBooking();
    Boolean getPaymentAtBeginningOfService();
    Boolean getPaymentAfterServiceCompleted();
    Boolean getNeedDepositAtBooking();
    String getDepositAmountType();
    BigDecimal getDepositAmountValue();
    Boolean getChargeCancelationFee();
    String getCancelationAmountType();
    BigDecimal getCancelationAmountValue();
    Integer getCancelationBufferValue();
    String getCancelationBufferUnit();
    Boolean getRequireCreditCardOnFile();
    Boolean getRequireParticipantPetName();
    Boolean getRequireParticipantPetType();
    Boolean getRequirePetPersonality();
    Boolean getRequirePetBehaviour();
    Boolean getRequireBitingInfo();
    Boolean getRequireHairLengthInfo();
    Boolean getRequireVetInfo();
    Boolean getRequireNotes();
    Boolean getOfferRouteTracking();
    Boolean getRequireNotesPostService();
    Boolean getRequireCombsAndBladesUsedPostService();
    Boolean getRequireShampooUsedPostService();
    Boolean getRequireCologneUsedPostService();
    Boolean getRequireFoodFedPostService();
    Boolean getSendPictureToPetParentPostService();
    Boolean getSendReportCard();
    Boolean getIncludeReportCardPeedTimes();
    Boolean getIncludeReportCardPoopedTimes();
    Boolean getIncludeReportCardPoopDescription();
    Boolean getIncludeReportCardTimesFed();
    Boolean getIncludeReportCardWaterBowlFilled();
    Boolean getIncludeReportCardGeneralNotes();
    List<Vaccine> getAvailableParticipantVaccinations();
    List<Documents> getAvailableParticipantDocuments();
    String getScheduleType();
    @Value("#{target.petParentCanSelectAttendant}")
    Boolean getPetparentCanSelectAttendant();
    Boolean getPetparentCanSelectRecurringOptions();
    BigDecimal getPreBookingValue();
    String getPreBookingUnit();
    String getAvailabilityInterval();
    String getAvailabilityIntervalUnit();
    Boolean getIsTipsAllowed();
    @Value("#{target.pre_buffer_mins}")
    Optional<Integer> getPreBufferMins();
    @Value("#{target.postBufferMins}")
    Optional<Integer> getPostBufferMins();
    @Value("#{target.amountPerUnit}")
    Optional<BigDecimal> getAmountPerUnit();
    @Value("#{target.amount_currency}")
    Optional<String> getAmountCurrency();
    @Value("#{target.maxOvernights}")
    Optional<Integer> getMaxOvernights();
    Boolean getDeleted();

    interface PetType {
        @Value("#{target.petTypeId}")
        Integer getId();
        String getName();
        String getFileUrl();
        Boolean getDeleted();
    }

    interface Availability {
        String getAvailableDay();
        OffsetTime getAvailabilityOpenTime();
        OffsetTime getAvailabilityCloseTime();
    }

    interface Documents {
        @Value("#{target.documentOptionId}")
        Integer getId();
        String getName();
        Boolean getRequireDescription();
        Boolean getRequireUpload();
        Boolean getActive();
    }

    interface Vaccine {
        @Value("#{target.vaccinationRecordId}")
        Integer getId();
        String getName();
        Boolean getRequireDateAdministrated();
        Boolean getRequireDateExpires();
        Boolean getActive();
    }

    interface  Addon {
        @Value("#{target.addonServiceId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface Attendant {
        Integer getAttendantId();
        String getFirstName();
        String getLastName();
        Boolean getActive();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getId();
        String getInternalName();
        String getPublicName();
        String getLocationType();
        List<String> getMobileZipCode();
        Boolean getActive();
    }

    interface PetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }

    interface ServiceType {
        @Value("#{target.serviceTypeId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

}
