package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface PetListingProjection {

    Integer getId();
    Integer getCustomerId();
    String getName();
    LocalDate getDob();
    String getSex();
    String getColor();
    Boolean getSpayed();
    @Value("#{target.petType}")
    PetTypeProjection getPetTypeDetails();
    @Value("#{target.weightRange}")
    WeightRangeProjection getWeightRangeDetails();
    Optional<BigDecimal> getExactWeight();
    Optional<String> getWeightUnit();
    List<String> getPhotos();
    Boolean getDeleted();
}
