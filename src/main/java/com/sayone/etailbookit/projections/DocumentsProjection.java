package com.sayone.etailbookit.projections;


import org.springframework.beans.factory.annotation.Value;
public interface DocumentsProjection {

    Integer getId();
    @Value("#{target.name}")
    String getDocumentName();
    Boolean getActive();
    @Value("#{target.requires_upload_esign}")
    Boolean getSignatureRequired();
    @Value("#{target.fileURL}")
    String getFile();
    @Value("#{target.requires_description}")
    boolean getRequiredDescription();
    @Value("#{target.requires_upload}")
    boolean getRequireUpload();
    @Value("#{target.indexvalue}")
    Integer getIndexValue();
}
