package com.sayone.etailbookit.projections;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.OffsetTime;
import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
public interface AttendantDetailsProjection {

    Integer getAttendantId();
    String getFirstName();
    String getLastName();
    String getEmail();
    String getPhoneNo();
    String getTitle();
    String getColor();
    String[] getPreferredCommunications();
    Boolean getRevenueShare();
    BigDecimal getAmountPerHour();
    String getAmountCurrency();
    BigDecimal getAmountPerEvent();
    Integer getRevenuePercent();
    Boolean getTipEligible();
    Integer getCapacityLimit();
    List<ServiceType> getServiceTypes();
    List<AddonService> getAddonServices();
    List<AttendantPetType> getAttendantPetTypes();
    Boolean getActive();
    List<Availability> getAvailabilityDays();
    String getAvailabilityInterval();
    String getAvailabilityIntervalUnit();
    Venue getVenue();
    Boolean getDeleted();
    Boolean getGoogleCalendarSyncEnabled();
    Boolean getCalendarSyncInProgress();
    Boolean getGoogleCalendarAuthorized();

    List<PetSizeConstraint>  getPetSizeConstraints();

    List<PetSizeLimit>  getPetSizeLimits();
    interface PetSizeConstraint {
        Integer getId();
        Integer getCapacity();
        PetType getPetType();
        GeneralPetSizeProjection getGeneralPetSize();
        Boolean getDeleted();
    }

    interface PetSizeLimit {
        Integer getId();
        Integer getCapacity();
        PetType getPetType();
        GeneralPetSizeProjection getGeneralPetSize();
        Boolean getDeleted();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getId();
        String getInternalName();
        String getPublicName();
        Boolean getDeleted();
    }

    interface PetType {
        @Value("#{target.petTypeId}")
        Integer getId();
        String getName();
        @Value("#{target.temperaments}")
        Set<Temperament> getTemperamentList();
        Boolean getDeleted();
    }

    interface AttendantPetType {
        @Value("#{target.petType?.petTypeId}")
        Integer getId();
        @Value("#{target.petType?.name}")
        String getName();
        @Value("#{target.temperaments}")
        List<Temperament> getTemperamentList();
        @Value("#{target.generalPetSizes}")
        List<GeneralPetSize> getGeneralPetSize();
        Boolean getDeleted();
    }

    interface Temperament {
        @Value("#{target.temperamentId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface GeneralPetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }

    interface ServiceType {
        @Value("#{target.serviceTypeId}")
        Integer getId();
        String getName();
        String getFileUrl();
        Boolean getActive();
        Boolean getDeleted();
    }

    interface AddonService {
        @Value("#{target.addonServiceId}")
        Integer getId();
        String getName();
        Boolean getActive();
        Boolean getIsTaxable();
        Integer getTackOnExtraMinutes();
        BigDecimal getTackOnExtraAmount();
        String getTackOnExtraAmountCurrency();
        Boolean getDeleted();
    }

    interface Availability {
        String getAvailableDay();
        OffsetTime getAvailabilityOpenTime();
        OffsetTime getAvailabilityCloseTime();
    }
    
}
