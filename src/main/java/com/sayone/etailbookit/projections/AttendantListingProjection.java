package com.sayone.etailbookit.projections;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public interface AttendantListingProjection {

    Integer getAttendantId();
    String getFirstName();
    String getLastName();
    String getEmail();
    String getPhoneNo();
    String getTitle();
    String[] getPreferredCommunications();
    Boolean getRevenueShare();
    Integer getCapacityLimit();
    List<ServiceType> getServiceTypes();
    List<AttendantPetType> getAttendantPetTypes();
    Boolean getActive();
    Boolean getDeleted();
    Boolean getGoogleCalendarSyncEnabled();
    Boolean getCalendarSyncInProgress();
    Boolean getGoogleCalendarAuthorized();

    interface AttendantPetType {
        @Value("#{target.petType?.petTypeId}")
        Integer getId();
        @Value("#{target.petType?.name}")
        String getName();
        @Value("#{target.temperaments}")
        List<Temperament> getTemperamentList();
        @Value("#{target.generalPetSizes}")
        List<GeneralPetSize> getGeneralPetSize();
    }

    interface Temperament {
        @Value("#{target.temperamentId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }

    interface GeneralPetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }

    interface ServiceType {
        @Value("#{target.serviceTypeId}")
        Integer getId();
        String getName();
        String getFileUrl();
        Boolean getActive();
    }
    
}
