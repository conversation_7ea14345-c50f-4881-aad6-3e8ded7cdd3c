package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

public interface TimeSlotWeekViewProjection {

    Integer getId();

    String getSlotName();

    //@Value("#{target.slotStartTime}")
    String getSlotStartTime();

    String getSlotEndTime();

    String getColor();

    LocalDate getSlotDate();

    Boolean getSlotBooked();

    Integer getAttendantId();

    String getAttendantName();

    Integer getClusterId(); // for matching with parent cluster slot


   /* @Value("#{target.services}")
    List<Service> getService();

    Venue getVenue();

   Attendant getAttendant();

    interface Service {
        @Value("#{target.serviceId}")
        Integer getServiceId();

        @Value("#{target.name}")
        String getServiceName();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getVenueId();

        @Value("#{target.publicName}")
        String getVenueName();
    }

    interface Attendant{
        @Value("#{target.attendantId}")
        Integer getAttendantId();

        @Value("#{target.firstName}")
        String getAttendantName();

        @Value("#{target.deleted}")
        Boolean getDeleted();
    }
*/

}
