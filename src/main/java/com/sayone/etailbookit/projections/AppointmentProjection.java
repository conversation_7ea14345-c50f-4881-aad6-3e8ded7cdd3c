package com.sayone.etailbookit.projections;


import com.sayone.etailbookit.util.ServiceStatus;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public interface AppointmentProjection {

    Integer getAppointmentId();
    Integer getCustomerId();
    String getAppoinmentNo();
    Integer getServiceTypeId();
    String getServiceTypeName();
    Integer getServiceId();
    String getServiceName();
    Integer getAttendantId();
    String getAttendantFirstName();
    Integer getVenueId();
    String getVenueInternalName();
    Integer getShampooId();
    String getShampooName();
    Integer getCologneId();
    String getCologneName();
    LocalDate getDate();
    LocalTime getTime();
    LocalDate getDateTimeOnboarding();
    Boolean getRecurringEnabled();
    Boolean getHaveSibilings();
    Integer getSibilingCount();
    Integer getPetId();
    String getPetName();
    Integer getPetTypeId();
    Integer getFeedingCount();
    Boolean getBringYourFood();
    BigDecimal getAmount();
    String getAmountCurrency();
    BigDecimal getTipAmount();
    String getTipAmountCurrency();
    ServiceStatus getServiceStatus();
    String getGrainFreeRecipes();
    String getGrainFullRecipes();
    Integer getMaxOvernights();
    String getVariableScheduleMaxValue();
    String getVariableScheduleMinValue();
    String getFixedScheduleUnit();
    Integer getFixedScheduleValue();
    String getScheduleType();
    String getServiceHistoryNote();
    String getPetPhotos();
    String getServiceStartAt();
    String getServiceEndAt();
    String getDuration();
    Integer getAddressId();
    LocalDateTime getCreatedAt();

}
