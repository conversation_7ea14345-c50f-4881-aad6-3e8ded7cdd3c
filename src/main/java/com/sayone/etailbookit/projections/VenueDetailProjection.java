package com.sayone.etailbookit.projections;

import com.sayone.etailbookit.dto.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.OffsetTime;
import java.util.List;

public interface VenueDetailProjection {

    @Value("#{target.venueId}")
    Integer getId();
    String getInternalName();
    String getPublicName();
    Integer getEcommerceStoreId();
    String getLocationType();
    VenueAddress getLocationAddress();
    Boolean getSupportSimultaneousBookings();
    Integer getParticipantLimitService();
    BigDecimal getExtraCharge();
    String getExtraCurrency();
    String getAvailabilityInterval();
    String getAvailabilityIntervalUnit();
    List<VenueAvailability> getAvailabilityDays();
    List<ServiceType> getServiceTypes();
    @Value("#{target.venuePetTypes}")
    List<VenuePetType> getPetTypes();
    Boolean getActive();
    List<String> getMobileZipCode();
    Boolean getDeleted();

    interface VenueAddress {
        String getStreet1();
        String getStreet2();
        String getCity();
        String getState();
        String getZipcode();
        String getCountry();
    }

    interface ServiceType {
        @Value("#{target.serviceTypeId}")
        Integer getId();
        String getName();
        Boolean getActive();
        String getFileUrl();
        Boolean getDeleted();
    }
    
    interface VenueAvailability {
        String getAvailableDay();
        OffsetTime getAvailabilityOpenTime();
        OffsetTime getAvailabilityCloseTime();
    }
    
    interface VenuePetType {
        @Value("#{target.petType?.petTypeId}")
        Integer getId();
        @Value("#{target.petType?.name}")
        String getName();
        List<Temperament> getTemperaments();
        List<GeneralPetSize> getGeneralPetSizes();
    }
    
    interface Temperament {
        @Value("#{target.temperamentId}")
        Integer getId();
        String getName();
        Boolean getActive();
    }
    
    interface GeneralPetSize {
        @Value("#{target.generalPetSizeId}")
        Integer getId();
        String getSize();
        Integer getWeightValue();
        String getWeightUnit();
    }
    
}
