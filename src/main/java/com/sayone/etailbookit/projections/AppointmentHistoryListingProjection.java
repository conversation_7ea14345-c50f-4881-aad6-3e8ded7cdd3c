package com.sayone.etailbookit.projections;


import com.sayone.etailbookit.model.Service;
import com.sayone.etailbookit.util.ServiceStatus;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.util.List;

public interface AppointmentHistoryListingProjection {

    Integer getId();
    Integer getCustomerId();
    String getAppoinmentNo();
    Attendant getAttendant();
    Venue getVenue();
    Service getService();
    Pet getPet();
    @Value("#{target.convertToUserDate()}")
    String getDate();
    OffsetDateTime getServiceStartAt();
    OffsetDateTime getServiceEndAt();
    ServiceStatus getServiceStatus();
    @Value("#{target.calculateDuration(target)}")
    String getDuration();
    @Value("#{target.getStartTime()}")
    String getStartTime();
    @Value("#{target.getEndTime()}")
    String getEndTime();

    @Value("#{target.getAddNotes() != null && target.getAddNotes().size() > 0 ? true : false}")
    Boolean getIsServiceNotesPresent();

    interface Service {
        Integer getServiceId();
        String getName();
    }

    interface Attendant {
        Integer getAttendantId();
        String getFirstName();
        String getLastName();
    }

    interface Venue {
        @Value("#{target.venueId}")
        Integer getId();
        String getInternalName();
        String getPublicName();
    }

    interface Pet {
        String getName();
        PetType getPetType();
        List<String> getPhotos();
    }

    interface PetType {
        @Value("#{target.petTypeId}")
        Integer getId();
        String getName();
        String getFileUrl();
        Boolean getActive();
    }

}
