package com.sayone.etailbookit.projections;

import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

public interface TimeSlotProjectionOfAmonth {

    Integer getId();

    String getSlotName();


    String getSlotStartTime();


    String getSlotEndTime();

    String getColor();

    LocalDate getDate();

    Integer getRemainingSlots();



}
