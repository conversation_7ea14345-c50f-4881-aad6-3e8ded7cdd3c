package com.sayone.etailbookit.validator;

import com.sayone.etailbookit.dto.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EntityNotFoundException;
import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.repository.*;
import com.sayone.etailbookit.util.DisplayType;
import com.sayone.etailbookit.util.RetailerContext;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Component

public class Validator {

    @Autowired
    ConfigurationRepository configurationRepository;

    @Autowired
    AttendantRepository attendantRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    VenueRepository venueRepository;


    private static final String ENABLE_SLOT = "enable_slots";

    public static void validateServiceType(ServiceTypeDto serviceTypeDto, Integer serviceTypeId, ServiceTypeRepository serviceTypeRepository) throws EntityNotFoundException, BadRequestException {
        if (serviceTypeId != null) {
            ServiceType serviceType = serviceTypeRepository.findByServiceTypeId(serviceTypeId);
            if (serviceType == null) {
                throw new EntityNotFoundException("Service type not found");
            }
        }

        if (serviceTypeDto != null) {
            if (serviceTypeDto.getFile() == null && serviceTypeId == null) {
                throw new BadRequestException("Service type must have an image");
            }

            if ( StringUtils.isEmpty(serviceTypeDto.getName())) {
                throw new BadRequestException("Service type name cannot be empty");
            }

            List<ServiceType> serviceTypeList = serviceTypeRepository.findByNameIgnoreCaseAndRetailerAndDeleted(StringUtils.normalizeSpace(serviceTypeDto.getName().trim()), RetailerContext.getRetailer(),false);
            if(!serviceTypeList.isEmpty()){
                for(ServiceType serviceType : serviceTypeList){
                    if (serviceType != null && !serviceType.getServiceTypeId().equals(serviceTypeId)) {
                        throw new BadRequestException("Service type already exist with name " + StringUtils.normalizeSpace(serviceTypeDto.getName().trim()));
                    }
                }
            }
        }
    }

    public static void validatePetCologne(PetCologneDto petCologneDto, Integer petCologneSelectionId, PetCologneRepository petCologneRepository) throws EtailBookItException {
        if (petCologneSelectionId != null) {
            Optional<PetCologne> petCologne = petCologneRepository.findById(petCologneSelectionId);
            if (!petCologne.isPresent()) {
                throw new EntityNotFoundException("Cologne not found");
            }
        }

        if (petCologneDto != null) {
            if (StringUtils.isEmpty(petCologneDto.getName())) {
                throw new BadRequestException("Cologne name cannot be empty");
            }
            PetCologne petCologne = petCologneRepository.findByNameIgnoreCaseAndRetailerAndDeleted(StringUtils.normalizeSpace(petCologneDto.getName().trim()), RetailerContext.getRetailer(),false);
            if (petCologne != null && !petCologne.getId().equals(petCologneSelectionId)) {
                throw new BadRequestException("Cologne already exist with name " + StringUtils.normalizeSpace(petCologne.getName().trim()));
            }
        }

    }

    public static void validatePetShampoo(PetShampooDto petShampooDto, Integer petShampooId, PetShampooRepository petShampooRepository) throws EtailBookItException {

        if (petShampooId != null) {
            Optional<PetShampoo> petShampoo = petShampooRepository.findById(petShampooId);
            if (!petShampoo.isPresent()) {
                throw new EntityNotFoundException("Shampoo not found");
            }
        }

        if (petShampooDto != null) {
            if (StringUtils.isEmpty(petShampooDto.getName())) {
                throw new BadRequestException("Shampoo name cannot be empty");
            }
            PetShampoo petShampoo = petShampooRepository.findByNameIgnoreCaseAndRetailerAndDeleted(StringUtils.normalizeSpace(petShampooDto.getName().trim()), RetailerContext.getRetailer(),false);
            if (petShampoo != null && !petShampoo.getId().equals(petShampooId)) {
                throw new BadRequestException("Shampoo already exist with name " + StringUtils.normalizeSpace(petShampooDto.getName().trim()));
            }
        }
    }

    public static void validatePetType(PetTypeDto petTypeDto, Integer petTypeId, PetTypeRepository petTypeRepository) throws EtailBookItException {

        if (petTypeId != null) {
            PetType petType = petTypeRepository.findByPetTypeId(petTypeId);
            if (petType == null) {
                throw new EntityNotFoundException("Pet type not found");
            }
        }

        if(petTypeDto!=null) {
            if (StringUtils.isEmpty(petTypeDto.getName())) {
                throw new BadRequestException("Pet type name cannot be empty");
            }
            List<PetType> petTypes = petTypeRepository.findAllByNameIgnoreCaseAndRetailerAndDeleted(StringUtils.normalizeSpace(petTypeDto.getName().trim()), RetailerContext.getRetailer(),false);
            for (PetType existingPetType: petTypes) {
                if (existingPetType != null && !existingPetType.getPetTypeId().equals(petTypeId)) {
                    throw new BadRequestException("Pet type already exist with name " + StringUtils.normalizeSpace(petTypeDto.getName().trim()));
                }
            }
            if (petTypeDto.getHairLengths().getOptions() == null && petTypeDto.getHairLengths().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No hair length options specified for pet type");
            }
            if (petTypeDto.getHairTextures().getOptions() == null && petTypeDto.getHairTextures().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No hair texture options specified for pet type");
            }
            if (petTypeDto.getDesiredHairLengths().getOptions() == null && petTypeDto.getDesiredHairLengths().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No desired hair length options specified for pet type");
            }
            if (petTypeDto.getBreeds()!=null && petTypeDto.getBreeds().getOptions() == null && petTypeDto.getBreeds().getDisplayType().equals(DisplayType.OPTION)) {
               throw new BadRequestException("No breeds options specified for pet type");
            }
            if (petTypeDto.getVetInformation().getOptions() == null && petTypeDto.getVetInformation().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No vet information options specified for pet type");
            }
            if (petTypeDto.getDocumentOptions().getOptions() == null && petTypeDto.getDocumentOptions().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No document options specified for pet type");
            }
            if (petTypeDto.getAllergies().getOptions() == null && petTypeDto.getAllergies().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No allergy options specified for pet type");
            }
            if (petTypeDto.getEmergencyContactInfo().getOptions() == null && petTypeDto.getEmergencyContactInfo().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No emergency contact information options specified for pet type");
            }
            if (petTypeDto.getCombsBlades().getBlades() == null && petTypeDto.getCombsBlades().getCombs() == null && petTypeDto.getEmergencyContactInfo().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No blades and comb options specified for pet type");
            }
            if (petTypeDto.getTemperaments().getOptions() == null && petTypeDto.getTemperaments().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No temperament options specified for pet type");
            }
            if (petTypeDto.getVaccinationRecords().getOptions() == null && petTypeDto.getVaccinationRecords().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No vaccination information options specified for pet type");
            }
            if (petTypeDto.getWeightRanges().getOptions() == null && petTypeDto.getWeightRanges().getDisplayType().equals(DisplayType.OPTION)) {
                throw new BadRequestException("No weight range options specified for pet type");
            }
        }
    }

    public static void validateGeneralPetSize(GeneralPetSizeDto generalPetSizeDto, Integer generalPetSizeId, GeneralPetSizeRepository generalPetSizeRepository) throws EtailBookItException {
        if (generalPetSizeId != null) {
            GeneralPetSize generalPetSize = generalPetSizeRepository.findByGeneralPetSizeId(generalPetSizeId);
            if (generalPetSize == null) {
                throw new EntityNotFoundException("General pet size not found");
            }
        }

        if(generalPetSizeDto!=null) {
            if (StringUtils.isEmpty(generalPetSizeDto.getSize())) {
                throw new BadRequestException("General pet size name cannot be empty");
            }
            GeneralPetSize generalPetSize = generalPetSizeRepository.findBySizeAndRetailer(generalPetSizeDto.getSize(), RetailerContext.getRetailer());
            if (generalPetSize != null && !generalPetSize.getGeneralPetSizeId().equals(generalPetSizeId)) {
                throw new BadRequestException("General pet size already exist with name " + generalPetSizeDto.getSize());
            }
        }
    }

    public static void validateAddonService(AddonServiceDto addonServiceDto, Integer addonServiceId, AddonServiceRepository addonServiceRepository) throws EtailBookItException {
        AddonService addonService;

        if (addonServiceId != null) {
            addonService = addonServiceRepository.findByAddonServiceId(addonServiceId);
            if (addonService == null) {
                throw new EntityNotFoundException("Add-on service not found");
            }
        }

        if(addonServiceDto != null) {
            if (StringUtils.isEmpty(addonServiceDto.getName())) {
                throw new BadRequestException("Add-on service name cannot be empty");
            }
            addonService = addonServiceRepository.findByNameAndRetailerAndDeleted(StringUtils.normalizeSpace(addonServiceDto.getName().trim()), RetailerContext.getRetailer(), false);
            if (addonService != null && !addonService.getAddonServiceId().equals(addonServiceId)) {
                throw new BadRequestException("Add-on service already exist with label ::" + StringUtils.normalizeSpace(addonServiceDto.getName().trim()));
            }
        }
    }

    public static void validateFeedingInformation(FeedingInformationDto feedingInformationDto, Integer feedingInformationId, FeedingInformationRepository feedingInformationRepository) throws EntityNotFoundException {
        if (feedingInformationId != null) {
            FeedingInformation feedingInformation = feedingInformationRepository.findByFeedingInformationId(feedingInformationId);
            if (feedingInformation == null) {
                throw new EntityNotFoundException("Feeding Information Id not found");
            }
        }
    }

    public  void validateVenue(VenueDto venueDto, Integer venueId) throws EtailBookItException {
        if (venueId != null) {
            Optional<Venue> venue = venueRepository.findById(venueId);
            if (!venue.isPresent()) {
                throw new EntityNotFoundException("Venue not found");
            }
        }

        if (venueDto != null) {
            List<Venue> venueList = venueRepository.findByName(
                    StringUtils.normalizeSpace(venueDto.getInternalName().toLowerCase().trim()),
                    RetailerContext.getRetailer(), false
            );
            if (venueList != null && !venueList.isEmpty()) {
                for (Venue venue : venueList) {
                    if (!venue.getVenueId().equals(venueDto.getId())) {
                        throw new BadRequestException("Venue already exist with internal name " + StringUtils.normalizeSpace(venueDto.getInternalName().trim()));
                    }
                }

            }
            if (venueDto.getLocationType() == null) {
                throw new BadRequestException("Location type not specified");
            }
            if (venueDto.getLocationType().equals("store") && venueDto.getEcommerceStoreId() == null) {
                throw new BadRequestException("Store for venue not specified");
            }
            if (ObjectUtils.isEmpty(venueDto.getServiceTypeIds())) {
                throw new BadRequestException("No service type(s) selected for venue");
            }
            if (ObjectUtils.isEmpty(venueDto.getPetTypes())) {
                throw new BadRequestException("No pet type(s) selected for venue");
            }
            if (venueDto.getParticipantLimitService() == null && venueDto.getParticipantLimitService() <= 0) {
                throw new BadRequestException("Venue capacity not specified");
            }
            Configuration configuration = configurationRepository.findByNameAndRetailer(ENABLE_SLOT, RetailerContext.getRetailer());
            if (!configuration.isActive()){
                if (ObjectUtils.isEmpty(venueDto.getAvailabilityDays())) {
                    throw new BadRequestException("No availability specified for venue");
                } else {
                    for (AvailabilityDto availability : venueDto.getAvailabilityDays()) {
                        if (ObjectUtils.isEmpty(availability.getAvailableDay())) {
                            throw new BadRequestException("Available day should be specified ");
                        }
                        if (ObjectUtils.isEmpty(availability.getAvailabilityOpenTime())) {
                            throw new BadRequestException("Available open time should be specified");
                        }
                        if (ObjectUtils.isEmpty(availability.getAvailabilityCloseTime())) {
                            throw new BadRequestException("Available close time should be specified");
                        }
                        if (availability.getAvailabilityOpenTime().equals(availability.getAvailabilityCloseTime())) {
                            throw new BadRequestException("Available Open time and close time cannot be the same");
                        }
                    }
                }
        }
        }
    }

    public  void validateAttendant(AttendantDto attendantDto, Integer attendantId) throws EtailBookItException {
        if (attendantId != null) {
            Attendant attendant = attendantRepository.findByAttendantIdAndDeleted(attendantId,false);
            if (attendant == null) {
                throw new EntityNotFoundException("Attendant not found");
            }
        }
        if(attendantDto != null) {
            List<Attendant> attendantNum = attendantRepository.findByPhoneNo(attendantDto.getPhoneNo());
            if (!attendantNum.isEmpty()) {
                for( Attendant attendantNumList : attendantNum ) {
                    if (!attendantNumList.getAttendantId().equals(attendantId) && attendantNumList.getPhoneNo().equals(attendantDto.getPhoneNo()))
                        throw new EntityNotFoundException("Phone number already exist");
                }
            }
            //todo: verify this check
            if (attendantDto.getFirstName() == null && attendantDto.getLastName() == null) {
                throw new BadRequestException("Attendant name cannot be empty");
            }
            if(ObjectUtils.isEmpty(attendantDto.getVenue()) || attendantDto.getVenue().getId() == null) {
                throw new BadRequestException("Please select venue where attendant works");
            }
            if (ObjectUtils.isEmpty(attendantDto.getServiceTypeIds())) {
                throw new BadRequestException("No service type(s) selected for attendant");
            }
            if (attendantDto.getCapacityLimit() == null && attendantDto.getCapacityLimit() <= 0) {
                throw new BadRequestException("No capacity limit specified for attendant");
            }
            if (ObjectUtils.isEmpty(attendantDto.getPetTypes())) {
                throw new BadRequestException("No pet type(s) selected for attendant");
            }
            if (ObjectUtils.isEmpty(attendantDto.getPhoneNo())) {
                throw new BadRequestException("Phone number is required");
            }
            Configuration configuration = configurationRepository.findByNameAndRetailer(ENABLE_SLOT, RetailerContext.getRetailer());
            if(!configuration.isActive()){
                if (ObjectUtils.isEmpty(attendantDto.getAvailabilityDays())) {
                    throw new BadRequestException("No availability specified for attendant");
                }else {
                    for (AvailabilityDto availability : attendantDto.getAvailabilityDays()) {
                        if (ObjectUtils.isEmpty(availability.getAvailableDay())) {
                            throw new BadRequestException("Available day should be specified ");
                        }
                        if (ObjectUtils.isEmpty(availability.getAvailabilityOpenTime())) {
                            throw new BadRequestException("Available open time should be specified");
                        }
                        if (ObjectUtils.isEmpty(availability.getAvailabilityCloseTime())) {
                            throw new BadRequestException("Available close time should be specified");
                        }
                        if(availability.getAvailabilityOpenTime().equals(availability.getAvailabilityCloseTime())){
                            throw new BadRequestException("Available Open time and close time cannot be the same");
                        }
                    }
                }
            }
            }
        }


    public static void validatePet(PetDto petDto, Integer petId, PetRepository petRepository) throws EtailBookItException {
        if (petId != null) {
            Optional<Pet> pet = petRepository.findById(petId);
            if (!pet.isPresent()) {
                throw new EntityNotFoundException("Pet not found");
            }
        }
        if(petDto != null) {
            if(petDto.getName() == null) {
                throw new BadRequestException("Pet name cannot be empty");
            }
            if(petDto.getPetTypeDetails() != null && petDto.getPetTypeDetails().getId() == null) {
                throw new BadRequestException("Pet type not selected");
            }
            if(petDto.getDocuments() != null){
                for(DocumentOptionDto documentList : petDto.getDocuments()) {
                    if(documentList.getDescription().length() > 255){
                        throw new BadRequestException("Description should not exceed 255 characters.");
                    }
                }
            }
            if(petDto.getVaccinationRecords() != null && petDto.getDob() != null) {
                for(VaccinationRecordsDto record : petDto.getVaccinationRecords()) {
                    if(
                            !ObjectUtils.isEmpty(record.getDateAdministrated()) &&
                            !ObjectUtils.isEmpty(petDto.getDob()) &&
                            LocalDate.parse(record.getDateAdministrated())
                            .isBefore(LocalDate.parse(petDto.getDob()))
                    ) {
                        throw new BadRequestException("Specified date of vaccine administration is before Pet's DoB");
                    }
                    if(
                            !ObjectUtils.isEmpty(record.getDateAdministrated()) &&
                            !ObjectUtils.isEmpty(record.getDateExpires()) &&
                            LocalDate.parse(record.getDateAdministrated())
                            .isAfter(LocalDate.parse(record.getDateExpires()))
                    ) {
                        throw new BadRequestException("Specified date of vaccine administration is after date of expiry");
                    }
                }
            }
     /*       if (petId == null && petDto.getPictures() == null) {
                throw new BadRequestException("Please add an image for the pet");
            }*/
            if (petDto.getPictures() != null && petDto.getPictures().size() > 1) {
                throw new BadRequestException("Multiple images are not allowed");
            }
        }
    }

    public void validateService(Integer ServiceId, ServiceDto serviceDto) throws EtailBookItException {
        if(ServiceId != null) {
            Service service = serviceRepository.findByServiceId(ServiceId);
            if(service == null)
                throw new EntityNotFoundException("Service is not found");
            if(service.getName().equalsIgnoreCase("Dog Grooming DEV")){
                throw new EtailBookItException("This is test data and can not be updated");
            }
        }

        if (serviceDto != null) {
            if (serviceDto.getName() == null) {
                throw new BadRequestException("Service name cannot be empty");
            }
            List<Service> serviceList = serviceRepository.findByNameIgnoreCaseAndRetailerAndDeleted(StringUtils.normalizeSpace(serviceDto.getName().trim()), RetailerContext.getRetailer(), false);
            if (!serviceList.isEmpty()) {
                for (Service service : serviceList) {
                    if (service != null && !service.getServiceId().equals(serviceDto.getServiceId())) {
                        throw new BadRequestException("Service already exist with name " + StringUtils.normalizeSpace(serviceDto.getName().trim()));
                    }
                }
            }
            if (ObjectUtils.isEmpty(serviceDto.getServiceType())) {
                throw new BadRequestException("No service type selected for service");
            }
            if (ObjectUtils.isEmpty(serviceDto.getPetType() == null)) {
                throw new BadRequestException("No pet type selected for service");
            }
            if (ObjectUtils.isEmpty(serviceDto.getVenues())) {
                throw new BadRequestException("No venues selected for service");
            }
            if (ObjectUtils.isEmpty(serviceDto.getAttendants())) {
                throw new BadRequestException("No attendants selected for service");
            }
            if (serviceDto.getScheduleType() == null) {
                throw new BadRequestException("Duration type not specified for service");
            }
            if (serviceDto.getFixedScheduleUnit() == null) {
                throw new BadRequestException("Duration unit not specified for service");
            }
            if (
                    (serviceDto.getFixedScheduleUnit().equalsIgnoreCase("FIXED") && serviceDto.getFixedScheduleValue() == null)
                            || (serviceDto.getFixedScheduleUnit().equalsIgnoreCase("VARIABLE") && serviceDto.getVariableScheduleMinValue() == null || serviceDto.getVariableScheduleMaxValue() == null)) {
                throw new BadRequestException("Duration not specified for service");
            }
            if (serviceDto.getPreBookingValue() == null) {
                throw new BadRequestException("Please specify how far in advance service must be booked");
            }
            if (serviceDto.getPreBookingUnit() == null) {
                throw new BadRequestException("Please specify how far in advance service must be booked");
            }
            if (serviceDto.getAmountPerUnit() == null) {
                throw new BadRequestException("Service cost not specified for service");
            }
            //todo: check if message is understandable
            if (serviceDto.getServiceUnit() == null) {
                throw new BadRequestException("Service cost interval not specified for service");
            }
            Configuration configuration = configurationRepository.findByNameAndRetailer(ENABLE_SLOT, RetailerContext.getRetailer());
            if (!configuration.isActive()){
                if (ObjectUtils.isEmpty(serviceDto.getAvailabilityDays())) {
                    throw new BadRequestException("No availability specified for service");
                } else {
                    for (AvailabilityDto availability : serviceDto.getAvailabilityDays()) {
                        if (ObjectUtils.isEmpty(availability.getAvailableDay())) {
                            throw new BadRequestException("Available day should be specified ");
                        }
                        if (ObjectUtils.isEmpty(availability.getAvailabilityOpenTime())) {
                            throw new BadRequestException("Available open time should be specified");
                        }
                        if (ObjectUtils.isEmpty(availability.getAvailabilityCloseTime())) {
                            throw new BadRequestException("Available close time should be specified");
                        }

                        LocalTime closeTimeBound;
                        LocalTime openTime = LocalTime.parse(availability.getAvailabilityOpenTime());
                        LocalTime closeTime = LocalTime.parse(availability.getAvailabilityCloseTime());
                        if (serviceDto.getScheduleType().equals("FIXED")) {
                            Integer time = serviceDto.getFixedScheduleValue();
                            if (serviceDto.getFixedScheduleUnit().equals("DAY")) {
                                closeTimeBound = openTime.plus(Duration.ofDays(time));
                            } else if (serviceDto.getFixedScheduleUnit().equals("HOUR")) {
                                closeTimeBound = openTime.plus(Duration.ofHours(time));
                            } else {
                                closeTimeBound = openTime.plus(Duration.ofMinutes(time));
                            }
                        } else if (serviceDto.getScheduleType().equals("VARIABLE")) {
                            Integer time = Integer.parseInt(serviceDto.getVariableScheduleMaxValue());
                            if (serviceDto.getFixedScheduleUnit().equals("DAY")) {
                                closeTimeBound = openTime.plus(Duration.ofDays(time));
                            } else if (serviceDto.getFixedScheduleUnit().equals("HOUR")) {
                                closeTimeBound = openTime.plus(Duration.ofHours(time));
                            } else {
                                closeTimeBound = openTime.plus(Duration.ofMinutes(time));
                            }
                        } else { //its for implementing onboarding - Error fixed for now
                            Integer time = 0;
                            if (serviceDto.getFixedScheduleUnit().equals("HOUR")) {
                                closeTimeBound = openTime.plus(Duration.ofHours(time));
                            }
                            closeTimeBound = openTime.plus(Duration.ofMinutes(time));
                        }
                        if (closeTime.isBefore(closeTimeBound)) {
                            throw new BadRequestException("Available close time should be after the service completion");
                        }
                    }

                }
        }
            }

        }

    public static void validateAppointment(AppointmentDto appointmentDto, AppointmentRepository appointmentRepository) throws BadRequestException {

        if (appointmentDto != null) {
            if (appointmentDto.getServiceType() == null) {
                throw new BadRequestException("No service type selected for appointment");
            }
            if (appointmentDto.getService() == null) {
                throw new BadRequestException("No service selected for appointment");
            }
            if (appointmentDto.getVenue() == null) {
                throw new BadRequestException("No venue selected for appointment");
            }
            if (appointmentDto.getCustomerId() == null) {
                throw new BadRequestException("No customer selected for appointment");
            }
            if (appointmentDto.getPet() == null) {
                throw new BadRequestException("No pet selected for appointment");
            }
           // if (appointmentDto.getVaccinationInfo() != null && appointmentDto.getDate() != null) {
            if (appointmentDto.getVaccinationInfo() != null && appointmentDto.getStartTime().toLocalDate()!= null) {
                for (VaccinationRecordsDto record:appointmentDto.getVaccinationInfo()) {
                    if(!ObjectUtils.isEmpty(record.getDateAdministrated()) && LocalDate.parse(record.getDateAdministrated())
                            //.isAfter(LocalDate.parse(appointmentDto.getDate(), DateTimeFormatter.ofPattern("yyyyMMdd")))) {
                            .isAfter(appointmentDto.getStartTime().toLocalDate())) {
                        throw new BadRequestException("Vaccination administration date must be before appointment date");
                    }
                }
            }
        }
    }

    public static void validateDocument(boolean withoutFile, DocumentOptionDto documentOptionDto) throws BadRequestException{

        if(!withoutFile && ObjectUtils.isEmpty(documentOptionDto.getFile())){
            throw new BadRequestException("Document File is required");
        }

        if(ObjectUtils.isEmpty(documentOptionDto.getName())){
            throw new BadRequestException("Please specify a document name");
        }

    }

    public static void validateQuoteService(List<QuoteAdjustmentDto> adjustmentQuotes) throws BadRequestException{
        for(QuoteAdjustmentDto quoteAdjustments:adjustmentQuotes){
            if(quoteAdjustments.getQuoteprice() == null){
                throw new BadRequestException("Please enter a numeric value as price");
            }
        }
    }
}