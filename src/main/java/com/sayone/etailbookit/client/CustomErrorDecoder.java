package com.sayone.etailbookit.client;

import com.sayone.etailbookit.exception.EtailBookItException;
import feign.FeignException;
import feign.codec.ErrorDecoder;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
public class CustomErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, feign.Response response) {
        if (response.status() >= 400 && response.status() <= 499) {
            return new EtailBookItException("Client error: " + response.status() + " - " + response.reason());
        }
        if (response.status() >= 500 && response.status() <= 599) {
            return new EtailBookItException("Server error: " + response.status() + " - " + response.reason());
        }
        return FeignException.errorStatus(methodKey, response);
    }
} 