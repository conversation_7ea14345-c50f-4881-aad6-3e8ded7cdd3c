package com.sayone.etailbookit.client;

import com.sayone.etailbookit.dto.POSCustomerResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
    name = "pos-api-client",
    url = "${pos.api.feign.url}",
    configuration = POSApiClientConfig.class
)
public interface POSApiClient {

    @GetMapping("/auth/api/v1/customer/list-by-ids")
    POSCustomerResponseDto getCustomerListByIds(
        @RequestHeader("Authorization") String authorization,
        @RequestHeader("X-TenantSchema") String tenantSchema,
        @RequestParam("customerIds") List<Integer> customerIds
    );
} 