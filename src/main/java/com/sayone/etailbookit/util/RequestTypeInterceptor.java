package com.sayone.etailbookit.util;

import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class RequestTypeInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws EtailBookItException {
        String requestType = request.getHeader("User-type");
        if(requestType != null && requestType.equals("Retailer")){
            RequestTypeContext.setRequestType("Retailer");
        }
        else {
            RequestTypeContext.setRequestType("Customer");
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        RequestTypeContext.clearContext();
    }
}
