package com.sayone.etailbookit.util;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Utils {
    public static boolean isNotEmpty(Collection list) {
        return list != null && !list.isEmpty();
    }

    public static List<String> serviceStatusValues = Stream.of(ServiceStatus.values())
            .map(ServiceStatus::name)
            .collect(Collectors.toList());
}
