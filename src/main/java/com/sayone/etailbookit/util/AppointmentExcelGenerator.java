package com.sayone.etailbookit.util;

import com.sayone.etailbookit.dto.BatchAppointmentDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppointmentExcelGenerator {
    public void createHeaderRow(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        Cell cellId = headerRow.createCell(0);
        Cell cellCustomerName = headerRow.createCell(1);
        Cell cellService = headerRow.createCell(2);
        Cell cellPetName = headerRow.createCell(3);
        Cell cellOrderId = headerRow.createCell(4);
        Cell cellEventId = headerRow.createCell(5);
        Cell cellServiceStartAt = headerRow.createCell(6);
        Cell cellServiceEndAt = headerRow.createCell(7);
        Cell cellDuration = headerRow.createCell(8);
        Cell cellDate = headerRow.createCell(9);
        Cell cellAmount = headerRow.createCell(10);
        cellId.setCellValue("ID");
        cellCustomerName.setCellValue("Customer Name");
        cellService.setCellValue("Service");
        cellPetName.setCellValue("Pet Name");
        cellOrderId.setCellValue("Order ID");
        cellEventId.setCellValue("Event ID");
        cellServiceStartAt.setCellValue("Service Start At");
        cellServiceEndAt.setCellValue("Service End At");
        cellDuration.setCellValue("Duration");
        cellDate.setCellValue("Date");
        cellAmount.setCellValue("Amount");
    }

    public void createDataRow(Sheet sheet, BatchAppointmentDto item) {
        Row row = sheet.createRow(sheet.getLastRowNum() + 1);
        Cell cellId = row.createCell(0);
        Cell cellCustomerName = row.createCell(1);
        Cell cellService = row.createCell(2);
        Cell cellPetName = row.createCell(3);
        Cell cellOrderId = row.createCell(4);
        Cell cellEventId = row.createCell(5);
        Cell cellServiceStartAt = row.createCell(6);
        Cell cellServiceEndAt = row.createCell(7);
        Cell cellDuration = row.createCell(8);
        Cell cellDate = row.createCell(9);
        Cell cellAmount = row.createCell(10);
        cellId.setCellValue(item.getId());
        cellCustomerName.setCellValue(item.getCustomerName());
        cellService.setCellValue(item.getService());
        cellPetName.setCellValue(item.getPetName());
        cellOrderId.setCellValue(item.getOrderId());
        cellEventId.setCellValue(item.getEventId());
        cellServiceStartAt.setCellValue(item.getServiceStartAt());
        cellServiceEndAt.setCellValue(item.getServiceEndAt());
        cellDuration.setCellValue(item.getDuration());
        cellDate.setCellValue(item.getDate());
        cellAmount.setCellValue(item.getAmount() != null ? item.getAmount().doubleValue() : 0.0);
    }
}
