package com.sayone.etailbookit.util;

import com.amazonaws.services.dynamodbv2.datamodeling.unmarshallers.BigDecimalSetUnmarshaller;
import com.sayone.etailbookit.dto.AddonServiceDto;
import com.sayone.etailbookit.dto.DiscountDto;
import com.sayone.etailbookit.dto.PaymentDto;
import com.sayone.etailbookit.dto.ServiceBreedInfoDto;
import com.sayone.etailbookit.mapper.AddonServiceMapper;
import com.sayone.etailbookit.model.*;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;


public class AmountCalculation {


    public static PaymentDto serviceAmount(Appointment appointment) {

        PaymentDto paymentDto = new PaymentDto();
        Service service = appointment.getService();
        String serviceUnit = service.getServiceUnit();
        String scheduleUnit = service.getFixedScheduleUnit();
        String amountCurrency = service.getAmount_currency();
        BigDecimal serviceCost = service.getAmountPerUnit() != null ?  service.getAmountPerUnit() : BigDecimal.ZERO;
        //todo: fix this amount
//        BigDecimal serviceLateDiscountAmount = service.getServiceLateDiscountPercent();
        BigDecimal serviceAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal serviceAmountAlone=BigDecimal.ZERO;

        if(service.getScheduleType().equalsIgnoreCase("FIXED")){
            Integer timeTaken = service.getFixedScheduleValue();
            serviceAmount = calculateServiceCharge(scheduleUnit,serviceUnit,timeTaken,serviceCost);
            serviceAmountAlone=serviceAmount;

        }else if(service.getScheduleType().equalsIgnoreCase("VARIABLE")){
            //need to get two values from appointment dto that have input by user at time of appointment
            Integer durationCount = Integer.valueOf(service.getVariableScheduleMaxValue());
            serviceAmount = calculateServiceCharge(scheduleUnit,serviceUnit,durationCount,serviceCost);
            serviceAmountAlone=serviceAmount;

        }else if(service.getScheduleType().equalsIgnoreCase("OVERNIGHT")){
            //get overnight days from appointment data
            Integer timeTaken = service.getMaxOvernights();
            serviceAmount = calculateServiceCharge(scheduleUnit,serviceUnit,timeTaken,serviceCost);
            serviceAmountAlone=serviceAmount;

            if(appointment.getOvernights() >= service.getServiceLateDiscountDays()){
                discountAmount = (serviceAmount.multiply(service.getServiceLateDiscountPercent())).divide(new BigDecimal(100));
//                serviceAmount = serviceAmount.subtract(serviceLateDiscountAmount);
            }
        }
        if(service.getIsServiceLateDiscountAllowed() && appointment.getHaveSibilings() && (appointment.getSibilingCount()>0)){
            // Currently only handle the discount type doller . need to implement % in next release
            discountAmount = discountAmount.add(service.getSiblingDiscountValue());
            serviceAmount = serviceAmount.subtract(service.getSiblingDiscountValue());
        }
        // added Shamppo charge if that is not free
        BigDecimal shampooCharge=BigDecimal.ZERO;
        if(appointment.getShamppo() != null && appointment.getShamppo().getExtraCharge()!= null){
             shampooCharge=BigDecimal.valueOf(appointment.getShamppo().getExtraCharge());
            serviceAmount = serviceAmount.add(BigDecimal.valueOf(appointment.getShamppo().getExtraCharge()));
            paymentDto.setShampooPrice(appointment.getShamppo().getExtraCharge());
        }
        // added Cologne charge if that is not free
        BigDecimal cologneCharge=BigDecimal.ZERO;
        if(appointment.getCologne() != null && appointment.getCologne().getExtraCharge()!= null){
             cologneCharge=BigDecimal.valueOf(appointment.getCologne().getExtraCharge());
            serviceAmount = serviceAmount.add(BigDecimal.valueOf(appointment.getCologne().getExtraCharge()));
            paymentDto.setColognePrice(appointment.getCologne().getExtraCharge());
        }
        BigDecimal venueCharge=BigDecimal.ZERO;
        // added Venue extra charge if that is not free
        if(appointment.getVenue().getExtraCharge() != null){
            // Currently only handle the charge type doller . need to implement % in next release
             venueCharge=appointment.getVenue().getExtraCharge();
            serviceAmount = serviceAmount.add(appointment.getVenue().getExtraCharge());
        }

        BigDecimal addOnServiceCharge=BigDecimal.ZERO;
        if(appointment.getAddOnService() != null && !appointment.getAddOnService().isEmpty()){
            for (AddonService addonService : appointment.getAddOnService()) {
                if (addonService.getTackOnExtraAmount() != null) {
                    addOnServiceCharge=addOnServiceCharge.add(addonService.getTackOnExtraAmount());
                    serviceAmount = serviceAmount.add(addonService.getTackOnExtraAmount());
                }
            }
        }
        BigDecimal quoteAdjustmentCharge=BigDecimal.ZERO;
        if(appointment.getQuoteAdjustments()!=null && !appointment.getQuoteAdjustments().isEmpty()){
            for(QuoteAdjustments quoteAdjustments:appointment.getQuoteAdjustments()){
                if(quoteAdjustments.getQuotePrice()!=null){
                    quoteAdjustmentCharge=quoteAdjustmentCharge.add(quoteAdjustments.getQuotePrice());
                }
            }
            serviceAmount=serviceAmount.add(quoteAdjustmentCharge);
        }
       /* BigDecimal additionalCharges=BigDecimal.ZERO;
        additionalCharges=additionalCharges.add(quoteAdjustmentCharge.add(addOnServiceCharge).add(venueCharge).add(shampooCharge).add(cologneCharge));*/
        paymentDto.setServiceAmountAlone(serviceAmountAlone);
        List<ServiceBreedInfoDto> serviceBreedInfoDtoList = new ArrayList<>();
        if (service.getServiceBreedsInformations() != null && appointment.getPet().getPetBreedsInformations() != null) {
            for (PetBreedsInformation petBreedsInformation: appointment.getPet().getPetBreedsInformations()) {
                for (ServiceBreedsInformation serviceBreedsInformation: service.getServiceBreedsInformations()) {
                    if (serviceBreedsInformation.getBreed().getId().equals(petBreedsInformation.getBreed().getId()) && petBreedsInformation.getBreed().isActive()) {
                        serviceAmount = serviceAmount.add(serviceBreedsInformation.getChargeAmount());

                        ServiceBreedInfoDto serviceBreedInfoDto = new ServiceBreedInfoDto();
                        serviceBreedInfoDto.setId(serviceBreedsInformation.getId());
                        serviceBreedInfoDto.setBreedId(serviceBreedsInformation.getBreed().getId());
                        serviceBreedInfoDto.setBreedName(serviceBreedsInformation.getBreed().getName());
                        serviceBreedInfoDto.setDurationType(serviceBreedsInformation.getDurationType());
                        serviceBreedInfoDto.setDuration(serviceBreedsInformation.getDuration());
                        serviceBreedInfoDto.setChargeAmount(serviceBreedsInformation.getChargeAmount());
                        serviceBreedInfoDtoList.add(serviceBreedInfoDto);
                        break;
                    }
                }
            }
        }
        paymentDto.setServiceBreedInfoDtoList(serviceBreedInfoDtoList);
        paymentDto.setServiceAmount(serviceAmount);
        if(discountAmount.compareTo(BigDecimal.ZERO) >0 ){
            DiscountDto discount = new DiscountDto();
            discount.setDiscountName("Service Discount");
            discount.setDiscountType("DOLLAR");
            discount.setDiscountValue(discountAmount);
            paymentDto.setDiscount(discount);
        }
        if(service.getMinimumFee() != null)
            paymentDto.setMinimumFee(service.getMinimumFee());
        if(appointment.getSource().equalsIgnoreCase("Bookit Appointment Price Edited")){
            paymentDto.setServiceAmount(appointment.getAmount());
        }
        paymentDto.setTime(appointment.getTime().toString());
        paymentDto.setDate(appointment.getDate().toString());
        paymentDto.setIsTaxable(appointment.getService().getIsTaxable());

        return paymentDto;
    }

    public static PaymentDto getPaymentDetails(Appointment appointment) {
        System.out.println("--------Entered getPaymentDetails --------");
        PaymentDto paymentInputs =  new PaymentDto();
        BigDecimal payableAmount = BigDecimal.ZERO;
        paymentInputs.setRetailer(RetailerContext.getRetailer());
        paymentInputs.setCustomerId(appointment.getCustomerId());
        paymentInputs.setAppointmentId(appointment.getId());
        paymentInputs.setServiceAmount(appointment.getAmount());
        if(appointment.getSource().equalsIgnoreCase("GroomBar")){
            payableAmount=appointment.getAmount();
        }
        if(appointment.getDiscountAmount() != null && appointment.getDiscountAmount().compareTo(BigDecimal.ZERO)>0){
            DiscountDto discounts = new DiscountDto();
            discounts.setDiscountValue(appointment.getDiscountAmount());
            discounts.setDiscountName("Service Discount");
            discounts.setDiscountType("DOLLAR");
            paymentInputs.setDiscount(discounts);
            paymentInputs.setHasDiscount(true);
            payableAmount = appointment.getAmount().subtract(appointment.getDiscountAmount());
        }
        if(appointment.getService().getMinimumFee() != null && appointment.getService().getMinimumFee().compareTo(BigDecimal.ZERO)>0 ) {
            paymentInputs.setMinimumFee(appointment.getService().getMinimumFee());
            if(appointment.getService().getMinimumFee().compareTo(payableAmount) > 0){
                payableAmount = appointment.getService().getMinimumFee();
            }
        }
        List<ServiceBreedInfoDto> serviceBreedInfoDtoList = new ArrayList<>();
        if (appointment.getService().getServiceBreedsInformations() != null && appointment.getPet().getPetBreedsInformations() != null) {
            for (PetBreedsInformation petBreedsInformation: appointment.getPet().getPetBreedsInformations()) {
                for (ServiceBreedsInformation serviceBreedsInformation: appointment.getService().getServiceBreedsInformations()) {
                    if (serviceBreedsInformation.getBreed().getId().equals(petBreedsInformation.getBreed().getId()) && petBreedsInformation.getBreed().isActive()) {
                        payableAmount = payableAmount.add(serviceBreedsInformation.getChargeAmount());
                        ServiceBreedInfoDto serviceBreedInfoDto = new ServiceBreedInfoDto();
                        serviceBreedInfoDto.setId(serviceBreedsInformation.getId());
                        serviceBreedInfoDto.setBreedId(serviceBreedsInformation.getBreed().getId());
                        serviceBreedInfoDto.setBreedName(serviceBreedsInformation.getBreed().getName());
                        serviceBreedInfoDto.setDurationType(serviceBreedsInformation.getDurationType());
                        serviceBreedInfoDto.setDuration(serviceBreedsInformation.getDuration());
                        serviceBreedInfoDto.setChargeAmount(serviceBreedsInformation.getChargeAmount());
                        serviceBreedInfoDtoList.add(serviceBreedInfoDto);
                        break;
                    }
                }
            }
        }
        paymentInputs.setServiceBreedInfoDtoList(serviceBreedInfoDtoList);
        paymentInputs.setPayableAmount(payableAmount);


        if(ObjectUtils.notEqual(appointment.getService().getIsTaxable(),false))
            paymentInputs.setIsTaxable(true);
        if(appointment.getService().getIsTipsAllowed()) {
            paymentInputs.setTipEnabled(true);
            if(appointment.getTipAmount()!= null && appointment.getTipAmount().compareTo(BigDecimal.ZERO)>0)
                paymentInputs.setTipAmount(appointment.getTipAmount());
        }
        if(appointment.getService().getPaymentAtTimeOfBooking())
            paymentInputs.setPaymentType(PaymentType.PAYMENT_AT_TIME_OF_BOOKING);
        else if(appointment.getService().getPaymentAfterServiceCompleted())
            paymentInputs.setPaymentType(PaymentType.PAYMENT_AFTER_SERVICE_COMPLETE);
        else if(appointment.getService().getPaymentAtBeginningOfService())
            paymentInputs.setPaymentType(PaymentType.PAYMENT_AT_BEGINNING_OF_SERVICE);

        paymentInputs.setServiceName(appointment.getService().getName());
        if(!appointment.getSource().equalsIgnoreCase("GroomBar")){
            paymentInputs.setDuration(appointment.calculateDuration(appointment));
        }
        System.out.println("--------Exited getPaymentDetails --------");
        return paymentInputs;
    }

    public static BigDecimal calculateServiceCharge(String scheduleUnit, String serviceUnit,Integer timeTaken,BigDecimal serviceCost){
        if (serviceUnit.equalsIgnoreCase("EVENT"))
            return serviceCost;
        else if (scheduleUnit.equalsIgnoreCase("MINUTE") && serviceUnit.equalsIgnoreCase("HOUR"))
            return new BigDecimal(timeTaken).multiply(serviceCost.divide(new BigDecimal(60), 2, RoundingMode.HALF_UP));
        else if (scheduleUnit.equalsIgnoreCase("MINUTE") && serviceUnit.equalsIgnoreCase("DAY"))
            return new BigDecimal(timeTaken).multiply(serviceCost.divide(new BigDecimal(720), 2, RoundingMode.HALF_UP));
        else if (scheduleUnit.equalsIgnoreCase("HOUR") && serviceUnit.equalsIgnoreCase("HOUR"))
            return new BigDecimal(timeTaken).multiply(serviceCost);
        else if (scheduleUnit.equalsIgnoreCase("HOUR") && serviceUnit.equalsIgnoreCase("DAY"))
            return new BigDecimal(timeTaken).multiply(serviceCost.divide(new BigDecimal(12), 2, RoundingMode.HALF_UP));
        else if (scheduleUnit.equalsIgnoreCase("DAY") && serviceUnit.equalsIgnoreCase("HOUR"))
            return new BigDecimal(timeTaken).multiply(serviceCost.multiply(new BigDecimal(12)));
        else if (scheduleUnit.equalsIgnoreCase("DAY") && serviceUnit.equalsIgnoreCase("DAY"))
            return new BigDecimal(timeTaken).multiply(serviceCost);
        else
            return new BigDecimal(timeTaken).multiply(serviceCost);
    }
}
