package com.sayone.etailbookit.util;

public class RequestTypeContext {
    private static ThreadLocal<String> requestType = new InheritableThreadLocal<String>();

    public static String getRequestType(){
        return requestType.get();
    }

    public static void setRequestType(String requestType1){
        requestType.set(requestType1);
    }

    public static void clearContext(){
        requestType.remove();
    }
}
