package com.sayone.etailbookit.util;

import com.sayone.etailbookit.dto.AppointmentSlotsDto;
import com.sayone.etailbookit.model.*;

import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.util.List;

public class ServiceFilter {
    private LocalTime serviceStartTime;
    private LocalTime serviceEndTime;

    public ServiceFilter(LocalTime serviceStartTime, LocalTime serviceEndTime){
        this.serviceStartTime = serviceStartTime;
        this.serviceEndTime = serviceEndTime;
    }

    public boolean isServiceAvailable(AppointmentSlotsDto appointmentSlot){
        //Check day availability
        LocalTime serviceEndTime = this.serviceEndTime;
        LocalTime serviceStartTime = this.serviceStartTime;
        //Check if timeslot is before service start time or after service end time
        if(appointmentSlot.getSlotStartTime().toLocalTime().isBefore(serviceStartTime) ||
                appointmentSlot.getSlotEndTime().toLocalTime().isAfter(serviceEndTime)){
            return false;
        }
        LocalTime slotStartTime = appointmentSlot.getSlotStartTime().toLocalTime();
        LocalTime slotEndTime = appointmentSlot.getSlotEndTime().toLocalTime();
        return ((slotStartTime.isAfter(serviceStartTime) || slotStartTime.equals(serviceStartTime))
                && slotStartTime.isBefore(serviceEndTime)) &&
                slotEndTime.isAfter(serviceStartTime) && (slotEndTime.isBefore(serviceEndTime) ||
                slotEndTime.equals(serviceEndTime));
    }

}
