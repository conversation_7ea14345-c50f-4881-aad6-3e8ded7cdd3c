package com.sayone.etailbookit.util;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.OutputStream;

@Configuration
public class ByteArrayToMultipartFile {

    public static MultipartFile convert(byte[] fileBytes, String fileName, String contentType) {
        // Create a DiskFileItem instance
        DiskFileItem fileItem = new DiskFileItem(
                "file",        // Form field name
                contentType,   // Content type
                false,         // Is form field
                fileName,      // File name
                fileBytes.length, // Size
                null           // Temporary directory (null for default)
        );

        try (OutputStream os = fileItem.getOutputStream()) {
            IOUtils.copy(new ByteArrayInputStream(fileBytes), os);
        } catch (Exception e) {
            throw new RuntimeException("Error converting byte array to MultipartFile", e);
        }

        // Wrap DiskFileItem in a CommonsMultipartFile
        return new CommonsMultipartFile(fileItem);
    }

}
