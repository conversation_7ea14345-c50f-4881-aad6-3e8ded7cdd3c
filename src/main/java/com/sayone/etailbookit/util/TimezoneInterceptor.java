package com.sayone.etailbookit.util;

import com.sayone.etailbookit.exception.EtailBookItException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.TimeZone;

@Component
public class TimezoneInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws EtailBookItException {
        String timezone = request.getHeader("time_zone");
        if(timezone == null){
            //set default as UTC
            OffsetContext.setOffset("+00:00");
            TimeZoneContext.setTimeZone(ZoneOffset.UTC.toString());

        }
        else {
            OffsetContext.setOffset(
                    TimeZone.getTimeZone(timezone).toZoneId().getRules().getOffset(Instant.now())
                            .toString().equalsIgnoreCase("Z") ?
                            "+00:00"
                            : TimeZone.getTimeZone(timezone).toZoneId().getRules().getOffset(Instant.now()).toString()
            );
            TimeZoneContext.setTimeZone(timezone);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        TimeZoneContext.clearContext();
    }
}
