package com.sayone.etailbookit.util;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

@Converter()
public class OffsetDateTimeConverter implements AttributeConverter<OffsetDateTime, Timestamp> {

    @Override
    public Timestamp convertToDatabaseColumn(OffsetDateTime attribute) {
        return (attribute == null) ? null : Timestamp.valueOf(attribute.toLocalDateTime());
    }

    @Override
    public OffsetDateTime convertToEntityAttribute(Timestamp dbData) {
        return (dbData == null) ? null : OffsetDateTime.of(dbData.toLocalDateTime(), ZoneOffset.UTC);
    }
}