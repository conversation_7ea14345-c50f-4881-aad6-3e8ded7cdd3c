package com.sayone.etailbookit.util;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.kinesis.AmazonKinesis;
import com.amazonaws.services.kinesis.AmazonKinesisClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.regions.Region;

@Configuration
public class KinesisConfig {

   /* @Value("${aws.kinesis.credentials.accessKey}")
    private String accessKey;
    @Value("${aws.kinesis.credentials.secretKey}")
    private String secretKey;*/

    @Value("${aws.kinesis.region}")
    private String region;

    @Bean
    public AmazonKinesis buildAmazonKinesis() {
      //  BasicAWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        return AmazonKinesisClientBuilder.standard()
          //      .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .withRegion(Regions.fromName(region))
                .build();
    }

}
