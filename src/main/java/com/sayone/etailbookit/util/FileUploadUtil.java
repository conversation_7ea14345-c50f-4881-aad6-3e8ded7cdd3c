package com.sayone.etailbookit.util;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.sayone.etailbookit.exception.BadRequestException;
import com.sayone.etailbookit.exception.EtailBookItException;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.prefs.BackingStoreException;

import org.apache.commons.io.FilenameUtils;


public class FileUploadUtil {

    private static Logger LOGGER = LoggerFactory.getLogger(FileUploadUtil.class);


    public static String uploadFile(MultipartFile multipartFile, String bucketName, AmazonS3 amazonS3Client, String awsEndpoint, String folderPath,FileType fileType) throws Exception {


        String fileName = generateFileName(multipartFile);
        String fileExtension = FilenameUtils.getExtension(fileName);
        switch (fileType) {
            case Photos:
                if(fileExtension.equals("jpeg") || fileExtension.equals("jpg") || fileExtension.equals("png") || fileExtension.equals("JPEG") || fileExtension.equals("JPG") || fileExtension.equals("PNG")) {
                    LOGGER.info("Image File Validated");
                    break;
                }
                else{
                    throw new BadRequestException("Invalid file type, please upload jpeg or png files");
                }
            case Documents:
                if(fileExtension.equals("doc") || fileExtension.equals("docx") || fileExtension.equals("pdf")){
                    LOGGER.info("Document File Validated");
                    break;
                }
                else{
                    throw new BadRequestException("Invalid file type, please upload doc or pdf files");
                }
            case ExcelFile:
                if(fileExtension.equals("xlsx")){
                    LOGGER.info("Excel file  validated");
                    break;
                }else{
                    throw  new BadRequestException("Invalid File Type, please upload xlsx file");
                }
            case DocumentsORPhotos:
                if(fileExtension.equals("doc") || fileExtension.equals("docx") || fileExtension.equals("pdf")){
                    LOGGER.info("Document File Validated");
                    break;
                }
                if(fileExtension.equals("jpeg") || fileExtension.equals("jpg") || fileExtension.equals("png") || fileExtension.equals("JPEG") || fileExtension.equals("JPG") || fileExtension.equals("PNG")) {
                    LOGGER.info("Image File Validated");
                    break;
                }
                else{
                    throw new BadRequestException("File Type Not Supported");
                }
            default:
                LOGGER.info("File Type Not Supported");
        }
        File file = convertMultiPartToFile(multipartFile);
        String fileUrl;
        try {
            fileUrl = awsEndpoint + "/" + folderPath + "/" + fileName;
            amazonS3Client.putObject(bucketName, folderPath + "/" + fileName, file);
            file.delete();
            return fileUrl;
        }
        catch (Exception e) {
            file.delete();
            LOGGER.error("Exception occurred while file upload ::::" + e);
            throw e;
        }
    }

    private static File convertMultiPartToFile(MultipartFile file) throws IOException {
        File convFile = new File(file.getOriginalFilename());
        FileOutputStream fos = new FileOutputStream(convFile);
        fos.write(file.getBytes());
        fos.close();
        return convFile;
    }

    private static String generateFileName(MultipartFile multipartFile) {
        return new Date().getTime() + "-" + multipartFile.getOriginalFilename().replace(" ", "_");
    }

    public static boolean deleteFile(String fileUrl, AmazonS3 amazonS3Client, String bucketName, String folderPath) throws IOException {
        try {
            String fileName = null;
            if(ObjectUtils.isNotEmpty(fileUrl) || fileUrl != null) {
                fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            }
            DeleteObjectsResult deleteObjectsResult = amazonS3Client.deleteObjects(new DeleteObjectsRequest(bucketName)
                    .withKeys(Collections.singletonList(new DeleteObjectsRequest.KeyVersion(folderPath + "/" + fileName)))
            .withQuiet(false));

            if (deleteObjectsResult.getDeletedObjects().size() == 1)
                return true;
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public static boolean deleteMultipleFiles(List<String> fileUrls, AmazonS3 amazonS3Client, String bucketName, String folderPath) throws IOException {
        List<DeleteObjectsRequest.KeyVersion> keys = new ArrayList<>();
        for (String fileUrl : fileUrls) {
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            keys.add(new DeleteObjectsRequest.KeyVersion(folderPath + "/" + fileName));
        }

        DeleteObjectsRequest multiObjectDeleteRequest = new DeleteObjectsRequest(bucketName)
                .withKeys(keys)
                .withQuiet(false);

        DeleteObjectsResult delObjRes = amazonS3Client.deleteObjects(multiObjectDeleteRequest);

        int successfulDeletes = delObjRes.getDeletedObjects().size();
        LOGGER.info("No. of Files Deleted ::::"+successfulDeletes);
        return successfulDeletes == fileUrls.size();
    }

}
