package com.sayone.etailbookit.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins("*").allowedMethods("GET","POST","PUT","DELETE");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new RetailInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("actuator/health", "swagger", "favicon", "webjars", "error", "api-docs");
        registry.addInterceptor(new TimezoneInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("actuator/health", "swagger", "favicon", "webjars", "error", "api-docs");
        registry.addInterceptor(new RequestTypeInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("actuator/health", "swagger", "favicon", "error", "webjars", "api-docs");
    }

    @Bean
    public WebMvcConfigurer corsConfigurer()
    {
        return new WebMvcConfigurer() {

        };
    }
}
