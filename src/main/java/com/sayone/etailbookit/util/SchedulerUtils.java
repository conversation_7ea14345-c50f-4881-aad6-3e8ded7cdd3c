package com.sayone.etailbookit.util;

import com.sayone.etailbookit.dto.AppointmentSlotsDto;
import com.sayone.etailbookit.dto.ServiceScheduleType;
import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.AttendantModelAvailabilityProjections;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class SchedulerUtils {
    public static List<AppointmentSlotsDto> generateFreeTimeSlotsForDay(
            Venue venue, Attendant attendant, Service requiredService, List<Appointment> existingAppointments,
            Integer slotDuration, String durationUnit, OffsetDateTime startTime, OffsetDateTime stopTime,
            Integer slotInterval
    ){
        List<AppointmentSlotsDto> freeTimeSlots = new ArrayList<>();
        //First time slot is taken based on when the venue is opened
        OffsetDateTime slotStart = startTime;
        OffsetDateTime slotEnd = startTime.plus(slotDuration,ChronoUnit.valueOf((durationUnit + "S").toUpperCase()));
        int preBuffer = requiredService.getPre_buffer_mins() != null ? requiredService.getPre_buffer_mins() : 0;
        int postBuffer = requiredService.getPostBufferMins() != null ? requiredService.getPostBufferMins() : 0;
        //Loop continues as long as time slot is within the venue opening and closing times
        while((stopTime.isAfter(slotStart) || stopTime.isEqual(slotEnd) ) && (startTime.isBefore(slotStart) || startTime.isEqual(slotStart))){
            int venueCapacity = 0;
            int attendantCapacity = 0;
            //int newAppointmentSizeConstraintCapacity=0;
            boolean eligibility = true;
            for( Appointment appointment : existingAppointments){
                OffsetDateTime existingSlotStart = appointment.getServiceStartAt();
                OffsetDateTime existingSlotEnd = appointment.getServiceEndAt();
                //Appointment has been booked, but service has not started
                if(existingSlotStart == null) {
                    existingSlotStart = OffsetDateTime.of(appointment.getDate(), appointment.getTime().toLocalTime(), ZoneOffset.UTC);
                }
                //Either service has started and ongoing or appointment had been booked and service has not started
                if (existingSlotEnd == null) {
                    Integer duration = 0;
                    if (ServiceScheduleType.FIXED.toString().equals(appointment.getService().getScheduleType())) {
                        duration = appointment.getService().getFixedScheduleValue();
                    }
                    else if (ServiceScheduleType.VARIABLE.toString().equals(appointment.getService().getScheduleType())) {
                        duration = Integer.parseInt(appointment.getService().getVariableScheduleMaxValue());
                    }
                    existingSlotEnd = existingSlotStart.plus(
                            duration,
                            ChronoUnit.valueOf((appointment.getService().getFixedScheduleUnit() + "S").toUpperCase())
                    );
                }
                if(
                        existingSlotEnd != null && !existingSlotEnd.isBefore(slotStart) && !existingSlotStart.isAfter(slotEnd)
                ) {
                    // Keeps Track of venue capacity for overlapping time slots
                    venueCapacity += 1;
                    if(appointment.getAttendant().getAttendantId().equals(attendant.getAttendantId()))
                        attendantCapacity += 1;
                    //Checks if venue supports DoubleBooking and check if the two services are the same
                    if(!venue.getSupportSimultaneousBookings()){
                        if(!appointment.getService().getServiceId().equals(requiredService.getServiceId())){
                            eligibility = false;
                            break;
                        }
                    }
                }
              /*  if(appointment.getAttendant().getAttendantId().equals(attendant.getAttendantId())) {
                    for(PetSizeLimit petSizeLimit:attendant.getPetSizeLimits()){
                        for(GeneralPetSize generalPetSize:appointment.getPetType().getGeneralPetSizes()){
                            if(petSizeLimit.getGeneralPetSize().equals(generalPetSize)){
                                newAppointmentSizeConstraintCapacity=petSizeLimit.getCapacity();
                                newAppointmentSizeConstraintCapacity=newAppointmentSizeConstraintCapacity-1;
                                if(newAppointmentSizeConstraintCapacity<=0){
                                    eligibility=false;
                                    break;
                                }
                            }
                        }
                    }
                }*/
            }
            //if capacity has been exceeded, then slot is invalid
            if(venueCapacity >=  venue.getParticipantLimitService() || attendantCapacity >= attendant.getCapacityLimit()){
                eligibility = false;
            }
            //if time slot is outside the available start and end time, then slot is invalid
            if(slotStart.isBefore(startTime) || slotEnd.isAfter(stopTime)){
                eligibility = false;
            }
            if(eligibility){
                AppointmentSlotsDto freeSlot = new AppointmentSlotsDto();
                if(freeTimeSlots.isEmpty()) {
                    freeSlot.setSlotDate(slotStart.plus(preBuffer+postBuffer,ChronoUnit.MINUTES).toLocalDate());
                    freeSlot.setSlotStartTime(slotStart.plus(preBuffer, ChronoUnit.MINUTES));
                    freeSlot.setSlotEndTime(slotEnd.plus(preBuffer, ChronoUnit.MINUTES));
                    freeSlot.setAttendantId(attendant.getAttendantId());
                    freeSlot.setAttendantName(attendant.getFirstName());
                }
                else {
                    freeSlot.setSlotDate(slotStart.plus(preBuffer+postBuffer,ChronoUnit.MINUTES).toLocalDate());
                    freeSlot.setSlotStartTime(slotStart.plus(preBuffer + postBuffer, ChronoUnit.MINUTES));
                    freeSlot.setSlotEndTime(slotEnd.plus(preBuffer + postBuffer, ChronoUnit.MINUTES));
                    freeSlot.setAttendantId(attendant.getAttendantId());
                     freeSlot.setAttendantName(attendant.getFirstName());
                }
                slotStart = freeSlot.getSlotEndTime();
                slotEnd = slotStart.plus(slotDuration, ChronoUnit.valueOf((durationUnit+"S").toUpperCase()));
                freeTimeSlots.add(freeSlot);
            }
            else {
                slotStart = slotStart.plus(slotInterval,ChronoUnit.MINUTES);
                slotEnd = slotStart.plus(slotDuration,ChronoUnit.valueOf((durationUnit+"S").toUpperCase()));
            }
//            break;
        }
        return freeTimeSlots;
    }
}
