package com.sayone.etailbookit.util;

import org.springframework.stereotype.Component;
import java.io.FileOutputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;

import org.springframework.stereotype.Component;

import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.text.pdf.parser.TextMarginFinder;

@Component
public class SignUtil {
    public Rectangle findXandYCoordinatesOfPdf(String src, String dest) throws  DocumentException, java.io.IOException {
        PdfReader reader = new PdfReader(src);
        Rectangle rect;
        PdfReaderContentParser parser = new PdfReaderContentParser(reader);
        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(dest));
        TextMarginFinder finder= null;
        int numberofPages=reader.getNumberOfPages();
        if(numberofPages==1){
            rect=new Rectangle(50,30,200,35);
            return rect;
        }
        for (int i = reader.getNumberOfPages(); i <= reader.getNumberOfPages(); i++) {
            finder = parser.processContent(i, new TextMarginFinder());
            PdfContentByte cb = stamper.getOverContent(i);
            cb.rectangle(finder.getLlx(), finder.getLly(),
                    finder.getWidth(), finder.getHeight());
            cb.stroke();
            System.out.println(" cordinates "+finder.getLlx()+"y cordinates"+finder.getLly());
        }
        stamper.close();
        reader.close();
        if(finder.getLly()-30>=0) {
            //float y=finder.getLly()-10;
            rect = new Rectangle(finder.getLlx(),finder.getLly()-30,200,35);
        }
        else {
            rect = new Rectangle(finder.getLlx(),0,200,35);
        }

        return rect;

    }
}
