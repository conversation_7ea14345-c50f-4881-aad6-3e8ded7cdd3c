package com.sayone.etailbookit.util;

public class RetailerContext {
    private static ThreadLocal<String> retailer = new InheritableThreadLocal<String>();

    public static String getRetailer(){
        return retailer.get();
    }

    public static void setRetailer(String retailerName){
         retailer.set(retailerName);
    }

    public static void clearContext(){
        retailer.remove();
    }
}
