package com.sayone.etailbookit.util;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class DaysOfWeek {

    Map<String, String> daysOfWeek;

    public DaysOfWeek() {
        daysOfWeek = new HashMap();
        daysOfWeek.put("MON","MONDAY");
        daysOfWeek.put("TUES","TUESDAY");
        daysOfWeek.put("WED","WEDNESDAY");
        daysOfWeek.put("THUR","THURSDAY");
        daysOfWeek.put("FRI","FRIDAY");
        daysOfWeek.put("SAT","SATURDAY");
        daysOfWeek.put("SUN","SUNDAY");
    }

    public Map<String, String> getMap() {
        return daysOfWeek;
    }

    public String getKeyByValue(Map<String,String> map, String value) {
        if (map.containsValue(value)) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (Objects.equals(entry.getValue(), value)) {
                    return entry.getKey().toString();
                }
            }
        }
        return "";
    }

    public String getDayOfWeek(String key) {
        return daysOfWeek.get(key) != null?daysOfWeek.get(key).toString():"";
    }

    public String getKey(String value){
        Optional<String> key=  daysOfWeek.entrySet().stream().filter(entry->entry.getValue().equals(value)).map(Map.Entry::getKey).findFirst();
        return key.orElse("key not found");
    }
}
