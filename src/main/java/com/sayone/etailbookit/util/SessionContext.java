package com.sayone.etailbookit.util;

public class SessionContext {
    private static ThreadLocal<String> sessionkey = new InheritableThreadLocal<String>();

    public static String getSession(){return sessionkey.get();}

    public static void setSession(String sessionkeyName){
        sessionkey.set(sessionkeyName);
    }

    public static void clearContext(){
        sessionkey.remove();
    }
}
