package com.sayone.etailbookit.util;

import com.sayone.etailbookit.dto.CancelledAppointmentsDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CancelledAppointmentExcelGenerator {

    public void createHeaderRow(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        Cell cellCustomerName = headerRow.createCell(0);
        Cell cellPetName = headerRow.createCell(1);
        Cell cellAppointmentDateTime = headerRow.createCell(2);
        Cell cellVenue = headerRow.createCell(3);
        Cell cellService = headerRow.createCell(4);
        Cell cellCancellationRate = headerRow.createCell(5);
        cellCustomerName.setCellValue("Client Name");
        cellPetName.setCellValue("Pet Name");
        cellAppointmentDateTime.setCellValue("Appointment Date");
        cellVenue.setCellValue("Venues");
        cellService.setCellValue("Services");
        cellCancellationRate.setCellValue("Appointment Cancellation Rate");
    }

    public void createDataRow(Sheet sheet, CancelledAppointmentsDto item) {
        Row row = sheet.createRow(sheet.getLastRowNum() + 1);
        Cell cellCustomerName = row.createCell(0);
        Cell cellPetName = row.createCell(1);
        Cell cellAppointmentDateTime=row.createCell(2);
        Cell cellVenue=row.createCell(3);
        Cell cellService=row.createCell(4);
        Cell cellCancellationRate = row.createCell(5);
        cellCustomerName.setCellValue(item.getCustomerName());
        cellPetName.setCellValue(item.getPetName());
        cellAppointmentDateTime.setCellValue(item.getAppointmentDateTime());
        cellVenue.setCellValue(item.getVenue());
        cellService.setCellValue(item.getService());
        if(item.getAppointmentCancellationRate()!=null) {
            cellCancellationRate.setCellValue(item.getAppointmentCancellationRate().doubleValue());
        }

    }
}
