package com.sayone.etailbookit.util;

import org.springframework.context.annotation.Configuration;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

@Configuration
public class UserTimeZoneConversion {
    public String convertToUserTime(LocalTime time) {
        String convertedTime = time.atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ofPattern( "hh:mm:ss a" )).toString();
        return convertedTime.split("[+\\-Z]")[0];
    }

    public String convertToUserTimeZone(OffsetDateTime offsetDateTime,String timeZone){
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        long offsetDifference= tz.getOffset(new Date().getTime()) / 1000 / 60;
        OffsetDateTime userOffsetTime=offsetDateTime.plusMinutes(offsetDifference);
        LocalTime time=userOffsetTime.toLocalTime();
        String convertedTime = time.atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ofPattern( "hh:mm:ss a" )).toString();
        return convertedTime.split("[+\\-Z]")[0];
    }

    public String convertToUserDate(OffsetDateTime offsetDateTime,String timeZone){
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        long offsetDifference= tz.getOffset(new Date().getTime()) / 1000 / 60;
        OffsetDateTime userOffsetTime=offsetDateTime.plusMinutes(offsetDifference);
        LocalDate date=userOffsetTime.toLocalDate();
        return date.toString();
    }

    public String convertToOffsetDate(String offsetDateTime){
        LocalDate date=OffsetDateTime.parse(offsetDateTime).toLocalDate();
        return date.toString();
    }
    public static OffsetDateTime getStartOfDayInUTC(String timeZone, OffsetDateTime localDate) {
        ZonedDateTime startOfDay = localDate.atZoneSameInstant(ZoneId.of(timeZone))
                .toLocalDate()
                .atStartOfDay(ZoneId.of(timeZone));
        return startOfDay.withZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime();
    }

    public static OffsetDateTime getEndOfDayInUTC(String timeZone, OffsetDateTime localDate) {
        ZonedDateTime endOfDay = localDate.atZoneSameInstant(ZoneId.of(timeZone))
                .toLocalDate()
                .atTime(23, 59, 59)
                .atZone(ZoneId.of(timeZone));
        return endOfDay.withZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime();
    }
}
