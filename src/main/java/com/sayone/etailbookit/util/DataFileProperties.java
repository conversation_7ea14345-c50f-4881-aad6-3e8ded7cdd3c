package com.sayone.etailbookit.util;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class DataFileProperties {

    @Value("${aws.s3.endpoint}")
    private  String awsEndpoint;

    @Value("${aws.s3.bucketName}")
    private  String bucketName;

    @Value("${service.type.prefix}")
    private  String serviceTypePrefix;

    @Value("${pet.type.prefix}")
    private  String petTypePrefix;

    @Value("${pet.photos.prefix}")
    private  String petPhotosPrefix;

    @Value("${pet.documents.prefix}")
    private  String petDocumentPrefix;

    @Value("${service.photos.prefix}")
    private  String servicePhotosPrefix;

    @Value("${appointment.daily.export.prefix}")
    private  String appointmentDailyExportPrefix;

}
