package com.sayone.etailbookit.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.InstanceProfileCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kinesis.KinesisClient;

@Configuration
public class KinesisClientConfig {

    /*@Bean
    public KinesisClient kinesisClient() {
        return KinesisClient.create();
    }*/
    @Value("${aws.kinesis.region}")
    private String region;

    @Bean
    public KinesisClient kinesisClient() {
        return KinesisClient.builder()
             //   .credentialsProvider(InstanceProfileCredentialsProvider.create())
                .region(Region.of(region))
                .build();
    }
}
