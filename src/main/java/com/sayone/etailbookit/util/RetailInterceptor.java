package com.sayone.etailbookit.util;

import com.sayone.etailbookit.exception.EtailBookItException;
import com.sayone.etailbookit.exception.UnAuthorizedException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class RetailInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws EtailBookItException {

        String retailer = request.getHeader("retailer");
        if(retailer == null){
            throw new UnAuthorizedException("Retailer not specified");
        }
        RetailerContext.setRetailer(retailer);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        RetailerContext.clearContext();
    }
}
