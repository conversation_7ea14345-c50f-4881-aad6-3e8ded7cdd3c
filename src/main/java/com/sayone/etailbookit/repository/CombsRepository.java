package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Combs;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface CombsRepository extends JpaRepository<Combs, Integer>, JpaSpecificationExecutor<Combs> {
    void deleteByPetType(PetType petType);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM Combs A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Combs T where T.petType.petTypeId=?1 and T.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    List<Combs> findByRetailer(String retailer);
}