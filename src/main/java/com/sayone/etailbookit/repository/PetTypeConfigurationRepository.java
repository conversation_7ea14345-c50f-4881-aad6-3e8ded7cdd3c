package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.model.PetTypeConfiguration;
import com.sayone.etailbookit.util.PetTypeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface PetTypeConfigurationRepository extends JpaRepository<PetTypeConfiguration, Integer>, JpaSpecificationExecutor<PetTypeConfiguration> {

    PetTypeConfiguration findByNameAndPetTypeAndRetailer(PetTypeConfig petTypeConfig, PetType petType, String retailer);

    List<PetTypeConfiguration> findByPetTypePetTypeId(Integer petTypeId);

    @Transactional
    @Modifying
    @Query("Delete from PetTypeConfiguration E where E.petType.petTypeId=?1 and E.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    List<PetTypeConfiguration> findByRetailer(String retailer);
}