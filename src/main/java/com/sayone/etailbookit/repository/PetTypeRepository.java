package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.DocumentOption;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.projections.PetProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;


public interface PetTypeRepository extends JpaRepository<PetType, Integer>, JpaSpecificationExecutor<PetType> {

    PetType findByPetTypeId(Integer petTypeId);
    PetType findByPetTypeIdAndDeleted(Integer petTypeId, <PERSON>olean deleted);

    List<PetType> findAllByNameIgnoreCaseAndRetailerAndDeleted(String name, String retailer, Boolean deleted);

    @Query(value = "select name as petTypeName from pet_type where id=?1 ;",nativeQuery = true)
    PetProjection getPetTypeName(@Param("petTypeId") Integer petTypeId);

    @Query(
            "Select P from PetType P where P.retailer = ?1 and P.deleted = ?3 " +
                    "and (?2 is null or lower(P.name) like %?2%)"
    )
    Page<PetType> getPetTypes(String retailer, String search, Boolean deleted, Pageable pageable);

    @Query("Select P from PetType P where P.generalPetSizes is empty")
    List<PetType> getPetTypesWithoutSizes();

    @Modifying
    @Query(value = "Delete from pet_type_document_options A where A.pet_type_id = :pet_type_id AND A.document_options_id = :document_options_id", nativeQuery = true)
    void deleteByPetTypeIdAndDocumentOptionId(@Param("pet_type_id") Integer petTypeId, @Param("document_options_id") Integer documentOptionId);

    long countAllByDocumentOptionsContains(DocumentOption documentOption);

    @Modifying
    @Transactional
    @Query("Select P from PetType P where P.retailer=?1")
    List<PetType> findByRetailer(String retailer);

    @Query(value = "SELECT pt  FROM PetType pt WHERE LOWER(pt.name)=LOWER(?1) and pt.retailer =?2")
    PetType findByPetTypenameAndRetailer(String petTypename, String retailer);

    PetType findByName(String groombarPetType);
  
    @Transactional
    @Modifying
    @Query("Update PetType set deleted=:deleted where petTypeId=:petTypeId and retailer=:retailer")
    void deletePetType(Integer petTypeId, String retailer, boolean deleted);

    List<PetType> findByRetailerAndActiveAndDeleted(String retailer, Optional<Boolean> active, boolean deleted);
}