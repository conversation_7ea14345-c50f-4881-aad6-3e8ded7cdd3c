package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.VenueAvailability;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface VenueAvailabilityRepository extends JpaRepository<VenueAvailability, Integer>, JpaSpecificationExecutor<VenueAvailability> {

    List<VenueAvailability> findByVenueVenueId(Integer id);

    @Transactional
    @Modifying
    @Query("Delete from VenueAvailability V where V.venue.id=?1 and V.retailer=?2")
    void deleteByVenueId(Integer venueId, String retailer);

    @Query("Select V from VenueAvailability V where V.retailer=?1")
    List<VenueAvailability> findBy<PERSON>etailer(String retailer);
}
