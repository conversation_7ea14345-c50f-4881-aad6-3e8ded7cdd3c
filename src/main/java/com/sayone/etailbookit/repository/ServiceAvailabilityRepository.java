package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Service;
import com.sayone.etailbookit.model.ServiceAvailability;

import com.sayone.etailbookit.projections.ServiceAvailabilityProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

public interface ServiceAvailabilityRepository extends JpaRepository<ServiceAvailability, Integer>, JpaSpecificationExecutor<ServiceAvailability> {
    void deleteByService(Service service);

    @Query(value = "select sa.id as serviceAvailableId,sa.available_day as availableDay,sa.availability_open_time as availabilityOpenTime," +
            "sa.availability_close_time as availabilityCloseTime " +
            "from service_availability sa " +
            "where service_id=?1 ;",nativeQuery = true)
    List<ServiceAvailabilityProjection> getServiceAvailabilityDays(@Param("serviceId") Integer serviceId);

    @Transactional
    @Modifying
    @Query("Delete from ServiceAvailability S where S.service.serviceId=?1 and S.retailer=?2")
    void deleteByServiceId(Integer serviceId, String retailer);

    List<ServiceAvailability> findByRetailer(String retailer);
}
