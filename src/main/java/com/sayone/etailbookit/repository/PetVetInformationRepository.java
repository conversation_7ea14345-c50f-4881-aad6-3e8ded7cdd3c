package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.PetVetInformation;
import com.sayone.etailbookit.projections.VetProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

public interface PetVetInformationRepository extends JpaRepository<PetVetInformation, Integer>, JpaSpecificationExecutor<PetVetInformation> {

    void deleteByPet(Pet pet);

    Optional<PetVetInformation> findByPetIdAndVetInformationId(Integer petId, Integer id);

    @Transactional
    @Modifying
    @Query("Delete from PetVetInformation A where A.vetInformation.id = ?1")
    void deleteByVetInformation(Integer vetInformationId);

    @Transactional
    @Modifying
    @Query("Delete from PetVetInformation P where P.pet.id=?1 and P.retailer=?2")
    void deleteByPetId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from PetVetInformation P where P.vetInformation.id=?1 and P.retailer=?2")
    void deleteByVetInformationId(Integer id, String retailer);

    @Query("Select P from PetVetInformation P where P.pet.id=?1 and P.retailer=?2 ")
    List<PetVetInformation> findByPetIdAndRetailer(Integer id, String retailer);
}