package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.PetVaccinationRecords;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface PetVaccinationRecordRepository extends JpaRepository<PetVaccinationRecords, Integer>, JpaSpecificationExecutor<PetVaccinationRecords> {
    void deleteByPet(Pet pet);

    Optional<PetVaccinationRecords> findByPetIdAndVaccinationRecordsVaccinationRecordId(Integer petId, Integer vaccinationRecordId);

    List<PetVaccinationRecords> findByDateExpires(LocalDate seventhDay);

    @Transactional
    @Modifying
    @Query(value="Delete from pet_vaccination_records P where P.vaccination_record_id = ?1", nativeQuery=true)
    void deleteByVaccinationInfo(Integer vaccinationRecordsId);

    @Transactional
    @Modifying
    @Query(value="Delete from PetVaccinationRecords P where P.vaccinationRecords.id= ?1")
    void deleteByVaccinationInfoId(Integer vaccinationRecordId);

    @Transactional
    @Modifying
    @Query(value="Delete from PetVaccinationRecords P where P.pet.id= ?1 and P.retailer=?2")
    void deleteByPetId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query(value="Delete from PetVaccinationRecords P where P.pet.id= ?1")
    void deleteByPetId(Integer id);

    List<PetVaccinationRecords> findByRetailer(String retailer);

    List<PetVaccinationRecords> findAllByVaccinationRecordsVaccinationRecordIdAndPetId(Integer documentId, Integer petId);


    @Transactional
    @Modifying()
    @Query("Delete from PetVaccinationRecords A where A.id NOT IN ?1 AND A.pet.id = ?2")
    void deleteAllByIdNotInAndPetId(List<Integer> ids, Integer petId);
}