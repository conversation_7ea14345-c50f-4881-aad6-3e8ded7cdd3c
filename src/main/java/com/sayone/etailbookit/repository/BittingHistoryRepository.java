package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.BittingHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface BittingHistoryRepository extends JpaRepository<BittingHistory, Integer>, JpaSpecificationExecutor<BittingHistory> {

    List<BittingHistory> findByRetailer(String retailer);

}