package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.dto.EmergencyContactInfoDto;
import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.PetEmergencyContactInfo;
import com.sayone.etailbookit.projections.EmergencyContactProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

public interface PetEmergencyContactRepository extends JpaRepository<PetEmergencyContactInfo, Integer>, JpaSpecificationExecutor<PetEmergencyContactInfo> {
    void deleteByPet(Pet pet);

    @Query(value = "select pec.value as contactValue,eci.name as contactName " +
            "FROM pet_emergency_contact_info pec " +
            "LEFT OUTER JOIN emergency_contact_info eci on pec.emergency_contact_id = eci.id " +
            "where pec.pet_id=?1 ", nativeQuery = true)
    List<EmergencyContactProjection> getEmergencyByPet(Integer petId);

    Optional<PetEmergencyContactInfo> findByPetIdAndEmergencyContactInfoEmergencyContactInfoId(Integer petId, Integer emergencyContactInfoId);

    @Transactional
    @Modifying
    @Query("Delete from PetEmergencyContactInfo A where A.emergencyContactInfo.emergencyContactInfoId = ?1")
    void deleteByEmergencyContactInfo(Integer emergencyContactInfosId);

    @Transactional
    @Modifying
    @Query(value = "Delete from pet_emergency_contact_info A where A.pet_id=:id and A.retailer=:retailer",nativeQuery = true)
    void deleteByPetId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from PetEmergencyContactInfo A where A.emergencyContactInfo.emergencyContactInfoId = ?1 and A.retailer=?2")
    void deleteByEmergencyContactInfoId(Integer emergencyContactInfoId, String retailer);

    List<PetEmergencyContactInfo> findByRetailer(String retailer);
}