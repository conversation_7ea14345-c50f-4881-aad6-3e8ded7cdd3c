package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.FeedingInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface FeedingInformationRepository extends JpaRepository<FeedingInformation, Integer>, JpaSpecificationExecutor<FeedingInformation> {

    FeedingInformation findByRetailer(String retailer);

    FeedingInformation findByFeedingInformationId(int feedingInformationId);
}