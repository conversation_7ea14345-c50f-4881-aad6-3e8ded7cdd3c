package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.HairTexture;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.projections.HairTextureProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

public interface HairTextureRepository extends JpaRepository<HairTexture, Integer>, JpaSpecificationExecutor<HairTexture> {

    void deleteByPetType(PetType petType);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM HairTexture A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);


    List<HairTexture> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from HairTexture E where E.petType.petTypeId=?1 and E.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);
}