package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Allergies;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;


public interface AllergiesRepository extends JpaRepository<Allergies, Integer>, JpaSpecificationExecutor<Allergies> {
    void deleteByPetType(PetType petType);
    Allergies findByAllergyId(Integer bladeId);

    List<Allergies> findByPetTypePetTypeId(Integer id);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM Allergies A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Allergies A where A.petType.petTypeId=?1 and A.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Query("Select A from Allergies A where A.retailer=?1")
    List<Allergies> findByRetailer(String retailer);
}