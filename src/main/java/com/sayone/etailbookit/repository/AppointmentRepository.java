package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.*;
import com.sayone.etailbookit.util.ServiceStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.time.*;
import java.util.List;
import java.util.Optional;
import java.util.Set;


public interface AppointmentRepository extends JpaRepository<Appointment, Integer>, JpaSpecificationExecutor<Appointment> {

    @Query("Select A from Appointment A where A.pet = ?1")
    List<Appointment> findByPet(Pet pet);

    @Query("Select A from Appointment A where A.shamppo = ?1")
    List<Appointment> findByShamppo(PetShampoo petShampoo);

    @Query("Select A from Appointment A where A.cologne = ?1")
    List<Appointment> findByCologne(PetCologne petCologne);

    @Query("Select A from Appointment A where A.service = ?1")
    List<Appointment> findByService(Service service);

    @EntityGraph(attributePaths = {"attendant"}, type = EntityGraph.EntityGraphType.FETCH)
    @Query(
            value = "Select A from Appointment A where A.id = ?1"
    )
    AppointmentDetailsProjection getAppointmentDetails(Integer appointmentId);

    @Query(
            value = "Select A.addOnService from Appointment A where A.id = ?1"
    )
    List<AddonService> getAppointmentAddOnservices(@Param("appointmentId") Integer appointmentId);

    @Query(
            value = "Select A.desiredHairLengths from Appointment A where A.id = ?1"
    )
    List<AppointmentDesiredHairLengths> getAppointmentDesiredHairLength(@Param("appointmentId") Integer appointmentId);

    @Query(
            value = "Select A.allergies from Appointment A where A.id = ?1"
    )
    List<Allergies> getAppointmentAllergies(@Param("appointmentId") Integer appointmentId);

    @Query(
            value = "Select A.vaccinationInfo from Appointment A where A.id = ?1"
    )
    List<AppointmentVaccinationInformation> getAppointmentVaccination(@Param("appointmentId") Integer appointmentId);

    @Query(
            value = "Select A.otherDoc from Appointment A where A.id = ?1"
    )
    List<AppointmentDocuments> getAppointmentDocuments(@Param("appointmentId") Integer appointmentId);

    @Query(
            value = "Select A from Appointment A where A.date=?1 and A.retailer = ?2 and A.serviceStatus <> ?3 order by A.createdAt desc"
    )
    Page<AppointmentListingProjection> getAppointmentsByDate(LocalDate date, String retailer, ServiceStatus serviceStatus, Pageable paging);

    @Query(
            value = "Select A from Appointment A where A.date=?1 and A.retailer = ?2 order by A.createdAt desc"
    )
    Page<AppointmentListingProjection> getAppointmentsByDate(LocalDate date, String retailer, Pageable paging);

   /* @Query(
            value = "SELECT A FROM Appointment A WHERE A.appointmentStartDateAndTime BETWEEN :startDateTime AND :endDateTime " +
                    "AND (:search IS NULL OR A.appoinmentNo = :search) AND A.retailer = :retailer " +
                    "AND (:serviceStatus IS NULL OR A.serviceStatus <> :serviceStatus) ORDER BY A.createdAt DESC"
    )
    Page<AppointmentListingProjection> getAppointmentsByDateTimeRange(String startDateTime, String endDateTime, String search, String retailer, ServiceStatus serviceStatus, Pageable paging);
*/
    @Query(
            value = "SELECT A FROM Appointment A WHERE A.appointmentStartDateAndTime >= :startDateTime  and A.appointmentStartDateAndTime <:endDateTime " +
                    "AND (:search IS NULL OR A.appoinmentNo = :search) AND A.retailer = :retailer " +
                    "AND (:serviceStatus IS NULL OR A.serviceStatus  NOT IN (5, 4,6)) ORDER BY A.createdAt DESC"
    )
    Page<AppointmentListingProjection> getAppointmentsByDateTimeRange(String startDateTime, String endDateTime, String search, String retailer, ServiceStatus serviceStatus, Pageable paging);

    @Query(
            value =
                    "Select A from Appointment A where A.date=?1 and A.appoinmentNo like '%'||?2||'%' and A.retailer = ?3 " +
                            "order by A.createdAt desc"
    )
    Page<AppointmentListingProjection> getAppointmentsByDate(LocalDate date, String search, String retailer, Pageable paging);

    @Query(
            value =
                    "Select A from Appointment A where " +
                            "(:customer is null or A.customerId = :customer) " +
                            "and (A.appointmentStartDateAndTime >= :start and A.appointmentStartDateAndTime <= :end) " +
                            "and (:pet is null or A.pet.id = :pet) " +
                            "and (:attendant is null or A.attendant.id = :attendant) " +
                            "and (:venue is null or A.venue.venueId = :venue) " +
                            "and (:serviceStatus IS NULL OR A.serviceStatus NOT IN (5,4,6)) and A.retailer = :retailer order by A.createdAt desc"
    )
    List<AppointmentListingProjection> getAllAppointments(
            @Param("customer") Integer customer, @Param("start") String start, @Param("end") String end,
            @Param("pet") Integer pet, @Param("attendant") Integer attendant, @Param("venue") Integer venue,
            @Param("serviceStatus") ServiceStatus serviceStatus,
            @Param("retailer") String retailer
    );

    @Query(
            value =
                    "Select A from Appointment A where (:customer is null or A.customerId = :customer) " +
                            "and (:petType is null or A.pet.petType.petTypeId = :petType) " +
                            "and A.retailer = :retailer order by A.createdAt desc"
    )
    Page<AppointmentListingProjection> getAllAppointmentsWithoutDate(@Param("customer") Integer customer, @Param("petType") Integer petType, @Param("retailer") String retailer, Pageable paging);

    @Query(
            value =
                    "Select A from Appointment A where A.customerId = :customer " +
                            "and (:petType is null or A.pet.petType.petTypeId = :petType) " +
                            "and (:search is null or A.appoinmentNo like %:search% or A.pet.name like %:search% )" +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (A.date >= :start and A.date <= :end) " +
                            "and (:service is null or A.service.name like %:service%)"+
                            "and A.retailer = :retailer order by A.createdAt desc"
    )
    Page<AppointmentHistoryListingProjection> getAllAppointmentByCustomerSearch(
            @Param("customer") Integer customer, @Param("petType") Integer petType, @Param("search") String search,
            @Param("status") ServiceStatus status, @Param("start") LocalDate start, @Param("end") LocalDate end,
            @Param("service")String service,@Param("retailer") String retailer, Pageable paging);

    @Query(
            value =
                    "Select A from Appointment A where A.customerId = :customer " +
                            "and (:petType is null or A.pet.petType.petTypeId = :petType) " +
                            "and (:search is null or A.appoinmentNo like %:search% or A.pet.name like %:search% ) " +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (:service is null or A.service.name like %:service%)"+
                            "and A.retailer = :retailer order by A.createdAt desc"
    )
    Page<AppointmentHistoryListingProjection> getAllAppointmentByCustomerSearch(
            @Param("customer") Integer customer, @Param("petType") Integer petType, @Param("search") String search,
            @Param("status") ServiceStatus status,@Param("service")String service,
            @Param("retailer") String retailer, Pageable paging);

    @Query(
            value =
                    "Select A from Appointment A where A.customerId = :customer " +
                            "and (:petType is null or A.pet.petType.petTypeId = :petType) " +
                            "and (:search is null or A.appoinmentNo like %:search% or A.pet.name like %:search% )" +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (A.date <= :end) " +
                            "and (:service is null or A.service.name like %:service%)"+
                            "and A.retailer = :retailer order by A.createdAt desc"
    )
    Page<AppointmentHistoryListingProjection> getAllAppointmentByCustomerSearchBefore(
            @Param("customer") Integer customer, @Param("petType") Integer petType, @Param("search") String search,
            @Param("status") ServiceStatus status, @Param("end") LocalDate end,
            @Param("service") String service,@Param("retailer") String retailer, Pageable paging);

    @Query(
            value =
                    "Select A from Appointment A where A.customerId = :customer " +
                            "and (:petType is null or A.pet.petType.petTypeId = :petType) " +
                            "and (:search is null or A.appoinmentNo like %:search% or A.pet.name like %:search% ) " +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (:service is null or A.service.name like %:service%)"+
                            "and (A.date >= :start) " +
                            "and A.retailer = :retailer order by A.createdAt desc"
    )
    Page<AppointmentHistoryListingProjection> getAllAppointmentByCustomerSearchAfter(
            @Param("customer") Integer customer, @Param("petType") Integer petType, @Param("search") String search,
            @Param("status") ServiceStatus status, @Param("start") LocalDate start,
            @Param("service") String serviceName,@Param("retailer") String retailer, Pageable paging);


    @EntityGraph(attributePaths = {"attendant"}, type = EntityGraph.EntityGraphType.FETCH)
    @Query(
            value =
                    "Select A from Appointment A where A.retailer = :retailer " +
                            "and (:customer is null or A.customerId = :customer) " +
                            "and (:pet is null or A.pet.id = :pet) " +
                            "and (:attendant is null or A.attendant.attendantId = :attendant) " +
                            "and (:venue is null or A.venue.venueId = :venue) " +
                            "and (:search is null or A.customerName like %:search% or A.pet.name like %:search%  or A.serviceType.name like %:search%  or A.venue.internalName like %:search%   or A.venue.publicName like %:search% )" +
                            "and (:service is null or A.service.serviceId = :service) "+
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (:petType is null or A.petType.petTypeId = :petType) " +
                            "order by A.createdAt desc"
    )
    Page<AppointmentsListing> getAllAppointments(
            @Param("customer") Integer customer, @Param("pet") Integer pet, @Param("attendant") Integer attendant,
            @Param("venue") Integer venue, @Param("search") String search,@Param("service") Integer service,
            @Param("status") ServiceStatus status, @Param("petType") Integer petType,
            @Param("retailer") String retailer, Pageable paging
    );

    @EntityGraph(attributePaths = {"attendant"}, type = EntityGraph.EntityGraphType.FETCH)
    @Query(
            value =
                    "Select A from Appointment A where A.retailer = :retailer " +
                            "and (:customer is null or A.customerId = :customer) " +
                            "and (:pet is null or A.pet.id = :pet) " +
                            "and (:attendant is null or A.attendant.attendantId = :attendant) " +
                            "and (:venue is null or A.venue.venueId = :venue) " +
                            "and (:search is null or A.customerName like %:search% or A.pet.name like %:search%  or A.serviceType.name like %:search%  or A.venue.internalName like %:search%   or A.venue.publicName like %:search% )" +
                            "and (:service is null or A.service.serviceId = :service)"+
                            "and (A.appointmentStartDateAndTime >= :start and A.appointmentStartDateAndTime <= :end) " +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (:petType is null or A.petType.petTypeId = :petType) " +
                            "order by A.createdAt desc"
    )
    Page<AppointmentsListing> getAllAppointments(
            @Param("customer") Integer customer, @Param("pet") Integer pet, @Param("attendant") Integer attendant,
            @Param("venue") Integer venue, @Param("search") String search,
            @Param("service") Integer service,
            @Param("start") String start, @Param("end") String end,
            @Param("status") ServiceStatus status, @Param("petType") Integer petType,
            @Param("retailer") String retailer, Pageable paging
    );

    @Query(
            value =
                    "Select A from Appointment A where A.retailer = :retailer " +
                            "and (:customer is null or A.customerId = :customer) " +
                            "and (:pet is null or A.pet.id = :pet) " +
                            "and (:attendant is null or A.attendant.attendantId = :attendant) " +
                            "and (:venue is null or A.venue.venueId = :venue) " +
                            "and (:search is null or A.customerName like %:search% or A.pet.name like %:search%  or A.serviceType.name like %:search%  or A.venue.internalName like %:search%   or A.venue.publicName like %:search% )" +
                            "and (:service is null or A.service.serviceId = :service)"+
                            "and (A.date <= :end) " +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (:petType is null or A.petType.petTypeId = :petType) " +
                            "order by A.date desc"
    )
    Page<AppointmentsListing> getAllAppointmentsBefore(
            @Param("customer") Integer customer, @Param("pet") Integer pet, @Param("attendant") Integer attendant,
            @Param("venue") Integer venue, @Param("search") String search,@Param("service") Integer service,
            @Param("end") LocalDate end, @Param("status") ServiceStatus status, @Param("petType") Integer petType,
            @Param("retailer") String retailer, Pageable paging
    );

    @Query(
            value =
                    "Select A from Appointment A where A.retailer = :retailer " +
                            "and (:customer is null or A.customerId = :customer) " +
                            "and (:pet is null or A.pet.id = :pet) " +
                            "and (:attendant is null or A.attendant.attendantId = :attendant) " +
                            "and (:venue is null or A.venue.venueId = :venue) " +
                            "and (:search is null or A.customerName like %:search% or A.pet.name like %:search%  or A.serviceType.name like %:search%  or A.venue.internalName like %:search%   or A.venue.publicName like %:search% )" +
                            "and (:service is null or A.service.serviceId = :service)"+
                            "and (A.date >= :start) " +
                            "and (:status is null or A.serviceStatus = :status) " +
                            "and (:petType is null or A.petType.petTypeId = :petType) " +
                            "order by A.date desc"
    )
    Page<AppointmentsListing> getAllAppointmentsAfter(
            @Param("customer") Integer customer, @Param("pet") Integer pet, @Param("attendant") Integer attendant,
            @Param("venue") Integer venue, @Param("search") String search,@Param("service") Integer service,
            @Param("start") LocalDate start, @Param("status") ServiceStatus status, @Param("petType") Integer petType,
            @Param("retailer") String retailer, Pageable paging
    );

    @Query("Select A from Appointment A where A.customerId = ?1 and A.paymentStatus = ?2 and A.retailer = ?3 order by A.date desc")
    List<AppointmentPaymentProjection> getUnpaidAppointmentsForCustomer(Integer customer, String paymentStatus, String retailer);


    @Modifying
    @Query(value = "update appointment set appointment_date =?1,appointment_time = ?2 where id = ?3", nativeQuery = true)
    int updateDateAndTime(LocalDate date, LocalTime time, Integer appointmentId);

    @Modifying
    @Query(value = "update appointment set order_reference = ?1,payment_status = ?2 where id = ?3", nativeQuery = true)
    int updateOrderDetails(Long orderReference, String paymentStatus, Integer appointmentId);

    @Query(value = "SELECT MAX(A.id) as appointmentId FROM Appointment A where A.retailer = ?1")
    Integer getLastAppointment(String retailer);

    @Query("SELECT a FROM Appointment a WHERE a.venue = ?1 and a.date BETWEEN ?2 AND ?3")
    List<Appointment> getAppointmentsByVenueAndDate(Venue venue, LocalDate startDate, LocalDate endDate);

    List<Appointment> findByCustomerId(Integer customer);

    List<Appointment> findByDate(LocalDate tommorrow);

    @Query("Select a from Appointment a where a.pet = ?1 and a.date = ?2 and a.time = ?3 and a.serviceStatus <> ?4")
    List<Appointment> getAppointmentsByDateTimeAndPetAndServiceStatus(Pet pet, LocalDate date, OffsetTime time, ServiceStatus serviceStatus);

    @Query("Select A from Appointment A where A.customerName is null")
    List<Appointment> findAppointmentsWithoutName();

    @Transactional
    @Modifying
    @Query(value="Delete from appointment_vaccination_info A where A.vaccination_info_id = ?1", nativeQuery=true)
    void deleteByVaccinationInfo(Integer AppointmentvaccinationRecordsId);

    @Modifying
    @Query("update Appointment A set A.temperament.temperamentId = null where A.temperament.temperamentId = ?1")
    void updateTemparament(Integer temperamentId);

    @Modifying
    @Query("update Appointment A set A.hairLength.hairLengthId = null where A.hairLength.hairLengthId = ?1")
    void updateByHairLength(Integer hairLengthId);

    @Modifying
    @Query("update Appointment A set A.hairTexture.hairTextureId = null where A.hairTexture.hairTextureId = ?1")
    void updateByHairTexture(Integer hairTextureId);

   @Query("Select a from Appointment a where a.id=:id and a.orderReference=:orderReference")
    Optional<Appointment> findByIdAndOrderReference(Integer id, Long orderReference);

    @Query(value = "Select id from Appointment a where a.appointment_date=?1 and A.retailer=?2",nativeQuery = true)
    List<Integer> getAppointmentsIdByDate(LocalDate date, String retailer);

    // OPTIMIZATION: Single query for multiple dates
    @Query(value = "Select id from Appointment a where a.appointment_date IN :dates and a.retailer = :retailer", nativeQuery = true)
    List<Integer> getAppointmentsIdByDateRange(@Param("dates") Set<LocalDate> dates, @Param("retailer") String retailer);

    @Query(value = "SELECT a FROM Appointment a where a.customerId = ?1")
    List<AppointmentUploadedDocumentsProjection> findAllDocumentsByCustomerId(Integer customerId, Pageable paging);

    @Query(value = "Select a from Appointment a where a.retailer=?1")
    List<Appointment> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment T where T.hairLength.hairLengthId=?1 and T.retailer=?2")
    void deleteByHairLengthId(Integer hairLengthId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment T where T.hairTexture.hairTextureId=?1 and T.retailer=?2")
    void deleteByHaiTextureId(Integer hairTextureId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment E where E.petType.petTypeId=?1 and E.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment E where E.temperament.temperamentId=?1 and E.retailer=?2")
    void deleteByTemperamentId(Integer temperamentId, String retailer);

    @Transactional
    @Modifying
    @Query(value = "Delete from appointment E where E.weight_range_id=:weightRangeId and E.retailer=:retailer",nativeQuery = true)
    void deleteByWeightRangeId(Integer weightRangeId,String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment E where E.cologne.id=?1 and E.retailer=?2")
    void deleteByPetCologneId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment E where E.shamppo.id=?1 and E.retailer=?2")
    void deleteByPetShampooId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment E where E.service.serviceId=?1 and E.retailer=?2")
    void deleteByServiceId(Integer serviceId, String retailer);

    @Query("Select A from Appointment A where A.pet.id=?1 and A.retailer=?2")
    List<Appointment> findByPetId(Integer petId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Appointment E where E.pet.id=?1 and E.retailer=?2")
    void deleteByPetId(Integer id, String retailer);

    @Query(value = "select  a.appoinment_no as appointment   from public.appointment a where a.customer_id =:sourceCustomerId and retailer =:retailer",nativeQuery = true)
    List<String> getAppointmentName(Integer sourceCustomerId, String retailer);

    @Query(value = "select * from appointment a where a.customer_id =:sourceCustomerId and a.retailer=:retailer order by id asc",nativeQuery = true)
    List<Appointment> findByCustomerIdAndRetailer(Integer sourceCustomerId, String retailer);

    @Query(value = "select * from appointment a where a.service_status = '0' or a.service_status ='2' and a.retailer=:retailer",nativeQuery = true)
    List<Appointment> findAppointmentsWithServiceStatus(String retailer);

    @Query("Select A from Appointment A where A.appointmentStartDateAndTime=?1 And A.serviceStatus <> ?2 And A.attendant.attendantId=?3 And A.retailer=?4")
    List<Appointment> findByDateTimeServiceStatusAttendantAndRetailer(String appointmentTime, ServiceStatus serviceStatus,Integer attendant,String retailer);

    @Query("Select A from Appointment A where A.appointmentStartDateAndTime=?1 And A.serviceStatus <> ?2 And A.venue.venueId=?3 And A.retailer=?4")
    List<Appointment> findByDateTimeServiceStatusVenueAndRetailer(String appointmentTime, ServiceStatus serviceStatus, Integer venue, String retailer);

    @Query("Select A from Appointment A where A.date=?1 and A.serviceStatus<> ?2")
    List<Appointment> findByDateAndServiceStatus(LocalDate tomorrow, ServiceStatus serviceStatus);

    @Query(value = "SELECT * FROM appointment A WHERE DATE(CAST(A.start_date_and_time AS timestamp) "+
          "+ (CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute')) = :today AND  A.service_status NOT IN (5, 4, 6) AND A.retailer = :retailer", nativeQuery = true)
    Page<Appointment> findByDateServiceStatusAndRetailer(
            @Param("today") LocalDate today,
          //  @Param("serviceStatus") ServiceStatus serviceStatus,
            @Param("retailer") String retailer,
            @Param("offsetDifference") int offsetDifference,
            Pageable pageable
    );


    @Query("Select A from Appointment A where A.serviceStatus='4' and A.retailer= ?1")
    Page<CancelledAppointmentProjections> findCancelledAppointments(String retailer,Pageable paging);

    @Query(value = "select * from appointment a where a.appointment_date >:appointmentDate and a.is_manual_slots =true  and a.retailer=:retailer order by a.id asc",nativeQuery = true)
    List<Appointment> findAppointmentsAfterDateAndRetailer(LocalDate appointmentDate,String retailer);

   // @EntityGraph(attributePaths = {"attendant"}, type = EntityGraph.EntityGraphType.FETCH)
   @Query(
           value = "SELECT subquery.id AS appointmentId, " +
                   "       DATE(CAST(subquery.start_date_and_time AS timestamp) + (CAST(:offsetDifference AS INTEGER) * interval '1 minute')) AS date, " +
                   "       subquery.start_date_and_time AS startTime, " +
                   "       subquery.end_date_and_time AS endTime, " +
                   "       s.color AS serviceColor, " +
                   "       s.name AS service, " +
                   "       (subquery.total_count - 5) AS remainingAppointments " +
                   "FROM ( " +
                   "    SELECT a.id, " +
                   "           a.appointment_date, " +
                   "           a.start_date_and_time, " +
                   "           a.end_date_and_time, " +
                   "           a.service, " +
                   "           COUNT(*) OVER (PARTITION BY DATE(CAST(a.start_date_and_time AS timestamp)+ (CAST(:offsetDifference AS INTEGER) * interval '1 minute'))) AS total_count, " +
                   "           ROW_NUMBER() OVER (PARTITION BY DATE(CAST(a.start_date_and_time AS timestamp)+ (CAST(:offsetDifference AS INTEGER) * interval '1 minute')) " +
                   "                              ORDER BY CAST(a.start_date_and_time AS timestamp)) AS row_num " +
                   "    FROM appointment a " +
                   "    WHERE CAST(a.start_date_and_time AS timestamp) + (CAST(:offsetDifference AS INTEGER) * interval '1 minute') " +
                   "          BETWEEN :startDate AND :endDate " +
                   "      AND (:serviceStatus IS NULL OR a.service_status NOT IN (5, 4, 6)) " +
                   "      AND (a.retailer = :retailer) " +
                   ") subquery " +
                   "JOIN service s ON subquery.service = s.id " +
                   "WHERE subquery.row_num <= 5 " +
                   "ORDER BY CAST(subquery.start_date_and_time AS timestamp)",
           nativeQuery = true
   )
   List<AppointmentMonthViewProjection> getAppointmentsOfMonthView(
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("serviceStatus") ServiceStatus serviceStatus,
           @Param("retailer") String retailer,
            @Param("offsetDifference") Integer offsetDifference
    );

   @Query("Select A from Appointment A where A.pet.id=?1 and A.waiverAcknowledged=?2")
    List<Appointment> findByPetIdAndWaiverStatus(Integer petId, Boolean aFalse);

    @Query("SELECT a FROM Appointment a WHERE a.appointmentStartDateAndTime >= :currentDate AND a.retailer = :retailer AND a.serviceStatus IN (:statuses) AND a.timeSlotId!=null")
    Page<AppointmentDetailsProjection> findByStartDateStatusRetailer(
           @Param("currentDate") String currentDate,
           @Param("retailer") String retailer,
           @Param("statuses") List<ServiceStatus> statuses,
           Pageable pageable);

    @EntityGraph(attributePaths = {"attendant"}, type = EntityGraph.EntityGraphType.FETCH)
    @Query(
            value = "Select A from Appointment A where A.id IN :appointmentIds"
    )
    List<AppointmentDetailsProjection> getAppointmentDetailsByIds(@Param("appointmentIds") List<Integer> appointmentIds);

    List<Appointment> findByServiceType(ServiceType serviceType);

    @Query("SELECT a FROM Appointment a WHERE a.attendant.id = :attendantId AND a.retailer = :retailer ORDER BY a.id ASC")
    List<Appointment> findByAttendantAndRetailer(@Param("attendantId") Integer attendantId, @Param("retailer") String retailer);
    
    // Find appointments by date and retailer (excluding cancelled appointments) - for block validation
    @Query("SELECT a FROM Appointment a WHERE a.date = :date AND a.retailer = :retailer AND a.serviceStatus NOT IN (4, 5, 6)")
    List<Appointment> findByDateAndRetailerExcludingCancelled(@Param("date") LocalDate date, @Param("retailer") String retailer);
    
    // Find appointments by date, attendant and retailer (excluding cancelled appointments) - for block validation
    @Query("SELECT a FROM Appointment a WHERE a.date = :date AND a.attendant.attendantId = :attendantId AND a.retailer = :retailer AND a.serviceStatus NOT IN (4, 5, 6)")
    List<Appointment> findByDateAttendantAndRetailerExcludingCancelled(@Param("date") LocalDate date, @Param("attendantId") Integer attendantId, @Param("retailer") String retailer);
}