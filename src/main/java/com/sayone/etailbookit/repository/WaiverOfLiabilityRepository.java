package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.WaiverOfLiability;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface WaiverOfLiabilityRepository extends JpaRepository<WaiverOfLiability, Integer>, JpaSpecificationExecutor<WaiverOfLiability> {
    WaiverOfLiability findByRetailer(String retailer);
}
