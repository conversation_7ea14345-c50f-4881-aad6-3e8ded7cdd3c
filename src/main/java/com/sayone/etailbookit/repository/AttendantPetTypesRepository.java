package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.model.AttendantPetTypes;
import com.sayone.etailbookit.projections.AttendantPetTypeProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface AttendantPetTypesRepository extends JpaRepository<AttendantPetTypes, Integer>, JpaSpecificationExecutor<AttendantPetTypes> {

    @Query(
            value = "SELECT pet_type_id as petTypeId, id as attendantPetTypeId FROM public.attendant_pet_types WHERE attendant_id = ?1",
            nativeQuery = true
    )
    List<AttendantPetTypeProjection> getPetTypes(Integer id);

    void deleteByAttendant(Attendant attendant);

    @Query("Select A from AttendantPetTypes A where A.petType.petTypeId = ?1")
    List<AttendantPetTypes> findByPetType(Integer id);

    @Query("Select A from AttendantPetTypes A where A.generalPetSizes is empty")
    List<AttendantPetTypes> getAttendantPetTypesWithoutSize();

    @Transactional
    @Modifying
    @Query(value = "Delete from attendant_pet_types_temperaments A where A.temperaments_id = ?1", nativeQuery = true)
    void deleteByTemparament(Integer temperamentId);

    @Transactional
    @Modifying
    @Query("Delete from AttendantPetTypes A where A.attendant.attendantId=?1 and retailer=?2")
    void deleteByAttendantId(Integer attendantId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from AttendantPetTypes T where T.petType.petTypeId=?1 and T.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    List<AttendantPetTypes> findByRetailer(String retailer);
}