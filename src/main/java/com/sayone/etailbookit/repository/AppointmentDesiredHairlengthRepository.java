package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.AppointmentDesiredHairLengths;
import com.sayone.etailbookit.model.AppointmentVaccinationInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface AppointmentDesiredHairlengthRepository extends JpaRepository<AppointmentDesiredHairLengths, Integer>, JpaSpecificationExecutor<AppointmentDesiredHairLengths> {
    @Transactional
    @Modifying
    @Query("Delete from AppointmentDesiredHairLengths A where A.desiredHairLength.desiredHairLengthId = ?1")
    void deleteByDesiredHairLength(Integer desiredHairlengthId);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentDesiredHairLengths A where A.appointment.id=?1 and retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentDesiredHairLengths A where A.desiredHairLength.desiredHairLengthId = ?1 and A.retailer=?2")
    void deleteByDesiredHairLengthId(Integer id, String retailer);

    @Query("Select A from AppointmentDesiredHairLengths A where A.retailer=?1")
    List<AppointmentDesiredHairLengths> findByRetailer(String retailer);
}
