package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.BlockDateInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

public interface BlockDatesRepository extends JpaRepository <BlockDateInfo, Integer>{

    List<BlockDateInfo> findByRetailer(String retailer);

    List<BlockDateInfo> findByRetailerAndBlockDateBetween(String retailer, LocalDate startDate, LocalDate endDate);

    List<BlockDateInfo> findByBlockDateAndRetailer(LocalDate date, String retailer);

    List<BlockDateInfo> findByOffsetBlockDateAndRetailer(String date, String retailer);

   @Query( value = "Select A from BlockDateInfo A where " +
             "(A.offsetBlockDate >= :startDate and A.offsetBlockDate <= :endDate) " +
            "and A.retailer = :retailer order by A.createdAt desc")
    List<BlockDateInfo> findByRetailerAndOffsetBlockDateBetween(String retailer, String startDate, String endDate);

    @Query(value = "SELECT * FROM blockdates WHERE DATE(offset_block_date) = DATE(:startDate) AND retailer = :retailer", nativeQuery = true)
    List<BlockDateInfo> findByRetailerAndOffsetBlockDate(String startDate, String retailer);

    @Query(value = "Select b from BlockDateInfo b  where b.retailer =:retailer and(:slotStartTime > b.blockStartTime and :slotStartTime< b.blockEndTime) or(:slotEndTime > b.blockStartTime and :slotEndTime < b.blockEndTime)")
    List<BlockDateInfo> findBlockTimesBetweenSlotTime(String retailer, OffsetDateTime slotStartTime, OffsetDateTime slotEndTime);

    @Query("SELECT b FROM BlockDateInfo b " +
            "WHERE b.deleted = FALSE AND " +
            "b.blockStartTime >= :startTime AND " +
            "b.blockStartTime < :endTime AND b.retailer=:retailer")
    List<BlockDateInfo> findBlocksBetween(OffsetDateTime startTime, OffsetDateTime endTime,String retailer);

    // Find blocks by offsetBlockDate, retailer and block type
    @Query("SELECT b FROM BlockDateInfo b WHERE b.offsetBlockDate = :offsetBlockDate AND b.retailer = :retailer AND b.blockType = :blockType")
    List<BlockDateInfo> findByOffsetBlockDateAndRetailerAndBlockType(String offsetBlockDate, String retailer, BlockDateInfo.BlockedTimeType blockType);


    // Find blocks by offsetBlockDate, retailer, attendant and block type
    @Query("SELECT b FROM BlockDateInfo b WHERE b.offsetBlockDate = :offsetBlockDate AND b.retailer = :retailer AND b.attendant.attendantId = :attendantId AND b.blockType = :blockType")
    List<BlockDateInfo> findByOffsetBlockDateAndRetailerAndAttendantAndBlockType(String offsetBlockDate, String retailer, Integer attendantId, BlockDateInfo.BlockedTimeType blockType);

    // Find blocks by blockDate (LocalDate), retailer and block type - TIMEZONE-SAFE
    @Query("SELECT b FROM BlockDateInfo b WHERE b.blockDate = :blockDate AND b.retailer = :retailer AND b.blockType = :blockType")
    List<BlockDateInfo> findByBlockDateAndRetailerAndBlockType(LocalDate blockDate, String retailer, BlockDateInfo.BlockedTimeType blockType);

    // Find blocks by blockDate (LocalDate), retailer, attendant and block type - TIMEZONE-SAFE
    @Query("SELECT b FROM BlockDateInfo b WHERE b.blockDate = :blockDate AND b.retailer = :retailer AND b.attendant.attendantId = :attendantId AND b.blockType = :blockType")
    List<BlockDateInfo> findByBlockDateAndRetailerAndAttendantAndBlockType(LocalDate blockDate, String retailer, Integer attendantId, BlockDateInfo.BlockedTimeType blockType);

}
