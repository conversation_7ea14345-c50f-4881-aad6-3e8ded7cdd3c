package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.AppointmentDocuments;
import com.sayone.etailbookit.model.AppointmentVetInformation;
import com.sayone.etailbookit.model.ServiceHistoryAddNotes;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface AppointmentVetInfoRepository extends JpaRepository<AppointmentVetInformation, Integer>, JpaSpecificationExecutor<AppointmentVetInformation> {
    @Transactional
    @Modifying
    @Query("Delete from AppointmentVetInformation A where A.vetInformation.id = ?1")
    void deleteByVetInformation(Integer vetInformationId);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentVetInformation A where A.appointment.id=?1 and A.retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Query(value="Select * from appointment_vet_information W where  W.retailer=:retailer",nativeQuery = true)
    List<AppointmentVetInformation> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentVetInformation A where A.vetInformation.id=?1 and A.retailer=?2")
    void deleteByVetInformationId(Integer id, String retailer);

    @Modifying
    @Query("Delete from AppointmentVetInformation S where S.id=?1")
    void deleteById(Integer id);
}
