package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.model.Temperament;
import com.sayone.etailbookit.projections.TemperamentProjection;;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

public interface TemperamentRepository extends JpaRepository<Temperament, Integer>, JpaSpecificationExecutor<Temperament> {

    Temperament findByTemperamentId(Integer temperamentId);

    void deleteByPetType(PetType petType);

    List<Temperament> findByPetTypePetTypeId(Integer id);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM Temperament A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Query("Select T from Temperament T where T.retailer=?1")
    List<Temperament> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Temperament T where T.petType.petTypeId=?1 and T.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);
}