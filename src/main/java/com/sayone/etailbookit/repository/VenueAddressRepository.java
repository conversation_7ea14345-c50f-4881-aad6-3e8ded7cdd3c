package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.VenueAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface VenueAddressRepository extends JpaRepository<VenueAddress, Integer>, JpaSpecificationExecutor<VenueAddress> {

    @Query("Select V from VenueAddress V where V.retailer=?1")
    List<VenueAddress> findByRetailer(String retailer);
}