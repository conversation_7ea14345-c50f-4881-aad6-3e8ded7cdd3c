package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.GeneralPetSize;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.projections.GeneralPetSizeProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface GeneralPetSizeRepository extends JpaRepository<GeneralPetSize, Integer>, JpaSpecificationExecutor<GeneralPetSize> {

    GeneralPetSize findByGeneralPetSizeId(Integer generalPetSizeId);

    GeneralPetSize findBySizeAndRetailer(String size, String retailer);

    Set<GeneralPetSize> findByPetType(PetType petType);

    @Transactional
    @Modifying
    @Query(value = "Update pet set size_id = null where size_id = ?1", nativeQuery = true)
    void deleteGeneralPetSizeFromPet(Integer id);

    @Transactional
    @Modifying
    @Query(value = "Delete from attendant_pet_types_general_pet_sizes where general_pet_sizes_id = ?1", nativeQuery = true)
    void deleteGeneralPetSizeFromAttendant(Integer id);

    @Transactional
    @Modifying
    @Query(value = "Delete from venue_pet_types_general_pet_sizes where general_pet_sizes_id = ?1", nativeQuery = true)
    void deleteGeneralPetSizeFromVenue(Integer id);

    @Transactional
    @Modifying
    @Query(value = "Delete from service_general_pet_size where general_pet_size_id = ?1", nativeQuery = true)
    void deleteGeneralPetSizeFromService(Integer id);

    @Transactional
    @Modifying
    @Query("Update  GeneralPetSize T set T.deleted =?3  where T.petType.petTypeId=?1 and T.retailer=?2")
    void updateDeletedByPetTypeId(Integer petTypeId, String retailer,boolean deleted);

    List<GeneralPetSize> findByRetailer(String retailer);

    List<GeneralPetSize> findAllByPetTypePetTypeIdAndRetailer(Integer petTypeId, String retailer);

    void deleteAllBySizeAndPetTypePetTypeIdAndRetailer(String size, Integer petTypeId, String retailer);

    @Transactional
    @Modifying
    @Query(value = "Delete from pet_size_constraint where general_pet_size_id = ?1", nativeQuery = true)
    void deleteGeneralPetSizeFromPetSizeConstraint(Integer generalPetSizeId);

    @Modifying
    @Transactional
    @Query("Update GeneralPetSize G set G.deleted=?2 where G.generalPetSizeId=?1")
    void updateDeleted(Integer generalPetSizeId, boolean b);

    @Query("Select G from GeneralPetSize G where G.petType=?1 and G.retailer=?2")
    List<GeneralPetSize> findAllByPetTypeAndRetailer(PetType petTypeId, String retailer);

    @Transactional
    @Modifying
    @Query(value = "Delete from pet_size_limit where general_pet_size_id = ?1", nativeQuery = true)
    void deleteGeneralPetSizeFromPetSizeLimit(Integer generalPetSizeId);
}