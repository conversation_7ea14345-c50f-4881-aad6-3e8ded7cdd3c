package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.HairLength;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;


public interface HairLengthRepository extends JpaRepository<HairLength, Integer>, JpaSpecificationExecutor<HairLength> {
    void deleteByPetType(PetType petType);

    List<HairLength> findByPetTypePetTypeId(Integer id);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM HairLength A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from HairLength T where T.petType.petTypeId=?1 and T.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    List<HairLength> findByRetailer(String retailer);
    @Transactional
    @Modifying
    @Query("Update HairLength set deleted=:deleted where hairLengthId=:hairLengthId and retailer=:retailer")
    void deleteHairLength(Integer hairLengthId, String retailer, boolean deleted);

    @Transactional
    @Modifying
    @Query("Update HairLength set deleted=:deleted where petType.petTypeId=:petTypeId and retailer=:retailer")
    void deleteByHairLengthByPetTypeId(Integer petTypeId, String retailer,boolean deleted);

}