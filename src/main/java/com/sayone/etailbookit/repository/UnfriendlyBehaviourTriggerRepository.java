package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.UnfriendlyBehaviourTrigger;
import com.sayone.etailbookit.projections.UnfriendlyBehaviourProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UnfriendlyBehaviourTriggerRepository extends JpaRepository<UnfriendlyBehaviourTrigger, Integer>, JpaSpecificationExecutor<UnfriendlyBehaviourTrigger> {

    @Query(value = "select ub.name as unfriendlyBehaviour " +
            "FROM pet_unfriendly_behaviour_triggers pub " +
            "LEFT OUTER JOIN unfriendly_behaviour_trigger ub on pub.unfriendly_behaviour_triggers_id = ub.id " +
            "where pub.pet_id=?1 ", nativeQuery = true)
    List<UnfriendlyBehaviourProjection> getUnfriendlyBehaviour(Integer petId);

    List<UnfriendlyBehaviourTrigger> findByRetailer(String retailer);
}