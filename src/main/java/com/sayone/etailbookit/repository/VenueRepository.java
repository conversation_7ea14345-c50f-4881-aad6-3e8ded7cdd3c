package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Venue;
import com.sayone.etailbookit.projections.VenueDetailProjection;
import com.sayone.etailbookit.projections.VenueDropdownProjection;
import com.sayone.etailbookit.projections.VenueListingProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

public interface VenueRepository extends JpaRepository<Venue, Integer>, PagingAndSortingRepository<Venue, Integer>, JpaSpecificationExecutor<Venue> {

    @Query("Select V from Venue V where V.retailer = ?1")
    List<Venue> findByRetailer(String retailer);

    Venue findByVenueId(Integer venueId);

    @Query("Select V from Venue V where V.id = ?1")
    VenueDetailProjection getVenueDetails(Integer id);

    @Query(
            "Select V from Venue V where V.retailer = :retailer and V.deleted = :deleted and " +
            "(:search is null or lower(V.internalName) like %:search% " +
            "or lower(V.publicName) like %:search%)"
    )
    Page<VenueListingProjection> findByKeyword(
            @Param("search") String search, @Param("retailer") String retailer,@Param("deleted") Boolean deleted, Pageable paging
    );
    @Query(
        "Select V from Venue V where V.retailer = :retailer and V.deleted = :deleted and lower(V.internalName) like %:internalName%"
    )
    List<Venue> findByName(
            @Param("internalName") String internalName, @Param("retailer") String retailer,@Param("deleted") Boolean deleted
    );

    List<VenueDropdownProjection> findByRetailerOrderByVenueIdDesc(String retailer);

    Venue findByInternalName(String groombarVenue);

    @Transactional
    @Modifying
    @Query("Update Venue set deleted=:deleted where venueId=:venueId and retailer=:retailer")
    void deleteVenue(Integer venueId, String retailer,Boolean deleted);
}