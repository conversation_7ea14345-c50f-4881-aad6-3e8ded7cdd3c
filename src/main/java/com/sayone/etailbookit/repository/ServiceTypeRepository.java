package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.ServiceType;
import com.sayone.etailbookit.projections.ServiceTypeProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ServiceTypeRepository extends JpaRepository<ServiceType, Integer>, JpaSpecificationExecutor<ServiceType> {

    ServiceType findByServiceTypeId(Integer serviceTypeId);

    List<ServiceType> findByNameIgnoreCaseAndRetailerAndDeleted(String name, String retailer, Boolean deleted);

    @Query("Select s from ServiceType s WHERE s.active=?1 and s.retailer = ?2 and s.deleted=?3 order by s.name")
    Page<ServiceType> findByActiveAndRetailerAndDeleted(Boolean active, String retailer, Boolean deleted,Pageable paging);

    @Query(
            "SELECT s FROM ServiceType s WHERE s.retailer = ?1 AND s.deleted = ?2 " +
                    "and (?3 is null or lower(s.name) like %?3%)"
    )
    Page<ServiceTypeProjection> getAll(String retailer, Boolean deleted, String search,Pageable pageable);

    @Query("SELECT s FROM ServiceType s WHERE s.retailer = ?1 AND s.services is NOT empty AND s.active = true order by s.name")
    List<ServiceTypeProjection> findAllByRetailer(String retailer);

    @Query("Select S From ServiceType S where S.retailer=?1")
    List<ServiceType> findByRetailer(String retailer);

    ServiceType findByName(String groombarServiceType);
  
    @Query("Select p from ServiceType p where LOWER(p.name) LIKE %?1% AND p.active=?2 AND p.retailer=?3 AND p.deleted=?4")
    Page<ServiceType> findByKeywordActiveRetailerAndDeleted(String search, boolean active, String retailer, boolean deleted, Pageable paging);

    @Query("SELECT st FROM ServiceType st " +
            "JOIN st.services s " +
            "JOIN st.venues v " +
            "JOIN st.attendants a " +
            "WHERE s.id = :serviceId " +
            "AND v.id = :venueId " +
            "AND a.id = :attendantId")
    List<ServiceType> findByServiceVenueAttendant(@Param("serviceId") Integer serviceId,
                                                  @Param("venueId") Integer venueId,
                                                  @Param("attendantId") Integer attendantId);

}