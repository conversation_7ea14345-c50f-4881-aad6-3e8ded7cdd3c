package com.sayone.etailbookit.repository;


import com.sayone.etailbookit.model.PetSizeLimit;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PetSizeLimitRepository extends JpaRepository<PetSizeLimit, Integer>, JpaSpecificationExecutor<PetSizeLimit> {
    void deleteAllByIdNotInAndAttendantAttendantId(List<Integer> ids, Integer attendantId);

    void deleteAllByAttendantAttendantId(Integer attendantId);

    @Query(value = "Select * from pet_size_limit p where p.retailer=?1 ",nativeQuery = true)
    List<PetSizeLimit> findByRetailer(String retailer);

    List<PetSizeLimit> findAllByPetType(PetType petTypeId);
}
