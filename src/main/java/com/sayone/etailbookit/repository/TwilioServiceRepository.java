package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.TwilioService;
import com.sayone.etailbookit.projections.TwilioServiceProjection;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TwilioServiceRepository extends JpaRepository<TwilioService, Integer>, JpaSpecificationExecutor<TwilioService> {
    @Query("select ts from TwilioService ts where ts.retailer = ?1 order by createdAt desc")
    List<TwilioServiceProjection> getLatestConversation(String retailer, Pageable paging);
}
