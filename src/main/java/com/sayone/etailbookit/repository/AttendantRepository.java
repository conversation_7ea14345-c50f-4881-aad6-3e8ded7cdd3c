package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.projections.AttendantDetailsProjection;
import com.sayone.etailbookit.projections.AttendantListingProjection;
import com.sayone.etailbookit.projections.AttendantModelAvailabilityProjections;
import io.swagger.models.auth.In;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

public interface AttendantRepository extends JpaRepository<Attendant, Integer> ,PagingAndSortingRepository<Attendant, Integer>, JpaSpecificationExecutor<Attendant> {

    Attendant findByAttendantId(Integer attendantId);

    @Query("Select a from Attendant a where a.id = ?1")
    AttendantDetailsProjection getAttendantDetails(Integer attendantId);

    List<AttendantListingProjection> findByActiveAndRetailer(Boolean active, String retailer);

    List<AttendantListingProjection> findByRetailerOrderByAttendantIdDesc(String retailer);

    @Query(
            "Select a from Attendant a where a.retailer = :retailer and a.deleted = :deleted and " +
                    "(LOWER(a.firstName) like %:search% " +
                    "or LOWER(a.lastName) like %:search% "+
                    "or LOWER(a.title) LIKE %:search%)"
    )
    Page<AttendantListingProjection> findByKeyword(
            @Param("search") String search, @Param("retailer") String retailer, @Param("deleted") Boolean deleted, Pageable paging
    );

    Page<AttendantListingProjection> findByRetailerAndDeleted(String retailer, Boolean deleted,Pageable paging);

    @Query("Select A from Attendant A where A.retailer = ?1")
    List<Attendant> findByRetailer(String retailer);

    List<Attendant> findByPhoneNo(String phoneNo);

    @Transactional
    @Modifying
    @Query("Update  Attendant A set A.deleted =?3 where A.venue.venueId=?1 and A.retailer=?2")
    void deleteByVenueIdAndDeleted(Integer venueId, String retailer,boolean deleted);

    @Query("Select A from Attendant A where A.venue.venueId=?1 And A.retailer=?2 And A.deleted=?3")
    List<Attendant> findByVenueId(Integer venueId, String retailer,boolean deleted);

    Attendant findByFirstName(String groombarAttendant);

    @Query("Select a from Attendant a where a.id = ?1 and a.deleted=?2")
    Attendant findByAttendantIdAndDeleted(Integer attendantId, boolean deleted);

    @Query("Select a from Attendant a where a.id = ?1 and a.retailer = ?2")
    Attendant findByAttendantIdAndRetailer(Integer attendantId, String retailer);

    @Query(value = "Select A from Attendant A where A.attendantId = :attendantId")
    AttendantModelAvailabilityProjections findAttendantAndAvailability(Integer attendantId);

    @Transactional
    @Modifying
    @Query("Update Attendant A set A.deleted= ?2 where A.attendantId=?1")
    void updateAttendantDeleted(Integer attendantId,boolean deleted);
}