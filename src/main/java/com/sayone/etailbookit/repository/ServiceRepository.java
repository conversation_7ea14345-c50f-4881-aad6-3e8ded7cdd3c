package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.DocumentOption;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.model.Service;
import com.sayone.etailbookit.model.ServiceType;
import com.sayone.etailbookit.projections.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;


public interface ServiceRepository extends JpaRepository<Service, Integer>, JpaSpecificationExecutor<Service> {

    Service findByServiceId(Integer serviceId);

    @Query("Select MAX(S.internalItemNumber) from Service S")
    Long getMaxIIN();

    @Query("Select S.internalItemNumber from Service S where S.serviceId = ?1")
    Long getIINForId(Integer id);

    @Query("Select S from Service S where S.internalItemNumber is null and S.retailer= ?1 order by id Desc")
    List<Service> getUnsyncedServices(String retailer);

    List<Service> findByNameIgnoreCaseAndRetailerAndDeleted(String name, String retailer, Boolean deleted);

    @Query("Select s from Service s WHERE s.isActive=?1 and s.retailer = ?2 and s.deleted=?3 order by s.name")
    List<Service> findByIsActiveAndRetailerAndDeleted(Boolean isActive, String retailer,Boolean deleted);

    @Query("SELECT s FROM Service s WHERE s.retailer = ?1 order by s.name")
    List<ServiceNameListing> getAll(String retailer);

    //drop down after soft deletion
    @Query("SELECT s FROM Service s WHERE s.retailer = ?1 And s.deleted = false")
    Page<ServiceListingProjection> getAll(String Retailer, Boolean deleted, Pageable paging);

    @Query("SELECT s FROM Service s WHERE LOWER(s.name) LIKE %?1% AND s.retailer = ?2 And s.deleted = false")
    Page<ServiceListingProjection> findByKeyword(String search, String Retailer, Boolean deleted,Pageable paging);

    @Query(
            value = "Select S from Service S where S.serviceType = ?1 and S.retailer =?2"
    )
    List<ServiceListingProjection> findServicesByServiceType(ServiceType service, String retailer);

    @Query("Select s from Service s where s.serviceId = ?1")
    Service getServiceDetails(@Param("serviceId") Integer serviceId);

    @Query(value = "select sas.addon_services_id as addOnServiceId,add.name as adOnserviceName " +
            "from service_addon_services sas " +
            "LEFT OUTER JOIN addon_service add on sas.addon_services_id = add.id " +
            "where service_id=?1 ;",nativeQuery = true)
    List<AddOnServiceProjection> getAddOnServices(@Param("serviceId") Integer serviceId);

    @Transactional
    @Modifying
    @Query(value="Delete from service_available_participant_vaccinations A where A.available_participant_vaccinations_id = ?1", nativeQuery=true)
    void deleteByVaccinationInfo(Integer vaccinationRecordsId);

    @Transactional
    @Modifying
    @Query(value="Delete from service_available_participant_documents A where A.available_participant_documents_id = ?1", nativeQuery=true)
    void deleteByDocumnetOption(Integer documentOptionId);

    long countAllByAvailableParticipantDocumentsContains(DocumentOption documentOption);

    @Query(value = "SELECT s FROM Service s WHERE s.retailer = ?1")
    List<Service> getAllServices(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Service E where E.petType.petTypeId=?1 and E.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Service E where E.serviceType.serviceTypeId=?1 and E.retailer=?2")
    void deleteByServiceType(Integer serviceTypeId, String retailer);

    Service findByName(String groombarService);

    @Query("SELECT s.petType FROM Service s WHERE s.serviceId = :serviceId and s.retailer=:retailer")
    List<PetType> findPetType(Integer serviceId, String retailer);
}
