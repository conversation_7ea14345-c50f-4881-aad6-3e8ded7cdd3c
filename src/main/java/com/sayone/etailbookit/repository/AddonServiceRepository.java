package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.AddonService;
import com.sayone.etailbookit.model.PetCologne;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AddonServiceRepository extends JpaRepository<AddonService, Integer>, JpaSpecificationExecutor<AddonService> {

    AddonService findByAddonServiceId(Integer addonServiceId);

    AddonService findByNameAndRetailerAndDeleted(String name, String retailer,  Boolean deleted);

    //drop down after soft deletion
    @Query("Select a from AddonService a WHERE a.active=?1 and a.retailer = ?2 and a.deleted=?3 order by a.name")
    Page<AddonService> findByActiveAndRetailerAndDeleted(Boolean active, String retailer,Boolean deleted,Pageable paging);

    Page<AddonService> findByRetailer(String retailer, Pageable pageable);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM AddonService A where A.retailer = ?1 And deleted = ?2 ")
    Integer getByLastIndexValueByRetailerAndDeleted(String retailer,Boolean deleted);

    Page<AddonService> findAByRetailerAndDeleted(String retailer, Boolean deleted, Pageable paging);

    @Query("Select A from AddonService A where A.retailer=?1")
    List<AddonService> findByRetailerwithoutPagination(String retailer);

    @Query("select a FROM AddonService a WHERE LOWER(a.name) LIKE %?1% AND a.retailer = ?2 AND a.deleted = ?3")
    Page<AddonService> findByKeyword(String lowerCase, String retailer, boolean b, Pageable paging);

    @Query("Select p from AddonService p where LOWER(p.name) LIKE %?1% AND p.active=?2 AND p.retailer=?3 AND p.deleted=?4")
    Page<AddonService> findByKeywordActiveRetailerAndDeleted(String search, boolean active, String retailer, boolean deleted, Pageable paging);
}