package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PetSizeConstraint;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface PetSizeConstraintRepository extends JpaRepository<PetSizeConstraint, Integer>, JpaSpecificationExecutor<PetSizeConstraint> {
    void deleteAllByIdNotInAndAttendantAttendantId(List<Integer> ids, Integer attendantId);

    void deleteAllByAttendantAttendantId(Integer attendantId);

    @Query(value = "Select * from pet_size_constraint P where P.retailer=?1",nativeQuery = true)
    List<PetSizeConstraint> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query(value = "Delete from PetSizeConstraint p where p.attendant.attendantId =?1 and p.retailer=?2 ")
    void deleteByAttendantId(Integer attendantId, String retailer);


    List<PetSizeConstraint> findAllByPetType(PetType petTypeId);
}
