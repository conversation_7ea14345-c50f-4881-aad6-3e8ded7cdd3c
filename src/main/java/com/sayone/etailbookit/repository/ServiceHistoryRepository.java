package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.ServiceHistory;
import com.sayone.etailbookit.projections.ServiceHistoryProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;


public interface ServiceHistoryRepository extends JpaRepository<ServiceHistory, Integer>, JpaSpecificationExecutor<ServiceHistory> {

    @Query(value = "select sh.notes as serviceHistoryNote, " +
            "c.name as combName,b.name as bladeName,pc.name as petCologneName,ps.name as petShampooName,app.appoinment_no as appointmentNo," +
            "sh.food_fed as foodFed,sh.times_fed as timesFed,sh.times_peed as timesPeed," +
            "sh.times_pooped as timesPooped,sh.poop_description as poopDescription,sh.is_filled_water_bowl as isFilledWaterBowl,sh.pictures_sent as picturesSent " +
            "FROM service_history sh " +
            "LEFT OUTER JOIN combs c on sh.comb_id = c.id " +
            "LEFT OUTER JOIN blades b on sh.blade_id = b.id " +
            "LEFT OUTER JOIN pet_shampoo ps on sh.shampoo_id = ps.id " +
            "LEFT OUTER JOIN pet_cologne pc on sh.cologne_id = pc.id " +
            "LEFT OUTER JOIN appointment app on sh.appointment_id = app.id " +
            "where sh.appointment_id=?1 ", nativeQuery = true)
    ServiceHistoryProjection getServiceNote(Integer appointmentId);

    @Query(value = "Select S from ServiceHistory S where S.appointment = ?1")
    ServiceHistory getByAppointment(Appointment appointment);

    ServiceHistory findByAppointmentId(Integer appointmentId);

    @Transactional
    @Modifying
    @Query("Delete from ServiceHistory S where S.appointment.id=?1 and retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Query(value="Select * from service_history W where  W.retailer=:retailer",nativeQuery = true)
    List<ServiceHistory> findByRetailer(String retailer);

    @Modifying
    @Query("Delete from ServiceHistory S where S.id=?1")
    void deleteById(Integer id);

    @Transactional
    @Modifying
    @Query("Delete from ServiceHistory T where T.blade.id=?1 and T.retailer=?2")
    void deleteByBladeId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from ServiceHistory T where T.comb.id=?1 and T.retailer=?2")
    void deleteByCombsId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from ServiceHistory T where T.petCologne.id=?1 and T.retailer=?2")
    void deleteByCologneId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from ServiceHistory T where T.petShampoo.id=?1 and T.retailer=?2")
    void deleteByPetShampooId(Integer id, String retailer);
}
