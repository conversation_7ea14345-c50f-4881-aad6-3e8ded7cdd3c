package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.ServiceHistoryAddNotes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface ServiceHistoryAddNotesRepository extends JpaRepository<ServiceHistoryAddNotes,Integer> {
    List<ServiceHistoryAddNotes> findByAppointmentId(Integer id);

    Page<ServiceHistoryAddNotes> findAllByAppointmentPetIdOrderByAppointmentIdAscModifiedAtAsc(Integer id, Pageable pageable);

    @Transactional
    @Modifying
    @Query("Delete from ServiceHistoryAddNotes S where S.appointment.id=?1 and S.retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Query(value="Select * from service_add_notes W where  W.retailer=:retailer",nativeQuery = true)
    List<ServiceHistoryAddNotes> findByRetailer(String retailer);

    @Modifying
    @Query("Delete from ServiceHistoryAddNotes S where S.id=?1")
    void deleteById(Integer id);


}
