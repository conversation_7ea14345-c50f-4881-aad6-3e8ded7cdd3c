package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PetCologne;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PetCologneRepository extends JpaRepository<PetCologne, Integer>, JpaSpecificationExecutor<PetCologne> {

    PetCologne findByNameIgnoreCaseAndRetailerAndDeleted(String name, String retailer,Boolean deleted);
    @Query("Select a from PetCologne a WHERE a.active=?1 and a.retailer = ?2 and a.deleted=?3 order by a.name")
    Page<PetCologne> findByActiveAndRetailerAndDeleted(Boolean active, String retailer, Boolean deleted,Pageable paging);

    Page<PetCologne> findByRetailer(String retailer, Pageable pageable);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM PetCologne A where A.retailer = ?1 And deleted = ?2 ")
    Integer getByLastIndexValueByRetailerAndDeleted(String retailer, Boolean deleted);

    @Query(
            "Select C from PetCologne C where C.retailer = ?1 and C.deleted = ?2 " +
                    "and (?3 is null or lower(C.name) like %?3%)"
    )
    Page<PetCologne> getPetColognes(String retailer, Boolean deleted, String search, Pageable paging);

    @Query("Select P from PetCologne P where P.retailer=?1")
    List<PetCologne> findByRetailerWithoutPagination(String retailer);

    @Query("SELECT s FROM PetCologne s WHERE LOWER(s.name) LIKE %?1% AND s.retailer = ?2 And s.deleted = false")
    Page<PetCologne> findByKeyword(String toLowerCase, String retailer, boolean b, Pageable paging);

    @Query("SELECT s FROM PetCologne s WHERE s.retailer = ?1 And s.deleted = false")
    Page<PetCologne> getAll(String retailer, boolean b, Pageable paging);

    @Query("Select p from PetCologne p where LOWER(p.name) LIKE %?1% AND p.active=?2 AND p.retailer=?3 AND p.deleted=?4")
    Page<PetCologne> findByKeywordActiveRetailerAndDeleted(String lowerCase, boolean active, String retailer, boolean deleted, Pageable paging);
}