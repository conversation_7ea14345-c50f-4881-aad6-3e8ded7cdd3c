package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Appointment;
import com.sayone.etailbookit.model.AppointmentVaccinationInformation;
import com.sayone.etailbookit.model.PetVaccinationRecords;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface AppointmentVaccinationInformationRepository extends JpaRepository<AppointmentVaccinationInformation, Integer>, JpaSpecificationExecutor<AppointmentVaccinationInformation> {


    @Transactional
    @Modifying
    @Query("Delete from AppointmentVaccinationInformation A where A.vaccinationRecords.vaccinationRecordId = ?1")
    void deleteByVaccinationInfo(Integer vaccinationRecordsId);

    @Query("Select A from AppointmentVaccinationInformation A where A.vaccinationRecords.vaccinationRecordId = ?1")
    List<AppointmentVaccinationInformation> findByVaccinationRecordsVaccinationRecordId(Integer vaccinationRecordsId);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentVaccinationInformation A where A.appointment.id = ?1")
    void deleteByAppointmentId(Integer id);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentVaccinationInformation A where A.vaccinationRecords.id = ?1 and retailer=?2")
    void deleteByVaccinationInfoId(Integer vaccinationRecordId, String retailer);

    List<AppointmentVaccinationInformation> findByRetailer(String retailer);
}
