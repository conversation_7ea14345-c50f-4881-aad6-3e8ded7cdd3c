package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Attendant;
import com.sayone.etailbookit.model.Vacation;
import com.sayone.etailbookit.projections.VacationProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;

@Repository
public interface VacationRepository extends JpaRepository<Vacation,Integer>, JpaSpecificationExecutor<Vacation> {

    @Query(
            value =
                    "Select V from Vacation V where " +
                            "(V.vacationStartTime >= :startDate and V.vacationStartTime <= :endDate) " +
                            " and V.retailer = :retailer order by V.createdAt desc"
    )
    List<VacationProjection> getAllVacations(OffsetDateTime startDate, OffsetDateTime endDate, String retailer);

    @Query(value = "Select V from Vacation V where V.id=?1 and V.attendant=?2 and V.retailer=?3")
    Vacation findByIdAttendantAndRetailer(Integer vacationId, Attendant attendant, String retailer);

    List<Vacation> findByAttendantAndRetailer(Integer attendantId, String retailer);
}