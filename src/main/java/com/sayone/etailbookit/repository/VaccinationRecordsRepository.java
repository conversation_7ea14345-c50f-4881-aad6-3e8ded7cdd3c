package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.dto.VaccinationRecordsDto;
import com.sayone.etailbookit.model.PetDocuments;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.model.PetVaccinationRecords;
import com.sayone.etailbookit.model.VaccinationRecords;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

public interface VaccinationRecordsRepository extends JpaRepository<VaccinationRecords, Integer>, JpaSpecificationExecutor<VaccinationRecords> {

    VaccinationRecords findByVaccinationRecordId(Integer vaccinationRecordId);

    void deleteByPetType(PetType petType);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM VaccinationRecords A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Query("Select V from VaccinationRecords V where V.retailer=?1")
    List<VaccinationRecords> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from VaccinationRecords V where V.petType.petTypeId=?1 and V.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);
}