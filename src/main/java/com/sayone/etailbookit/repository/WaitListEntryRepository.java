package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.WaitlistEntry;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WaitListEntryRepository extends JpaRepository<WaitlistEntry,Integer> {
    
    @Query("SELECT CASE WHEN COUNT(w) > 0 THEN true ELSE false END FROM WaitlistEntry w " +
            "JOIN w.customers c " +
            "WHERE w.timeSlot.id = :timeSlotId AND c.ecom_id = :ecomId AND c.retailer = :retailer")
    boolean existsInWaitlist(@Param("timeSlotId") Integer timeSlotId,
                             @Param("ecomId") Integer ecomId,
                             @Param("retailer") String retailer);
    
    @Query("SELECT w FROM WaitlistEntry w " +
            "JOIN w.customers c " +
            "WHERE w.timeSlot.id = :timeSlotId AND c.ecom_id = :ecomId AND c.retailer = :retailer")
    List<WaitlistEntry> findByTimeSlotAndCustomer(@Param("timeSlotId") Integer timeSlotId,
                                                  @Param("ecomId") Integer ecomId,
                                                  @Param("retailer") String retailer);
    
    @Query("SELECT w FROM WaitlistEntry w WHERE w.timeSlot.id = :timeSlotId")
    List<WaitlistEntry> findByTimeSlotId(@Param("timeSlotId") Integer timeSlotId);

   @Query("SELECT w FROM WaitlistEntry w JOIN w.customers c WHERE c.retailer = :retailer ORDER BY w.createdAt DESC")
    Page<WaitlistEntry> findAllByRetailer(@Param("retailer") String retailer, Pageable pageable);

}
