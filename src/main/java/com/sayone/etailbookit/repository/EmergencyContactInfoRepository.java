package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.EmergencyContactInfo;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface EmergencyContactInfoRepository extends JpaRepository<EmergencyContactInfo, Integer>, JpaSpecificationExecutor<EmergencyContactInfo> {

    EmergencyContactInfo findByEmergencyContactInfoId(Integer emergencyContactInfoId);

    void deleteByPetType(PetType petType);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM EmergencyContactInfo A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from EmergencyContactInfo E where E.petType.petTypeId=?1 and E.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Query("Select A from EmergencyContactInfo A where A.retailer=?1")
    List<EmergencyContactInfo> findByRetailer(String retailer);


}