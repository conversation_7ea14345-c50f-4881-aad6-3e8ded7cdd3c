package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.DocumentOption;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.projections.DocumentsProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DocumentOptionRepository extends JpaRepository<DocumentOption, Integer>, JpaSpecificationExecutor<DocumentOption> {

    DocumentOption findByDocumentOptionId(Integer documentOptionId);

    void deleteByPetType(PetType petType);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM DocumentOption A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Query(value = "SELECT * FROM document_option where retailer = :retailer",nativeQuery = true)
    Page<DocumentsProjection> findAllDocuments(@Param("retailer") String retailer, Pageable pageable);

    @Query(value = "SELECT * FROM document_option where retailer = :retailer AND active = 'true' order by name",nativeQuery = true)
    List<DocumentsProjection> findAllActiveDocuments(String retailer);

    @Query("Select A from DocumentOption A where A.retailer=?1")
    List<DocumentOption> findByRetailer(String retailer);
}