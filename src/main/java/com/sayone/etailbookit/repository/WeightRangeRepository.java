package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.model.WeightRange;
import com.sayone.etailbookit.projections.WeightRangeProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

public interface WeightRangeRepository extends JpaRepository<WeightRange, Integer>, JpaSpecificationExecutor<WeightRange> {
    void deleteByPetType(PetType petType);

    WeightRange findByWeightRangeId(Integer weightRangeId);

    @Query(value = "select min_value as minValue,max_value as maxValue from weight_range where id=?1 ;", nativeQuery = true)
    WeightRangeProjection getWeightRange(@Param("weightRangeId") Integer weightRangeId);

    List<WeightRange> findByPetTypePetTypeId(Integer id);

    @Modifying
    @Query("Delete from WeightRange S where S.id=?1")
    void deleteById(Integer id);

    @Transactional
    @Modifying
    @Query("Delete from WeightRange W where W.petType.petTypeId=?1 and W.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Query(value="Select * from weight_range W where  W.retailer=:retailer",nativeQuery = true)
    List<WeightRange> findByRetailer(String retailer);
}