package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.ThreatReaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface ThreatReactionRepository extends JpaRepository<ThreatReaction, Integer>, JpaSpecificationExecutor<ThreatReaction> {

    List<ThreatReaction> findByRetailer(String retailer);

}