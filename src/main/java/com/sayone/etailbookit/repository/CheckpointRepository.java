package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.KinesisCheckpoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CheckpointRepository extends JpaRepository<KinesisCheckpoint, Long> {
    Optional<KinesisCheckpoint> findByStreamNameAndShardId(String streamName, String shardId);
}

