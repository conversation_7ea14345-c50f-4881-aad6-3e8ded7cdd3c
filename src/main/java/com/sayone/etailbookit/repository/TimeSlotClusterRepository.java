package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.dto.TimeSlotMinimalDTO;
import com.sayone.etailbookit.model.TimeSlotCluster;
import com.sayone.etailbookit.model.TimeSlots;
import com.sayone.etailbookit.projections.TimeSlotProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
public interface TimeSlotClusterRepository extends JpaRepository<TimeSlotCluster, Integer> {

   // @Query("Select timeSlots from TimeSlotCluster t where t.id=:timeSlotClusterId order by id asc")
   @Query("SELECT ts FROM TimeSlots ts WHERE ts.timeSlotCluster.clusterId = :timeSlotClusterId ORDER BY ts.slotStartTime ASC")
    List<TimeSlots> findSlotByClusterId(Integer timeSlotClusterId);

    @Modifying
    @Transactional
    @Query("DELETE FROM TimeSlotCluster c WHERE c.clusterId IN :clusterIds")
    int deleteClustersByIds(List<Integer> clusterIds);

 @Query("SELECT new com.sayone.etailbookit.dto.TimeSlotMinimalDTO(" +
         "ts.slotStartTime, " +
         "ts.slotEndTime, " +
         "c.clusterId, " +
         "a.deleted) " +
         "FROM TimeSlots ts " +
         "JOIN ts.timeSlotCluster c " +
         "JOIN ts.attendant a " +
         "WHERE c.clusterId IN :clusterIds " +
         "AND ts.deleted = false " +
         "AND a IS NOT NULL " +
         "GROUP BY c.clusterId, ts.slotStartTime, ts.slotEndTime, a.deleted " +
         "HAVING ts.slotStartTime = MIN(ts.slotStartTime)")
 List<TimeSlotMinimalDTO> findFirstSlotsByClusterIds(@Param("clusterIds") Set<Integer> clusterIds);






 @Query("SELECT t FROM TimeSlots t WHERE t.timeSlotCluster.clusterId IN :clusterIds")
    List<TimeSlots> findAllByClusterIdIn(@Param("clusterIds") Set<Integer> clusterIds);

}
