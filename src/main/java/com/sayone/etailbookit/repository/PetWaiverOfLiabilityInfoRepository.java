package com.sayone.etailbookit.repository;


import com.sayone.etailbookit.model.PetWaiverOfLiabilityInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface PetWaiverOfLiabilityInfoRepository extends JpaRepository<PetWaiverOfLiabilityInfo, Integer>, JpaSpecificationExecutor<PetWaiverOfLiabilityInfo> {
   @Transactional
   @Modifying
   @Query("Delete from PetWaiverOfLiabilityInfo P where P.pet.id=?1 and P.retailer=?2")
    void deleteByPetId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from PetWaiverOfLiabilityInfo P where P.waiverOfLiability.waiverOfLiabilityId=?1 and P.retailer=?2")
    void deleteByWaiverId(Integer waiverOfLiabilityId, String retailer);


    List<PetWaiverOfLiabilityInfo> findByRetailer(String retailer);

    @Query("Select P from PetWaiverOfLiabilityInfo P where P.pet.id=?1 and P.retailer=?2")
    PetWaiverOfLiabilityInfo findByPet(Integer petId, String retailer);
}
