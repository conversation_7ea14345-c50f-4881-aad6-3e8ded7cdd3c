package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PersonalityParameter;
import com.sayone.etailbookit.projections.PetPersonalityProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PersonalityParameterRepository extends JpaRepository<PersonalityParameter, Integer>, JpaSpecificationExecutor<PersonalityParameter> {

    @Query(value = "select pp.name as personality " +
            "FROM pet_personality_parameters ppp " +
            "LEFT OUTER JOIN personality_parameter pp on ppp.personality_parameter_id = pp.id " +
            "where ppp.pet_id=?1 and ppp.retailer=?2", nativeQuery = true)
    List<PetPersonalityProjection> getPersonalityByPet(Integer petId, String retailer);

    List<PersonalityParameter> findByRetailer(String retailer);

}