package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.ServiceBreedsInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServiceBreedsInformationRepository  extends JpaRepository<ServiceBreedsInformation, Integer>, JpaSpecificationExecutor<ServiceBreedsInformation> {
  void deleteAllByIdNotInAndServiceServiceId(List<Integer> ids, Integer serviceId);

  void deleteAllByServiceServiceId(Integer serviceId);

  @Query("Select S from ServiceBreedsInformation S where S.service.serviceId=?1 and S.breed.id=?2")
    ServiceBreedsInformation findByServiceIdAndBreedId(Integer serviceId,Integer breedId);
}
