package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.DesiredHairLength;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface DesiredHairLengthRepository extends JpaRepository<DesiredHairLength, Integer>, JpaSpecificationExecutor<DesiredHairLength> {

    void deleteByPetType(PetType petType);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM DesiredHairLength A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    DesiredHairLength findByDesiredHairLengthId(Integer desiredHairLengthId);

    @Query("Select A from DesiredHairLength A where A.retailer=?1")
    List<DesiredHairLength> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from DesiredHairLength T where T.petType.petTypeId=?1 and T.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);
}