package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.PetDocuments;
import com.sayone.etailbookit.projections.PetDocumentsProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

public interface PetDocumentRepository extends JpaRepository<PetDocuments, Integer>, JpaSpecificationExecutor<PetDocuments> {
    void deleteByPet(Pet pet);

    @Transactional
    @Modifying
    @Query("Delete from PetDocuments A where A.documentOption.documentOptionId = ?1")
    void deleteByDocumnetOption(Integer documentOptionId);

    @Query("select A from PetDocuments A where pet.customerId = ?1")
    List<PetDocumentsProjection> findByCustomerId(Integer customerId);

    long countAllByDocumentOptionDocumentOptionId(int documentOptionId);

    @Transactional
    @Modifying
    @Query("Delete from PetDocuments A where A.pet.id= ?1 and retailer=?2")
    void deleteByPetId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from PetDocuments A where A.pet.id= ?1")
    void deleteByPetId(Integer id);

    @Transactional
    @Modifying
    @Query("Delete from PetDocuments A where A.documentOption.documentOptionId = ?1 and A.retailer=?2")
    void deleteByDocumnetOptionId(Integer documentOptionId, String retailer);

    List<PetDocuments> findByRetailer(String retailer);

    @Query("Select D from PetDocuments D where D.documentOption.documentOptionId = ?1 and D.pet.id= ?2")
    PetDocuments findByDocumentOptionIdAndPetId(Integer documentId, Integer petId);

    List<PetDocuments> findAllByDocumentOptionDocumentOptionIdAndPetId(Integer documentId, Integer petId);

    @Transactional
    @Modifying()
    @Query("Delete from PetDocuments A where A.id NOT IN ?1 AND A.pet.id = ?2")
    void deleteAllByIdNotInAndPetId(List<Integer> ids, Integer petId);
}