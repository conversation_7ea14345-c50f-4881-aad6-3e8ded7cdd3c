package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.dto.PetBirthdayDto;
import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.projections.PetDetailsProjection;
import com.sayone.etailbookit.projections.PetDropdownProjection;
import com.sayone.etailbookit.projections.PetListingProjection;
import com.sayone.etailbookit.projections.PetNameAndCountProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface PetRepository extends JpaRepository<Pet, Integer>, JpaSpecificationExecutor<Pet> {

    @Query("Select p from Pet p where p.id = ?1")
    PetDetailsProjection getPetDetails(Integer id);


    List<PetDropdownProjection> findAByRetailerOrderByIdDesc(String retailer);

    @Query("Select p from Pet p where p.customerId = ?1 and p.retailer = ?2 and p.deleted= ?3 and Lower(p.name) LIKE %?4%")
    List<PetDropdownProjection> findByCustomer(Integer customerId, String retailer,Boolean deleted,String search);

    @Query(
            "select p from Pet p where p.customerId = ?1 and p.retailer = ?2 and " +
            "(p.deceaseDate is null or p.deceaseDate = '') and p.deleted= ?3 and Lower(p.name) LIKE %?4%")
    List<PetDropdownProjection> findByCustomerWithoutDeceasedDate(
            Integer customer, String retailer,Boolean deleted, String search
    );

    @Query("select a FROM Pet a WHERE LOWER(a.name) LIKE %?1% AND a.customerId = ?2 AND a.retailer = ?3 AND a.deleted = ?4")
    Page<PetListingProjection> findByKeyword(String search, Integer customerId,String retailer,Boolean deleted,Pageable paging);

    Page<PetListingProjection> findAllByCustomerIdAndRetailerAndDeleted(Integer customerId, String retailer,Boolean deleted, Pageable paging);

    @Query("Select P from Pet P where P.size is null")
    List<PetListingProjection> getPetsWithoutSize();

    @Modifying
    @Query("update Pet P set P.hairLength.hairLengthId = null where P.hairLength.hairLengthId= ?1")
    void updateByHairLength(Integer hairLengthId);

    @Modifying
    @Query("update Pet P set P.hairTexture.hairTextureId = null where P.hairTexture.hairTextureId = ?1")
    void updateByHairTexture(Integer hairTextureId);

    @Query("Select P from Pet P where P.retailer = ?1")
    List<Pet> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Pet T where T.hairLength.hairLengthId=?1 and T.retailer=?2")
    void deleteByHairLengthId(Integer hairLengthId,String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Pet E where E.petType.petTypeId=?1 and E.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Pet E where E.temperament.temperamentId=?1 and E.retailer=?2")
    void deleteByTemperamentId(Integer temperamentId,String retailer);

    @Transactional
    @Modifying
    @Query(value = "Delete from pet E where E.weight_range_id=:weightRangeId and E.retailer=:retailer",nativeQuery = true)
    void deleteByWeightRangeId(Integer weightRangeId,String retailer);

    @Transactional
    @Modifying
    @Query("Delete from Pet T where T.hairTexture.hairTextureId=?1 and T.retailer=?2")
    void deleteByHairTextureId(Integer hairTextureId, String retailer);
  
    @Query("Select p from Pet p where p.id=?1")
    Pet findByPetId(Integer petId);

    @Query("Select P from Pet P where P.id=?1 and P.customerId=?2")
    Pet findByIdAndCustomer(int petId, int customerId);

    @Query("Select P from Pet P where P.id=?1 and P.customerId=?2")
    Optional<Pet> findByPetIdAndCustomerId(int petId, int customerId);

    @Query(value = "select p.name as petName ,count(p.name ) from public.pet p where  p.customer_id =:sourceCustomerId  and p.retailer=:retailer group by p.name",nativeQuery = true)
    List<PetNameAndCountProjection> getPetNameAndCount(Integer sourceCustomerId, String retailer);

    @Query(value = " select * from pet p where p.customer_id =:sourceCustomerId and p.retailer=:retailer order by id asc",nativeQuery = true)
    List<Pet> findByCustomerIdAndRetailer(Integer sourceCustomerId, String retailer);

    @Modifying
    @Query(value = "update pet set deleted=?1 where id=?2",nativeQuery = true)
    void deletePet(Boolean deleted,int petId);

    @Query("select a FROM Pet a WHERE LOWER(a.name) LIKE %?1%  AND a.retailer = ?2 AND a.deleted = ?3")
    List<Pet> findByName(String petName, String retailer, boolean b, Pageable paging);

    @Query("Select a FROM Pet a where EXTRACT(month  FROM a.dob)=?1 AND a.retailer=?2 AND a.deleted=?3")
    List<Pet> findByDob(Integer dob, String retailer, boolean b, Pageable paging);

    @Query(value = "Select * FROM Pet a WHERE LOWER(a.name) LIKE %?1% AND EXTRACT(month  FROM a.dob)=?2 AND a.retailer=?3 AND a.deleted=?4",nativeQuery = true)
    List<Pet> findByNameAndDob(String petName, Integer localDate, String retailer, boolean b, Pageable paging);

    @Query("Select a from Pet a where a.id=?1 and a.retailer=?2 and a.deleted=?3")
    Optional<Pet>findByPetIdAndRetailer(Integer petId, String retailer, boolean b);

    @Query("Select a from Pet a WHERE a.retailer=?1 AND a.deleted=?2")
    List<Pet> findAllByRetailerAndDeleted(String retailer, boolean b, Pageable paging);

    @Query("Select p from Pet p where p.customerId=?1 and p.retailer=?2 and p.deleted=?3")
    List<Pet>findByCustomerId(Integer customerId,String retailer,boolean deleted,Pageable paging);

    @Query("SELECT COUNT(p) FROM Pet p WHERE p.customerId=?1 and p.retailer=?2")
    long countByCustomerCount(Integer customerId,String retailer);

    @Query("SELECT COUNT(p) FROM Pet p WHERE p.id=?1 and p.retailer=?2 and p.deleted=?3")
    Long countByPetIds(Integer petId, String retailer,boolean deleted);

    @Query("Select count(p) from Pet p where LOWER(p.name) LIKE %?1% AND EXTRACT(month  FROM p.dob)=?2 AND p.retailer=?3 AND p.deleted=?4")
    long countByNameAndDob(String lowerCase, Integer petDob, String retailer, boolean b);

    @Query("Select count(a) from Pet a where LOWER(a.name) LIKE %?1%  AND a.retailer = ?2 AND a.deleted = ?3")
    long countOfName(String s, String retailer, boolean b);

    @Query("Select count(a) from Pet a where EXTRACT(month  FROM a.dob)=?1 AND a.retailer=?2 AND a.deleted=?3")
    long countOfDob(Integer petDob, String retailer, boolean b);

    @Query("Select count(a) from Pet a where a.retailer=?1 AND a.deleted=?2")
    long countOfAllPets(String retailer, boolean b);

    @Query(value = "Select * from Pet p where p.name=?1",nativeQuery = true)
    Pet findByPetName(String groombarPet);

    @Query("Select p from Pet p where p.retailer = ?1 AND exactWeight IS NOT NULL AND exactWeight <>0")
    List<Pet> petList (String retailer);

    List<Pet> findByIdInOrCustomerIdIn(List<Integer> ids, List<Integer> customerIds);

    @Query("SELECT p FROM Pet p WHERE p.petType IN :petTypes AND p.customerId = :customerId")
    List<Pet> findPetsByPetTypesAndCustomerId(@Param("petTypes") List<PetType> petTypes,
                                              @Param("customerId") Integer customerId);

    List<Pet> findByNameAndCustomerIdAndRetailer(String name, Integer customerId, String retailer);

    @Modifying
    @Query("update Pet A set A.temperament.temperamentId = null where A.temperament.temperamentId = ?1")
    void updateTempermant(Integer temperamentId);
}
