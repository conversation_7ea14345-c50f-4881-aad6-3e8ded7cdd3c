package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.AppointmentDesiredHairLengths;
import com.sayone.etailbookit.model.AppointmentEmergencyContactInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface AppointmentEmergencyContactInfoRpository extends JpaRepository<AppointmentEmergencyContactInfo, Integer>, JpaSpecificationExecutor<AppointmentEmergencyContactInfo> {
    @Transactional
    @Modifying
    @Query("Delete from AppointmentEmergencyContactInfo A where A.emergencyContactInfo.emergencyContactInfoId = ?1")
    void deleteByEmergencyContactInfo(Integer emergencyContactInfoId);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentEmergencyContactInfo A where A.appointment.id = ?1 and retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Query("Select A from AppointmentEmergencyContactInfo A where A.retailer=?1")
    List<AppointmentEmergencyContactInfo> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentEmergencyContactInfo A where A.emergencyContactInfo.emergencyContactInfoId = ?1 and A.retailer=?2")
    void deleteByEmergencyContactInfoId(Integer emergencyContactInfoId, String retailer);
}
