package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.GoogleCalendarEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface GoogleCalendarEventRepository extends JpaRepository<GoogleCalendarEvent, Integer> {
    
    Optional<GoogleCalendarEvent> findByAppointmentId(Integer appointmentId);
    
    List<GoogleCalendarEvent> findByAttendantId(Integer attendantId);
    
    Optional<GoogleCalendarEvent> findByGoogleEventId(String googleEventId);
    
    void deleteByAppointmentId(Integer appointmentId);
    
    List<GoogleCalendarEvent> findByRetailer(String retailer);

    List<GoogleCalendarEvent> findByAppointmentIdIn(Set<Integer> appointmentIds);
}

