package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Pet;
import com.sayone.etailbookit.model.PetBreedsInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

public interface PetBreedsInformationRepository extends JpaRepository<PetBreedsInformation, Integer>, JpaSpecificationExecutor<PetBreedsInformation> {
  @Transactional
  void deleteAllByIdNotInAndPetId(List<Integer> ids, Integer petId);

  @Transactional
  void deleteAllByPetId(Integer petId);

  @Transactional
  void deleteByBreedId(Integer breedId);

  @Modifying
  @Transactional
  @Query("Update PetBreedsInformation p set p.pet.id=null where p.breed.id=:breedId")
  void updatePetIdByBreed(Integer breedId);

  @Modifying
  @Transactional
  @Query("Update PetBreedsInformation p set p.breed.id=null where p.breed.id=:breedId")
  void updateBreedId(Integer breedId);
   Set<PetBreedsInformation> findByPet(Pet pet);
  @Transactional
  @Modifying
  @Query("Delete from PetBreedsInformation P where P.pet.id=?1 and P.retailer=?2 ")
  void deleteByPetId(Integer id, String retailer);

}