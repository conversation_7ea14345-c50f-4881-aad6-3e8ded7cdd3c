package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.QuoteAdjustments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Set;

@Repository
public interface QuoteAdjustmentRepository extends JpaRepository<QuoteAdjustments, Integer>, JpaSpecificationExecutor<QuoteAdjustments> {
   @Query(value = "Select q from QuoteAdjustments q where q.quoteAdjustmentId=?1")
    QuoteAdjustments findByQuoteAdjustmentId(Integer quoteAdjustmentId);

   Set<QuoteAdjustments> findAllByAppointmentId(Integer appointmentId);

   @Transactional
   QuoteAdjustments save(QuoteAdjustments quoteAdjustments);

   @Transactional
   @Modifying
   @Query("Delete from QuoteAdjustments Q where Q.appointment.id=?1 and Q.retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

   @Query("Select Q from QuoteAdjustments Q where Q.retailer=?1 ")
    List<QuoteAdjustments> findByRetailer(String retailer);
}
