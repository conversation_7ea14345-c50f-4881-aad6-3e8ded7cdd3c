package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.dto.CustomerDto;
import com.sayone.etailbookit.model.Customer;
import com.sayone.etailbookit.projections.CustomerProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface CustomerRepository extends JpaRepository<Customer,Integer>, JpaSpecificationExecutor<Customer> {
   @Query("Select C from Customer C where C.ecom_id=?1 and C.retailer=?2")
    Customer findByEcomIdandRetailer(Integer id, String retailer);

    @Query("Select C from Customer C where C.ecom_id=?1 and C.retailer=?2")
    CustomerProjection getByEcomIdandRetailer(Integer customerId, String retailer);

    @Query("Select C.firstName  from Customer C where C.ecom_id=?1 and C.retailer=?2")
    String getCustomerName(Integer customerID, String retailer);

}
