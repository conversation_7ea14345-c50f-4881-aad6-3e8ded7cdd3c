package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Configuration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface ConfigurationRepository extends JpaRepository<Configuration, Integer>, JpaSpecificationExecutor<Configuration> {

    Configuration findByNameAndRetailer(String name, String retailer);

    List<Configuration> findByRetailer(String retailer);
}