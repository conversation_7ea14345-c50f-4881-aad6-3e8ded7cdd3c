package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.*;

import com.sayone.etailbookit.projections.AttendantAvailabilityProjections;
import io.swagger.models.auth.In;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.time.LocalTime;
import java.time.OffsetTime;
import java.util.List;

public interface AttendantAvailabilityRepository extends JpaRepository<AttendantAvailability, Integer>, JpaSpecificationExecutor<AttendantAvailability> {

    void deleteByAttendant(Attendant attendant);

    @Transactional
    @Modifying
    @Query("Delete from AttendantAvailability A where A.attendant.id=?1 and A.retailer=?2")
    void deleteByAttendantId(Integer attendantId, String retailer);

    List<AttendantAvailability> findByRetailer(String retailer);

    @Query(value = "Select * from attendant_availability A where A.available_day=?1 and A.availability_open_time >= ?2 and A.availability_close_time<= ?3 and A.retailer=?4",nativeQuery = true)
    List<AttendantAvailability> findByAvailabilityTimRangeAndRetailer1(String availableDay, OffsetTime startTime, OffsetTime endTime, String retailer);

    @Query(value = "Select Aa.* from attendant_availability Aa " +
           " JOIN attendant a ON Aa.attendant_id = a.id " +
           "JOIN attendant_pet_types apt ON a.id = apt.attendant_id "+
            "where Aa.available_day=?1 and " +
            "date_part('hour', Aa.availability_open_time) >= date_part('hour', CAST(?2 AS timetz))  AND date_part('minute', Aa.availability_open_time) >=date_part('minute',CAST(?2 AS timetz))"+
            "and date_part('hour', Aa.availability_close_time) <= date_part('hour', CAST(?3 AS timetz)) AND date_part('minute', Aa.availability_close_time) <=date_part('minute',CAST(?3 AS timetz))" +
            "and Aa.retailer=?4 and  a.deleted=?5 "+
            " AND apt.pet_type_id=?6"
            ,nativeQuery = true)
    List<AttendantAvailability> findByAvailabilityTimRangeAndRetailer(String availableDay, OffsetTime startTime, OffsetTime endTime, String retailer, boolean deleted, Integer petTypeId);

    @Query(value = " SELECT Aa.* FROM attendant_availability Aa " +
            "               JOIN attendant a ON Aa.attendant_id = a.id " +
            "               JOIN attendant_pet_types apt ON a.id = apt.attendant_id " +
            "               JOIN venue v ON a.venue_id = v.id " +
            "               WHERE Aa.available_day =?1" +
            "               AND v.id = ?2" +
            "               AND apt.pet_type_id = ?3" +
            "               AND Aa.retailer =?4" +
            "               AND a.deleted =?5",nativeQuery = true)
    List<AttendantAvailability> findByAvailableDayServiceTypeVenueAndPetType(String string, Integer currentVenue, Integer petType, String retailer, boolean deleted);
}