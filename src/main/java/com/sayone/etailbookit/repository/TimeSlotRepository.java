package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.*;
import com.sayone.etailbookit.projections.TimeSlotMinimalProjection;
import com.sayone.etailbookit.projections.TimeSlotProjection;
import com.sayone.etailbookit.projections.TimeSlotProjectionOfAmonth;
import com.sayone.etailbookit.projections.TimeSlotWeekViewProjection;
import com.sayone.etailbookit.service.impl.TimeSlotService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

@Repository
public interface TimeSlotRepository extends JpaRepository<TimeSlots, Integer>, JpaSpecificationExecutor<TimeSlots> {

    TimeSlotProjection getTimeSlotsById(@Param("id") Integer id);

    List<TimeSlots> getTimeSlotsByTimeSlotClusterAndAttendant(TimeSlotCluster timeSlotCluster, Attendant attendant);

    List<TimeSlotProjection> findBySlotDateAndRetailer(LocalDate localDate, String retailer);

    @Query(
            value = "SELECT subquery.id AS id, " +
                    "       subquery.cluster_id AS clusterId, " +
                    "       DATE(subquery.slot_start_time + CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute') AS slotDate, " +
                    "       TO_CHAR(subquery.slot_start_time , 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') AS slotStartTime, " +
                    "       TO_CHAR(subquery.slot_end_time , 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') AS slotEndTime, " +
                    "       subquery.color AS color, " +
                    "       subquery.slot_name AS slotName, " +
                    "       subquery.slot_booked AS slotBooked, " +
                    "       subquery.attendant_id AS attendantId, " +
                    "       subquery.attendant_name AS attendantName " +
                    "FROM ( " +
                    "    SELECT ts.id, " +
                    "           ts.slot_start_time, " +
                    "           ts.slot_end_time, " +
                    "           ts.slot_name, " +
                    "           ts.color, " +
                    "           ts.slot_booked, " +
                    "           ts.retailer, " +
                    "           ts.time_slot_cluster_id AS cluster_id, " +
                    "           a.id AS attendant_id, " +
                    "           CONCAT(a.first_name, ' ', a.last_name) AS attendant_name " +
                    "    FROM time_slots ts " +
                    "    LEFT JOIN attendant a ON ts.attendant_id = a.id " +
                    "    WHERE ts.slot_start_time BETWEEN :startDate AND :endDate " +
                  //  "      AND DATE(ts.slot_start_time + CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute') = :convertedDate " +
                    "      AND (ts.retailer = :retailer) AND ts.deleted = :deleted " +
                    ") subquery " +
                    "ORDER BY subquery.slot_start_time",
            nativeQuery = true
    )
    List<TimeSlotWeekViewProjection> getAllTimeSlots(OffsetDateTime startDate, OffsetDateTime endDate, String retailer,Integer offsetDifference, boolean deleted);




    @Query("Select A from TimeSlots A join A.services s where A.slotDate =:utcAppointmentDate and s=:requiredService and A.venue=:currentVenue and A.attendant=:requiredAttendant and A.retailer=:retailer")
    List<TimeSlotProjection> findByDateServiceVenueAttendantAndRetailer(LocalDate utcAppointmentDate, Service requiredService, Venue currentVenue, Attendant requiredAttendant, String retailer);

   /* @Query(
            value = "SELECT A FROM TimeSlots A WHERE A.slotStartTime BETWEEN :startDateTime AND :endDateTime " +
                    "AND A.retailer = :retailer " +
                    "ORDER BY A.id DESC"
    )*/
    @Query(value = "SELECT A FROM TimeSlots A WHERE A.slotStartTime BETWEEN :startDateTime AND  :endDateTime AND A.retailer = :retailer  and A.deleted =:deleted ORDER BY A.id DESC")
    Page<TimeSlotProjection> getSlotsByDateTimeRange(OffsetDateTime startDateTime, OffsetDateTime endDateTime, String retailer,boolean deleted, Pageable paging);

    @Query(value = "Select T from TimeSlots T join T.services s where T.slotStartTime >=:slotStartTime AND T.slotStartTime < :slotEndTime AND s=:service AND T.venue=:venue AND T.retailer=:retailer and T.slotBooked=:booked")
    List<TimeSlotProjection> findBySlotSlotStartTimeServiceVenueAndRetailer(OffsetDateTime slotStartTime, OffsetDateTime slotEndTime, Service service,Venue venue, String retailer,boolean booked);


    @Query("SELECT T.slotStartTime AS slotStartTime, T.slotEndTime AS slotEndTime, T.timeSlotCluster.clusterId AS clusterId, T.attendant.deleted AS attendantDeleted FROM TimeSlots T JOIN T.services s WHERE T.slotStartTime >= :slotStartTime AND T.slotStartTime < :slotEndTime AND s = :service AND T.venue = :venue AND T.retailer = :retailer AND T.slotBooked = :booked")
    List<TimeSlotMinimalProjection> findMinimalBySlotStartTimeAndServiceVenueRetailerBooked(
            @Param("slotStartTime") OffsetDateTime slotStartTime,
            @Param("slotEndTime") OffsetDateTime slotEndTime,
            @Param("service") Service service,
            @Param("venue") Venue venue,
            @Param("retailer") String retailer,
            @Param("booked") boolean booked
    );

    @Query("Select A from TimeSlots A join A.services s  where A.slotDate =:utcAppointmentDate and s=:requiredService and A.venue=:currentVenue and A.retailer=:retailer And A.slotBooked=:booked")
    List<TimeSlotProjection> findByDateServiceVenueAndRetailer(LocalDate utcAppointmentDate, Service requiredService, Venue currentVenue,String retailer,boolean booked);

    @Query("SELECT  t.slotStartTime AS slotStartTime, t.slotEndTime AS slotEndTime, t.timeSlotCluster.clusterId AS clusterId, a.deleted AS attendantDeleted FROM TimeSlots t JOIN t.attendant a JOIN t.services s WHERE t.slotStartTime >= :startDate AND t.slotStartTime < :endDate AND s = :service AND t.venue = :venue AND t.retailer = :retailer AND t.slotBooked = false ")
    List<TimeSlotMinimalProjection> findAllMinimalSlotsInRange(
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("service") Service service,
            @Param("venue") Venue venue,
            @Param("retailer") String retailer
    );


    @Query(
            value = "SELECT subquery.id AS id, " +
                    "       DATE(subquery.slot_start_time + CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute') AS date, " +
                    "       TO_CHAR(subquery.slot_start_time , 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') AS slotStartTime, " +
                    "       TO_CHAR(subquery.slot_end_time , 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') AS slotEndTime, " +
                    "       (subquery.total_count - 5) AS remainingSlots, " +
                    "       subquery.color AS color, " +
                    "       subquery.slot_name AS slotName " +
                    "FROM ( " +
                    "    SELECT ts.id, " +
                    "           ts.slot_start_time, " +
                    "           ts.slot_end_time, " +
                    "           ts.slot_name, " +
                    "           ts.color, " +
                    "           ts.retailer, " +
                    "           COUNT(*) OVER (PARTITION BY DATE(ts.slot_start_time + CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute')) AS total_count, " +
                    "           ROW_NUMBER() OVER (PARTITION BY DATE(ts.slot_start_time + CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute') " +
                    "                              ORDER BY ts.slot_start_time) AS row_num " +
                    "    FROM time_slots ts " +
                    "    WHERE ts.slot_start_time BETWEEN :startDate and :endDate and DATE(slot_start_time + CAST(:offsetDifference AS INTEGER) * INTERVAL '1 minute') = :convertedDate" +
                    "      AND (ts.retailer = :retailer) and ts.deleted=:deleted " +
                    ") subquery " +
                    "WHERE subquery.row_num <= 5 " +
                    "ORDER BY subquery.slot_start_time",
            nativeQuery = true
    )
    List<TimeSlotProjectionOfAmonth> getAllTimeSlotsOfAMonth(
            OffsetDateTime startDate,
            OffsetDateTime endDate,
            String retailer,
            Integer offsetDifference,
            LocalDate convertedDate,
            boolean deleted
    );






    @Query("SELECT DISTINCT t.timeSlotCluster.clusterId FROM TimeSlots t WHERE t.slotDate >= :startDate AND t.retailer = :retailer")
    List<Integer> findClusterIdsForTimeSlots(LocalDate startDate, String retailer);

    @Modifying
    @Transactional
    @Query("DELETE FROM TimeSlots t WHERE t.slotDate >= :startDate AND t.retailer = :retailer")
    int deleteAllFromNovemberOnwards(LocalDate startDate, String retailer);

    @Modifying
    @Transactional
    @Query("Delete from TimeSlots T where T.attendant.attendantId=:attendantId and T.slotDate >= :startDate AND T.retailer = :retailer")
    void deleteByAttendantRetailerAndDate(Integer attendantId, LocalDate startDate, String retailer);


    @Query("SELECT CASE WHEN COUNT(ts) > 0 THEN true ELSE false END " +
            "FROM TimeSlots ts " +
            "WHERE ts.attendant.attendantId = :attendantId " +
            "AND ts.slotStartTime = :slotStartTime " +
            "AND ts.timeSlotCluster.clusterId = :clusterId "+
            "AND ts.retailer=:retailer")
    boolean existsSlotByStartTime(@Param("attendantId") Integer attendantId,
                                  @Param("slotStartTime") OffsetDateTime slotStartTime,
                                  @Param("clusterId") Integer clusterId,
                                  @Param("retailer")String retailer);
  
    @Query("Select T from TimeSlots T where T.retailer=?1")
    List<TimeSlots> findByRetailer(String retailer);


    @Transactional
    @Modifying
    @Query("DELETE FROM TimeSlots t WHERE t.timeSlotCluster = :cluster AND t.attendant.id = :attendantId AND t.slotStartTime >= :fromDateTime")
    void deleteSlotsFromStartTime(@Param("cluster") TimeSlotCluster cluster,
                                  @Param("attendantId") Integer attendantId,
                                  @Param("fromDateTime") OffsetDateTime fromDateTime);

}
