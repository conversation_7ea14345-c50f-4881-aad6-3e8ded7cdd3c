package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.dto.PetShampooDto;
import com.sayone.etailbookit.model.PetShampoo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PetShampooRepository extends JpaRepository<PetShampoo, Integer>, JpaSpecificationExecutor<PetShampoo> {

    PetShampoo findByNameIgnoreCaseAndRetailerAndDeleted(String name, String Retailer,Boolean deleted);

    @Query("Select a from PetShampoo a WHERE a.active=?1 and a.retailer = ?2 and a.deleted=?3 order by a.name")
    Page<PetShampoo> findByActiveAndRetailerAndDeleted(Boolean active, String retailer,Boolean deleted,Pageable paging);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM PetShampoo A where A.retailer = ?1 And deleted = ?2 ")
    Integer getByLastIndexValueByRetailerAndDeleted(String retailer,Boolean deleted);

    @Query(
            "Select S from PetShampoo S where S.retailer = ?1 and S.deleted = ?2 " +
                    "and (?3 is null or lower(S.name) like %?3%)"
    )
    Page<PetShampoo> getPetShampoos(String retailer, Boolean deleted, String search, Pageable paging);

    List<PetShampoo> findByRetailer(String retailer);

    @Query("SELECT s FROM PetShampoo s WHERE LOWER(s.name) LIKE %?1% AND s.retailer = ?2 And s.deleted = false")
    Page<PetShampoo> findByKeyword(String toLowerCase, String retailer, boolean b, Pageable paging);

    @Query("SELECT s FROM PetShampoo s WHERE s.retailer = ?1 And s.deleted = false")
    Page<PetShampoo> getAll(String retailer, boolean b, Pageable paging);

    @Query("Select p from PetShampoo p where LOWER(p.name) LIKE %?1% AND p.active=?2 AND p.retailer=?3 AND p.deleted=?4")
    Page<PetShampoo> findByKeywordActiveRetailerAndDeleted(String lowerCase, boolean active, String retailer, boolean deleted, Pageable paging);
}