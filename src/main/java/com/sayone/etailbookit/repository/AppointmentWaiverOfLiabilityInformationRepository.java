package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.AppointmentWaiverOfLiabilityInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface AppointmentWaiverOfLiabilityInformationRepository extends JpaRepository<AppointmentWaiverOfLiabilityInformation, Integer>, JpaSpecificationExecutor<AppointmentWaiverOfLiabilityInformation> {

   @Transactional
   @Modifying
   @Query("Delete from AppointmentWaiverOfLiabilityInformation A where A.appointment.id=?1 and retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentWaiverOfLiabilityInformation A where A.waiverOfLiability.waiverOfLiabilityId=?1 and retailer=?2")
    void deleteByWaiverId(Integer waiverOfLiabilityId, String retailer);


    List<AppointmentWaiverOfLiabilityInformation> findByRetailer(String retailer);

    @Query("Select A from AppointmentWaiverOfLiabilityInformation A where A.appointment.id=?1 and A.retailer=?2")
    AppointmentWaiverOfLiabilityInformation findByAppointmentId(Integer id, String retailer);
}
