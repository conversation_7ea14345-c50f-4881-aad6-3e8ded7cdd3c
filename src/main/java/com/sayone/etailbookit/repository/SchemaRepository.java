package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Schema;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface  SchemaRepository extends JpaRepository<Schema,Integer> {
    @Query("Select S from Schema S where S.bookitEnabled= ?1")
    List<Schema> findByBookitEnabled(Boolean bookitEnabled);
}
