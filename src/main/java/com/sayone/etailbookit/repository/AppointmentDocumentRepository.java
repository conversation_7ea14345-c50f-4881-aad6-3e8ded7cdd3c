package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.AppointmentDocuments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface AppointmentDocumentRepository extends JpaRepository<AppointmentDocuments, Integer>, JpaSpecificationExecutor<AppointmentDocuments> {
    @Transactional
    @Modifying
    @Query("Delete from AppointmentDocuments A where A.documentOption.documentOptionId = ?1")
    void deleteByDocumnetOption(Integer documentOptionId);
    long countAllByDocumentOptionDocumentOptionId(int documentOptionId);
    @Query(value="SELECT COUNT(*) from appointment_other_doc A where A.other_doc_id = ?1", nativeQuery=true)
    long countAllByDocumentOptionId(Integer documentOptionId);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentDocuments A where A.appointment.id = ?1 and retailer=?2")
    void deleteByAppointmentId(Integer id, String retailer);

    @Query("Select A from AppointmentDocuments A where A.retailer=?1")
    List<AppointmentDocuments> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from AppointmentDocuments A where A.documentOption.documentOptionId = ?1 and A.retailer=?2")
    void deleteByDocumentOptionId(Integer documentOptionId, String retailer);
}
