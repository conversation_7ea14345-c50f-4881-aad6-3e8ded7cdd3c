package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.PetType;
import com.sayone.etailbookit.model.VetInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface VetInformationRepository extends JpaRepository<VetInformation, Integer>, JpaSpecificationExecutor<VetInformation> {
    void deleteByPetType(PetType petType);

    List<VetInformation> findByPetTypePetTypeId(Integer id);

    @Query(value = "SELECT MAX(A.indexValue) as indexId FROM VetInformation A where A.retailer = ?1")
    Integer getByLastIndexValueByRetailer(String retailer);

    @Query("Select V from VetInformation V where V.retailer=?1")
    List<VetInformation> findByRetailer(String retailer);

    @Transactional
    @Modifying
    @Query("Delete from VetInformation V where V.petType.petTypeId=?1 and V.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);
}