package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Scheduler;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

@Repository
public interface SchedulerMessageRepository extends JpaRepository<Scheduler, Integer> {

    Optional<Scheduler> findByStatusAndLocalDateAndSchedulerType(String success, LocalDate now, String appointment);
}
