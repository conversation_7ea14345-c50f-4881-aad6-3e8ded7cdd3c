package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Breed;
import com.sayone.etailbookit.model.PetType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Set;

@Repository
public interface BreedRepository extends JpaRepository<Breed, Integer>, JpaSpecificationExecutor<Breed> {
    void deleteByPetType(PetType petType);

    @Transactional
    @Modifying
    @Query("Delete from Breed T where T.petType.petTypeId=?1 and T.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    List<Breed> findByRetailer(String retailer);

    @Modifying
    @Transactional
    @Query("Update Breed B set B.petType.petTypeId = null where B.id=:breedId")
    void updatePetTypeIdByBreedId(Integer breedId);

    @Modifying
    @Transactional
    @Query("Update Breed B set B.deleted =:deleted where B.id=:breedId")
    void updateDeletedByBreedId(Integer breedId,boolean deleted);

    @Query("Select B from Breed B where B.name=:name and B.petType=:petType and B.retailer=:retailer and B.deleted=:b")
    List<Breed> findByNamePetTypeRetailerAndDeleted(String name,PetType petType, String retailer, boolean b);
}
