package com.sayone.etailbookit.repository;

import com.sayone.etailbookit.model.Venue;
import com.sayone.etailbookit.model.VenuePetTypes;
import com.sayone.etailbookit.projections.VenuePetTypeProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface VenuePetTypesRepository extends JpaRepository<VenuePetTypes, Integer>, JpaSpecificationExecutor<VenuePetTypes> {

    @Query(
            value = "SELECT pet_type_id as petTypeId, id as venuePetTypeId FROM public.venue_pet_types WHERE venue_id = ?1",
            nativeQuery = true
    )
    List<VenuePetTypeProjection> getPetTypes(Integer id);

    void deleteByVenue(Venue venue);

    List<VenuePetTypes> findByVenueVenueId(int venueId);

    @Query("Select V from VenuePetTypes V where V.petType.petTypeId = ?1")
    List<VenuePetTypes> findByPetType(Integer id);

    @Query("Select V from VenuePetTypes V where V.generalPetSizes is empty")
    List<VenuePetTypes> getVenuePetTypesWithoutSize();

    @Transactional
    @Modifying
    @Query(value = "Delete from venue_pet_types_temperaments A where A.temperaments_id = ?1", nativeQuery = true)
    void deleteByTemparament(Integer temperamentsId);

    @Transactional
    @Modifying
    @Query("Delete from VenuePetTypes V where V.venue.id=?1 and V.retailer=?2")
    void deleteByVenueId(Integer venueId, String retailer);

    @Transactional
    @Modifying
    @Query("Delete from VenuePetTypes V where V.petType.petTypeId=?1 and V.retailer=?2")
    void deleteByPetTypeId(Integer petTypeId, String retailer);

    @Query("Select V from VenuePetTypes V where V.retailer=?1")
    List<VenuePetTypes> findByRetailer(String retailer);
}