package com.sayone.etailbookit;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;


@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class ETailBookItApplication extends SpringBootServletInitializer {

	public static void main(String[] args) {
		SpringApplication.run(ETailBookItApplication.class, args);
	}

}