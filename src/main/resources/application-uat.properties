## PostgreSQL
spring.datasource.url=jdbc:postgresql://${DATA_SOURCE}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# Flyway migration
flyway.url=jdbc:postgresql://${DATA_SOURCE_URL}
flyway.user=${DATA_SOURCE_USERNAME}
flyway.password=${DATA_SOURCE_PASSWORD}
spring.flyway.baselineOnMigrate = true

management.endpoint.web.exposure.include = health

spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

eureka.client.serviceUrl.defaultZone  = ${EUREKA_URI}
spring.application.name = bookit

#Sentry error reporting
sentry.dsn=${SENTRY_DSN}

aws.access_key_id = ${AWS_ACCESS_KEY_ID}
aws.secret_access_key = ${AWS_SECRET_ACCESS_KEY}
aws.s3.bucketName = ${AWS_BUCKET_NAME}
aws.s3.region = ${AWS_REGION}
aws.s3.endpoint = ${AWS_END_POINT}

service.type.prefix = service_type
pet.type.prefix = pet_type
pet.photos.prefix = pet_photo
pet.documents.prefix = pet_document
service.photos.prefix = service_photos

## Scheduler
appointment.slot.interval = 15
appointment.lookAheadPeriod = 3

##File Size Limit
 spring.servlet.multipart.max-file-size = 2MB

#quartz
spring.datasource.initialization-mode=always
spring.quartz.job.store.type = JDBC
spring.quartz.propertis.org.quartz.threadPool.threadCount = 1

# SENDGRID CONFIG (SendGridAutoConfiguration)
spring.sendgrid.api-key = ${SENDGRID_API_KEY}

##Ecomm Authentication Keys

ecom.auth.uri = ${ECOMM_API_BASE_URL}
ecom.auth.client.id = ${ECOMM_API_CLIENT_ID}
ecom.auth.client.secret = ${ECOMM_API_CLIENT_SECRET}

##FeignClient Configuration
feign.client.config.pos-api-client.connectTimeout=5000
feign.client.config.pos-api-client.readTimeout=10000
feign.client.config.pos-api-client.loggerLevel=full

##FeignClient URL Configuration
pos.api.feign.url=${FEIGN_CLIENT_URL}

##Set scheduler time
##if schedulerInterval set in env variable then schedule interval will be that value.
##By default, the value is 24 hours
scheduler.timeintervel = ${SCHEDULER_INTERVEL:1440}

#This is done in order to mitigate the zero-day exploit on log4j (Can be removed if log4j has been upgraded to 2.15rc2)
log4j2.formatMsgNoLookups=true

# Kinesis config
aws.kinesis.app-name=${KINESIS_APP_NAME}
aws.kinesis.credentials.accessKey=${KINESIS_CREDENTIAL_ACCESSKEY}
aws.kinesis.credentials.secretKey=${KINESIS_CREDENTIAL_SECRETKEY}
aws.kinesis.region=${KINESIS_REGION}
aws.kinesis.stream=${KINESIS_STREAM}
aws.kinesis.shard.id.servicing=${AWS_KINESIS_SHARD_FOR_SERVICING}
