CREATE TABLE IF NOT EXISTS public.service_add_notes
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    created_at timestamp without time zone,
    modified_at timestamp without time zone,
    retailer character varying(255) COLLATE pg_catalog."default",
    value character varying(255) COLLATE pg_catalog."default",
    service_history_id integer,
    appointment_id integer,
    CONSTRAINT service_add_notes_pkey PRIMARY KEY (id),
    CONSTRAINT fk44u3wxg55c0lhun3g0n0xs8pq FOREIGN KEY (service_history_id)
        REFERENCES public.service_history (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fksvx1b0ynfmfwyvw1jg4kd3t3s FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.appointment_add_notes
(
    appointment_id integer NOT NULL,
    add_notes_id integer NOT NULL,
    CONSTRAINT uk_3vw57xeyj0x86nh58h7x9svh9 UNIQUE (add_notes_id),
    CONSTRAINT fkhe3mmw9ig5yq5lyqiun3elw0x FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkni8xh6te2fxss39ivj2souw59 FOREIGN KEY (add_notes_id)
        REFERENCES public.service_add_notes (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.blockdates
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    block_date date,
    created_at timestamp without time zone,
    modified_at timestamp without time zone,
    retailer character varying(255) COLLATE pg_catalog."default",
    CONSTRAINT blockdates_pkey PRIMARY KEY (id)
);