CREATE TABLE IF NOT EXISTS public.vacation
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    created_at timestamp without time zone,
    deleted boolean,
    modified_at timestamp without time zone,
    retailer character varying(255) COLLATE pg_catalog."default",
    vacation_end_date date,
    vacation_end_time timestamp without time zone,
    vacation_start_date date,
    vacation_start_time timestamp without time zone,
    attendant_id integer,
    CONSTRAINT vacation_pkey PRIMARY KEY (id),
    CONSTRAINT fk8n4531kq38k03ihf4pm8e0qkl FOREIGN KEY (attendant_id)
        REFERENCES public.attendant (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)