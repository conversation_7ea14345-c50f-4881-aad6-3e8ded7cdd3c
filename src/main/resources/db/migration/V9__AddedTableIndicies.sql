CREATE INDEX CONCURRENTLY addon_service_retailer
    ON public.addon_service USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY cologne_retailer
    ON public.pet_cologne USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY shampoo_retailer
    ON public.pet_shampoo USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY personality_parameter_retailer
    ON public.personality_parameter USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY configuration_retailer
    ON public.configuration USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY general_pet_size_retailer
    ON public.general_pet_size USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_type_retailer
    ON public.service_type USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_type_retailer
    ON public.pet_type USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_retailer
    ON public.pet USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_retailer
    ON public.service USING btree
    (retailer COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_type_config_pet_type_id
    ON public.pet_type_config USING btree
    (pet_type_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY document_option_pet_type_id
    ON public.document_option USING btree
    (pet_type_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY vet_information_pet_type_id
    ON public.vet_information USING btree
    (pet_type_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY vaccination_record_pet_type_id
    ON public.vaccination_record USING btree
    (pet_type_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY hair_texture_pet_type_id
    ON public.hair_texture USING btree
    (pet_type_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY temperament_pet_type_id
    ON public.temperament USING btree
    (pet_type_id ASC NULLS LAST)
    TABLESPACE pg_default;
--
--CREATE INDEX CONCURRENTLY threat_reaction_pet_id
--    ON public.threat_reaction USING btree
--    (pet_id ASC NULLS LAST)
--    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_documents_pet_id
    ON public.pet_documents USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_vet_information_pet_id
    ON public.pet_vet_information USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_vaccination_records_pet_id
    ON public.pet_vaccination_records USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_emergency_contact_info_pet_id
    ON public.pet_emergency_contact_info USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_unfriendly_behaviour_triggers_pet_id
    ON public.pet_unfriendly_behaviour_triggers USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_personality_parameters_pet_id
    ON public.pet_personality_parameters USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_threat_reactions_pet_id
    ON public.pet_threat_reactions USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_biting_histories_pet_id
    ON public.pet_biting_histories USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_allergies_pet_id
    ON public.pet_allergies USING btree
    (pet_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_biting_histories_biting_histories_id
    ON public.pet_biting_histories USING btree
    (biting_histories_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY pet_vaccination_records_vaccination_record_id
    ON public.pet_vaccination_records USING btree
    (vaccination_record_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_pet_types_attendant_id
    ON public.attendant_pet_types USING btree
    (attendant_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_pet_types_temperaments_temperaments_id
    ON public.attendant_pet_types_temperaments USING btree
    (temperaments_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_pet_types_temperaments_attendant_pet_types_id
    ON public.attendant_pet_types_temperaments USING btree
    (attendant_pet_types_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_pet_types_general_pet_sizes_general_pet_sizes_id
    ON public.attendant_pet_types_general_pet_sizes USING btree
    (general_pet_sizes_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_pet_types_general_pet_sizes_attendant_pet_types_id
    ON public.attendant_pet_types_general_pet_sizes USING btree
    (attendant_pet_types_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY venue_pet_types_venue_id
    ON public.venue_pet_types USING btree
    (venue_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY venue_pet_types_general_pet_sizes_general_pet_sizes_id
    ON public.venue_pet_types_general_pet_sizes USING btree
    (general_pet_sizes_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY venue_pet_types_general_pet_sizes_venue_pet_types_id
    ON public.venue_pet_types_general_pet_sizes USING btree
    (venue_pet_types_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY venue_pet_types_temperaments_temperaments_id
    ON public.venue_pet_types_temperaments USING btree
    (temperaments_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY venue_pet_types_temperaments_venue_pet_types_id
    ON public.venue_pet_types_temperaments USING btree
    (venue_pet_types_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_addon_services_addon_services_id
    ON public.service_addon_services USING btree
    (addon_services_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_addon_services_services_id
    ON public.service_addon_services USING btree
    (services_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_attendants_services_id
    ON public.service_attendants USING btree
    (services_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_attendants_attendants_id
    ON public.service_attendants USING btree
    (attendants_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_service_types_service_types_id
    ON public.attendant_service_types USING btree
    (service_types_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY attendant_service_types_attendants_id
    ON public.attendant_service_types USING btree
    (attendants_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_venues_venues_id
    ON public.service_venues USING btree
    (venues_id ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE INDEX CONCURRENTLY service_available_participant_documents_available_participant_documents_id
    ON public.service_available_participant_documents USING btree
    (available_participant_documents_id ASC NULLS LAST)
    TABLESPACE pg_default;

