
CREATE TABLE public.addon_service (
    id integer NOT NULL,
    active boolean,
    is_taxable boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    tack_on_extra_amount numeric(19,2),
    tack_on_extra_amount_currency character varying(255),
    tack_on_extra_minutes integer
);

CREATE TABLE public.appointment (
    id integer NOT NULL,
    address_id integer,
    amount numeric(19,2),
    amount_currency character varying(255),
    appoinment_no character varying(255),
    bring_your_food boolean NOT NULL,
    customer_id integer,
    appointment_date date NOT NULL,
    date_time_onboarding date,
    discount_amount numeric(19,2),
    feeding_count integer,
    grain_free_recipes text[],
    grain_full_recipes text[],
    have_sibilings boolean NOT NULL,
    order_reference bigint,
    overnights integer,
    payment_status character varying(255),
    recurring_enabled boolean NOT NULL,
    retailer character varying(255),
    service_end_at timestamp with time zone,
    service_start_at timestamp with time zone,
    service_status integer,
    sibiling_count integer,
    appointment_time time without time zone NOT NULL,
    tip_amount numeric(19,2),
    tip_amount_currency character varying(255),
    variable_schedule_duration integer,
    attendant integer,
    cologne integer,
    pet integer,
    service integer,
    service_type integer,
    shamppo integer,
    venue integer
);

CREATE TABLE public.appointment_addon_service (
    appointment_id integer NOT NULL,
    addon_service_id integer NOT NULL
);

CREATE TABLE public.appointment_desired_hair_length (
    id integer NOT NULL,
    retailer character varying(255),
    value integer,
    appointment_id integer,
    hair_length_id integer
);

CREATE TABLE public.allergies (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.appoinement_allergy_option (
    appointment_id integer NOT NULL,
    allergy_option_id integer NOT NULL
);

CREATE TABLE public.appointment_document_option (
    appointment_id integer NOT NULL,
    document_option_id integer NOT NULL
);

CREATE TABLE public.appointment_vaccination_record (
    appointment_id integer NOT NULL,
    vaccination_record_id integer NOT NULL
);

CREATE TABLE public.attendant (
    id integer NOT NULL,
    active boolean,
    amount_currency character varying(255),
    amount_per_event numeric(19,2),
    amount_per_hour numeric(19,2),
    availability_interval character varying(255),
    availability_interval_unit character varying(255),
    capacity_limit integer,
    email character varying(255),
    first_name character varying(255),
    last_name character varying(255),
    phone_no character varying(255),
    preferred_communications text[],
    retailer character varying(255),
    revenue_percent integer,
    revenue_share boolean,
    tip_eligible boolean,
    title character varying(255)
);

CREATE TABLE public.attendant_addon_service_type (
    attendant_id integer NOT NULL,
    addon_service_type_id integer NOT NULL
);

CREATE TABLE public.attendant_availability (
    id integer NOT NULL,
    availability_close_time time without time zone,
    availability_open_time time without time zone,
    available_day character varying(255),
    attendant_id integer NOT NULL
);

CREATE TABLE public.attendant_capacity_pet_type (
    attendant_id integer NOT NULL,
    capacity_pet_type_id integer NOT NULL
);

CREATE TABLE public.attendant_pet_sizes (
    attendant_pet_type_id integer NOT NULL,
    general_pet_size_id integer NOT NULL
);

CREATE TABLE public.attendant_pet_types (
    id integer NOT NULL,
    retailer character varying(255),
    attendant_id integer NOT NULL,
    pet_type_id integer NOT NULL
);

CREATE TABLE public.attendant_service_type (
    attendant_id integer NOT NULL,
    service_type_id integer NOT NULL
);

CREATE TABLE public.attendant_temperament (
    attendant_pet_type_id integer NOT NULL,
    temperament_id integer NOT NULL
);

CREATE TABLE public.bitting_history (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.blades (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.combs (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.configuration (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.desired_hair_length (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.document_option (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    requires_description boolean,
    requires_upload boolean,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.emergency_contact_info (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.feeding_information (
    id integer NOT NULL,
    amount_currency character varying(255),
    amount_per_extra_cup numeric(19,2),
    display boolean,
    feed_count integer,
    grain_free_recipes text[],
    grain_full_recipes text[],
    retailer character varying(255)
);

CREATE TABLE public.general_pet_size (
    id integer NOT NULL,
    retailer character varying(255),
    size character varying(255) NOT NULL,
    weight_unit character varying(255),
    weight_value integer
);

CREATE TABLE public.hair_length (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.hair_texture (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.personality_parameter (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.pet (
    id integer NOT NULL,
    allergies_text character varying(255),
    bring_food boolean,
    color character varying(255),
    customer_id integer,
    decease_date character varying(255),
    dob date,
    exact_weight numeric(19,2),
    feed_count integer,
    grain_free_recipes text[],
    grain_full_recipes text[],
    name character varying(255),
    photos text[],
    retailer character varying(255),
    sex character varying(255),
    spayed boolean,
    weight_unit character varying(255) DEFAULT 'lb'::character varying,
    hair_length_id integer,
    hair_texture_id integer,
    pet_type_id integer,
    temperament_id integer,
    weight_range_id integer
);

CREATE TABLE public.pet_allergies (
    pet_id integer NOT NULL,
    allergy_id integer NOT NULL
);

CREATE TABLE public.pet_biting_history (
    pet_id integer NOT NULL,
    biting_history_id integer NOT NULL
);

CREATE TABLE public.pet_cologne (
    id integer NOT NULL,
    active boolean NOT NULL,
    extra_charge double precision,
    extra_currency character varying(255),
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.pet_documents (
    id integer NOT NULL,
    description character varying(255),
    file character varying(255),
    retailer character varying(255),
    document_option_id integer NOT NULL,
    pet_id integer NOT NULL
);

CREATE TABLE public.pet_emergency_contact_info (
    id integer NOT NULL,
    retailer character varying(255),
    value character varying(255) NOT NULL,
    emergency_contact_id integer NOT NULL,
    pet_id integer NOT NULL
);

CREATE TABLE public.pet_personality_parameters (
    pet_id integer NOT NULL,
    personality_parameter_id integer NOT NULL
);

CREATE TABLE public.pet_shampoo (
    id integer NOT NULL,
    active boolean NOT NULL,
    extra_charge double precision,
    extra_currency character varying(255),
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.pet_threat_reaction (
    pet_id integer NOT NULL,
    threat_reaction_id integer NOT NULL
);

CREATE TABLE public.pet_type (
    id integer NOT NULL,
    active boolean,
    file_url character varying(255),
    name character varying(255),
    retailer character varying(255)
);

CREATE TABLE public.pet_type_config (
    id integer NOT NULL,
    display_type integer NOT NULL,
    name integer NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.pet_unfriendly_behaviour_trigger (
    pet_id integer NOT NULL,
    unfriendly_behaviour_trigger_id integer NOT NULL
);

CREATE TABLE public.pet_vaccination_records (
    id integer NOT NULL,
    date_administrated date NOT NULL,
    date_expires date NOT NULL,
    retailer character varying(255),
    pet_id integer NOT NULL,
    vaccination_record_id integer NOT NULL
);

CREATE TABLE public.pet_vet_information (
    id integer NOT NULL,
    retailer character varying(255),
    value character varying(255) NOT NULL,
    pet_id integer NOT NULL,
    vet_information_id integer NOT NULL
);

CREATE TABLE public.service (
    id integer NOT NULL,
    amount_per_event numeric(19,2),
    amount_currency character varying(255),
    availability_interval character varying(255),
    availability_interval_unit character varying(255),
    available_participant_allergies boolean,
    available_participant_temperaments boolean,
    available_participant_weights boolean,
    cancelation_amount_type character varying(255) NOT NULL,
    cancelation_amount_value numeric(19,2),
    cancelation_buffer_unit character varying(255) NOT NULL,
    cancelation_buffer_value integer,
    charge_cancelation_fee boolean,
    deposit_amount_type character varying(255) NOT NULL,
    deposit_amount_value numeric(19,2),
    enable_late_onboarding_config boolean,
    fixed_schedule_unit character varying(255),
    fixed_schedule_value integer,
    include_report_card_general_notes boolean,
    include_report_card_peed_times boolean,
    include_report_card_poop_description boolean,
    include_report_card_pooped_times boolean,
    include_report_card_times_fed boolean,
    include_report_card_water_bowl_filled boolean,
    is_active boolean,
    is_service_late_discount_allowed boolean,
    is_sibling_discount_allowed boolean,
    is_taxable boolean,
    is_tips_allowed boolean,
    late_onboarding_charge_interval character varying(255) NOT NULL,
    late_onboarding_days integer,
    late_onboarding_hourly_charge numeric(19,2),
    max_overnights integer,
    minimum_fee numeric(19,2),
    name character varying(255),
    need_deposit_at_booking boolean,
    offer_route_tracking boolean,
    payment_after_service_completed boolean,
    payment_at_beginning_of_service boolean,
    payment_at_time_of_booking boolean,
    petparent_can_select_attendant boolean,
    petparent_can_select_datetime boolean,
    petparent_can_select_recurring_options boolean,
    post_buffer_mins integer,
    pre_booking_unit character varying(255),
    pre_booking_value numeric(19,2),
    pre_buffer_mins integer,
    require_biting_info boolean,
    require_cologne_used_post_service boolean,
    require_combs_and_blades_used_post_service boolean,
    require_credit_card_on_file boolean,
    require_food_fed_post_service boolean,
    require_hair_length_info boolean,
    require_notes boolean,
    require_notes_post_service boolean,
    require_participant_pet_name boolean,
    require_participant_pet_type boolean,
    require_pet_behaviour boolean,
    require_pet_personality boolean,
    require_shampoo_used_post_service boolean,
    require_vet_info boolean,
    retailer character varying(255),
    schedule_type character varying(255) NOT NULL,
    send_picture_to_pet_parent_post_service boolean,
    send_report_card boolean,
    service_late_discount_days integer,
    service_late_discount_percent numeric(19,2),
    service_unit character varying(255) NOT NULL,
    sibling_discount_type character varying(255) NOT NULL,
    sibling_discount_value numeric(19,2),
    variable_schedule_max_value character varying(255),
    variable_schedule_min_value character varying(255),
    pet_type integer,
    service_type integer
);

CREATE TABLE public.service_addon_service (
    service_id integer NOT NULL,
    addon_service_id integer NOT NULL
);

CREATE TABLE public.service_attendants (
    service_id integer NOT NULL,
    attendants_id integer NOT NULL
);

CREATE TABLE public.service_availability (
    id integer NOT NULL,
    availability_close_time time without time zone,
    availability_open_time time without time zone,
    available_day character varying(255),
    service_id integer NOT NULL
);

CREATE TABLE public.service_document_option (
    service_id integer NOT NULL,
    document_option_id integer NOT NULL
);

CREATE TABLE public.service_general_sizes (
    service_id integer NOT NULL,
    general_pet_size_id integer NOT NULL
);

CREATE TABLE public.service_history (
    id integer NOT NULL,
    actual_over_nights integer,
    actual_variable_schedule_duration integer,
    cup_fed_per_time integer,
    food_fed character varying(255),
    is_filled_water_bowl character varying(255),
    notes character varying(255),
    photos text[],
    pictures_sent boolean,
    poop_description character varying(255),
    retailer character varying(255),
    times_fed integer,
    times_peed integer,
    times_pooped integer,
    appointment_id integer NOT NULL,
    blade_id integer,
    comb_id integer,
    cologne_id integer,
    shampoo_id integer
);

CREATE TABLE public.service_type (
    id integer NOT NULL,
    active boolean NOT NULL,
    file_url character varying(255),
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.service_vaccination_record (
    service_id integer NOT NULL,
    vaccination_record_id integer NOT NULL
);

CREATE TABLE public.service_venu (
    service_id integer NOT NULL,
    venu_id integer NOT NULL
);

CREATE TABLE public.temperament (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.threat_reaction (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.unfriendly_behaviour_trigger (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    retailer character varying(255)
);

CREATE TABLE public.vaccination_record (
    id integer NOT NULL,
    active boolean,
    name character varying(255) NOT NULL,
    requires_date_administrated boolean,
    requires_date_expires boolean,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.venue (
    id integer NOT NULL,
    active boolean,
    availability_interval character varying(255),
    availability_interval_unit character varying(255),
    ecommerce_store_id integer,
    extra_charge numeric(19,2),
    extra_currency character varying(255),
    internal_name character varying(255),
    location_type character varying(255),
    mobile_zip_code text[],
    participant_limit_service integer,
    public_name character varying(255),
    retailer character varying(255),
    support_simultaneous_bookings boolean,
    location_address_id integer
);

CREATE TABLE public.venue_address (
    id integer NOT NULL,
    city character varying(255),
    country character varying(255),
    retailer character varying(255),
    state character varying(255),
    street1 character varying(255),
    street2 character varying(255),
    zipcode character varying(255)
);

CREATE TABLE public.venue_availability (
    id integer NOT NULL,
    availability_close_time time without time zone,
    availability_open_time time without time zone,
    available_day character varying(255),
    retailer character varying(255),
    venue_id integer NOT NULL
);

CREATE TABLE public.venue_pet_sizes (
    venue_pet_type_id integer NOT NULL,
    general_pet_size_id integer NOT NULL
);

CREATE TABLE public.venue_pet_types (
    id integer NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL,
    venue_id integer NOT NULL
);

CREATE TABLE public.venue_service_type (
    venue_id integer NOT NULL,
    service_type_id integer NOT NULL
);

CREATE TABLE public.venue_temperament (
    venue_pet_type_id integer NOT NULL,
    temperament_id integer NOT NULL
);

CREATE TABLE public.vet_information (
    id integer NOT NULL,
    active boolean NOT NULL,
    name character varying(255) NOT NULL,
    retailer character varying(255),
    pet_type_id integer NOT NULL
);

CREATE TABLE public.weight_range (
    id integer NOT NULL,
    max_value numeric(19,2) NOT NULL,
    min_value numeric(19,2) NOT NULL,
    retailer character varying(255),
    weight_unit character varying(255) DEFAULT 'lb'::character varying,
    pet_type_id integer NOT NULL
);

ALTER TABLE public.addon_service ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.addon_service_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.allergies ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.allergies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.appointment_desired_hair_length ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.appointment_desired_hair_length_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.appointment ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.appointment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.attendant_availability ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.attendant_availability_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.attendant ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.attendant_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.attendant_pet_types ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.attendant_pet_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.bitting_history ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.bitting_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.blades ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.blades_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.combs ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.combs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.configuration ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.configuration_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.desired_hair_length ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.desired_hair_length_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.document_option ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.document_option_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.emergency_contact_info ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.emergency_contact_info_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.feeding_information ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.feeding_information_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.general_pet_size ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.general_pet_size_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.hair_length ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.hair_length_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.hair_texture ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.hair_texture_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.personality_parameter ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.personality_parameter_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_cologne ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_cologne_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_documents ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_emergency_contact_info ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_emergency_contact_info_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_shampoo ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_shampoo_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_type_config ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_type_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_type ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_vaccination_records ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_vaccination_records_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.pet_vet_information ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.pet_vet_information_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.service_availability ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.service_availability_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.service_history ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.service_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.service ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.service_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.service_type ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.service_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.temperament ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.temperament_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.threat_reaction ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.threat_reaction_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.unfriendly_behaviour_trigger ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.unfriendly_behaviour_trigger_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.vaccination_record ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.vaccination_record_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.venue_address ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.venue_address_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.venue_availability ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.venue_availability_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.venue ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.venue_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.venue_pet_types ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.venue_pet_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.vet_information ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.vet_information_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE public.weight_range ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.weight_range_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

ALTER TABLE ONLY public.addon_service
    ADD CONSTRAINT addon_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.allergies
    ADD CONSTRAINT allergies_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.appoinement_allergy_option
    ADD CONSTRAINT appoinement_allergy_option_pkey PRIMARY KEY (appointment_id, allergy_option_id);

ALTER TABLE ONLY public.appointment_addon_service
    ADD CONSTRAINT appointment_addon_service_pkey PRIMARY KEY (appointment_id, addon_service_id);

ALTER TABLE ONLY public.appointment_desired_hair_length
    ADD CONSTRAINT appointment_desired_hair_length_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.appointment_document_option
    ADD CONSTRAINT appointment_document_option_pkey PRIMARY KEY (appointment_id, document_option_id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT appointment_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.appointment_vaccination_record
    ADD CONSTRAINT appointment_vaccination_record_pkey PRIMARY KEY (appointment_id, vaccination_record_id);

ALTER TABLE ONLY public.attendant_addon_service_type
    ADD CONSTRAINT attendant_addon_service_type_pkey PRIMARY KEY (attendant_id, addon_service_type_id);

ALTER TABLE ONLY public.attendant_availability
    ADD CONSTRAINT attendant_availability_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.attendant_capacity_pet_type
    ADD CONSTRAINT attendant_capacity_pet_type_pkey PRIMARY KEY (attendant_id, capacity_pet_type_id);

ALTER TABLE ONLY public.attendant_pet_sizes
    ADD CONSTRAINT attendant_pet_sizes_pkey PRIMARY KEY (attendant_pet_type_id, general_pet_size_id);

ALTER TABLE ONLY public.attendant_pet_types
    ADD CONSTRAINT attendant_pet_types_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.attendant
    ADD CONSTRAINT attendant_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.attendant_service_type
    ADD CONSTRAINT attendant_service_type_pkey PRIMARY KEY (attendant_id, service_type_id);

ALTER TABLE ONLY public.attendant_temperament
    ADD CONSTRAINT attendant_temperament_pkey PRIMARY KEY (attendant_pet_type_id, temperament_id);

ALTER TABLE ONLY public.bitting_history
    ADD CONSTRAINT bitting_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.blades
    ADD CONSTRAINT blades_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.combs
    ADD CONSTRAINT combs_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.configuration
    ADD CONSTRAINT configuration_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.desired_hair_length
    ADD CONSTRAINT desired_hair_length_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.document_option
    ADD CONSTRAINT document_option_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.emergency_contact_info
    ADD CONSTRAINT emergency_contact_info_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.feeding_information
    ADD CONSTRAINT feeding_information_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.general_pet_size
    ADD CONSTRAINT general_pet_size_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.hair_length
    ADD CONSTRAINT hair_length_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.hair_texture
    ADD CONSTRAINT hair_texture_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.personality_parameter
    ADD CONSTRAINT personality_parameter_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_allergies
    ADD CONSTRAINT pet_allergies_pkey PRIMARY KEY (pet_id, allergy_id);

ALTER TABLE ONLY public.pet_biting_history
    ADD CONSTRAINT pet_biting_history_pkey PRIMARY KEY (pet_id, biting_history_id);

ALTER TABLE ONLY public.pet_cologne
    ADD CONSTRAINT pet_cologne_pkey PRIMARY KEY (id);


ALTER TABLE ONLY public.pet_documents
    ADD CONSTRAINT pet_documents_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_emergency_contact_info
    ADD CONSTRAINT pet_emergency_contact_info_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_personality_parameters
    ADD CONSTRAINT pet_personality_parameters_pkey PRIMARY KEY (pet_id, personality_parameter_id);

ALTER TABLE ONLY public.pet
    ADD CONSTRAINT pet_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_shampoo
    ADD CONSTRAINT pet_shampoo_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_threat_reaction
    ADD CONSTRAINT pet_threat_reaction_pkey PRIMARY KEY (pet_id, threat_reaction_id);

ALTER TABLE ONLY public.pet_type_config
    ADD CONSTRAINT pet_type_config_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_type
    ADD CONSTRAINT pet_type_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_unfriendly_behaviour_trigger
    ADD CONSTRAINT pet_unfriendly_behaviour_trigger_pkey PRIMARY KEY (pet_id, unfriendly_behaviour_trigger_id);

ALTER TABLE ONLY public.pet_vaccination_records
    ADD CONSTRAINT pet_vaccination_records_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pet_vet_information
    ADD CONSTRAINT pet_vet_information_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.service_addon_service
    ADD CONSTRAINT service_addon_service_pkey PRIMARY KEY (service_id, addon_service_id);

ALTER TABLE ONLY public.service_attendants
    ADD CONSTRAINT service_attendants_pkey PRIMARY KEY (service_id, attendants_id);

ALTER TABLE ONLY public.service_availability
    ADD CONSTRAINT service_availability_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.service_document_option
    ADD CONSTRAINT service_document_option_pkey PRIMARY KEY (service_id, document_option_id);

ALTER TABLE ONLY public.service_general_sizes
    ADD CONSTRAINT service_general_sizes_pkey PRIMARY KEY (service_id, general_pet_size_id);

ALTER TABLE ONLY public.service_history
    ADD CONSTRAINT service_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.service
    ADD CONSTRAINT service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.service_type
    ADD CONSTRAINT service_type_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.service_vaccination_record
    ADD CONSTRAINT service_vaccination_record_pkey PRIMARY KEY (service_id, vaccination_record_id);

ALTER TABLE ONLY public.service_venu
    ADD CONSTRAINT service_venu_pkey PRIMARY KEY (service_id, venu_id);

ALTER TABLE ONLY public.temperament
    ADD CONSTRAINT temperament_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.threat_reaction
    ADD CONSTRAINT threat_reaction_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.unfriendly_behaviour_trigger
    ADD CONSTRAINT unfriendly_behaviour_trigger_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.vaccination_record
    ADD CONSTRAINT vaccination_record_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.venue_address
    ADD CONSTRAINT venue_address_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.venue_availability
    ADD CONSTRAINT venue_availability_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.venue_pet_sizes
    ADD CONSTRAINT venue_pet_sizes_pkey PRIMARY KEY (venue_pet_type_id, general_pet_size_id);

ALTER TABLE ONLY public.venue_pet_types
    ADD CONSTRAINT venue_pet_types_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.venue
    ADD CONSTRAINT venue_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.venue_service_type
    ADD CONSTRAINT venue_service_type_pkey PRIMARY KEY (venue_id, service_type_id);

ALTER TABLE ONLY public.venue_temperament
    ADD CONSTRAINT venue_temperament_pkey PRIMARY KEY (venue_pet_type_id, temperament_id);

ALTER TABLE ONLY public.vet_information
    ADD CONSTRAINT vet_information_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.weight_range
    ADD CONSTRAINT weight_range_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.venue_temperament
    ADD CONSTRAINT fk1156w32jkgct4wiwokw7em8e6 FOREIGN KEY (venue_pet_type_id) REFERENCES public.venue_pet_types(id);

ALTER TABLE ONLY public.venue_service_type
    ADD CONSTRAINT fk19j8yao75701ygu8airak7sn2 FOREIGN KEY (service_type_id) REFERENCES public.service_type(id);

ALTER TABLE ONLY public.attendant_addon_service_type
    ADD CONSTRAINT fk1euonur68v7aq6ir7kokhh4co FOREIGN KEY (attendant_id) REFERENCES public.attendant(id);

ALTER TABLE ONLY public.service_general_sizes
    ADD CONSTRAINT fk2ud7eqrnwkx2hmpqxrlsq07yc FOREIGN KEY (general_pet_size_id) REFERENCES public.general_pet_size(id);

ALTER TABLE ONLY public.pet_vaccination_records
    ADD CONSTRAINT fk2yq2s9ja2t93unwyu8vt2er8x FOREIGN KEY (pet_id) REFERENCES public.pet(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet_unfriendly_behaviour_trigger
    ADD CONSTRAINT fk3cph4env99n3w26tsg8es0brj FOREIGN KEY (pet_id) REFERENCES public.pet(id);

ALTER TABLE ONLY public.service_document_option
    ADD CONSTRAINT fk3fcvyc4tt1cs0jtrb4dm9d098 FOREIGN KEY (document_option_id) REFERENCES public.document_option(id);

ALTER TABLE ONLY public.attendant_pet_sizes
    ADD CONSTRAINT fk3m427v20wiuwekuptjw1jg64x FOREIGN KEY (attendant_pet_type_id) REFERENCES public.attendant_pet_types(id);

ALTER TABLE ONLY public.attendant_pet_sizes
    ADD CONSTRAINT fk418y9gf71t5cqhl5o6nne57g1 FOREIGN KEY (general_pet_size_id) REFERENCES public.general_pet_size(id);

ALTER TABLE ONLY public.service_attendants
    ADD CONSTRAINT fk4memxpb69jxh4nf4pqvb1awjh FOREIGN KEY (service_id) REFERENCES public.service(id);

ALTER TABLE ONLY public.vet_information
    ADD CONSTRAINT fk4o49xwih36a43cjr3hpewi46n FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet_emergency_contact_info
    ADD CONSTRAINT fk4qnmlqwg6nt1w3ev8h9puopot FOREIGN KEY (pet_id) REFERENCES public.pet(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.service_general_sizes
    ADD CONSTRAINT fk56sk5u9jnrvcmkvo1i617yexk FOREIGN KEY (service_id) REFERENCES public.service(id);

ALTER TABLE ONLY public.service_history
    ADD CONSTRAINT fk5jw9plulmg7lkq0d7hmstymud FOREIGN KEY (shampoo_id) REFERENCES public.pet_shampoo(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fk5kn9jxdjw17ta1bo94vhx28q FOREIGN KEY (attendant) REFERENCES public.attendant(id);

ALTER TABLE ONLY public.pet_allergies
    ADD CONSTRAINT fk5q8sif78kgtha4kslonf8yx5b FOREIGN KEY (pet_id) REFERENCES public.pet(id);

ALTER TABLE ONLY public.pet
    ADD CONSTRAINT fk5ycbmtoe3wfv3abryk4nklqjo FOREIGN KEY (hair_length_id) REFERENCES public.hair_length(id);

ALTER TABLE ONLY public.appointment_addon_service
    ADD CONSTRAINT fk6802cqr6wiropal4im3o58mtr FOREIGN KEY (addon_service_id) REFERENCES public.addon_service(id);

ALTER TABLE ONLY public.attendant_pet_types
    ADD CONSTRAINT fk78k6k8krthrd591r5wvum6kmg FOREIGN KEY (attendant_id) REFERENCES public.attendant(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fk7bdq9q0hepelcvm0cbig1sid FOREIGN KEY (service_type) REFERENCES public.service_type(id);

ALTER TABLE ONLY public.venue_availability
    ADD CONSTRAINT fk7gc5mkba547jky2mfq55c67r3 FOREIGN KEY (venue_id) REFERENCES public.venue(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.hair_length
    ADD CONSTRAINT fk81ywl57qwnrp0x1itbtexhokh FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.service_availability
    ADD CONSTRAINT fk8optf13n2hi2ayrjobv3afau0 FOREIGN KEY (service_id) REFERENCES public.service(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.attendant_availability
    ADD CONSTRAINT fk8otoa3p1wajpxa1m03hmhask2 FOREIGN KEY (attendant_id) REFERENCES public.attendant(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.blades
    ADD CONSTRAINT fk8sfbefmyrwcv3bnen9u2j0gih FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet
    ADD CONSTRAINT fk90n4dnhkhbj08rg212muspkor FOREIGN KEY (hair_texture_id) REFERENCES public.hair_texture(id);

ALTER TABLE ONLY public.temperament
    ADD CONSTRAINT fk941628mx2mxpfmd922w17on9b FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.venue_pet_sizes
    ADD CONSTRAINT fk9gk5dups9ou4u2x13d8ydwtwt FOREIGN KEY (general_pet_size_id) REFERENCES public.general_pet_size(id);

ALTER TABLE ONLY public.appoinement_allergy_option
    ADD CONSTRAINT fk9i8k7d712o2awb68ehmjo09dp FOREIGN KEY (allergy_option_id) REFERENCES public.allergies(id);

ALTER TABLE ONLY public.hair_texture
    ADD CONSTRAINT fk9iurkw7ij7eyutg4unaawmb5g FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet_personality_parameters
    ADD CONSTRAINT fk9rq54j1dokxtmw5ru41dahdiy FOREIGN KEY (pet_id) REFERENCES public.pet(id);

ALTER TABLE ONLY public.pet_vet_information
    ADD CONSTRAINT fk9y784akffd2vbja51xj5ogt1f FOREIGN KEY (vet_information_id) REFERENCES public.vet_information(id);

ALTER TABLE ONLY public.attendant_pet_types
    ADD CONSTRAINT fka03pnld1yca7p4qw12eeocreo FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id);

ALTER TABLE ONLY public.venue_pet_sizes
    ADD CONSTRAINT fkaehkvx16i1rb4s65wjusws87w FOREIGN KEY (venue_pet_type_id) REFERENCES public.venue_pet_types(id);

ALTER TABLE ONLY public.venue_pet_types
    ADD CONSTRAINT fkatx49u9do134tmdob7h150epa FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fkavwjt639v0g8dasupre0ne7e2 FOREIGN KEY (service) REFERENCES public.service(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fkbfslkvv528p1wrm4t562jmlfb FOREIGN KEY (shamppo) REFERENCES public.pet_shampoo(id);

ALTER TABLE ONLY public.service_vaccination_record
    ADD CONSTRAINT fkbrkt5aaxp4clddntftrja94j7 FOREIGN KEY (service_id) REFERENCES public.service(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fkbx4mmq5joo6s2eyxdtn7se6i2 FOREIGN KEY (cologne) REFERENCES public.pet_cologne(id);

ALTER TABLE ONLY public.pet_documents
    ADD CONSTRAINT fkc00hru13n418o5a8c68px1exy FOREIGN KEY (pet_id) REFERENCES public.pet(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet
    ADD CONSTRAINT fkc1u0f9npdkvdswepubsjkdb5f FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id);

ALTER TABLE ONLY public.service_addon_service
    ADD CONSTRAINT fkcmbwkipp10g3ggpuy6ku6m6aa FOREIGN KEY (service_id) REFERENCES public.service(id);

ALTER TABLE ONLY public.pet_unfriendly_behaviour_trigger
    ADD CONSTRAINT fkd20g28f5wop5p2w8skijh3s5b FOREIGN KEY (unfriendly_behaviour_trigger_id) REFERENCES public.unfriendly_behaviour_trigger(id);

ALTER TABLE ONLY public.venue_service_type
    ADD CONSTRAINT fkdckk4202pxk9avk5wbu4qrfod FOREIGN KEY (venue_id) REFERENCES public.venue(id);

ALTER TABLE ONLY public.appointment_document_option
    ADD CONSTRAINT fkdsnj9htjswdrvrrpaiex7i1pk FOREIGN KEY (document_option_id) REFERENCES public.document_option(id);

ALTER TABLE ONLY public.combs
    ADD CONSTRAINT fkdx2y34bhlsoyejywct2yj7j5x FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.attendant_temperament
    ADD CONSTRAINT fkeeh1fvj75l0vy7of6a15i7sao FOREIGN KEY (temperament_id) REFERENCES public.temperament(id);

ALTER TABLE ONLY public.pet_documents
    ADD CONSTRAINT fketmvt3y5xyc2w7v37n64aghw9 FOREIGN KEY (document_option_id) REFERENCES public.document_option(id);

ALTER TABLE ONLY public.appointment_document_option
    ADD CONSTRAINT fkf2abyccgka323u53jw0pbgcex FOREIGN KEY (appointment_id) REFERENCES public.appointment(id);

ALTER TABLE ONLY public.pet_vaccination_records
    ADD CONSTRAINT fkfjkrn54s4fbihx1vckowwdsbv FOREIGN KEY (vaccination_record_id) REFERENCES public.vaccination_record(id);

ALTER TABLE ONLY public.service_addon_service
    ADD CONSTRAINT fkfkqy45ku8fsrot8n08gogh2v8 FOREIGN KEY (addon_service_id) REFERENCES public.addon_service(id);

ALTER TABLE ONLY public.appointment_desired_hair_length
    ADD CONSTRAINT fkfqa8mdd2t96ykirvxif4kwdaa FOREIGN KEY (hair_length_id) REFERENCES public.hair_length(id);

ALTER TABLE ONLY public.attendant_service_type
    ADD CONSTRAINT fkfwva3dw3asmi0oyvfc7fx4d66 FOREIGN KEY (service_type_id) REFERENCES public.service_type(id);

ALTER TABLE ONLY public.pet_allergies
    ADD CONSTRAINT fkh1mhujchr2ggol1f1nogy5i38 FOREIGN KEY (allergy_id) REFERENCES public.allergies(id);

ALTER TABLE ONLY public.service_venu
    ADD CONSTRAINT fki1u0qfk33xrtdm97xu0jvfuk2 FOREIGN KEY (venu_id) REFERENCES public.venue(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fkibiwii24372tvmbpl3iadjy74 FOREIGN KEY (venue) REFERENCES public.venue(id);

ALTER TABLE ONLY public.venue_pet_types
    ADD CONSTRAINT fkibp7y8ewip85urto459vwe7s9 FOREIGN KEY (venue_id) REFERENCES public.venue(id);

ALTER TABLE ONLY public.emergency_contact_info
    ADD CONSTRAINT fkikwaa1otvrr8pr61xrp60h4rt FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet_type_config
    ADD CONSTRAINT fkilwq1um33c85w627wjnw81ap5 FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.weight_range
    ADD CONSTRAINT fkimo0rlwxadww3nf1yjof2boh8 FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.service_history
    ADD CONSTRAINT fkipepd655a05ygm1i1qg792sjv FOREIGN KEY (cologne_id) REFERENCES public.pet_cologne(id);

ALTER TABLE ONLY public.desired_hair_length
    ADD CONSTRAINT fkj3tpu0nhrmw19xd9q955b2ple FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet_biting_history
    ADD CONSTRAINT fkj6akx8w764l0ma2hbcy05y4qr FOREIGN KEY (pet_id) REFERENCES public.pet(id);

ALTER TABLE ONLY public.pet_emergency_contact_info
    ADD CONSTRAINT fkjgyjtum1v0ww8eb0dkhtbs5mw FOREIGN KEY (emergency_contact_id) REFERENCES public.emergency_contact_info(id);

ALTER TABLE ONLY public.venue
    ADD CONSTRAINT fkjr3atkcniumus7ptgfjef4xpx FOREIGN KEY (location_address_id) REFERENCES public.venue_address(id);

ALTER TABLE ONLY public.appointment_vaccination_record
    ADD CONSTRAINT fkjxtvnlhub5vkax8ksow7wlomd FOREIGN KEY (vaccination_record_id) REFERENCES public.vaccination_record(id);

ALTER TABLE ONLY public.vaccination_record
    ADD CONSTRAINT fkk4ot1ysah1ca2kl3dmln50vc1 FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.service_document_option
    ADD CONSTRAINT fkkx8qn8lv1kxs741je82yg3nen FOREIGN KEY (service_id) REFERENCES public.service(id);

ALTER TABLE ONLY public.venue_temperament
    ADD CONSTRAINT fkl5pxdd6a1s9j8pkkfgdxrnyoh FOREIGN KEY (temperament_id) REFERENCES public.temperament(id);

ALTER TABLE ONLY public.pet_vet_information
    ADD CONSTRAINT fkl9ky36aaef94nkkhtpsrdvngk FOREIGN KEY (pet_id) REFERENCES public.pet(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.service
    ADD CONSTRAINT fkliurqxdcd56ka3yd21476fak0 FOREIGN KEY (pet_type) REFERENCES public.pet_type(id);

ALTER TABLE ONLY public.service_history
    ADD CONSTRAINT fklvgvkltubt96pxws6yfvhvmoa FOREIGN KEY (blade_id) REFERENCES public.blades(id);

ALTER TABLE ONLY public.attendant_capacity_pet_type
    ADD CONSTRAINT fklwo7yqy8mqcjkshxooetyp2gf FOREIGN KEY (attendant_id) REFERENCES public.attendant(id);

ALTER TABLE ONLY public.attendant_service_type
    ADD CONSTRAINT fkmbktrntwbx3nqj24ax1pjiiln FOREIGN KEY (attendant_id) REFERENCES public.attendant(id);

ALTER TABLE ONLY public.appointment
    ADD CONSTRAINT fkn3b6g6bcdg7kh872xf3gq038w FOREIGN KEY (pet) REFERENCES public.pet(id);

ALTER TABLE ONLY public.service_vaccination_record
    ADD CONSTRAINT fkn3wer7q6a3rtlh5qp7kbfnoww FOREIGN KEY (vaccination_record_id) REFERENCES public.vaccination_record(id);

ALTER TABLE ONLY public.attendant_addon_service_type
    ADD CONSTRAINT fkn82qboxj2d565cv4ua0efgp9w FOREIGN KEY (addon_service_type_id) REFERENCES public.addon_service(id);

ALTER TABLE ONLY public.attendant_temperament
    ADD CONSTRAINT fknj7jt73li1orfgxohg9vnkfs8 FOREIGN KEY (attendant_pet_type_id) REFERENCES public.attendant_pet_types(id);

ALTER TABLE ONLY public.pet_threat_reaction
    ADD CONSTRAINT fknpur7bdmcaly2mpabfeb1gh8d FOREIGN KEY (pet_id) REFERENCES public.pet(id);

ALTER TABLE ONLY public.appointment_desired_hair_length
    ADD CONSTRAINT fknr8ja18qr8oi8lrdw63d94ft7 FOREIGN KEY (appointment_id) REFERENCES public.appointment(id);

ALTER TABLE ONLY public.appointment_vaccination_record
    ADD CONSTRAINT fkol6web0umyb4qxd3beay5aycj FOREIGN KEY (appointment_id) REFERENCES public.appointment(id);

ALTER TABLE ONLY public.service_history
    ADD CONSTRAINT fkos706b1uusrqrv709m6n7wciw FOREIGN KEY (appointment_id) REFERENCES public.appointment(id);

ALTER TABLE ONLY public.pet_personality_parameters
    ADD CONSTRAINT fkothsfohg8mkb41ui26dbwud5p FOREIGN KEY (personality_parameter_id) REFERENCES public.personality_parameter(id);

ALTER TABLE ONLY public.pet_threat_reaction
    ADD CONSTRAINT fkoux6i3ndyskdg68ygoyhimavq FOREIGN KEY (threat_reaction_id) REFERENCES public.threat_reaction(id);

ALTER TABLE ONLY public.attendant_capacity_pet_type
    ADD CONSTRAINT fkoybnch6o27t61c8um6g67w9wt FOREIGN KEY (capacity_pet_type_id) REFERENCES public.pet_type(id);

ALTER TABLE ONLY public.pet_biting_history
    ADD CONSTRAINT fkpn7yjtx2oh5qv342hq1oxrm6w FOREIGN KEY (biting_history_id) REFERENCES public.bitting_history(id);

ALTER TABLE ONLY public.service_history
    ADD CONSTRAINT fkq7pavj5dldobww8erdbub6lxx FOREIGN KEY (comb_id) REFERENCES public.combs(id);

ALTER TABLE ONLY public.service
    ADD CONSTRAINT fkq9w7usvq6avkkucjmfxg8evs4 FOREIGN KEY (service_type) REFERENCES public.service_type(id);

ALTER TABLE ONLY public.appointment_addon_service
    ADD CONSTRAINT fkqaeuo1wu3fgjd5b938qpx46y5 FOREIGN KEY (appointment_id) REFERENCES public.appointment(id);

ALTER TABLE ONLY public.appoinement_allergy_option
    ADD CONSTRAINT fkqd2q0nkb2mxoapccag3kkuoa5 FOREIGN KEY (appointment_id) REFERENCES public.appointment(id);

ALTER TABLE ONLY public.service_venu
    ADD CONSTRAINT fkr6kqpdgwlas2nmiokm1qkeepn FOREIGN KEY (service_id) REFERENCES public.service(id);

ALTER TABLE ONLY public.pet
    ADD CONSTRAINT fkrwc7qdgm56p8ffu3c8949unff FOREIGN KEY (temperament_id) REFERENCES public.temperament(id);

ALTER TABLE ONLY public.allergies
    ADD CONSTRAINT fksghhh29ge3irtpk4ye5jamdjw FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.service_attendants
    ADD CONSTRAINT fkswvuaellrfryio1mp6x91wvcp FOREIGN KEY (attendants_id) REFERENCES public.attendant(id);

ALTER TABLE ONLY public.document_option
    ADD CONSTRAINT fktq8v3dpc38r3b239xuq9evy1x FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.pet
    ADD CONSTRAINT fkvbnd3e30dhq7vt4t4uusr8he FOREIGN KEY (weight_range_id) REFERENCES public.weight_range(id);