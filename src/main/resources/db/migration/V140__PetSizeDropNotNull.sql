ALTER TABLE pet_size_limit ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE pet_size_limit DROP CONSTRAINT IF EXISTS fk_pet_size_limit_pet_type_id,
    ADD CONSTRAINT fk_pet_size_limit_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE pet_size_constraint ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE pet_size_constraint DROP CONSTRAINT IF EXISTS fk_pet_size_constraint_pet_type_id,
    ADD CONSTRAINT fk_pet_size_constraint_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;