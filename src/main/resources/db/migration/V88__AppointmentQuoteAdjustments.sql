CREATE TABLE IF NOT EXISTS public.appointment_quote_adjustments
(
    appointment_id integer NOT NULL,
    quote_adjustments_id integer NOT NULL,
    CONSTRAINT appointment_quote_adjustments_pkey PRIMARY KEY (appointment_id, quote_adjustments_id),
    CONSTRAINT fk87vqx9e8mx6ircobdn2bmnp7o FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk9w3e2906q7f99elvvxc5e2nr6 FOREIGN KEY (quote_adjustments_id)
        REFERENCES public.quote_adjustments (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)