CREATE TABLE IF NOT EXISTS public.time_slots_services
(
    time_slots_id integer NOT NULL,
    services_id integer NOT NULL,
    CONSTRAINT time_slots_services_pkey PRIMARY KEY (time_slots_id, services_id),
    CONSTRAINT fkcp5lo7nxllna7r0aw9wcaijjj FOREIGN KEY (services_id)
        REFERENCES public.service (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkpcwcjegfekxxew049a2phak1v FOREIGN KEY (time_slots_id)
        REFERENCES public.time_slots (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

INSERT INTO time_slots_services (time_slots_id, services_id)
SELECT  id,service_id FROM time_slots WHERE service_id IS NOT NULL;


ALTER TABLE IF EXISTS public.time_slots
DROP COLUMN service_id;

ALTER TABLE IF EXISTS public.time_slots
    ADD COLUMN slot_booked  BOOLEAN DEFAULT false;