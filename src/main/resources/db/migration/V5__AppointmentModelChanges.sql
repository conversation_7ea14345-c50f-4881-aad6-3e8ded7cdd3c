-- added timestamps
ALTER TABLE public.addon_service
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.addon_service
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.allergies
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.allergies
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.appointment
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.appointment
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.appointment_desired_hair_length
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.appointment_desired_hair_length
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.attendant
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.attendant
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.attendant_availability
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.attendant_availability
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.attendant_pet_types
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.attendant_pet_types
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.bitting_history
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.bitting_history
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.blades
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.blades
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.combs
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.combs
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.configuration
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.configuration
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.desired_hair_length
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.desired_hair_length
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.document_option
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.document_option
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.emergency_contact_info
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.emergency_contact_info
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.feeding_information
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.feeding_information
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.venue_address
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.venue_address
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.venue_availability
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.venue_availability
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.venue_pet_types
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.venue_pet_types
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.vet_information
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.vet_information
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.weight_range
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.weight_range
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.service_availability
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.service_availability
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.service_history
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.service_history
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.service_type
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.service_type
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.temperament
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.temperament
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.threat_reaction
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.threat_reaction
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.unfriendly_behaviour_trigger
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.unfriendly_behaviour_trigger
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.vaccination_record
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.vaccination_record
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.venue
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.venue
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.general_pet_size
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.general_pet_size
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.hair_length
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.hair_length
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.hair_texture
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.hair_texture
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.personality_parameter
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.personality_parameter
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_cologne
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_cologne
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_documents
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_documents
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_emergency_contact_info
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_emergency_contact_info
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_shampoo
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_shampoo
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_type
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_type
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_type_config
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_type_config
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_vaccination_records
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_vaccination_records
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.pet_vet_information
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.pet_vet_information
    ADD COLUMN modified_at timestamp without time zone;

ALTER TABLE public.service
    ADD COLUMN created_at timestamp without time zone;

ALTER TABLE public.service
    ADD COLUMN modified_at timestamp without time zone;

-- Appointment model changes to include file upload and additional fields

ALTER TABLE public.appointment
    ADD COLUMN duration character varying(255) COLLATE pg_catalog."default";

ALTER TABLE public.appointment
    ADD COLUMN hair_length_id integer;

ALTER TABLE public.appointment
    ADD COLUMN hair_texture_id integer;

ALTER TABLE public.appointment
    ADD COLUMN pet_type_id integer;

ALTER TABLE public.appointment
    ADD COLUMN temperament_id integer;

ALTER TABLE public.appointment
    ADD COLUMN weight_range_id integer;

ALTER TABLE public.appointment
    ADD CONSTRAINT fkd17fvvbq90oi2qlfhr1wbkdlc FOREIGN KEY (hair_texture_id)
    REFERENCES public.hair_texture (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

ALTER TABLE public.appointment
    ADD CONSTRAINT fkh8nqe68v7h0193c12qmdmqxgt FOREIGN KEY (pet_type_id)
    REFERENCES public.pet_type (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

ALTER TABLE public.appointment
    ADD CONSTRAINT fkh9bk7hhucxgmyesqds52q0cn9 FOREIGN KEY (hair_length_id)
    REFERENCES public.hair_length (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

ALTER TABLE public.appointment
    ADD CONSTRAINT fkrgkgf6m6g0b0ej6fw25ugq617 FOREIGN KEY (temperament_id)
    REFERENCES public.temperament (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

ALTER TABLE public.appointment
    ADD CONSTRAINT fksaxpbemhaiug9kmkpbta9il77 FOREIGN KEY (weight_range_id)
    REFERENCES public.weight_range (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

CREATE TABLE IF NOT EXISTS public.appointment_documents
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    description character varying(255) COLLATE pg_catalog."default",
    file character varying(255) COLLATE pg_catalog."default",
    retailer character varying(255) COLLATE pg_catalog."default",
    appointment_id integer NOT NULL,
    document_option_id integer NOT NULL,
    CONSTRAINT appointment_documents_pkey PRIMARY KEY (id),
    CONSTRAINT fkee8p7do5w3dk8vxutg5uh9s1t FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkmxd10pdtvji2uc7x73norwt9j FOREIGN KEY (document_option_id)
        REFERENCES public.document_option (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.appointment_emergency_contact_info
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    retailer character varying(255) COLLATE pg_catalog."default",
    value character varying(255) COLLATE pg_catalog."default" NOT NULL,
    appointment_id integer NOT NULL,
    emergency_contact_id integer NOT NULL,
    CONSTRAINT appointment_emergency_contact_info_pkey PRIMARY KEY (id),
    CONSTRAINT fk2bkndoi1eb3olvdlsn1kpv0gv FOREIGN KEY (emergency_contact_id)
        REFERENCES public.emergency_contact_info (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkjxraifxvoivsu04thcd8gatm9 FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

ALTER TABLE public.appointment_other_doc DROP CONSTRAINT fkoyfjkw1a0unrn0blt096t06nq;

ALTER TABLE public.appointment_other_doc
    ADD CONSTRAINT fkhmjo8up57lv9t5n4nhaxu1k8m FOREIGN KEY (other_doc_id)
    REFERENCES public.appointment_documents (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
    
CREATE TABLE IF NOT EXISTS public.appointment_personality_parameters
(
    appointment_id integer NOT NULL,
    personality_parameters_id integer NOT NULL,
    CONSTRAINT appointment_personality_parameters_pkey PRIMARY KEY (appointment_id, personality_parameters_id),
    CONSTRAINT fkmkk6cb8nw1gk3gv7h41i7pj6n FOREIGN KEY (personality_parameters_id)
        REFERENCES public.personality_parameter (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkrwejr3ag5qdr6o2vpec94h7st FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.appointment_unfriendly_behaviour_triggers
(
    appointment_id integer NOT NULL,
    unfriendly_behaviour_triggers_id integer NOT NULL,
    CONSTRAINT appointment_unfriendly_behaviour_triggers_pkey PRIMARY KEY (appointment_id, unfriendly_behaviour_triggers_id),
    CONSTRAINT fk7a5qlr5dqsns4foydbwyb9jbx FOREIGN KEY (unfriendly_behaviour_triggers_id)
        REFERENCES public.unfriendly_behaviour_trigger (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkm12nl4iqqpnnitrj3m3awk8r7 FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

ALTER TABLE public.appointment_vaccination_info DROP CONSTRAINT fk2mrtomyo81jyxatkpq67cyv37;

CREATE TABLE IF NOT EXISTS public.appointment_vaccination_records
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    date_administrated date NOT NULL,
    date_expires date NOT NULL,
    retailer character varying(255) COLLATE pg_catalog."default",
    appointment_id integer NOT NULL,
    vaccination_record_id integer NOT NULL,
    CONSTRAINT appointment_vaccination_records_pkey PRIMARY KEY (id),
    CONSTRAINT fk102dyni481m7sdcr3um6la87o FOREIGN KEY (vaccination_record_id)
        REFERENCES public.vaccination_record (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkrsdljvv7tk725giovln5rjgl4 FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

ALTER TABLE public.appointment_vaccination_info
    ADD CONSTRAINT fkalsts40wjbjsqey616j4ctwrb FOREIGN KEY (vaccination_info_id)
    REFERENCES public.appointment_vaccination_records (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

CREATE TABLE IF NOT EXISTS public.appointment_vet_information
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    retailer character varying(255) COLLATE pg_catalog."default",
    value character varying(255) COLLATE pg_catalog."default" NOT NULL,
    appointment_id integer NOT NULL,
    vet_information_id integer NOT NULL,
    CONSTRAINT appointment_vet_information_pkey PRIMARY KEY (id),
    CONSTRAINT fk3uk3vxlqtc1166lpdw4opw5kr FOREIGN KEY (appointment_id)
        REFERENCES public.appointment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkgq936eo10iqykf5hcs6ki3fty FOREIGN KEY (vet_information_id)
        REFERENCES public.vet_information (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

ALTER TABLE public.pet_allergies DROP CONSTRAINT fkh1mhujchr2ggol1f1nogy5i38;

ALTER TABLE public.pet_allergies
    ADD CONSTRAINT fkmwosw8h3855j9a3qt7efi01lt FOREIGN KEY (allergies_id)
    REFERENCES public.allergies (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

ALTER TABLE public.pet_personality_parameters DROP CONSTRAINT fkothsfohg8mkb41ui26dbwud5p;

ALTER TABLE public.pet_personality_parameters
    ADD CONSTRAINT fkr2tvtxek0teod9960x73nm2gv FOREIGN KEY (personality_parameters_id)
    REFERENCES public.personality_parameter (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;

ALTER TABLE public.service_attendants DROP CONSTRAINT fk4memxpb69jxh4nf4pqvb1awjh;

ALTER TABLE public.service_attendants
    ADD CONSTRAINT fkoouf9t2gm2blnwwbuxkgesrub FOREIGN KEY (services_id)
    REFERENCES public.service (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


-- Time zones for each venue
ALTER TABLE public.venue
    ADD COLUMN timezone character varying(255) COLLATE pg_catalog."default";