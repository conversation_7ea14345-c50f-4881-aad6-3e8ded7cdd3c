CREATE TABLE IF NOT EXISTS public.service_temperaments
(
    service_id integer NOT NULL,
    temperaments_id integer NOT NULL,
    CONSTRAINT service_temperaments_pkey PRIMARY KEY (service_id, temperaments_id),
    CONSTRAINT fkceenk49w43el4dagk6asdbcwi FOREIGN KEY (temperaments_id)
        REFERENCES public.temperament (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkjc71oeqqmkehespa3mhhr29pe FOREIGN KEY (service_id)
        REFERENCES public.service (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)