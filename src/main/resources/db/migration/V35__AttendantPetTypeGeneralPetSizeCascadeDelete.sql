ALTER TABLE attendant_pet_types_general_pet_sizes
   DROP CONSTRAINT fkmcn18yfftq9rk03wokjnc6w5c
 , ADD  CONSTRAINT fkmcn18yfftq9rk03wokjnc6w5c
   FOREIGN KEY (attendant_pet_types_id) REFERENCES attendant_pet_types (id) ON DELETE CASCADE;


ALTER TABLE attendant_pet_types_general_pet_sizes
   DROP CONSTRAINT fkdmgx3ogwsqtv69hv26516av5j
 , ADD  CONSTRAINT fkdmgx3ogwsqtv69hv26516av5j
   FOREIGN KEY (general_pet_sizes_id) REFERENCES general_pet_size (id) ON DELETE CASCADE;
