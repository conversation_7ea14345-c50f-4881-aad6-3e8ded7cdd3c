CREATE TABLE public.appointment_add_on_service (
	appointment_id int4 NOT NULL,
	add_on_service_id int4 NOT NULL,
	CONSTRAINT appointment_add_on_service_pkey PRIMARY KEY (appointment_id, add_on_service_id),
	CONSTRAINT fkbqrbkmap74bkcmbmfepll9f0v FOREIGN KEY (add_on_service_id) REFERENCES public.addon_service(id),
	CONSTRAINT fkjt23n9p9jlah5yocyclymhlj4 FOREIGN KEY (appointment_id) REFERENCES public.appointment(id)
);

CREATE TABLE public.appointment_allergies (
	appointment_id int4 NOT NULL,
	allergies_id int4 NOT NULL,
	CONSTRAINT appointment_allergies_pkey PRIMARY KEY (appointment_id, allergies_id),
	CONSTRAINT fk7t0yjphlhcv3umfkyup5p763r FOREIG<PERSON> KEY (appointment_id) REFERENCES public.appointment(id),
	CONSTRAINT fkte13sgrlf3vpacy797up7a9fj FOREIGN KEY (allergies_id) REFERENCES public.allergies(id)
);

CREATE TABLE public.appointment_other_doc (
	appointment_id int4 NOT NULL,
	other_doc_id int4 NOT NULL,
	CONSTRAINT appointment_other_doc_pkey PRIMARY KEY (appointment_id, other_doc_id),
	CONSTRAINT fkfuplsjad7qdd8ep47fr96poth FOREIGN KEY (appointment_id) REFERENCES public.appointment(id),
	CONSTRAINT fkoyfjkw1a0unrn0blt096t06nq FOREIGN KEY (other_doc_id) REFERENCES public.document_option(id)
);

CREATE TABLE public.appointment_vaccination_info (
	appointment_id int4 NOT NULL,
	vaccination_info_id int4 NOT NULL,
	CONSTRAINT appointment_vaccination_info_pkey PRIMARY KEY (appointment_id, vaccination_info_id),
	CONSTRAINT fk2mrtomyo81jyxatkpq67cyv37 FOREIGN KEY (vaccination_info_id) REFERENCES public.vaccination_record(id),
	CONSTRAINT fk67gdyinmj1h9pd955k04wwdxe FOREIGN KEY (appointment_id) REFERENCES public.appointment(id)
);

CREATE TABLE public.attendant_addon_services (
	attendants_id int4 NOT NULL,
	addon_services_id int4 NOT NULL,
	CONSTRAINT attendant_addon_services_pkey PRIMARY KEY (attendants_id, addon_services_id),
	CONSTRAINT fk6hruteee3merul6t3vhm97jiv FOREIGN KEY (addon_services_id) REFERENCES public.addon_service(id),
	CONSTRAINT fkss2l99xb7pn3oqobmwtynvs5w FOREIGN KEY (attendants_id) REFERENCES public.attendant(id)
);


CREATE TABLE public.attendant_capacity_pet_types (
	attendant_id int4 NOT NULL,
	capacity_pet_types_id int4 NOT NULL,
	CONSTRAINT attendant_capacity_pet_types_pkey PRIMARY KEY (attendant_id, capacity_pet_types_id),
	CONSTRAINT fkp9yj9vkttfmpeyfhi23t325mu FOREIGN KEY (attendant_id) REFERENCES public.attendant(id),
	CONSTRAINT fkpm8gd262bojnojgyeyfh4wknk FOREIGN KEY (capacity_pet_types_id) REFERENCES public.pet_type(id)
);

CREATE TABLE public.attendant_pet_types_general_pet_sizes (
	attendant_pet_types_id int4 NOT NULL,
	general_pet_sizes_id int4 NOT NULL,
	CONSTRAINT attendant_pet_types_general_pet_sizes_pkey PRIMARY KEY (attendant_pet_types_id, general_pet_sizes_id),
	CONSTRAINT fkdmgx3ogwsqtv69hv26516av5j FOREIGN KEY (general_pet_sizes_id) REFERENCES public.general_pet_size(id),
	CONSTRAINT fkmcn18yfftq9rk03wokjnc6w5c FOREIGN KEY (attendant_pet_types_id) REFERENCES public.attendant_pet_types(id)
);

CREATE TABLE public.attendant_pet_types_temperaments (
	attendant_pet_types_id int4 NOT NULL,
	temperaments_id int4 NOT NULL,
	CONSTRAINT attendant_pet_types_temperaments_pkey PRIMARY KEY (attendant_pet_types_id, temperaments_id),
	CONSTRAINT fk2xi2bmm8l1qq21tj6xe3hol9j FOREIGN KEY (attendant_pet_types_id) REFERENCES public.attendant_pet_types(id),
	CONSTRAINT fkfr7c89f9p2da34yop7nnph158 FOREIGN KEY (temperaments_id) REFERENCES public.temperament(id)
);

CREATE TABLE public.attendant_service_types (
	attendants_id int4 NOT NULL,
	service_types_id int4 NOT NULL,
	CONSTRAINT attendant_service_types_pkey PRIMARY KEY (attendants_id, service_types_id),
	CONSTRAINT fk3boovj01cp5vf5xfkxn1dmmxy FOREIGN KEY (service_types_id) REFERENCES public.service_type(id),
	CONSTRAINT fkhp1e33v06ct2gs536h2v6dw1y FOREIGN KEY (attendants_id) REFERENCES public.attendant(id)
);
ALTER TABLE public.feeding_information
    DROP COLUMN grain_free_recipes,
    DROP COLUMN grain_full_recipes;

ALTER TABLE public.pet_allergies
    RENAME COLUMN allergy_id TO allergies_id;

CREATE TABLE public.pet_biting_histories (
	pet_id int4 NOT NULL,
	biting_histories_id int4 NOT NULL,
	CONSTRAINT pet_biting_histories_pkey PRIMARY KEY (pet_id, biting_histories_id),
	CONSTRAINT fk2wxbmlbk43qnx6ry0pw8j71qd FOREIGN KEY (biting_histories_id) REFERENCES public.bitting_history(id),
	CONSTRAINT fkawvtd8cy6vl9dfu2i8we33cuj FOREIGN KEY (pet_id) REFERENCES public.pet(id)
);

ALTER TABLE public.pet_personality_parameters
    RENAME COLUMN personality_parameter_id TO personality_parameters_id;

CREATE TABLE public.pet_threat_reactions (
    pet_id int4 NOT NULL,
    threat_reactions_id int4 NOT NULL,
    CONSTRAINT pet_threat_reactions_pkey PRIMARY KEY (pet_id, threat_reactions_id),
    CONSTRAINT fko0y4mwscu1u2ci4nacaiuil92 FOREIGN KEY (pet_id) REFERENCES public.pet(id),
    CONSTRAINT fks5n45mk0yv19qqvfa7lnf3t8y FOREIGN KEY (threat_reactions_id) REFERENCES public.threat_reaction(id)
);

CREATE TABLE public.pet_unfriendly_behaviour_triggers (
	pet_id int4 NOT NULL,
	unfriendly_behaviour_triggers_id int4 NOT NULL,
	CONSTRAINT pet_unfriendly_behaviour_triggers_pkey PRIMARY KEY (pet_id, unfriendly_behaviour_triggers_id),
	CONSTRAINT fkqhk3qt4b9v5llv9kmr54tgpd8 FOREIGN KEY (pet_id) REFERENCES public.pet(id),
	CONSTRAINT fkr3so6rg3hustt55hyo52cy2q2 FOREIGN KEY (unfriendly_behaviour_triggers_id) REFERENCES public.unfriendly_behaviour_trigger(id)
);

CREATE TABLE public.service_addon_services (
	services_id int4 NOT NULL,
	addon_services_id int4 NOT NULL,
	CONSTRAINT service_addon_services_pkey PRIMARY KEY (services_id, addon_services_id),
	CONSTRAINT fka5e2ieg5709ob0nmtm59qbgtu FOREIGN KEY (addon_services_id) REFERENCES public.addon_service(id),
	CONSTRAINT fkqrjcfwoyuqia6b727ffwvp8sh FOREIGN KEY (services_id) REFERENCES public.service(id)
);

ALTER TABLE public.service_attendants
    RENAME COLUMN service_id TO services_id;

CREATE TABLE public.service_available_participant_documents (
	service_id int4 NOT NULL,
	available_participant_documents_id int4 NOT NULL,
	CONSTRAINT service_available_participant_documents_pkey PRIMARY KEY (service_id, available_participant_documents_id),
	CONSTRAINT fkc3adgxep3yusikg3pehq7gf7b FOREIGN KEY (service_id) REFERENCES public.service(id),
	CONSTRAINT fkrjyk9jjnodbrwfjjko6a7jfra FOREIGN KEY (available_participant_documents_id) REFERENCES public.document_option(id)
);

CREATE TABLE public.service_available_participant_vaccinations (
	service_id int4 NOT NULL,
	available_participant_vaccinations_id int4 NOT NULL,
	CONSTRAINT service_available_participant_vaccinations_pkey PRIMARY KEY (service_id, available_participant_vaccinations_id),
	CONSTRAINT fkac2i6a7xsod3i3658guc8hku5 FOREIGN KEY (available_participant_vaccinations_id) REFERENCES public.vaccination_record(id),
	CONSTRAINT fkcpfsrtjjvotg4rp35a0at7jyw FOREIGN KEY (service_id) REFERENCES public.service(id)
);

CREATE TABLE public.service_general_pet_size (
	service_id int4 NOT NULL,
	general_pet_size_id int4 NOT NULL,
	CONSTRAINT service_general_pet_size_pkey PRIMARY KEY (service_id, general_pet_size_id),
	CONSTRAINT fk8w1dsqbsu6hs6ls6h6yfg5qfo FOREIGN KEY (general_pet_size_id) REFERENCES public.general_pet_size(id),
	CONSTRAINT fklpoosc89ihlxl5w0bnkdbt78p FOREIGN KEY (service_id) REFERENCES public.service(id)
);

CREATE TABLE public.service_venues (
	services_id int4 NOT NULL,
	venues_id int4 NOT NULL,
	CONSTRAINT service_venues_pkey PRIMARY KEY (services_id, venues_id),
	CONSTRAINT fk4hdfgudwmnmhlbmuu7evdvyp0 FOREIGN KEY (services_id) REFERENCES public.service(id),
	CONSTRAINT fk7oqbiet7wakcv0j60lx2osf7a FOREIGN KEY (venues_id) REFERENCES public.venue(id)
);

CREATE TABLE public.venue_pet_types_temperaments (
	venue_pet_types_id int4 NOT NULL,
	temperaments_id int4 NOT NULL,
	CONSTRAINT venue_pet_types_temperaments_pkey PRIMARY KEY (venue_pet_types_id, temperaments_id),
	CONSTRAINT fk9i88qafaaahgj2ux90ol1ko06 FOREIGN KEY (temperaments_id) REFERENCES public.temperament(id),
	CONSTRAINT fkxi2upb9cvvgv3542x5pku4us FOREIGN KEY (venue_pet_types_id) REFERENCES public.venue_pet_types(id)
);

CREATE TABLE public.venue_service_types (
	venues_id int4 NOT NULL,
	service_types_id int4 NOT NULL,
	CONSTRAINT venue_service_types_pkey PRIMARY KEY (venues_id, service_types_id),
	CONSTRAINT fk6j3y3jvhous1hbvnljha98iem FOREIGN KEY (service_types_id) REFERENCES public.service_type(id),
	CONSTRAINT fkqkipv85h78p1k4f1ad2nadsad FOREIGN KEY (venues_id) REFERENCES public.venue(id)
);

--removing old join tables
DROP TABLE public.appoinement_allergy_option;
DROP TABLE public.appointment_addon_service;
DROP TABLE public.appointment_document_option;
DROP TABLE public.appointment_vaccination_record;
DROP TABLE public.attendant_addon_service_type;
DROP TABLE public.attendant_capacity_pet_type;
DROP TABLE public.attendant_pet_sizes;
DROP TABLE public.attendant_service_type;
DROP TABLE public.attendant_temperament;
DROP TABLE public.pet_biting_history;
DROP TABLE public.pet_threat_reaction;
DROP TABLE public.pet_unfriendly_behaviour_trigger;
DROP TABLE public.service_addon_service;
DROP TABLE public.service_allergy_option;
DROP TABLE public.service_document_option;
DROP TABLE public.service_general_sizes;
DROP TABLE public.service_temperament;
DROP TABLE public.service_vaccination_record;
DROP TABLE public.service_venu;
DROP TABLE public.service_weight_range;
DROP TABLE public.venue_pet_sizes;
DROP TABLE public.venue_service_type;
DROP TABLE public.venue_temperament;




