ALTER TABLE public.pet_size_constraint
DROP COLUMN size;

ALTER TABLE public.pet_size_constraint
    ADD COLUMN general_pet_size_id integer;

ALTER TABLE public.pet_size_constraint
    ADD CONSTRAINT fklrqj7qgoprp65rlfjomihzzgu FOREIGN KEY (general_pet_size_id)
        REFERENCES public.general_pet_size (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;

ALTER TABLE public.pet_size_constraint
    ADD COLUMN pet_type_id integer;

ALTER TABLE public.pet_size_constraint
    ADD CONSTRAINT fklrqj7qgoprp65rlfjomjaaha FOREIGN KEY (pet_type_id)
        REFERENCES public.pet_type (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;