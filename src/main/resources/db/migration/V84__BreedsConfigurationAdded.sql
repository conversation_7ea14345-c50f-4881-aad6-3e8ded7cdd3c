CREATE TABLE public.breeds (
   id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
   active bool NOT NULL,
   created_at timestamp NULL,
   deleted bool NULL,
   modified_at timestamp NULL,
   name varchar(255) NOT NULL,
   retailer varchar(255) NULL,
   pet_type_id int4 NOT NULL,
   CONSTRAINT breeds_pkey PRIMARY KEY (id),
   CONSTRAINT fk5iypkd151xwrj0oonn7eqvjf0 FOREIGN KEY (pet_type_id) REFERENCES public.pet_type(id)
);