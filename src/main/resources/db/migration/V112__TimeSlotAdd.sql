CREATE TABLE IF NOT EXISTS public.time_slots
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    availability_interval integer,
    availability_interval_unit character varying(255) COLLATE pg_catalog."default",
    available_day character varying(255) COLLATE pg_catalog."default",
    created_at timestamp without time zone,
    deleted boolean,
    modified_at timestamp without time zone,
    slot_end_time timestamp without time zone,
    slot_name character varying(255) COLLATE pg_catalog."default",
    slot_start_time timestamp without time zone,
    attendant_id integer,
    service_id integer,
    venue_id integer,
    CONSTRAINT time_slots_pkey PRIMARY KEY (id),
    CONSTRAINT fk1k9le9mn9wbxyb8xhqug94li2 FOREIGN KEY (service_id)
        REFERENCES public.service (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk8vp6rtt92qa6egf13lxgjy9ic FOREIGN KEY (attendant_id)
        REFERENCES public.attendant (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkaq4bglocejmgawvldc5f50x33 FOREIGN KEY (venue_id)
        REFERENCES public.venue (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);
