ALTER TABLE pet_type_document_options
      DROP CONSTRAINT fk94xgfie6lqk2qb0930gv8mk84
    , ADD  CONSTRAINT fk94xgfie6lqk2qb0930gv8mk84
      FOREIGN KEY (pet_type_id) REFERENCES pet_type (id) ON DELETE CASCADE;



      ALTER TABLE pet_type_document_options
            DROP CONSTRAINT fktgmklab2kf14upxwsokjpnrdf
          , ADD  CONSTRAINT fktgmklab2kf14upxwsokjpnrdf
            FOREIGN KEY (document_options_id) REFERENCES document_option (id) ON DELETE CASCADE;