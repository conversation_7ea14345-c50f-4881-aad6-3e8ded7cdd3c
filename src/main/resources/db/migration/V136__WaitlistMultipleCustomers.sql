-- Migration to support multiple customers per waitlist entry

-- Step 1: Create the join table for waitlist customers
CREATE TABLE waitlist_customers (
    waitlist_entry_id INTEGER NOT NULL,
    customer_ecom_id INTEGER NOT NULL,
    retailer VARCHAR(255) NOT NULL,
    
    CONSTRAINT pk_waitlist_customers PRIMARY KEY (waitlist_entry_id, customer_ecom_id, retailer),
    
    CONSTRAINT fk_waitlist_customers_entry FOREIGN KEY (waitlist_entry_id)
        REFERENCES waitlist_entries (id)
        ON DELETE CASCADE,
        
    CONSTRAINT fk_waitlist_customers_customer FOREIGN KEY (customer_ecom_id, retailer)
        REFERENCES customer (ecom_id, retailer)
        ON DELETE CASCADE
);

-- Step 2: Migrate existing data to the new structure
-- Insert existing customer relationships into the join table
INSERT INTO waitlist_customers (waitlist_entry_id, customer_ecom_id, retailer)
SELECT id, customer_ecom_id, retailer 
FROM waitlist_entries 
WHERE customer_ecom_id IS NOT NULL;

-- Step 3: Remove the old customer columns from waitlist_entries
ALTER TABLE waitlist_entries 
DROP COLUMN customer_ecom_id,
DROP COLUMN retailer;

-- Step 4: Drop the old foreign key constraint
-- Note: The constraint name might be different, so we'll handle this carefully
-- ALTER TABLE waitlist_entries DROP CONSTRAINT IF EXISTS fk_waitlist_customer; 