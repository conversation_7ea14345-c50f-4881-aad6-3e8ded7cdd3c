-- Add attendant_id and block_type columns to blockdates table
ALTER TABLE public.blockdates 
    ADD COLUMN attendant_id integer,
    ADD COLUMN block_type character varying(20);

-- Set default value for existing records (all existing blocks are store-wide)
UPDATE public.blockdates 
SET block_type = 'STORE' 
WHERE block_type IS NULL;

-- Add foreign key constraint for attendant_id
ALTER TABLE public.blockdates 
    ADD CONSTRAINT fk_blockdates_attendant 
    FOREIGN KEY (attendant_id) 
    REFERENCES public.attendant(id);

-- Create index for better performance on block_type queries
CREATE INDEX idx_blockdates_block_type ON public.blockdates(block_type);

-- Create index for better performance on attendant_id queries
CREATE INDEX idx_blockdates_attendant_id ON public.blockdates(attendant_id);
