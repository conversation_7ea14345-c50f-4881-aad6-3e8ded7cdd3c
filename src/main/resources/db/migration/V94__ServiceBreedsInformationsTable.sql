CREATE TABLE public.service_breeds_info (
        id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
        charge_amount numeric(19, 2) NULL,
        created_at timestamp NULL,
        duration numeric(19, 2) NULL,
        duration_type varchar(255) NULL,
        modified_at timestamp NULL,
        retailer varchar(255) NULL,
        breed_id int4 NOT NULL,
        service_id int4 NOT NULL,
        CONSTRAINT service_breeds_info_pkey PRIMARY KEY (id),
        CONSTRAINT fk16m2va1xmji58fmb2uo0o1r8q FOREIGN KEY (service_id) REFERENCES public.service(id),
        CONSTRAINT fkoh3ufckp9h819rhoukak27raa FOREIGN KEY (breed_id) REFERENCES public.breeds(id)
);