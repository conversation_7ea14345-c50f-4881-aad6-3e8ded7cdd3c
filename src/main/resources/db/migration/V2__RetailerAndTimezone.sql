
ALTER TABLE public.appointment
    ADD COLUMN variable_schedule_max_duration int4 DEFAULT NULL,
    ADD COLUMN variable_schedule_min_duration int4 DEFAULT NULL;

ALTER TABLE public.attendant_availability
    ADD COLUMN retailer varchar(255) DEFAULT NULL;

ALTER TABLE public.feeding_information
    ADD COLUMN grain_free_recipes_no _text DEFAULT NULL,
    ADD COLUMN grain_free_recipes_yes _text DEFAULT NULL,
    ADD COLUMN grain_full_recipes_no _text DEFAULT NULL,
    ADD COLUMN grain_full_recipes_yes _text DEFAULT NULL;

ALTER TABLE public.service_availability
    ADD COLUMN retailer varchar(255) DEFAULT NULL;

CREATE TABLE public.service_allergy_option (
	service_id int4 NOT NULL,
	allergy_option_id int4 NOT NULL,
	CONSTRAINT service_allergy_option_pkey PRIMARY KEY (service_id, allergy_option_id)
);

CREATE TABLE public.service_temperament (
	service_id int4 NOT NULL,
	temperament_id int4 NOT NULL,
	CONSTRAINT service_temperament_pkey PRIMARY KEY (service_id, temperament_id)
);

CREATE TABLE public.service_weight_range (
	service_id int4 NOT NULL,
	weight_range_id int4 NOT NULL,
	CONSTRAINT service_weight_range_pkey PRIMARY KEY (service_id, weight_range_id)
);

CREATE TABLE public.timezone (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	time_offset varchar(255) NULL,
	session_id varchar(255) NULL,
	CONSTRAINT timezone_pkey PRIMARY KEY (id)
);

CREATE TABLE public.venue_pet_types_general_pet_sizes (
	venue_pet_types_id int4 NOT NULL,
	general_pet_sizes_id int4 NOT NULL,
	CONSTRAINT venue_pet_types_general_pet_sizes_pkey PRIMARY KEY (venue_pet_types_id, general_pet_sizes_id),
	CONSTRAINT fkduqut4rruio5grpg3m03uw4j7 FOREIGN KEY (venue_pet_types_id) REFERENCES public.venue_pet_types(id),
	CONSTRAINT fkq8xq6dnoqjhrwulnfvl7ew0lc FOREIGN KEY (general_pet_sizes_id) REFERENCES public.general_pet_size(id)
);
