ALTER TABLE IF EXISTS public.document_option DROP COLUMN IF EXISTS pet_type_id;

ALTER TABLE IF EXISTS public.document_option
    ADD COLUMN fileURL character varying(255) COLLATE pg_catalog."default";
ALTER TABLE IF EXISTS public.document_option DROP CONSTRAINT IF EXISTS fktq8v3dpc38r3b239xuq9evy1x;
DROP INDEX IF EXISTS public.document_option_pet_type_id;

CREATE TABLE IF NOT EXISTS public.pet_type_document_options
(
    pet_type_id integer NOT NULL,
    document_options_id integer NOT NULL,
    CONSTRAINT pet_type_document_options_pkey PRIMARY KEY (pet_type_id, document_options_id),
    CONSTRAINT fk94xgfie6lqk2qb0930gv8mk84 FOREIGN KEY (pet_type_id)
        REFERENCES public.pet_type (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fktgmklab2kf14upxwsokjpnrdf FOREIGN KEY (document_options_id)
        REFERENCES public.document_option (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)