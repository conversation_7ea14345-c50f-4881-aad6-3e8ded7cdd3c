
ALTER TABLE IF EXISTS public.customer
    ADD COLUMN ecom_id integer;

ALTER TABLE IF EXISTS public.customer
    ADD COLUMN retailer character varying(255) COLLATE pg_catalog."default";
ALTER TABLE IF EXISTS public.customer
    ADD CONSTRAINT uk863ok0vwwoc8bie4v1utskcli UNIQUE (ecom_id, retailer);


CREATE SEQUENCE public_id_seq OWNED BY public.customer.id;

SELECT SETVAL('public_id_seq', (select max(id) from public.customer), false);

ALTER TABLE IF EXISTS public.customer ALTER COLUMN id SET DEFAULT nextval('public_id_seq');