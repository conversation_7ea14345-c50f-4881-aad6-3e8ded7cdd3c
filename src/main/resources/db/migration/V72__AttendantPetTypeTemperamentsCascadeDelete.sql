ALTER TABLE attendant_pet_types_temperaments
   DROP CONSTRAINT fk2xi2bmm8l1qq21tj6xe3hol9j
 , ADD  CONSTRAINT fk2xi2bmm8l1qq21tj6xe3hol9j
   FOREIGN KEY (attendant_pet_types_id) REFERENCES attendant_pet_types (id) ON DELETE CASCADE;


ALTER TABLE attendant_pet_types_temperaments
   DROP CONSTRAINT fkfr7c89f9p2da34yop7nnph158
 , ADD  CONSTRAINT fkfr7c89f9p2da34yop7nnph158
   FOREIGN KEY (temperaments_id) REFERENCES temperament (id) ON DELETE CASCADE;
