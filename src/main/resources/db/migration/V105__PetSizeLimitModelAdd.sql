CREATE TABLE IF NOT EXISTS public.pet_size_limit
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    capacity integer NOT NULL,
    created_at timestamp without time zone,
    deleted boolean,
    modified_at timestamp without time zone,
    retailer character varying(255) COLLATE pg_catalog."default",
    attendant_id integer NOT NULL,
    general_pet_size_id integer,
    pet_type_id integer NOT NULL,
    CONSTRAINT pet_size_limit_pkey PRIMARY KEY (id),
    CONSTRAINT fkdarff6bhi80cwebvy3mdqnk2c FOREIGN KEY (attendant_id)
        REFERENCES public.attendant (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkf685kprvrx022g1amm09ptmaj FOREIGN KEY (general_pet_size_id)
        REFERENCES public.general_pet_size (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkm0lc6929g5mpjnaj1etxdp1q3 FOREIGN KEY (pet_type_id)
        REFERENCES public.pet_type (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)