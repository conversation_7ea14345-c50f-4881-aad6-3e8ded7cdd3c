CREATE TABLE IF NOT EXISTS public.kinesis_checkpoint
(
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1 ),
    sequence_number character varying(255) COLLATE pg_catalog."default" NOT NULL,
    shard_id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    stream_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    CONSTRAINT kinesis_checkpoint_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.skipped_records
(
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1 ),
    error_message text COLLATE pg_catalog."default" NOT NULL,
    processed_at timestamp without time zone NOT NULL,
    record_data text COLLATE pg_catalog."default" NOT NULL,
    time_slot_cluster_id integer NOT NULL,
    CONSTRAINT skipped_records_pkey PRIMARY KEY (id)
);

