CREATE TABLE waitlist_entries (
    id SERIAL PRIMARY KEY,

    time_slot_id INTEGER NOT NULL,
    appointment_id INTEGER,

    customer_ecom_id INTEGER NOT NULL,
    retailer VARCHAR(255) NOT NULL,

    notified BOOL<PERSON>N DEFAULT FALSE,
    booked BOOLEAN DEFAULT FALSE,

     created_at timestamp without time zone,
      modified_at timestamp without time zone,

    CONSTRAINT fk_waitlist_slot FOREIGN KEY (time_slot_id)
        REFERENCES time_slots (id)
        ON DELETE CASCADE,

    CONSTRAINT fk_waitlist_appointment FOREIGN KEY (appointment_id)
        REFERENCES appointment (id)
        ON DELETE SET NULL,

    CONSTRAINT fk_waitlist_customer FOREIGN KEY (customer_ecom_id, retailer)
        REFERENCES customer (ecom_id, retailer)
        ON DELETE CASCADE
);

