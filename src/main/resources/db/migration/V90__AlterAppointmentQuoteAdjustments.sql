ALTER TABLE appointment_quote_adjustments
      DROP CONSTRAINT fk87vqx9e8mx6ircobdn2bmnp7o
    , ADD  CONSTRAINT fk87vqx9e8mx6ircobdn2bmnp7o
      FOREIGN KEY (appointment_id) REFERENCES appointment(id) ON DELETE CASCADE;


ALTER TABLE appointment_quote_adjustments
      DROP CONSTRAINT fk9w3e2906q7f99elvvxc5e2nr6
    , ADD  CONSTRAINT fk9w3e2906q7f99elvvxc5e2nr6
      FOREIGN KEY (quote_adjustments_id) REFERENCES quote_adjustments(id) ON DELETE CASCADE;