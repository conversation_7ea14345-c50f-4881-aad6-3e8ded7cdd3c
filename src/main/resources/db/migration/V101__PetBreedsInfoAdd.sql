CREATE TABLE public.pet_breeds_info (
    id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    breed_id int4 NOT NULL,
    pet_id int4 NOT NULL,
    retailer varchar(255) NULL,
    created_at timestamp NULL,
    modified_at timestamp NULL,
    CONSTRAINT pet_breeds_info_pkey PRIMARY KEY (id),
    CONSTRAINT fk16m2va1xmji58fmb2uo0o1s9s FOREIGN KEY (pet_id) REFERENCES public.pet(id),
    CONSTRAINT fkoh3ufckp9h819rhoukak27sbb FOREIGN KEY (breed_id) REFERENCES public.breeds(id)
);