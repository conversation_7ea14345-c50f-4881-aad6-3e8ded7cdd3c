CREATE TABLE IF NOT EXISTS public.retailer_mapping
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    created_at timestamp without time zone,
    modified_at timestamp without time zone,
    offset_time integer,
    schema_name character varying(255) COLLATE pg_catalog."default",
    time_zone character varying(255) COLLATE pg_catalog."default",
    CONSTRAINT retailer_mapping_pkey PRIMARY KEY (id)
)