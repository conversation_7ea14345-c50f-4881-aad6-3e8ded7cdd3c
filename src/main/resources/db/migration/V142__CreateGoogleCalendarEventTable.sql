-- Create table to map appointments to Google Calendar events
CREATE TABLE google_calendar_event (
    id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL,
    attendant_id INTEGER NOT NULL,
    google_event_id VARCHAR(1024) NOT NULL,
    calendar_id VARCHAR(255),
    retailer VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_google_calendar_event_appointment FOREIGN KEY (appointment_id) REFERENCES appointment(id) ON DELETE CASCADE,
    CONSTRAINT fk_google_calendar_event_attendant FOREIGN KEY (attendant_id) REFERENCES attendant(id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX idx_google_calendar_event_appointment ON google_calendar_event(appointment_id);
CREATE INDEX idx_google_calendar_event_attendant ON google_calendar_event(attendant_id);
CREATE INDEX idx_google_calendar_event_google_event_id ON google_calendar_event(google_event_id);

-- Add comment
COMMENT ON TABLE google_calendar_event IS 'Maps appointments to Google Calendar events for synchronization';

