-- Add Google Calendar sync fields to attendant table
ALTER TABLE attendant
ADD COLUMN google_calendar_sync_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN google_refresh_token TEXT,
ADD COLUMN google_calendar_id VARCHAR(255),
ADD COLUMN google_access_token TEXT,
ADD COLUMN google_token_expiry TIMESTAMP,
ADD COLUMN calendar_sync_in_progress BOOLEAN;

-- Add comment for clarity
COMMENT ON COLUMN attendant.google_calendar_sync_enabled IS 'Flag to enable/disable Google Calendar synchronization';
COMMENT ON COLUMN attendant.google_refresh_token IS 'OAuth2 refresh token for Google Calendar API access';
COMMENT ON COLUMN attendant.google_calendar_id IS 'Google Calendar ID (usually primary)';
COMMENT ON COLUMN attendant.google_access_token IS 'OAuth2 access token for Google Calendar API (cached)';
COMMENT ON COLUMN attendant.google_token_expiry IS 'Expiry timestamp for the cached access token';
COMMENT ON COLUMN attendant.calendar_sync_in_progress IS 'Flag to track if calendar sync is currently in progress (prevents concurrent syncs)';

