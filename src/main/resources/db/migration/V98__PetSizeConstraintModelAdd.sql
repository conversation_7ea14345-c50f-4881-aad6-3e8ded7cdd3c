CREATE TABLE public.pet_size_constraint (
   id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
   capacity integer NOT NULL,
   size character varying(255) NOT NULL,
   retailer varchar(255) NULL,
   attendant_id int4 NOT NULL,
   deleted bool NULL,
   created_at timestamp NULL,
   modified_at timestamp NULL,
   CONSTRAINT pet_size_constraint_pkey PRIMARY KEY (id),
   CONSTRAINT fk5iypkd151xwrj0oonn7eqvjg0 FOREIGN KEY (attendant_id) REFERENCES public.attendant(id)
);
