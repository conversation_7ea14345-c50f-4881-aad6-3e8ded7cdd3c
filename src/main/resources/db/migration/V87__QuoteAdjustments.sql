CREATE TABLE IF NOT EXISTS public.quote_adjustments
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    active boolean NOT NULL,
    created_at timestamp without time zone,
    deleted boolean,
    modified_at timestamp without time zone,
    quote_name character varying(255) COLLATE pg_catalog."default",
    quote_price numeric(19,2),
    retailer character varying(255) COLLATE pg_catalog."default",
    CONSTRAINT quote_adjustments_pkey PRIMARY KEY (id)
)
