ALTER TABLE public.service ADD COLUMN addon_offered boolean;

ALTER TABLE public.service ADD COLUMN cologne_offered boolean;

ALTER TABLE public.service ADD COLUMN require_desired_hair_length boolean;

ALTER TABLE public.service ADD COLUMN require_feeding_info boolean;

ALTER TABLE public.service ADD COLUMN shampoos_offered boolean;

CREATE TABLE IF NOT EXISTS public.service_colognes
(
    service_id integer NOT NULL,
    colognes_id integer NOT NULL,
    CONSTRAINT service_colognes_pkey PRIMARY KEY (service_id, colognes_id),
    CONSTRAINT fkmkd3624s8r7dxrdd95v71q5h9 FOREIGN KEY (service_id)
        REFERENCES public.service (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkqvrur4uvtfymw29s1l1l38n3v FOREIGN KEY (colognes_id)
        REFERENCES public.pet_cologne (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.service_shampoos
(
    service_id integer NOT NULL,
    shampoos_id integer NOT NULL,
    CONSTRAINT service_shampoos_pkey PRIMARY KEY (service_id, shampoos_id),
    CONSTRAINT fkav4e1kkg849ua2e73iuk205q5 FOREIGN KEY (shampoos_id)
        REFERENCES public.pet_shampoo (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fkexp5ycji3e8esa5k9p6oa85xx FOREIGN KEY (service_id)
        REFERENCES public.service (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);
