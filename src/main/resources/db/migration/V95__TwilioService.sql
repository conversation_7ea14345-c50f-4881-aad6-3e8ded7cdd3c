CREATE TABLE IF NOT EXISTS public.twilio_service
(
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    created_at timestamp without time zone,
    customer_id integer,
    retailer character varying(255) COLLATE pg_catalog."default",
    service_sid character varying(255) COLLATE pg_catalog."default",
    CONSTRAINT twilio_service_pkey PRIMARY KEY (id)
)