
ALTER TABLE hair_length ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE hair_length DROP CONSTRAINT IF EXISTS fk_hair_length_pet_type_id;
ALTER TABLE hair_length DROP CONSTRAINT IF EXISTS hair_length_pet_type_id_fkey;
ALTER TABLE hair_length
    ADD CONSTRAINT fk_hair_length_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;


ALTER TABLE hair_texture ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE hair_texture DROP CONSTRAINT IF EXISTS fk_hair_texture_pet_type_id,
    ADD CONSTRAINT fk_hair_texture_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE combs ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE combs DROP CONSTRAINT IF EXISTS fk_combs_pet_type_id,
    ADD CONSTRAINT fk_combs_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE blades ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE blades DROP CONSTRAINT IF EXISTS fk_blades_pet_type_id,
    ADD CONSTRAINT fk_blades_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE allergies ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE allergies DROP CONSTRAINT IF EXISTS fk_allergies_pet_type_id,
    ADD CONSTRAINT fk_allergies_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE vet_information
ALTER COLUMN pet_type_id DROP NOT NULL;

ALTER TABLE vet_information
    DROP CONSTRAINT IF EXISTS fk_vet_information_pet_type_id,
    ADD CONSTRAINT fk_vet_information_pet_type_id
        FOREIGN KEY (pet_type_id) REFERENCES pet_type(id)
        ON DELETE SET NULL;


ALTER TABLE desired_hair_length ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE desired_hair_length DROP CONSTRAINT IF EXISTS fk_desired_hair_length_pet_type_id,
    ADD CONSTRAINT fk_desired_hair_length_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE breeds ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE breeds DROP CONSTRAINT IF EXISTS fk_breeds_pet_type_id;
ALTER TABLE breeds DROP CONSTRAINT IF EXISTS breeds_pet_type_id_fkey;
ALTER TABLE breeds
    ADD CONSTRAINT fk_breeds_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE temperament ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE temperament DROP CONSTRAINT IF EXISTS fk_temperament_pet_type_id,
    ADD CONSTRAINT fk_temperament_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE vaccination_record ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE vaccination_record DROP CONSTRAINT IF EXISTS fk_vaccination_records_pet_type_id,
    ADD CONSTRAINT fk_vaccination_records_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE emergency_contact_info ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE emergency_contact_info DROP CONSTRAINT IF EXISTS  fk_emergency_contact_info_pet_type_id,
    ADD CONSTRAINT fk_emergency_contact_info_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE weight_range ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE weight_range DROP CONSTRAINT IF EXISTS fk_weight_range_pet_type_id,
    ADD CONSTRAINT fk_weight_range_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE general_pet_size ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE general_pet_size DROP CONSTRAINT IF EXISTS fkh1eflf7r6on53h56ifkvc5u2a,
    ADD CONSTRAINT fkh1eflf7r6on53h56ifkvc5u2a FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;


ALTER TABLE attendant_pet_types ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE attendant_pet_types DROP CONSTRAINT IF EXISTS fk_attendant_pet_types_pet_type_id,
    ADD CONSTRAINT fk_attendant_pet_types_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE venue_pet_types ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE venue_pet_types DROP CONSTRAINT IF EXISTS fk_venue_pet_types_pet_type_id,
    ADD CONSTRAINT fk_venue_pet_types_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE service ALTER COLUMN pet_type DROP NOT NULL;
ALTER TABLE service DROP CONSTRAINT IF EXISTS fk_service_pet_type_id,
    ADD CONSTRAINT fk_service_pet_type_id FOREIGN KEY (pet_type) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE pet ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE pet DROP CONSTRAINT IF EXISTS fk_pet_pet_type_id,
    ADD CONSTRAINT fk_pet_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE SET NULL;

ALTER TABLE pet_type_config ALTER COLUMN pet_type_id DROP NOT NULL;
ALTER TABLE pet_type_config DROP CONSTRAINT IF EXISTS fk_pet_type_config_pet_type_id,
    ADD CONSTRAINT fk_pet_type_config_pet_type_id FOREIGN KEY (pet_type_id) REFERENCES pet_type(id) ON DELETE CASCADE;
