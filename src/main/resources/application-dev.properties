## Esign Document
esignservice.keystore=${KEYSTORE_LOCATION}
esignservice.keystore.password=${KEYSTORE_PASSWORD}
esignservice.tempfolder=/tmp

## Twilio
twilio.account_sid=**********************************
twilio.auth_token=ff9e1c59d7ed3387598ce8bd27d65ef3
twilio.number=+***********
twilio.api_key=**********************************
twilio.api_secret=rSZh76SUW6K3Unp4khe4coRkc6cJUq1E

## PostgreSQL
spring.datasource.url=jdbc:postgresql://${DATA_SOURCE}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# Flyway migration
flyway.url=jdbc:postgresql://${DATA_SOURCE_URL}
flyway.user=${DATA_SOURCE_USERNAME}
flyway.password=${DATA_SOURCE_PASSWORD}
spring.flyway.enabled= true
spring.flyway.baselineOnMigrate = true

management.endpoint.web.exposure.include = health

spring.jpa.hibernate.ddl-auto= validate

spring.jpa.show-sql=false

#spring batch metatable creation
spring.batch.initialize-schema=always
spring.batch.job.enabled=false


#eureka.client.serviceUrl.defaultZone  = ${EUREKA_URI}
spring.application.name = bookit

#Sentry error reporting
sentry.dsn=${SENTRY_DSN}
# Disable auto-init to prevent Sentry from capturing exceptions outside custom handling
#sentry.auto-init=false
# Disable default PII and any global capturing
#sentry.send-default-pii=false
#sentry.capture-handled-errors=false
#sentry.logging.minimum-event-level=ERROR
#sentry.logging.level=ERROR
#sentry.stacktrace.attach=true

#aws.access_key_id = ${AWS_ACCESS_KEY_ID}
#aws.secret_access_key = ${AWS_SECRET_ACCESS_KEY}
aws.s3.bucketName = ${AWS_BUCKET_NAME}
aws.s3.region = ${AWS_REGION}
aws.s3.endpoint = ${AWS_END_POINT}

service.type.prefix = service_type
pet.type.prefix = pet_type
pet.photos.prefix = pet_photo
pet.documents.prefix = pet_document
service.photos.prefix = service_photos
appointment.daily.export.prefix=appointment_daily_export

## Scheduler
appointment.slot.interval = 15
#how far the system must look into the future to determine timeslots
appointment.lookAheadPeriod = 3

##FileSize
spring.servlet.multipart.max-file-size = 2MB

#quartz
spring.datasource.initialization-mode=always
spring.quartz.job.store.type = JDBC
spring.quartz.properties.org.quartz.threadPool.threadCount = 1

# SENDGRID CONFIG (SendGridAutoConfiguration)
spring.sendgrid.api-key = ${SENDGRID_API_KEY}

##Ecomm Authentication Keys

ecom.auth.uri = ${ECOMM_API_BASE_URL}
ecom.auth.client.id = ${ECOMM_API_CLIENT_ID}
ecom.auth.client.secret = ${ECOMM_API_CLIENT_SECRET}

##POS API Configuration
##pos.api.base.url = ${POS_API_BASE_URL}
##pos.api.auth.uri = ${pos.api.base.url}/auth/api/v1
##pos.auth.client.id=${POS_API_CLIENT_ID}
##pos.auth.client.secret=${POS_API_CLIENT_SECRET}

##FeignClient Configuration
feign.client.config.pos-api-client.connectTimeout=5000
feign.client.config.pos-api-client.readTimeout=10000
feign.client.config.pos-api-client.loggerLevel=full

##FeignClient URL Configuration
pos.api.feign.url=${FEIGN_CLIENT_URL}

##Set scheduler time
##if schedulerInterval set in env variable then schedule interval will be that value.
##By default, the value is 24 hours
scheduler.timeintervel = ${SCHEDULER_INTERVEL:1440}

#This is done in order to mitigate the zero-day exploit on log4j (Can be removed if log4j has been upgraded to 2.15rc2)
log4j2.formatMsgNoLookups=true

# Kinesis config
aws.kinesis.app-name=${KINESIS_APP_NAME}
aws.kinesis.region=${KINESIS_REGION}
aws.kinesis.stream=${KINESIS_STREAM_FOR_SERVICE_SYNC}
aws.kinesis.shard.id.servicing=${AWS_KINESIS_SHARD_FOR_SERVICING}
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

#kinesis config for slot generation
kinesis.stream-name=${KINESIS_STREAM_FOR_SLOT_GENERATION}
kinesis.polling-interval=5000
kinesis.max-records=25
kinesis.shard.id.slots=${KINESIS_SHARD_FOR_SLOT_GENERATION}


#spring batch metatable creation
spring.batch.initialize-schema=always
spring.batch.job.enabled=false

spring.datasource.hikari.maximumPoolSize=45
spring.datasource.hikari.connectionTimeout=30000
spring.datasource.hikari.idleTimeout=600000
spring.datasource.hikari.maxLifetime=1800000

# Google Calendar API Configuration
google.calendar.client-id=${GOOGLE_CALENDAR_CLIENT_ID}
google.calendar.client-secret=${GOOGLE_CALENDAR_CLIENT_SECRET}
# Redirect URI should point to FRONTEND callback page (configured in Google Cloud Console)
# This is where Google redirects after user authorizes
google.calendar.redirect-uri=${GOOGLE_CALENDAR_REDIRECT_URI:http://localhost:3000/google-calendar-sync/callback}
# Frontend URL for invitation emails (where user clicks from email)
google.calendar.frontend-url=${GOOGLE_CALENDAR_FRONTEND_URL}
google.calendar.application-name=ETailBookIt
google.calendar.scopes=https://www.googleapis.com/auth/calendar