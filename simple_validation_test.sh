#!/bin/bash

echo "=== COMPREHENSIVE BLOCK VALIDATION TEST ==="
echo "Testing all validation scenarios including NEW CONSISTENCY FIX"
echo ""

BASE_URL="http://localhost:8080/api/v1/block_dates"
TIMEZONE="UTC"

# Clean up existing test data first
echo "=== CLEANUP: Removing existing test blocks ==="
curl -s -H "retailer: test-retailer" -H "User-type: Retailer" -X DELETE "$BASE_URL/cleanup-test-data" > /dev/null 2>&1
echo "Cleanup completed"
echo ""

# Helper function to make curl requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "TEST: $description"
    echo "Request: $method $endpoint"
    if [ ! -z "$data" ]; then
        echo "Data: $data"
    fi
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -H "retailer: test-retailer" \
        -H "User-type: Retailer" \
        -X POST "$endpoint" \
        -d "$data")
    
    echo "Response: $response"
    echo "----------------------------------------"
    echo ""
}

# Test 1: Basic STORE block creation (no attendant required)
echo "=== SECTION 1: BASIC STORE BLOCK TESTS ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-15T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-12-15T09:00:00Z",
            "blockEndTime": "2025-12-15T17:00:00Z"
        }
    ]
}' "1.1 Create STORE partial block"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-16T00:00:00Z",
    "blockType": "STORE",
    "blockFullDay": true
}' "1.2 Create STORE full day block"

# Test 2: Test our NEW CONSISTENCY FIX
echo "=== SECTION 2: TESTING NEW CONSISTENCY FIX ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-17T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-12-17T10:00:00Z",
            "blockEndTime": "2025-12-17T12:00:00Z"
        }
    ]
}' "2.1 Create STORE partial block first"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-17T00:00:00Z",
    "blockType": "STORE",
    "blockFullDay": true
}' "2.2 Try STORE full day (should REJECT - NEW CONSISTENCY FIX)"

# Test 3: Time overlap tests
echo "=== SECTION 3: TIME OVERLAP TESTS ==="

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-18T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-12-18T10:00:00Z",
            "blockEndTime": "2025-12-18T12:00:00Z"
        }
    ]
}' "3.1 Create STORE block 10:00-12:00"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-18T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-12-18T12:00:00Z",
            "blockEndTime": "2025-12-18T14:00:00Z"
        }
    ]
}' "3.2 Try STORE block 12:00-14:00 (touching boundary - should ALLOW)"

make_request "POST" "$BASE_URL/create-with-multipletime-range?tz=$TIMEZONE" \
'{
    "date": "2025-12-18T00:00:00Z",
    "blockType": "STORE",
    "blockTimes": [
        {
            "blockStartTime": "2025-12-18T11:30:00Z",
            "blockEndTime": "2025-12-18T13:30:00Z"
        }
    ]
}' "3.3 Try STORE block 11:30-13:30 (overlapping - should REJECT)"

# Test 4: Recurring validation with proper date format
echo "=== SECTION 4: RECURRING VALIDATION ==="

make_request "POST" "$BASE_URL/create-recurring?tz=$TIMEZONE" \
'{
    "date": "2025-12-20T00:00:00Z",
    "blockType": "STORE",
    "blockFullDay": true,
    "recurringEnabled": true,
    "recurringPattern": "DAILY",
    "recurringInterval": 1,
    "recurringEndDate": "2025-12-22T00:00:00Z"
}' "4.1 Try 3-day STORE full day recurring (should ALLOW - no conflicts)"

echo "=== TEST SUMMARY ==="
echo "Completed basic validation tests focusing on:"
echo "1. Basic STORE block creation"
echo "2. NEW CONSISTENCY FIX verification"
echo "3. Time overlap edge cases"
echo "4. Recurring block validation"
